{"id": "d0686461-abff-4a57-a73f-b5ae5f42ac36", "title": "làm nó ng<PERSON> h<PERSON>n", "messages": [{"role": "user", "applyStatus": 0, "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "làm nó ng<PERSON> h<PERSON>n", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "<task>\nlàm nó ngắn hơn\n</task>\n\n<file_block_content location=\"Machine learning - Deep Learning - AI - ML - DL.md#L285-321\">\n285 | - Weaviate (https://github.com/weaviate/weaviate)\n286 | \t- <PERSON><PERSON> sở dữ liệu vector mã nguồn mở, cloud-native.\n287 | \t- L<PERSON>u trữ đối tượng và vector nhúng; hỗ trợ tìm kiếm vector ngữ nghĩa kết hợp lọc dữ liệu có cấu trúc.\n288 | \t- Cung cấp API GraphQL/REST, kiến trúc module hóa.\n289 | \t- T<PERSON><PERSON> hợ<PERSON> với OpenAI, Cohere, Hugging Face.\n290 | - Milvus (https://github.com/milvus-io/milvus)\n291 | \t- <PERSON><PERSON> thống cơ sở dữ liệu vector mã nguồn mở, cloud-native.\n292 | \t- <PERSON><PERSON><PERSON><PERSON> qu<PERSON>n lý, lập chỉ mục và tìm kiếm vector nhúng quy mô lớn.\n293 | \t- Hỗ trợ nhiều loại chỉ mục vector, thước đo khoảng cách.\n294 | \t- <PERSON><PERSON> cấp SDK đa ngôn ngữ, kiến trúc phân tán, khả năng mở rộng cao.\n295 | \t- Phù hợp cho tìm kiếm đa phương tiện, gợi ý sản phẩm, phân tích văn bản.\n296 | - <PERSON>aiss (https://github.com/facebookresearch/faiss)\n297 | \t- Thư viện mã nguồn mở từ Facebook AI Research (FAIR).\n298 | \t- Cung cấp thuật toán hiệu quả cho tìm kiếm tương đồng và phân cụm vector dày đặc.\n299 | \t- Không phải DB hoàn chỉnh, tập trung vào thuật toán cốt lõi.\n300 | \t- Tối ưu tốc độ/bộ nhớ, xử lý tập vector lớn, hỗ trợ CPU/GPU.\n301 | \t- Code C++ với giao diện Python.\n302 | - Cognita - https://github.com/truefoundry/cognita: Framework mã nguồn mở xây dựng ứng dụng RAG.\n303 | \t- Mục tiêu: Đơn giản hóa phát triển RAG, giảm boilerplate.\n304 | \t- Kiến trúc module hóa, dễ tùy chỉnh.\n305 | \t- Cung cấp các thành phần: Data Loaders, Parsers/Chunkers, Embedders, Vector Stores, Retrievers, LLMs, Orchestrator.\n306 | \t- Hỗ trợ đa dạng nguồn dữ liệu, vector DB, LLM.\n307 | \t- Tính năng nâng cao: Reranking, Query Transformations, Hybrid Search, Metadata Filtering, Context Window Management, Evaluation.\n308 | \t- Lợi ích: Tăng tốc phát triển, linh hoạt, giảm phức tạp.\n309 | - LLMWare:\n310 | \t- Framework mã nguồn mở xây dựng ứng dụng LLM doanh nghiệp (RAG, Agent).\n311 | \t- Cung cấp công cụ xây dựng pipeline RAG (parsing, embedding, truy vấn, phản hồi có kiểm chứng).\n312 | \t- Hỗ trợ nhiều loại dữ liệu và tích hợp mô hình mã nguồn mở/GGUF.\n313 | \t- Có ví dụ thực tế, chạy được trên CPU.\n314 | \t- Cung cấp Model HQ để triển khai mô hình cục bộ.\n315 | - GraphRAG\n316 | \t- Kỹ thuật kết hợp RAG với knowledge graph để cải thiện ngữ cảnh LLM.\n317 | \t- Sử dụng đồ thị tri thức (thực thể + mối quan hệ) để cung cấp ngữ cảnh liên quan và chính xác hơn.\n318 | \t- Hoạt động: Phân tích câu hỏi, truy vấn đồ thị, trích xuất ngữ cảnh, cung cấp cho LLM để tạo câu trả lời.\n319 | \t- Ưu điểm so với RAG truyền thống: Hiểu ngữ cảnh sâu sắc, truy vấn phức tạp/suy luận đa bước, tăng giải thích được, giảm nhiễu, kết hợp dữ liệu.\n320 | \t- Ứng dụng: Hỏi đáp chuyên ngành, phân tích tri thức, trợ lý ảo, phát hiện gian lận.\n321 | \t- Thách thức: Xây dựng/bảo trì đồ thị, tích hợp đồ thị-LLM, đảm bảo dữ liệu cập nhật.\n</file_block_content>\n\n<environment_details>\n# Obsidian Current File\nMachine learning - Deep Learning - AI - ML - DL.md\n\n# Obsidian Open Tabs\nWorking.md\nMachine learning - Deep Learning - AI - ML - DL.md\n\n# Current Time\n5/22/2025, 11:53:03 PM (Asia/Saigon, UTC+7:00)\n\n# Current Mode\n<slug>write</slug>\n<name>Write</name>\n</environment_details>"}], "id": "1b455641-a20b-4cad-a511-88f611c072f5", "mentionables": [{"type": "block", "content": "- Weaviate (https://github.com/weaviate/weaviate)\n\t- <PERSON><PERSON> sở dữ liệu vector mã nguồn mở, cloud-native.\n\t- Lưu trữ đối tượng và vector nhúng; hỗ trợ tìm kiếm vector ngữ nghĩa kết hợp lọc dữ liệu có cấu trúc.\n\t- Cung cấp API GraphQL/REST, kiến trúc module hóa.\n\t- Tích hợp với OpenAI, Cohere, Hugging Face.\n- Milvus (https://github.com/milvus-io/milvus)\n\t- <PERSON><PERSON> thống cơ sở dữ liệu vector mã nguồn mở, cloud-native.\n\t- <PERSON><PERSON><PERSON><PERSON> quản lý, lập chỉ mục và tìm kiếm vector nhúng quy mô lớn.\n\t- Hỗ trợ nhiều loại chỉ mục vector, thước đo khoảng cách.\n\t- <PERSON><PERSON> cấp SDK đa ngôn ngữ, kiến trúc phân tán, <PERSON>h<PERSON> năng mở rộng cao.\n\t- <PERSON><PERSON> hợp cho tìm kiếm đa phương tiện, gợi ý sản phẩm, phân tích văn bản.\n- Faiss (https://github.com/facebookresearch/faiss)\n\t- Thư viện mã nguồn mở từ Facebook AI Research (FAIR).\n\t- Cung cấp thuật toán hiệu quả cho tìm kiếm tương đồng và phân cụm vector dày đặc.\n\t- Không phải DB hoàn chỉnh, tập trung vào thuật toán cốt lõi.\n\t- Tối ưu tốc độ/bộ nhớ, xử lý tập vector lớn, hỗ trợ CPU/GPU.\n\t- Code C++ với giao diện Python.\n- Cognita - https://github.com/truefoundry/cognita: Framework mã nguồn mở xây dựng ứng dụng RAG.\n\t- Mục tiêu: Đơn giản hóa phát triển RAG, giảm boilerplate.\n\t- Kiến trúc module hóa, dễ tùy chỉnh.\n\t- Cung cấp các thành phần: Data Loaders, Parsers/Chunkers, Embedders, Vector Stores, Retrievers, LLMs, Orchestrator.\n\t- Hỗ trợ đa dạng nguồn dữ liệu, vector DB, LLM.\n\t- Tính năng nâng cao: Reranking, Query Transformations, Hybrid Search, Metadata Filtering, Context Window Management, Evaluation.\n\t- Lợi ích: Tăng tốc phát triển, linh hoạt, giảm phức tạp.\n- LLMWare:\n\t- Framework mã nguồn mở xây dựng ứng dụng LLM doanh nghiệp (RAG, Agent).\n\t- Cung cấp công cụ xây dựng pipeline RAG (parsing, embedding, truy vấn, phản hồi có kiểm chứng).\n\t- Hỗ trợ nhiều loại dữ liệu và tích hợp mô hình mã nguồn mở/GGUF.\n\t- Có ví dụ thực tế, chạy được trên CPU.\n\t- Cung cấp Model HQ để triển khai mô hình cục bộ.\n- GraphRAG\n\t- Kỹ thuật kết hợp RAG với knowledge graph để cải thiện ngữ cảnh LLM.\n\t- Sử dụng đồ thị tri thức (thực thể + mối quan hệ) để cung cấp ngữ cảnh liên quan và chính xác hơn.\n\t- Hoạt động: Phân tích câu hỏi, truy vấn đồ thị, trích xuất ngữ cảnh, cung cấp cho LLM để tạo câu trả lời.\n\t- Ưu điểm so với RAG truyền thống: Hiểu ngữ cảnh sâu sắc, truy vấn phức tạp/suy luận đa bước, tăng giải thích được, giảm nhiễu, kết hợp dữ liệu.\n\t- Ứng dụng: Hỏi đáp chuyên ngành, phân tích tri thức, trợ lý ảo, phát hiện gian lận.\n\t- Thách thức: Xây dựng/bảo trì đồ thị, tích hợp đồ thị-LLM, đảm bảo dữ liệu cập nhật.", "file": "Machine learning - Deep Learning - AI - ML - DL.md", "startLine": 285, "endLine": 321}]}, {"role": "assistant", "applyStatus": 0, "content": "", "reasoningContent": "", "id": "9451914a-a36c-4031-ad7b-bd113e8bc803", "metadata": {}}], "createdAt": 1747932621873, "updatedAt": 1747932784196, "schemaVersion": 1}