{"index": {"vectorIndexes": {"embedding": {"size": 768, "vectors": {"439623cad75dee7eaeace4bb00794485": [0.9998359719660047, [0.012018117122352123, 0.05290953442454338, -0.053215041756629944, -0.02716613933444023, 0.03764943405985832, 0.01586076244711876, 0.03182004392147064, -0.0028358804993331432, 0.032275520265102386, -0.018314305692911148, -0.014473380520939827, 0.13709574937820435, 0.06747134029865265, -0.0009117877925746143, 0.05175172910094261, -0.02358897030353546, 0.004437282681465149, 0.031151182949543, -0.06112074851989746, -0.03889773413538933, -0.0005544688319787383, 0.021364862099289894, 0.05406118184328079, 0.006972399540245533, -0.026973819360136986, -0.05412532016634941, -0.018546124920248985, 0.038060981780290604, 0.018370013684034348, -0.09597281366586685, 0.03454909101128578, 0.045316919684410095, -0.007874622009694576, 0.046741046011447906, 0.021372880786657333, 0.03544524684548378, 0.04965929314494133, 0.0034438474103808403, 0.0006497420836240053, -0.09598139673471451, -0.03751415014266968, -0.008309351280331612, 0.014741014689207077, 0.05521417409181595, -0.0143711157143116, -0.07355937361717224, 0.0007462096982635558, 0.01855670101940632, -0.04544713348150253, -0.00870338175445795, 0.03582726791501045, 0.0005745964590460062, 0.026996739208698273, 0.03601802885532379, -0.049563996493816376, -0.0008475424256175756, -0.028477605432271957, -0.0698307454586029, 0.02412421815097332, -0.020976796746253967, -0.028848208487033844, -0.04546864703297615, -0.001106946263462305, -0.04189262166619301, -0.0181391853839159, -0.026645202189683914, -0.008527621626853943, -0.0062613533809781075, -0.027589358389377594, -0.006251920014619827, -0.05170688033103943, 0.04207995533943176, -0.04016765579581261, 0.03623519837856293, 0.014548087492585182, -0.018772665411233902, -0.006139752455055714, 0.023886527866125107, 0.08427055180072784, 0.032228220254182816, -0.07930534332990646, -0.007022109813988209, 0.02887600287795067, 0.05697164312005043, 0.0330207459628582, -0.006755568087100983, -0.03000345453619957, -0.014922112226486206, -0.09478923678398132, -0.02641664259135723, 0.022433409467339516, 0.011415833607316017, -0.0015136233996599913, -0.023235443979501724, 0.06118730455636978, -0.0357324592769146, -0.09457098692655563, -0.09292511641979218, 0.07834115624427795, 0.010358423925936222, -0.0035170470364391804, 0.020911039784550667, -0.023610452190041542, -0.03981681913137436, 0.042996130883693695, -0.01621040143072605, -0.00691538630053401, -0.01324276439845562, -0.07384040206670761, 0.0479036420583725, 0.004852581769227982, -0.038232095539569855, 0.04117945209145546, -0.03697393834590912, -0.024827808141708374, -0.0010169186862185597, -0.0038827599491924047, 0.014224992133677006, -0.06523141264915466, -0.026564940810203552, 0.030242323875427246, 0.001937362365424633, -0.01019015908241272, 0.07229271531105042, 0.013251673430204391, 0.008382191881537437, -0.014399316161870956, -0.03513113409280777, -0.034340716898441315, -0.03295672684907913, 0.03921313211321831, -0.03570682555437088, -0.039445798844099045, 0.0170116126537323, -0.0429869219660759, 0.023454053327441216, 0.04932228475809097, -0.005314821843057871, 0.007846123538911343, 0.01862107403576374, -0.04264869913458824, -0.05532580614089966, -0.048553165048360825, -0.023008694872260094, -0.006092725787311792, -0.01820603385567665, -0.0045495894737541676, 0.09147621691226959, -0.04205384477972984, 0.031260740011930466, -0.05117693170905113, 0.03105260618031025, -0.045100606977939606, -0.007557863835245371, 0.001111802994273603, 0.023438850417733192, 0.01923067308962345, -0.013386399485170841, 0.03549482300877571, 0.04010806232690811, -0.0017575626261532307, -0.015373836271464825, -0.005076443776488304, 0.038534291088581085, 0.006898269057273865, -0.046081606298685074, -0.01542012020945549, -0.02979821152985096, 0.04291548579931259, -0.017530830577015877, 0.0074729351326823235, -0.09354578703641891, -0.046643782407045364, -0.05159171298146248, 0.006085082422941923, -0.0061097899451851845, -2.1663023289875127e-05, 0.055768612772226334, -0.00305677461437881, 0.025715796276926994, 0.08611199259757996, 0.013492582365870476, -0.01718556135892868, -0.0484171099960804, 0.025243086740374565, -0.010307344608008862, -0.017590846866369247, 0.022324584424495697, 0.02043953351676464, 0.04667384549975395, 0.006514118518680334, 0.034919627010822296, 0.017490874975919724, 0.0330035537481308, -0.019102318212389946, 0.01599697209894657, 0.09477124363183975, -0.03314129263162613, -0.00594451604411006, 0.0010396585566923022, 0.031121572479605675, 0.0101450951769948, -0.05908263474702835, -0.06884270906448364, -0.03859992325305939, 0.052966803312301636, -0.008116072975099087, -0.02491300366818905, -0.03714907169342041, 0.004168405197560787, -0.07453621923923492, -0.03420555964112282, -0.006211767438799143, -0.04875761643052101, -0.030037803575396538, -0.012947299517691135, 0.07345594465732574, -0.002976952586323023, 0.07715504616498947, -0.06961683928966522, -0.003604140132665634, -0.014489774592220783, 0.013898352161049843, 0.06426964700222015, 0.037544649094343185, -0.024459466338157654, 0.02284866012632847, -0.01160437986254692, -0.03633006289601326, -0.05224911868572235, 0.016134275123476982, 0.03453127294778824, 0.0037030947860330343, 0.06268106400966644, -0.0037761994171887636, 0.019866667687892914, -0.015958644449710846, -0.05032651871442795, -0.027389829978346825, 0.029741039499640465, 0.003583956742659211, -0.008778945542871952, 0.04998573660850525, -0.004306292627006769, 0.018913228064775467, -0.006924584973603487, 0.02049541287124157, 0.05128283426165581, -0.014163143932819366, -0.03724025934934616, -0.01674569398164749, -0.023951059207320213, 0.030281225219368935, -0.03588242828845978, -0.04089570418000221, -0.017641322687268257, 0.05058816447854042, 0.017132218927145004, -0.030878452584147453, 0.009897817857563496, 0.036707572638988495, -0.03334721550345421, -0.0100517887622118, -0.05836072191596031, -0.042728833854198456, -0.038671158254146576, -0.01679714024066925, -0.03989076614379883, 0.04798842966556549, -0.015609534457325935, 0.03324606269598007, -0.07881531119346619, -0.030695145949721336, -0.0029971685726195574, -0.022982951253652573, 0.03971291333436966, -0.03916417807340622, 0.03301844745874405, -0.043923694640398026, -0.045027244836091995, 0.012378249317407608, 0.0021787122823297977, -0.02385523170232773, 0.01904001645743847, 0.01848442852497101, -0.03319254890084267, 0.009764830581843853, 0.00564054399728775, -0.03187117725610733, 0.0052397423423826694, 0.029070008546113968, 0.06272720545530319, 0.0062643601559102535, -0.032122597098350525, 0.04852282628417015, -0.008941428735852242, 0.033237095922231674, 0.0879739299416542, -0.001044943812303245, 0.001092849881388247, 0.004771892912685871, 0.025393597781658173, 0.009242990985512733, 0.03266302868723869, 0.015527946874499321, -0.003757753875106573, -0.034771982580423355, 0.021694982424378395, -0.012212024070322514, -0.01246209628880024, 0.08360499143600464, 0.08720799535512924, -0.07559170573949814, 0.0007794616394676268, -0.020749319344758987, 0.01271662674844265, -0.12606501579284668, -0.04508313909173012, 0.0034303448628634214, 0.029568498954176903, 0.028002724051475525, 0.03428995981812477, -0.026191161945462227, 0.012680426239967346, 0.062139417976140976, 0.002306041307747364, 0.01618516631424427, -0.006737279240041971, 0.014823699370026588, -0.029646288603544235, 0.01957663521170616, -0.02883387915790081, 0.006525934673845768, -0.006064515560865402, 0.013443274423480034, 0.034268442541360855, -0.06505712121725082, 0.01862669363617897, 0.07757189124822617, 0.02017507702112198, 0.0492519773542881, 0.06584808975458145, -0.011065836064517498, 0.02258625067770481, 0.017053982242941856, -0.05227767676115036, 0.022223439067602158, 0.00331522012129426, 0.009427045471966267, 0.05322156473994255, 0.07954227924346924, 0.0822780504822731, 0.0003557998570613563, -0.03453236073255539, 0.024461235851049423, 0.03234827145934105, 0.051171302795410156, 0.018564701080322266, -0.005272827111184597, -0.037366874516010284, 0.0075592175126075745, 0.022941263392567635, -0.00738311605527997, 0.0653267651796341, -0.0015356832882389426, -0.009409390389919281, 0.042685769498348236, 0.061402782797813416, -0.0338674858212471, -0.04669930785894394, -0.013494313694536686, 0.0035117522347718477, -0.04686448350548744, 0.02225414477288723, -0.011846656911075115, 0.0022187577560544014, -0.021202905103564262, 0.020240068435668945, 0.03375621512532234, -0.04728759825229645, 0.005200662650167942, -0.017047978937625885, -0.033428847789764404, 0.03067709505558014, 0.009015095420181751, 0.05202716216444969, -0.06531579792499542, -0.011645359918475151, -0.03485532104969025, 0.013869819231331348, -0.07311540096998215, 0.0008021248504519463, 0.011354097165167332, -0.015030085109174252, -0.009855496697127819, 0.05148117616772652, 0.022643666714429855, 0.02224833332002163, 0.04006505757570267, 0.03286319971084595, -0.013472074642777443, -0.02835797145962715, 0.06078069284558296, -0.01676490530371666, 0.038625068962574005, -0.006693702191114426, 0.06558989733457565, -0.01287717092782259, -0.04392891749739647, 0.005926703568547964, -0.02313428930938244, -0.05209678038954735, 0.021093269810080528, 0.014386764727532864, -0.03152456507086754, -0.03383322060108185, -0.004131144378334284, -0.035992544144392014, 0.01886044442653656, 0.009201327338814735, -0.020865226164460182, 0.06890429556369781, 0.061151888221502304, 0.02145170234143734, -0.009535406716167927, -0.05477422475814819, 0.008864977397024632, -0.01829613372683525, -0.03441345691680908, -0.027011051774024963, 0.05999532341957092, 0.056872643530368805, 0.008474387228488922, 0.025731123983860016, -0.03231390565633774, 0.014407225884497166, 0.01111492421478033, -0.034909944981336594, 0.040767595171928406, 0.027549203485250473, 0.0002179759176215157, -0.008641012944281101, 0.043710824102163315, -0.0031752127688378096, -0.04112270101904869, 0.03340287134051323, 0.023950764909386635, 0.031254492700099945, -0.017121950164437294, -0.028938347473740578, -0.0018804467981681228, -0.012576268054544926, 0.01485138013958931, -0.03431791812181473, -0.09005293995141983, 0.018980467692017555, 0.013907869346439838, 0.053720999509096146, -0.0037895028945058584, -0.0015157180605456233, -0.005688284523785114, -0.03504512831568718, 0.022034507244825363, 0.0005591214867308736, -0.017611395567655563, -0.0238266009837389, -0.052470408380031586, 0.030242230743169785, -0.019309962168335915, -0.011322320438921452, 0.03376087173819542, 0.018968211486935616, 0.03185112401843071, 0.026615414768457413, 0.033091913908720016, 0.00612614443525672, 0.0656970962882042, 0.0194218959659338, -0.051974982023239136, -0.020892972126603127, -0.0591028667986393, 0.009006497450172901, -0.0418245792388916, 0.0003555900475475937, 0.03683930262923241, 0.007373191881924868, 0.005376340821385384, 0.01713929884135723, 0.004983758553862572, 0.016781743615865707, 0.02549394592642784, 0.013451255857944489, -0.01576041989028454, -0.06110100448131561, 0.002179476898163557, -0.006566747557371855, 0.044141437858343124, -0.0051614041440188885, -0.024266228079795837, 0.00429424736648798, 0.0745602399110794, 0.039364319294691086, 0.0027257977053523064, -0.052764151245355606, -0.0202911589294672, 0.03256580978631973, -0.05419178679585457, -0.050377119332551956, 0.0770459696650505, 0.0024499252904206514, 0.021827423945069313, -0.05458923056721687, -0.02714286372065544, -0.0012915761908516288, -0.03842560201883316, 0.019165895879268646, -0.020254334434866905, -0.004067972768098116, -0.053416430950164795, 0.05225149169564247, 0.04978971555829048, -0.03692033886909485, 0.018725840374827385, -0.02013016864657402, 0.01868274435400963, -0.004240626934915781, 0.01974404603242874, 0.01200054306536913, 0.0331498347222805, -0.010742710903286934, -0.033792074769735336, 0.034000590443611145, 0.06511937081813812, 0.023196643218398094, 0.0013582155806943774, 0.07098738104104996, 0.012483615428209305, -0.02298903279006481, 0.02148367278277874, 0.024602755904197693, 0.026790224015712738, 0.004316835664212704, -0.04180192947387695, 0.03853132203221321, 0.03028392419219017, 0.024739114567637444, 0.004498019814491272, 0.029812045395374298, -0.01855647750198841, 0.0491931289434433, 0.011465665884315968, -0.03664739429950714, -0.03523733466863632, -0.021188631653785706, -0.004414924420416355, -0.03943413123488426, 0.04990671947598457, -0.009299849160015583, -0.035075198858976364, -0.00864044576883316, 0.013017522171139717, -0.021813513711094856, 0.026762917637825012, -0.012393970973789692, -0.006699798628687859, -0.033967290073633194, 0.0005898874369449914, 0.05677960067987442, -0.01557396911084652, -0.0386274978518486, 0.053084079176187515, -0.0191655233502388, 0.006303383037447929, -0.021370334550738335, 0.011007236316800117, -0.058587606996297836, -0.03882589191198349, -0.0007657206151634455, 0.07295974344015121, 9.143388888332993e-05, -0.02295662648975849, 0.015295617282390594, 0.05358564853668213, -0.014322220347821712, -0.02859618328511715, -0.0099794315174222, -0.018518753349781036, -0.00034282542765140533, 0.020300954580307007, -0.0024005796294659376, -0.003126378171145916, 0.015072295442223549, -0.0010309815406799316, -0.08397302776575089, 0.049900978803634644, -0.025047222152352333, -0.014429023489356041, 0.03654497489333153, 0.008961671963334084, -0.007310052867978811, -0.0042161825112998486, -0.0029325669165700674, 0.020325833931565285, 0.0038202060386538506, -0.008593653328716755, -0.06329711526632309, 0.028044717386364937, -0.07242823392152786, 0.015501705929636955, 0.015495186671614647, -0.023111993446946144, 0.058396339416503906, 0.0038671873044222593, -0.040921956300735474, -0.06301302462816238, 0.03301277756690979, -0.030816255137324333, -0.0010830404935404658, 0.028738265857100487, 0.04582209885120392, 0.022023838013410568, 0.011267544701695442, -0.02385953813791275, 0.008739680983126163, -0.05068773403763771, 0.022081157192587852, 0.026431633159518242, 0.0010581872193142772, 0.008719580247998238, 0.006712131202220917, 0.04117147624492645, -0.015121831558644772, 0.008070612326264381, -0.03497573360800743, -0.012329570017755032, 0.025058778002858162, -0.013661108911037445, -0.013391000218689442, 0.004892969503998756, -0.0658688023686409, -0.0010548895224928856, 0.03428887203335762, 0.06872377544641495, -0.03431877866387367, -0.0492008700966835, 0.012073456309735775, -0.03108743391931057, 0.02990204654633999, -0.029186628758907318, 0.0652405396103859, 0.044089727103710175, 0.038919977843761444, -0.0679585263133049, 0.0688968300819397, -0.02200779877603054, 0.01375699695199728, -0.0010449563851580024, -0.0073294066824018955, -0.019885331392288208, -0.006634279154241085, -0.01961451582610607, -0.007583262398838997, -0.014011567458510399, -0.004157503135502338, -0.05779876932501793, -0.012539207935333252, -0.06495733559131622, 0.01061281282454729, -0.000569444615393877, 0.0006452069501392543, -0.015540968626737595, 0.016938380897045135, 0.0028390120714902878, 0.05829779803752899, 0.033380940556526184, 0.0005062824347987771, -0.01559047494083643, 0.0447569377720356, -0.024054914712905884, 0.0308810006827116, -0.02392381988465786, -0.011828973889350891, 0.018068647012114525, -0.021592894569039345, -0.010187932290136814, 0.03233475238084793, 0.04852106794714928, -0.002295201178640127, 0.0035789203830063343, 0.0075853317975997925, -0.0005154396640136838, -0.050330888479948044, -0.024883229285478592, 0.045838337391614914, -0.02042902261018753, 0.059267736971378326, 0.0009984634816646576, -0.026999512687325478, -0.013775608502328396, -0.00822555273771286, -0.03938858211040497, 0.009517607279121876, 0.002977444790303707, -0.07366074621677399, 0.03146958723664284, -0.017525870352983475, 0.07047662883996964, 0.013543643057346344, -0.04366893321275711, 0.043209176510572433, -0.016105467453598976, 0.04962462559342384, 0.0482339970767498, -0.025594837963581085, 0.022451117634773254, 0.0422092080116272, 0.01093991007655859, 0.013436656445264816, -0.04557926580309868, 0.044968441128730774, 0.04934900999069214, -0.025830727070569992, 0.036178648471832275, 0.023617560043931007, 0.04883618280291557, 0.022888530045747757, -0.06455991417169571, 0.005846234504133463, -0.03142871335148811, 0.07594271749258041, 0.014151892624795437, -0.01015571877360344, -0.04768485948443413, 0.04643353447318077, -0.0799025446176529, -0.024686848744750023, -0.003659630659967661, -0.014066836796700954, -0.018221542239189148, 0.0005797956837341189, 0.03813053295016289, -0.021112220361828804, -0.04470045492053032, -0.03814985230565071, 0.007745365612208843, 0.02936638332903385, 0.019819704815745354, -0.017431214451789856, 0.001925335149280727, -0.06408512592315674, -0.05173134431242943, 0.013001649640500546, 0.010897373780608177, -0.04516267776489258, 0.0903669074177742, 0.03279035910964012, -0.037061333656311035, -0.013495809398591518, 0.027420533820986748, 0.02090560644865036, -0.013995472341775894, 0.04089284688234329, -0.01605907641351223, 0.0674559697508812, -0.017422877252101898, 0.003213586052879691, 0.05215270444750786, -0.035878490656614304]], "efdabfec33e46eda7890ba8ca97e2b97": [0.9999916437227913, [0.022437000647187233, 0.045646000653505325, -0.03582138568162918, -0.0033321164082735777, 0.011381412856280804, -0.0019068631809204817, 0.0394035167992115, -0.004016705323010683, -0.004122875165194273, -0.015304163098335266, 0.02220076136291027, 0.0474688857793808, 0.06930358707904816, -0.02484910748898983, 0.03526464104652405, -0.033164896070957184, 0.028084812685847282, -0.016550078988075256, -0.0068399072624742985, -0.014614135958254337, -0.041239168494939804, -0.008596298284828663, 0.012768534943461418, -0.012110143899917603, -0.036898549646139145, -0.003607451682910323, 4.093077586730942e-05, 0.022546974942088127, -0.0036850511096417904, -0.04665769636631012, 0.03807345777750015, 0.025138743221759796, -0.056359220296144485, 0.009901859797537327, 0.04223901033401489, 0.05548767372965813, -0.017847547307610512, 0.07320120930671692, 0.004276788793504238, -0.03310791030526161, -0.05705327168107033, -0.005464975256472826, 0.005847001913934946, 0.04616302624344826, 0.01026598084717989, -0.00245337700471282, 0.023594584316015244, 0.003526321379467845, -0.050854217261075974, 0.020571529865264893, 0.02833455242216587, 0.010277222841978073, -0.018446065485477448, 0.042165808379650116, -0.05940231308341026, -0.06662487983703613, -0.055264994502067566, -0.053904253989458084, 0.017931466922163963, 0.012196864001452923, -0.052551303058862686, -0.0581376813352108, -0.06078813225030899, -0.047506749629974365, -0.02202850766479969, 0.009175142273306847, -0.011628218926489353, -0.02260679192841053, -0.012720716185867786, -0.0031070057302713394, -0.048918671905994415, 0.03831743448972702, -0.046902142465114594, 0.010367373004555702, 0.007048200815916061, -0.007440072949975729, -0.02217848226428032, -0.04464898630976677, 0.030902227386832237, 0.04298706352710724, 0.008139674551784992, -0.029001746326684952, 0.05032787844538689, 0.05613740533590317, 0.02393859066069126, -0.007513849530369043, -0.06293141841888428, -0.004433544352650642, -0.001656054868362844, 0.044148243963718414, 0.04748982936143875, 0.022553520277142525, -0.00019806079217232764, -0.02322992868721485, 0.04965771734714508, -0.010832007974386215, -0.05819357931613922, -0.11905542016029358, 0.04112512618303299, 0.07919959723949432, 0.013206951320171356, 0.005277369171380997, 0.012090052478015423, -0.082607701420784, -0.002838210668414831, -0.05002318695187569, -0.04747677966952324, 0.00491676339879632, -0.10436422377824783, 0.021885599941015244, 0.0023675428237766027, -0.00240949890576303, 0.042317647486925125, 0.011815909296274185, -0.06291849911212921, -0.016847658902406693, -0.0011263438500463963, -0.0010142209939658642, -0.006159070413559675, -0.011760461144149303, 0.05974758043885231, 0.0553738996386528, -0.02166503667831421, 0.044308457523584366, 0.03869958221912384, 0.020264413207769394, 0.004247705917805433, 0.015041349455714226, 0.01930980756878853, -0.028254203498363495, 0.05775149166584015, -0.02578224241733551, -0.0023596445098519325, -0.009706467390060425, 0.008420366793870926, 0.027050871402025223, 0.03910231590270996, -0.0025157614145427942, 0.029723789542913437, 0.07512901723384857, 0.010975043289363384, -0.02499685063958168, -0.05679912865161896, 0.02183946780860424, -0.003883820027112961, -0.05589551106095314, 0.053532570600509644, 0.111141636967659, 0.03324073553085327, 0.04550788551568985, 0.005824809893965721, -0.027974175289273262, -0.022077331319451332, 0.011283183470368385, 0.012107834219932556, -0.034238602966070175, 0.031106026843190193, -0.035470377653837204, 0.045282188802957535, 0.015077486634254456, -0.03285081684589386, -0.04004380479454994, -0.016822699457406998, 0.03153466433286667, 0.015905208885669708, -0.06166179105639458, -0.021877195686101913, -0.03216398507356644, 0.005634724628180265, -0.017937609925866127, -0.01142102014273405, -0.04309924319386482, 0.014598234556615353, -0.03534804657101631, -0.026724938303232193, 0.012718565762043, -0.03125123307108879, -0.009241755120456219, -0.03592076897621155, -0.025255216285586357, 0.044363558292388916, 0.05358602851629257, -0.009962394833564758, -0.032446492463350296, 0.06375966221094131, 0.012236026115715504, -0.012802371755242348, 0.030938569456338882, -0.002670285291969776, 0.02988622337579727, 0.03320500999689102, -0.028391914442181587, 0.010790262371301651, 0.04560588672757149, -0.04292839393019676, -0.038415729999542236, 0.025059619918465614, -0.02168569341301918, 0.005160767585039139, 0.0023379791527986526, 0.011322841979563236, -0.014476499520242214, -0.047786008566617966, -0.016698826104402542, -0.021869942545890808, -0.02054290845990181, 0.04724549502134323, -0.00768427224829793, 0.0337538905441761, 0.0062745101749897, -0.013729935511946678, -0.011884274892508984, 0.013713783584535122, -0.01727069355547428, 0.00016830676759127527, 0.03572511672973633, 0.08803121745586395, 0.05243676155805588, 0.09113942831754684, -0.05679036304354668, 0.04099108278751373, 0.012313093058764935, 0.02787877805531025, 0.0367693193256855, 0.04789290949702263, -0.005843956023454666, 0.010809719562530518, 0.02825576812028885, -0.005264284089207649, -0.0345572903752327, -0.011949747800827026, 0.0812331885099411, 0.021659579128026962, 0.02261001244187355, -0.003451624885201454, 0.006287559866905212, -0.03023359552025795, -0.0004014584992546588, -0.030141014605760574, -0.012525845319032669, 0.04290255904197693, 0.01711089536547661, 0.00929490476846695, 0.0016713333316147327, 0.12291312217712402, -0.040820296853780746, -0.003966104704886675, 5.5025771871441975e-05, 0.006821456830948591, -0.05650176852941513, 0.04416810721158981, -0.046072449535131454, -0.04556061327457428, -0.03692353144288063, -0.045536767691373825, 0.0005726773524656892, -0.019821470603346825, 0.0417475700378418, 0.0019796383567154408, -0.024380745366215706, 0.0743267685174942, -0.02379435859620571, -0.0029205416794866323, -0.03163180127739906, -0.047758229076862335, -0.01283376943320036, -0.04087063670158386, -0.02730698138475418, 0.023501845076680183, -0.03764703869819641, 0.010568344965577126, -0.07869783043861389, -0.06740007549524307, -0.0323786661028862, -0.039519235491752625, 0.007412596605718136, -0.011816383339464664, 0.047246020287275314, -0.08023771643638611, -0.0028245162684470415, 0.03322116658091545, -0.013241835869848728, -0.005208996590226889, -0.030994607135653496, 0.04496846720576286, -0.006999490782618523, -0.035948362201452255, -0.0035747764632105827, -0.05318346247076988, -0.02068440243601799, 0.0398237444460392, 0.07130444049835205, -0.024599818512797356, -0.07077956199645996, -0.00396221037954092, 0.02052898518741131, 0.029733411967754364, 0.036290768533945084, 0.06536128371953964, 0.014387844130396843, 0.008349694311618805, 0.004446289036422968, 0.004683323670178652, 0.03785592317581177, -0.02998846210539341, 0.06463360041379929, -0.02531445398926735, -0.010090327821671963, -0.03151950612664223, 0.01538811158388853, 0.06542321294546127, 0.02420874871313572, -0.09063618630170822, 0.04138009995222092, -0.019629696384072304, -0.022506197914481163, -0.12687715888023376, -0.03375774621963501, -0.020615261048078537, 0.007178231608122587, 0.05870480835437775, 0.006997518241405487, -0.06835051625967026, 0.01680843159556389, 0.00791067536920309, 0.0024899663403630257, 0.017043692991137505, 0.0073251016438007355, -0.028771787881851196, 0.006452399305999279, 0.04485556110739708, -0.03459734097123146, -0.022706080228090286, -0.017810683697462082, 0.0194143857806921, 0.022453755140304565, -0.03336929902434349, 0.004814022686332464, 0.06273075938224792, 0.04353230074048042, -0.0036145818885415792, 0.03511333838105202, 0.029755130410194397, 0.033113136887550354, -0.05272641405463219, -0.07099634408950806, -0.016826311126351357, -0.04499926418066025, 0.023068653419613838, 0.03931106626987457, 0.010594923049211502, 0.06847287714481354, 0.02989153563976288, -0.005148157011717558, -0.006474047899246216, 0.029238851740956306, 0.04496986046433449, 0.03588825464248657, 0.01839546486735344, -0.014019922353327274, -0.0021139918826520443, 0.0258512981235981, -0.015689468011260033, 0.039298851042985916, -0.010652397759258747, 0.007327571045607328, 0.0471465140581131, 0.050487130880355835, 0.008391917683184147, -0.03237753361463547, -0.036319613456726074, 0.042691946029663086, 0.012146297842264175, 0.026962533593177795, 0.028863679617643356, -0.08352953940629959, -0.004254452418535948, 0.01527728233486414, 0.038984328508377075, 0.002660642843693495, 0.001191856455989182, 0.008990437723696232, 0.01005594339221716, 0.03761439397931099, 0.02165495976805687, 0.04603690281510353, -0.06324438750743866, -0.008381335996091366, 0.021320397034287453, -0.025537097826600075, -0.060155242681503296, 0.008011413738131523, 0.02207375504076481, -0.008075809106230736, -0.03329141065478325, 0.02632455714046955, -0.020841971039772034, 0.09985261410474777, 0.0038057323545217514, 0.022844718769192696, -0.017845917493104935, -0.05151262506842613, 0.018863992765545845, -0.0747724175453186, -0.0024590822868049145, 0.03409262374043465, 0.011669037863612175, 0.01684981770813465, 0.019503654912114143, 0.00896343681961298, 0.030532103031873703, -0.031179137527942657, -0.00323050026781857, 0.05022900924086571, -0.019804803654551506, -0.028771106153726578, 0.0010929707204923034, -0.008941157720983028, -0.011733264662325382, 0.005864149425178766, -0.04050279036164284, -0.006342318840324879, 0.010141877457499504, 0.021532295271754265, -0.026807695627212524, -0.05783422291278839, -0.027231814339756966, 0.03552411496639252, -0.05926794558763504, -0.04381272569298744, 0.05031110346317291, 0.057176295667886734, -0.006120302714407444, 0.008566861972212791, -0.03178916871547699, 0.050398316234350204, -0.013933848589658737, -0.0791495144367218, -0.020622480660676956, 0.03284906968474388, 0.06738296151161194, 0.009639674797654152, 0.06327259540557861, 0.005228922236710787, -0.0451730452477932, 0.015630288049578667, 0.012864979915320873, 0.024045733734965324, -0.014157985337078571, -0.035280704498291016, -0.036615654826164246, 0.0013155888300389051, -0.010078095830976963, -0.05715107172727585, -0.05829865485429764, 0.05052740126848221, 0.018229790031909943, 0.04432622715830803, 0.025887593626976013, -0.04293331131339073, 0.0014546584570780396, 0.028035562485456467, 0.019304733723402023, -0.04764591157436371, -0.04276294261217117, 0.011736676096916199, -0.037553608417510986, 0.015136616304516792, 0.017366331070661545, -0.01086675375699997, 0.05248992517590523, 0.006197325419634581, 0.03515344858169556, 0.02490893378853798, -0.04519752413034439, 0.034098412841558456, 0.03535432741045952, 0.003738705301657319, -0.0018711122684180737, -0.014923309907317162, -0.002694140886887908, -0.001164639019407332, -0.08007094264030457, 0.014817454852163792, 0.050430089235305786, -0.04885116592049599, 0.0016453020507469773, -0.0067849415354430676, -0.023677494376897812, -0.007580956444144249, -0.0051787481643259525, 0.02531905099749565, -0.002191221108660102, -0.03149442374706268, 0.03332362323999405, 0.021578887477517128, -0.011744492687284946, 0.025926291942596436, -0.03883257135748863, 0.046264756470918655, 0.09391602873802185, 0.013982552103698254, -0.020796526223421097, -0.006974151358008385, -0.04357253387570381, -0.0032936842180788517, -0.02536292001605034, -0.02247084304690361, 0.08004441857337952, 0.033452194184064865, 0.007881986908614635, -0.07518457621335983, 0.01934714987874031, -0.018713705241680145, -0.06370660662651062, 0.04418155550956726, -0.010356210172176361, -0.047943297773599625, -0.047761909663677216, 0.021146420389413834, -0.03978041186928749, -0.05973208695650101, 0.025589032098650932, -0.01447728369385004, -0.0017417205963283777, 0.009075762704014778, 0.011617937125265598, 0.006831932347267866, 0.046951983124017715, 0.036206167191267014, -0.020493704825639725, 0.05429140478372574, 0.006661024410277605, 0.05197475478053093, 0.001167112379334867, 0.04761308431625366, 0.029441440477967262, -0.008896949701011181, 0.04898019880056381, -0.008627701550722122, 0.02374170534312725, 0.0032191204372793436, 0.0010869913967326283, 0.015718862414360046, 0.06323322653770447, 0.03028029017150402, -0.007639962248504162, 0.049728456884622574, 0.034100305289030075, 0.0661635771393776, -0.009018375538289547, -0.013407635502517223, -0.06914523988962173, 0.019976666197180748, 0.0011966812890022993, -0.02882467955350876, 0.013220347464084625, -0.015224752947688103, -0.015823369845747948, 0.022588802501559258, -0.038957610726356506, -0.05169247090816498, -0.011884921230375767, -0.0433141365647316, -0.006485681980848312, -0.03722870722413063, -0.02881394512951374, 0.07579601556062698, 0.018847594037652016, -0.0024279477074742317, -0.01500354427844286, -0.013088914565742016, 0.009776156395673752, 0.0005028656451031566, -0.012280779890716076, -0.046160738915205, -0.034605879336595535, 0.03528686240315437, 0.054040927439928055, 0.010654817335307598, -0.02151128277182579, -0.018043065443634987, 0.06211404129862785, 0.03530082479119301, -0.011508902534842491, 0.013106860220432281, 0.03661060333251953, -0.03512229025363922, 0.0490267239511013, 0.011132641695439816, -0.016033822670578957, -0.022965651005506516, 0.013359282165765762, -0.025131141766905785, 0.047870442271232605, -0.05190246179699898, -0.06979566067457199, -0.03355639427900314, -0.013365334831178188, -0.010574047453701496, 0.006423328071832657, -0.02947777882218361, 0.02984636463224888, -0.009944318793714046, -0.013269794173538685, -0.09868920594453812, -0.03027469292283058, -0.0417359285056591, 0.023499751463532448, 0.03472036495804787, 0.014876958914101124, 0.016507817432284355, -0.03064858727157116, -0.07445789873600006, -0.11916010081768036, 0.006707687396556139, -0.023196330294013023, -5.378100468078628e-05, 0.0565035380423069, 0.10916749387979507, 0.01853163354098797, 0.021340277045965195, -0.006571230944246054, -0.003852463560178876, -0.008283714763820171, -0.03153042495250702, 0.009280278347432613, 0.005204624030739069, 0.02402253821492195, -0.01871386729180813, 0.006774693261831999, 0.03192007914185524, -0.0024808861780911684, 0.0038535387720912695, -0.012854130938649178, 0.01439302135258913, 0.004325060173869133, -0.015737198293209076, 0.0036486212629824877, -0.040147166699171066, 0.008695575408637524, -0.01402436662465334, 0.07282896339893341, -0.006327582523226738, -0.03627491369843483, 0.03010556474328041, -0.00937429629266262, -0.004705789964646101, -0.0004777338181156665, 0.03553320840001106, 0.003333828877657652, 0.06930016726255417, -0.027225203812122345, 0.00546148931607604, -0.02708154171705246, -0.013128024525940418, -0.016658056527376175, -0.018504364416003227, 0.01156485453248024, -0.04885756969451904, -0.015987180173397064, 0.023839447647333145, -0.04163065925240517, -0.04801284149289131, -0.025456363335251808, -0.04051484540104866, -0.06403948366641998, 0.00051304412772879, -0.01970568299293518, -0.021170850843191147, 0.015488101169466972, 0.025600582361221313, 0.026050031185150146, 0.06997053325176239, -0.007973863743245602, -0.03401048108935356, 0.04884609580039978, 0.027642974629998207, -0.00966006051748991, 0.03782773017883301, -0.018669039011001587, -0.008065256290137768, 0.02258499525487423, -0.04244330897927284, 0.018561333417892456, 0.04207511618733406, 0.04677283763885498, -0.01689937151968479, -0.02003573067486286, 0.0048896754160523415, 0.021243950352072716, -0.021371599286794662, -0.03315798193216324, 0.04064563661813736, -0.03881728649139404, 0.04689224436879158, -0.048736635595560074, 0.044703107327222824, -0.005361612886190414, -0.03247727453708649, 0.03891738876700401, -0.02067914977669716, -0.04128074273467064, -0.04575243219733238, 0.0003197358746547252, -0.02510281652212143, 0.05336236581206322, 0.06303545087575912, 0.0551171600818634, 0.0316215343773365, -0.058464717119932175, 0.05823448300361633, 0.04068869352340698, 0.02035633660852909, 0.023660028353333473, -0.014029385522007942, 0.009752742946147919, 0.07996697723865509, -0.02096853032708168, 0.029606148600578308, 0.011312972754240036, -0.025960035622119904, 0.03196172043681145, 0.031707506626844406, 0.0437316857278347, -0.020291684195399284, 0.013698245398700237, 0.014103148132562637, -0.027831079438328743, 0.02192074991762638, 0.005977228283882141, -0.012113909237086773, -0.040503792464733124, 0.018908459693193436, -0.04387647286057472, -0.032338906079530716, 0.010057543404400349, -0.019094057381153107, 0.0038545671850442886, 0.023287171497941017, 0.05871446058154106, 0.012787646614015102, -0.05535459145903587, -0.08945108205080032, 0.05126876384019852, 0.011920886114239693, 0.0019485210068523884, 0.02562544494867325, -0.02086731418967247, -0.025537358596920967, -0.06660348922014236, 0.02416165918111801, 0.016623102128505707, -0.0032608124893158674, -0.020083172246813774, 0.004812450148165226, 0.0024265279062092304, -0.00014410278527066112, 0.02562425285577774, -0.0010917611652985215, -0.015982335433363914, 0.04316983371973038, 0.006624314468353987, 0.061781272292137146, -0.03154110535979271, -0.008508414961397648, 0.07268151640892029, -0.01704632304608822]], "fab50ff9baa285f7115a29bc8d7a23de": [0.9998474488534524, [0.008662505075335503, 0.03158685564994812, -0.10378341376781464, -0.03494565933942795, 0.08280210196971893, 0.001280563068576157, 0.03253142908215523, -0.014708179049193859, 0.02890811301767826, -0.003428479889407754, -0.004918856546282768, 0.07816006243228912, 0.04140922427177429, -0.0003687396820168942, 0.06294935196638107, -0.051725585013628006, 0.011881713755428791, 0.048338159918785095, -0.05243156850337982, -0.025361111387610435, -0.028122322633862495, 0.02993982657790184, 0.01278730109333992, -0.020329570397734642, -0.0546051450073719, -0.03927375376224518, 0.0036002350971102715, 0.013272738084197044, -0.021723547950387, -0.032511331140995026, 0.020745860412716866, 0.02036600187420845, 0.004634654149413109, -0.01594887487590313, -0.018761031329631805, 0.046266984194517136, 0.045996200293302536, 0.054960429668426514, 0.032308273017406464, -0.08511984348297119, -0.031741946935653687, 0.0073840725235641, -0.017176249995827675, 0.11298620700836182, 0.023848893120884895, -0.05671960860490799, 0.025397391989827156, 0.03038315661251545, -2.7068332201451994e-05, 0.006161925848573446, 0.02864238992333412, -0.03462882339954376, 0.00555612426251173, 0.030661188066005707, 0.010201220400631428, -0.044838715344667435, -0.018068227916955948, -0.04685262218117714, 0.022291632369160652, 0.04235636815428734, -0.03788784146308899, -0.05187614634633064, -0.02010199800133705, -0.009983707219362259, -0.01193501427769661, 0.008332055993378162, -0.030612854287028313, -0.026967884972691536, -0.06162375584244728, -0.006303766276687384, -0.07481460273265839, 0.03187771886587143, -0.005315256305038929, 0.046995144337415695, 0.024951830506324768, 0.03423303738236427, -0.007065402809530497, 0.002561317291110754, 0.0022431169636547565, 0.05273061618208885, -0.06132577359676361, 0.016070600599050522, 0.08909796178340912, 0.014666217379271984, 0.03694310784339905, -0.03630960360169411, -0.048294149339199066, -0.057426854968070984, -0.047435373067855835, -0.012200850062072277, 0.04021228104829788, 0.004338604398071766, 0.015580573119223118, -0.027486314997076988, 0.032836683094501495, -0.035250309854745865, -0.06738290935754776, -0.11131670325994492, 0.06176057457923889, 0.07247226685285568, -0.0029603622388094664, 0.014821906574070454, -0.007964575663208961, -0.07348859310150146, 0.057671476155519485, 0.00815580878406763, -0.05054359883069992, -0.0005551780923269689, -0.11611317098140717, 0.06102931499481201, -0.0009103370830416679, 0.005481179803609848, 0.057348959147930145, -0.01828800141811371, -0.05209633335471153, -0.012571493163704872, -0.029318470507860184, 0.021273326128721237, -0.028123466297984123, -0.034943461418151855, 0.03877314552664757, 0.05866280198097229, 0.025744231417775154, 0.05181734636425972, 0.034357208758592606, 0.04157504811882973, -0.0010883279610425234, -0.014091197401285172, -0.030731672421097755, -0.025553017854690552, 0.06269020587205887, -0.007006799336522818, 0.0006918029976077378, -0.0005255674477666616, -0.019338060170412064, -0.020085806027054787, 0.06936635822057724, -0.03638073056936264, -0.009367779828608036, 0.02458835020661354, 0.014329653233289719, -0.05274378880858421, -0.05567845702171326, -0.0032579542603343725, -0.010403882712125778, -0.04481356218457222, 0.02539099007844925, 0.09264206141233444, 0.009714455343782902, 0.055752914398908615, 0.008975700475275517, 0.012024243362247944, -0.07808683067560196, -0.0066015156917274, 0.015471047721803188, 0.006832892540842295, 0.013270242139697075, -0.05072247236967087, 0.017950205132365227, 0.037967536598443985, -0.02756812795996666, -0.05781308561563492, 0.006504701916128397, -0.027223188430070877, 0.005906775593757629, -0.07425200939178467, -0.03437931463122368, -0.051816850900650024, 0.03222626820206642, 0.0204402357339859, 0.022518735378980637, -0.061903465539216995, -0.0010205633006989956, -0.06117380037903786, 0.00224674423225224, 0.02569306641817093, 0.004028284922242165, 0.004241315647959709, -0.04803258925676346, -0.028193946927785873, 0.07395198941230774, 0.021927567198872566, 0.027141066268086433, -0.09128643572330475, 0.057596687227487564, 0.005307343788444996, 0.005592055153101683, 0.018275396898388863, 0.030898477882146835, 0.036655083298683167, -0.012038236483931541, 0.017209568992257118, 0.06911621987819672, 0.05553147941827774, -0.004644626285880804, -0.01445530541241169, 0.034845802932977676, -0.015248392708599567, 0.02054557576775551, 0.03911083564162254, 0.02074616402387619, -0.0030517578125, -0.01725936122238636, -0.06540913134813309, -0.05667116120457649, -0.006423608865588903, 0.006809334736317396, 0.018778271973133087, 0.012837711721658707, 0.01040808018296957, -0.034789782017469406, -0.019710654392838478, -0.022826969623565674, -0.028525730594992638, -0.01900685392320156, 0.03642063960433006, 0.04236301779747009, 0.033644989132881165, 0.0975533053278923, -0.07127206027507782, 0.01618596725165844, -0.017261499539017677, -0.020326796919107437, 0.013663174584507942, 0.05573427304625511, 0.010388758964836597, 0.024624964222311974, 0.007593157701194286, -0.016545718535780907, -0.05979790538549423, 0.04626651853322983, 0.07114267349243164, 0.0067680771462619305, 0.052732620388269424, 0.059758927673101425, 0.010986069217324257, 0.011709711514413357, -0.0652749091386795, -0.03139243274927139, 0.01982237584888935, -0.020613932982087135, 0.034896329045295715, 0.056136325001716614, -0.01977541856467724, 0.04426993057131767, 0.027707261964678764, 0.005842634942382574, 0.04269424080848694, -0.0015013419324532151, -0.055291324853897095, -0.019189901649951935, -0.035078540444374084, -0.019611794501543045, -0.01701989769935608, -0.059200216084718704, 0.0002644080377649516, 0.032244015485048294, 0.004058929160237312, -0.017864003777503967, -0.011832533404231071, 0.0460423044860363, 0.003665599739179015, 0.01604718528687954, -0.06086678057909012, -0.04824375733733177, -0.047438543289899826, -0.05334007740020752, -0.051155466586351395, 0.011670950800180435, -0.014365782029926777, 0.045224279165267944, -0.07268421351909637, -0.02886117249727249, -0.01850278116762638, -0.007520716637372971, 0.052638113498687744, -0.04556754603981972, 0.027011023834347725, -0.04879160597920418, 0.014096468687057495, 0.011564008891582489, -0.0003956982400268316, -0.011059639044106007, 0.0182342529296875, 0.04829995334148407, -0.06178194656968117, 0.013689960353076458, -0.004639556631445885, -0.005357679445296526, -0.03409314900636673, 0.07258985191583633, 0.05561964586377144, 0.0008659100858494639, -0.07556884735822678, 0.01693548820912838, 0.011814801022410393, 0.03220265358686447, 0.014453873969614506, -0.02545008808374405, 0.013327966444194317, 0.02196367084980011, 0.0310959629714489, 0.0023867757990956306, 0.03942260891199112, 0.009574001654982567, 0.03666575253009796, -0.018872907385230064, 0.009128845296800137, -0.008356182835996151, -0.016500040888786316, 0.06746535748243332, 0.05164020135998726, -0.07049016654491425, 0.012677527964115143, -0.02545781061053276, -0.013082318007946014, -0.1463010460138321, -0.03921230137348175, -0.04060417413711548, -0.039292607456445694, 0.05987060070037842, 0.04937010630965233, -0.01676369272172451, 0.000816392945125699, -0.008246243000030518, 0.0015415683155879378, -0.008924403227865696, -0.005482729058712721, -0.011936016380786896, -0.02441406063735485, 0.04445056617259979, -0.029127098619937897, 0.00778698967769742, 0.026520060375332832, 0.034487344324588776, 0.010794668458402157, -0.060986269265413284, 0.035972971469163895, 0.06512904912233353, 0.013695615343749523, -0.009373249486088753, 0.04485033079981804, 0.029542937874794006, 0.04970919340848923, 0.00475799897685647, -0.03863925114274025, 0.03693433851003647, 0.017657117918133736, 0.0010822435142472386, 0.025366224348545074, 0.050264716148376465, 0.038955122232437134, -0.011220729909837246, -0.019341306760907173, -0.013608195818960667, 0.03109029121696949, 0.02154056541621685, 0.0019035108853131533, -0.029883569106459618, -0.037397369742393494, 0.0016883803764358163, 0.002095145871862769, -0.004244612529873848, 0.031985849142074585, 0.005076118279248476, 0.0010843802010640502, 0.07086721807718277, 0.05763813108205795, 0.00046061709872446954, -0.023180602118372917, -0.030590509995818138, 0.024183716624975204, -0.03516722843050957, 0.015561144798994064, 0.024140682071447372, -0.05197160691022873, -0.010409330949187279, 0.026855193078517914, -0.0014952031197026372, -0.009704243391752243, 0.0016526372637599707, -0.022312099114060402, 0.00681797182187438, 0.031434763222932816, 0.008228205144405365, 0.04239654913544655, -0.05604928731918335, -0.03651142865419388, -0.0026871575973927975, 0.005445830058306456, -0.06145705655217171, 0.03585847094655037, -0.009109322912991047, -0.0016075565945357084, -0.012225230224430561, -0.014228620566427708, 0.004842719528824091, 0.04833441972732544, 0.017097463831305504, 0.009019682183861732, 0.019464919343590736, -0.013893695548176765, 0.015630191192030907, -0.06221958249807358, -0.0017818151973187923, 0.0031401379965245724, 0.036162640899419785, 0.029877085238695145, 0.002716951770707965, 0.006032517645508051, 0.005776429548859596, -0.04515912011265755, 0.0011451105820015073, 0.030498940497636795, -0.037139225751161575, -0.0021711792796850204, 0.016132699325680733, -0.02335541881620884, 0.049709588289260864, 0.04449235275387764, -0.03954175114631653, 0.012796745635569096, 0.02546553686261177, 0.021856248378753662, 0.020717144012451172, -0.053315419703722, -0.0235371645539999, 0.0070075057446956635, -0.010134012438356876, -0.025137590244412422, 0.03592147305607796, 0.02288278006017208, 0.015702098608016968, 0.038992349058389664, -0.028565343469381332, 0.06635105609893799, -0.008064467459917068, -0.04058389738202095, -0.04240969568490982, 0.03960508853197098, 0.017359931021928787, 0.013534171506762505, 0.07151568681001663, -0.01638009585440159, -0.03458807244896889, 0.015054799616336823, 0.015952488407492638, 0.02428581565618515, -0.021255845203995705, -0.04109693691134453, -0.029183078557252884, 0.017780443653464317, -0.01399989239871502, -0.06318221986293793, -0.10182706266641617, 0.0393943153321743, -0.006351497955620289, 0.04108899086713791, 0.017447669059038162, 0.009800138883292675, 0.005937608890235424, -0.009281035512685776, 0.07166266441345215, 0.010577257722616196, -0.030346253886818886, -0.035512249916791916, -0.037402596324682236, 0.04006436467170715, 0.02205944061279297, 0.024905968457460403, 0.030595185235142708, 0.023699257522821426, 0.02282288856804371, 0.01294725202023983, -0.02793605998158455, 0.020789500325918198, 0.040084611624479294, 0.029780250042676926, -0.03664691746234894, -0.024584755301475525, -0.016808325424790382, 0.04282143712043762, -0.03319896385073662, -0.00411265017464757, 0.007580848876386881, 0.001186427311040461, 0.0008841308881528676, 0.006752639077603817, -0.009424896910786629, -0.005366595461964607, 0.010394596494734287, 0.0053419810719788074, -0.048941243439912796, -0.05107991397380829, -0.009375246241688728, -0.0022383409086614847, 0.04055609926581383, 0.008291801437735558, -0.035178616642951965, 0.039560191333293915, 0.07376834750175476, 0.026618698611855507, -0.0024494410026818514, -0.040297944098711014, -0.003038776572793722, 0.02960977703332901, -0.04472999647259712, -0.03385842964053154, 0.06233809515833855, -0.004558699205517769, 0.05513567104935646, -0.06498070061206818, 0.006790684536099434, -0.005188386887311935, -0.03180735558271408, 0.057524506002664566, 0.020852066576480865, 0.019127272069454193, -0.042094700038433075, 0.03548087552189827, 0.0023082164116203785, -0.04639049619436264, 0.033691536635160446, 0.01783674769103527, 0.00889930035918951, -0.0063795046880841255, 0.008029477670788765, 0.027234084904193878, 0.04506043717265129, 0.0023444422986358404, -0.025780564174056053, 0.03502901643514633, 0.0036406703293323517, 0.04416980594396591, -0.015822643414139748, 0.06728524714708328, 0.005312186200171709, 0.013832553289830685, 0.050547290593385696, 0.007802017033100128, 0.023085137829184532, -0.01671837829053402, 0.004530622158199549, 0.0009182255016639829, 0.059163715690374374, 0.008015141822397709, 0.008692785166203976, 0.052764732390642166, 0.022173337638378143, 0.023329023271799088, -0.012152069248259068, -0.011845303699374199, -0.018281683325767517, 0.01780994050204754, 0.021920910105109215, -0.0026224828325212, 0.0048316107131540775, -0.03537912294268608, 0.02046515792608261, -0.02414296381175518, -0.02720489725470543, -0.013403214514255524, -0.010601856745779514, 0.0024389547761529684, 0.008394415490329266, -0.016370853409171104, -0.04810541123151779, 0.06651236116886139, -0.004410922061651945, -0.007273340132087469, 0.026743747293949127, -0.02823559194803238, -0.0103608975186944, -0.031677912920713425, -0.0039295353926718235, -0.05668887495994568, -0.031244678422808647, 0.009486843831837177, 0.07303135842084885, 0.03224514052271843, -0.00512335542589426, -0.029156267642974854, 0.045838840305805206, -0.006077486556023359, -0.012122193351387978, -0.018246695399284363, -0.023877782747149467, -0.011257578618824482, 0.02562829665839672, -0.011724043637514114, -0.019872194156050682, -0.0022794282995164394, 0.03378192335367203, -0.05541999638080597, -0.004686288069933653, -0.05139017477631569, -0.055142030119895935, 0.0020654993131756783, -0.012170430272817612, -0.012498238123953342, -0.0017779581248760223, -0.01520867832005024, 0.038425810635089874, -0.007514545693993568, -0.030823543667793274, -0.06295670568943024, -0.009604552760720253, -0.08387967199087143, 0.026315059512853622, 0.005430960562080145, -0.011249839328229427, 0.030978361144661903, -0.012801239266991615, -0.06742759793996811, -0.0765296220779419, 0.016351409256458282, -0.054936934262514114, -0.006550614722073078, 0.020633986219763756, 0.06396879255771637, 0.020066887140274048, 0.009230810217559338, -0.01108076237142086, 0.013325835578143597, -0.04430622607469559, 0.019596394151449203, 0.024691877886652946, -0.000567589420825243, 0.024819381535053253, 0.023234140127897263, -0.017249446362257004, -0.017272504046559334, 0.014935340732336044, -0.012062889523804188, 0.030969083309173584, 0.01578204147517681, 0.005894471425563097, 0.022817758843302727, -0.01831277646124363, -0.08700606226921082, 0.030389148741960526, 0.027836866676807404, 0.037538688629865646, 0.003994547296315432, -0.050057243555784225, 0.0182881448417902, -0.03798503428697586, 0.0047358241863548756, 0.0029878788627684116, 0.07206621021032333, 0.04221737012267113, 0.048364218324422836, -0.0891648530960083, 0.037672653794288635, -0.01063566468656063, 0.010203985497355461, -0.024891262874007225, 0.01491842232644558, 0.008320827968418598, -0.012723801657557487, -0.038445018231868744, -0.020166300237178802, -0.04406558722257614, 0.009109897539019585, -0.036204271018505096, -0.04615752398967743, -0.05856338515877724, 0.03197872266173363, 0.006628823932260275, -0.008770900778472424, -0.012232027016580105, 0.02638055384159088, -0.0003663303214125335, 0.07128159701824188, 0.03009449690580368, 0.015175593085587025, 0.007693682797253132, 0.0070656947791576385, -0.033286202698946, 0.025090383365750313, 0.04667491838335991, -0.006908165756613016, 0.029335176572203636, -0.011375868692994118, -0.02216797135770321, 0.007171852048486471, 0.045442093163728714, -0.038171570748090744, 0.00867178849875927, 0.023204339668154716, 0.02611694112420082, -0.0195786003023386, -0.0194692425429821, 0.051524966955184937, -0.011410115286707878, 0.08100567013025284, -0.0068254629150033, -0.012081041932106018, -0.037684403359889984, -0.011511576361954212, -0.03233523666858673, 0.005184355191886425, -0.010046621784567833, -0.03673134371638298, -0.014437471516430378, -0.014974301680922508, 0.07171298563480377, 0.03443354740738869, -0.060434892773628235, 0.014657657593488693, -0.05587056651711464, 0.022220073267817497, 0.027688447386026382, -0.020400194451212883, 0.006114248186349869, 0.008076616562902927, 0.02283097431063652, 0.05328087881207466, -0.02741203084588051, 0.03554608300328255, 0.06389209628105164, -0.029805494472384453, 0.03991524502635002, 0.02977304346859455, 0.01508439052850008, 0.006098403129726648, -0.04957831650972366, 0.0012188759865239263, -0.04419220611453056, 0.028513316065073013, 0.024759966880083084, 0.0002407735591987148, -0.02385053038597107, 0.09142657369375229, -0.03156401216983795, -0.04670481011271477, -0.042639147490262985, -0.022292733192443848, -0.05566782131791115, -0.014938067644834518, 0.06676516681909561, -0.014975691214203835, -0.061295319348573685, -0.04753755405545235, 0.05593863129615784, 0.0064636399038136005, 0.02857145108282566, 0.014451908878982067, 0.006964829284697771, -0.038722943514585495, -0.0512094721198082, 0.03645891696214676, 0.0175631046295166, -0.024196697399020195, 0.01855645701289177, 0.030968571081757545, 0.0016314663225784898, -0.0190740916877985, 0.059380266815423965, -0.039587169885635376, -0.04377364367246628, 0.05319851636886597, -0.01911289431154728, 0.06829866766929626, -0.028914732858538628, -0.05207695811986923, 0.0364270843565464, 0.00042388992733322084]], "c6cfe5ab819dc0a5d9078b44476478af": [0.999807137918698, [0.003831913461908698, 0.02347896248102188, -0.049828916788101196, -0.030051544308662415, 0.05247164145112038, -0.009781993925571442, 0.04416746273636818, -0.008660435676574707, -0.013768651522696018, -0.016352578997612, -0.0020776833407580853, 0.08151169866323471, 0.06399795413017273, -0.04177353158593178, 0.08477948606014252, -0.020523782819509506, 0.012531401589512825, 0.06852468103170395, -0.04642663896083832, 0.016811585053801537, -0.03674880042672157, 0.0048739067278802395, 0.001171379815787077, 0.004726195242255926, -0.047033827751874924, -0.05053071305155754, 0.023014089092612267, -0.04163900390267372, 0.006165939383208752, -0.010662814602255821, 0.02173207327723503, 0.016252711415290833, 0.02571769803762436, -0.006821927614510059, -0.009577451273798943, 0.0402756966650486, 0.014701231382787228, 0.01988118328154087, 0.03804510831832886, -0.07796360552310944, -0.04432792589068413, -0.04893496632575989, -0.01509276032447815, 0.08852964639663696, 0.022613005712628365, -0.02950270287692547, 0.02513723261654377, 0.016333255916833878, -0.002254924038425088, -0.014203502796590328, 0.050432201474905014, -0.03488009423017502, 0.01758420839905739, 0.04648177698254585, -0.0005232227267697453, -0.03672953322529793, -0.05978873372077942, -0.04441307857632637, 0.059064533561468124, 0.03747887536883354, -0.03923989087343216, -0.045182447880506516, -0.01024654321372509, -0.022684652358293533, -0.053875114768743515, -0.0061989156529307365, -0.027583619579672813, -0.05453282594680786, -0.023860622197389603, -0.00951294880360365, -0.04619212821125984, 0.016795216128230095, -0.048700001090765, 0.06981322169303894, 0.0006803905125707388, 0.04024617746472359, 0.005124568939208984, -0.03934862092137337, 0.014847923070192337, 0.02045578323304653, -0.017665868625044823, 0.022343561053276062, 0.09508821368217468, 0.020489653572440147, 0.04692207649350166, -0.0032940804958343506, -0.010746801272034645, -0.05411379411816597, -0.050198473036289215, -0.030888240784406662, 0.061806049197912216, 0.02905678004026413, -0.0014711404219269753, -0.019694888964295387, 0.042919691652059555, -0.031204301863908768, -0.09921319782733917, -0.10765989869832993, 0.013789834454655647, 0.04716954007744789, -0.022865232080221176, -0.0074941376224160194, -0.013133462518453598, -0.08882968872785568, 0.08410654217004776, -0.022308338433504105, -0.03614872694015503, -0.036939896643161774, -0.10340476036071777, 0.07730530202388763, -0.02512626349925995, 0.017761021852493286, 0.053400918841362, 0.009564414620399475, -0.022444315254688263, 0.021703151986002922, -0.022744113579392433, 0.026834815740585327, -0.06019575893878937, 0.017301157116889954, 0.014179954305291176, 0.028264904394745827, 0.009004954248666763, 0.06465049088001251, 0.03406887128949165, 0.012774385511875153, 0.022060493007302284, -0.037597402930259705, -0.013447185046970844, -0.019371891394257545, 0.04500185325741768, -0.014902404509484768, -0.038962677121162415, 0.020144961774349213, -0.01898323744535446, 0.030193809419870377, 0.025684144347906113, -0.03899902105331421, 0.037590112537145615, 0.06299800425767899, -0.013289516791701317, -0.056289996951818466, -0.07083380967378616, 0.011523829773068428, -0.048083625733852386, -0.05342401564121246, 0.040444839745759964, 0.05404569208621979, -0.021242184564471245, 0.038584038615226746, 0.014273890294134617, -0.013601250015199184, -0.024393415078520775, 0.005689001642167568, 0.004667258821427822, -0.005492940545082092, 0.005682649090886116, -0.032046448439359665, 0.04818769916892052, 0.05233798921108246, 0.0126601317897439, -0.02875903621315956, 0.015270140022039413, -0.0033858304377645254, -0.01668442226946354, -0.03799046203494072, -0.028895298019051552, -0.03731455281376839, 0.04368254542350769, -0.008668072521686554, -0.0021796736400574446, -0.05309048295021057, -0.04277186095714569, -0.018585417419672012, 0.007497942540794611, 0.02427896112203598, -0.0027127715293318033, -0.020937105640769005, -0.07771478593349457, -0.036907292902469635, 0.03070022724568844, 0.042426951229572296, 0.034233126789331436, -0.09097672998905182, 0.06624473631381989, -0.016490917652845383, -0.02936985343694687, 0.023166809231042862, 0.05376899614930153, 0.0658148005604744, -0.010176237672567368, 0.02124417945742607, 0.04173973575234413, 0.04813479632139206, -0.03310520574450493, -0.006345483940094709, 0.0798138678073883, -0.03555867075920105, -0.008089057169854641, -0.01275336928665638, 0.04049837961792946, -0.025921639055013657, -0.028174925595521927, -0.05252348631620407, -0.024526990950107574, 0.018092190846800804, 0.010977989993989468, -0.007551412098109722, 0.007693287916481495, 0.02035527490079403, -0.06348985433578491, -0.036068521440029144, -0.011223746463656425, -0.04116633161902428, -0.03485787659883499, 0.0021094782277941704, 0.055358123034238815, 0.013028578832745552, 0.056665126234292984, -0.08152499794960022, 0.006439225282520056, 0.006801065988838673, -0.011408396065235138, 0.012211055494844913, 0.060423027724027634, 0.0013483535731211305, 0.04041561484336853, 0.02718566171824932, -0.01724240928888321, -0.025548448786139488, -0.033558882772922516, 0.05463964492082596, 0.028675779700279236, 0.06354223191738129, 0.021504182368516922, 0.03335719555616379, 0.034477587789297104, -0.09267022460699081, -0.05991910398006439, 0.021938955411314964, -0.0025701522827148438, 0.041149113327264786, -0.008906533010303974, 0.021102599799633026, 0.01054395828396082, 0.0008527664467692375, 0.014098658226430416, 0.04480595514178276, 0.004674745723605156, -0.042965326458215714, 0.0036211819387972355, -0.05178415775299072, -0.001593977096490562, -0.03033929318189621, -0.03378593176603317, -0.00017313045100308955, 0.04438018426299095, -0.010433671995997429, -0.004035286605358124, 0.006770135834813118, 0.03073432482779026, -0.0397663488984108, -0.03117959201335907, -0.029925484210252762, -0.035438839346170425, -0.06310419738292694, -0.03421860560774803, -0.059565041214227676, 0.04328960180282593, 0.009971131570637226, 0.03455416485667229, -0.08651655912399292, -0.026869384571909904, -0.020714398473501205, -0.02925802767276764, 0.033532630652189255, -0.03359518572688103, 0.03260737657546997, -0.05590900033712387, 0.011830910108983517, 0.030491191893815994, -0.003915265668183565, 0.01173797994852066, 0.013972297310829163, 0.04384031519293785, -0.05920199304819107, -0.02945699542760849, 0.044761888682842255, -0.010902496054768562, -0.0627371147274971, 0.03652619943022728, 0.01568436808884144, -0.027171311900019646, -0.04900189861655235, -0.0029789116233587265, 0.041217442601919174, 0.03094126097857952, 0.018891220912337303, -0.014069140888750553, -0.002954673022031784, 0.002513173269107938, 0.025788087397813797, -0.053936075419187546, 0.050522636622190475, -0.007813118398189545, 0.017987996339797974, 0.00908583216369152, 0.01676560379564762, -0.018678216263651848, -0.04176703095436096, 0.04378394037485123, 0.062997005879879, -0.06499020755290985, 0.008759054355323315, -0.027474649250507355, -0.01815606839954853, -0.15371689200401306, -0.04511158540844917, -0.005504970904439688, 0.0030541152227669954, 0.07402675598859787, 0.022174926474690437, -0.05259260907769203, -0.020460527390241623, 0.014480993151664734, -0.0032801395282149315, 0.014790564775466919, -0.011580673977732658, -0.001272087451070547, -0.027529388666152954, 0.046565305441617966, -0.02392069809138775, 0.001831733388826251, 0.0022739407140761614, 0.038302697241306305, 0.011186081916093826, -0.027839399874210358, 0.052122607827186584, 0.08569522202014923, 0.026086783036589622, -0.0038838821928948164, 0.03973200172185898, 0.031141838058829308, 0.051855482161045074, 0.00591054605320096, -0.061448920518159866, 0.004783368669450283, 0.011827764101326466, 0.0016996675403788686, 0.0555669330060482, 0.0601525716483593, 0.05447482317686081, -0.005425683688372374, -0.013232307508587837, 0.010355386883020401, 0.0632854700088501, 0.008830326609313488, 0.01550908014178276, -0.022089291363954544, -0.046236585825681686, -0.008336123079061508, -0.004622655920684338, -0.00546613521873951, 0.05691002309322357, 0.02160726487636566, -0.017588110640645027, 0.058807093650102615, 0.025445908308029175, -0.025735825300216675, -0.040506258606910706, -0.04646657779812813, 0.02257521077990532, -0.037376176565885544, 0.013944977894425392, -0.0008406277047470212, -0.045455966144800186, -0.056365422904491425, 0.004373393952846527, 0.043406348675489426, -0.026440301910042763, 0.029504023492336273, -0.008181903511285782, -0.004417089745402336, 0.051647040992975235, 0.0158525500446558, 0.06704069674015045, -0.0716131329536438, -0.03556143119931221, 0.025347094982862473, 0.03371056914329529, -0.05768926069140434, 0.04126887768507004, 0.027561631053686142, -0.031865332275629044, -0.02354360930621624, 0.0003547729575075209, 0.01416844967752695, 0.05491850897669792, 0.019994687288999557, 0.02660266123712063, -0.00491712149232626, -0.0393991656601429, 0.024210859090089798, -0.0265961904078722, 0.01709134317934513, -0.007432075217366219, 0.06347301602363586, -0.00013584077532868832, -0.009721948765218258, -0.000525537645444274, -0.006841609254479408, -0.0061539593152701855, 0.008606535382568836, 0.0277591310441494, -0.03430428355932236, -0.018453499302268028, 0.017375187948346138, -0.029987359419465065, 0.015349476598203182, -0.008400823920965195, -0.021815072745084763, 0.04737476259469986, 0.05575445666909218, 0.019808588549494743, 0.004460189491510391, -0.0808783769607544, -0.004428970627486706, -0.018703477457165718, -0.009819768369197845, -0.020712757483124733, 0.032804593443870544, 0.0602937750518322, -0.024095548316836357, 0.030208181589841843, -0.048563651740550995, 0.02539718523621559, 0.0006076768040657043, -0.044537149369716644, -0.012323623523116112, 0.03313753753900528, 0.01068023033440113, 0.02327677235007286, 0.05180736258625984, -0.0038734145928174257, -0.012668759562075138, 0.04025803133845329, 0.012955463491380215, 0.0593566931784153, -0.038696788251399994, -0.02042735368013382, -0.016305899247527122, 0.014933038502931595, -0.0036783162504434586, -0.03495846688747406, -0.08500927686691284, 0.04126710444688797, 0.009783145040273666, 0.04907894879579544, 0.025215348228812218, 0.02150106616318226, -0.040025677531957626, -0.006110762245953083, 0.049137990921735764, -0.017208736389875412, -0.019031954929232597, -0.049978937953710556, -0.05961935222148895, 0.028241030871868134, -0.009177199564874172, 0.020113717764616013, 0.04445261135697365, 0.015282359905540943, 0.0062979962676763535, -0.007303412538021803, -0.006196440663188696, 0.027650345116853714, 0.053430408239364624, 0.015190494246780872, -0.036428775638341904, -0.01400737650692463, -0.034320998936891556, 0.04500635713338852, -0.011409145779907703, 0.016265640035271645, 0.012503787875175476, 0.0031164896208792925, -0.02032582461833954, 0.013997895643115044, -0.022776387631893158, -0.03971925750374794, 0.005296732764691114, -0.012185635976493359, -0.02383437007665634, -0.029224818572402, 0.01718740351498127, -0.03243562951683998, 0.04458817094564438, -0.012471026740968227, -0.0065935649909079075, 0.011125572957098484, 0.043664608150720596, 0.007558587938547134, 0.028951678425073624, -0.027297640219330788, -0.0144947599619627, 0.02411814033985138, -0.0532773956656456, -0.01875642128288746, 0.09067825227975845, 0.0057128299959003925, 0.008950243704020977, -0.07440127432346344, -0.015601160936057568, 0.00804632157087326, -0.04958587512373924, 0.03689746931195259, 0.00782214105129242, -0.041359852999448776, -0.06166590377688408, 0.049751780927181244, 0.021666480228304863, -0.04611431062221527, 0.036462899297475815, 0.005382846109569073, 0.014945673756301403, -0.012196945026516914, -0.005177992861717939, 0.015224508009850979, 0.037131305783987045, -0.022682130336761475, -0.02704326994717121, 0.04734189435839653, -0.0012563102645799518, 0.001879066345281899, -0.024042285978794098, 0.05785229057073593, 0.004655424039810896, 0.009326701052486897, 0.043327558785676956, 0.010254244320094585, -0.004242199473083019, 0.017571866512298584, 0.0037591694854199886, 0.0011180707952007651, 0.06177844479680061, 0.016766149550676346, 0.03293006122112274, 0.06303080916404724, 0.01680530235171318, 0.059691283851861954, 0.016885727643966675, 0.004992955829948187, -0.03572946414351463, 0.010331215336918831, -0.0011139679700136185, -0.02178460732102394, 0.018607247620821, 0.026945607736706734, -0.02676299400627613, 0.008603710681200027, 0.01842767372727394, -0.0015706638805568218, -0.004255052190274, -0.025356346741318703, -0.0027741650119423866, -0.012185610830783844, -0.05198606476187706, 0.05225503444671631, -0.01973811909556389, -0.04217667132616043, 0.0010317276464775205, -0.02292407676577568, -0.004152530338615179, -0.003682286012917757, -0.00564354145899415, -0.049944356083869934, -0.034893255680799484, 0.0034438364673405886, 0.07910563796758652, 0.03517336770892143, -0.01564040593802929, 0.02485285885632038, 0.06027958169579506, 0.006306525319814682, -0.01889263466000557, -0.002410812536254525, -0.006179117131978273, -0.022385651245713234, 0.025798188522458076, -0.01336220558732748, -0.013974950648844242, -0.023022500798106194, -0.0013429641257971525, -0.050994593650102615, 0.02231891267001629, -0.025160133838653564, -0.039647553116083145, 0.042128145694732666, -0.019837383180856705, -0.028240405023097992, -0.008481644093990326, -0.011783739551901817, 0.03355969116091728, -0.013473285362124443, -0.05233707278966904, -0.0723838359117508, 0.016911206766963005, -0.07497084140777588, 0.0028322692960500717, 0.011681308038532734, -0.022258006036281586, 0.04055120050907135, 0.013403307646512985, -0.03744819015264511, -0.09851906448602676, 0.01401266548782587, -0.04812159016728401, -0.0001222490391228348, 0.025164293125271797, 0.06740357726812363, 0.028710512444376945, 0.01670941710472107, -0.004838987719267607, 0.027942772954702377, -0.04557839035987854, -0.001288165571168065, -0.011037823744118214, -0.01073751226067543, 0.08272000402212143, 0.035735417157411575, -0.01667194813489914, -0.0017612411174923182, 0.008382453583180904, -0.01287125889211893, 0.005973234307020903, -0.011056529358029366, 0.0010341712040826678, 0.046555738896131516, -0.001341223600320518, -0.06478345394134521, 0.00015099579468369484, 0.04298657551407814, 0.054770614951848984, -0.02128835767507553, -0.03558740392327309, 0.03083892911672592, -0.0423467680811882, -0.014492464251816273, -0.0023255879059433937, 0.035581301897764206, 0.026718778535723686, 0.04967022314667702, -0.05080290511250496, 0.07890919595956802, -0.020009782165288925, -0.00023554818471893668, -0.0440991185605526, -0.0033276863396167755, 0.002880769781768322, -0.03924969583749771, -0.01295552309602499, -0.027804240584373474, -0.03360560163855553, -0.013239712454378605, -0.07482149451971054, -0.012430899776518345, -0.07884947210550308, 0.014594417996704578, 0.00877467729151249, -0.007151301018893719, -0.004121326841413975, 0.057643644511699677, -0.005354704800993204, 0.037009723484516144, 0.022901158779859543, 0.010677116923034191, -0.024071576073765755, -0.0011420854134485126, -0.02341429516673088, 0.009358457289636135, 0.008017637766897678, -0.005539060104638338, 0.016905277967453003, -0.03875463828444481, 0.021689174696803093, 0.02689223922789097, 0.017560943961143494, -0.028743287548422813, 0.033791471272706985, -0.00261186552233994, -0.010727851651608944, -0.040960200130939484, -0.012351151555776596, 0.031296271830797195, -0.0278010256588459, 0.04510178044438362, -0.0022804844193160534, -0.029361823573708534, -0.048510316759347916, -0.0009409887134097517, -0.028270866721868515, 0.004441271536052227, -0.03325723484158516, -0.03717206418514252, 0.01413008850067854, -0.01522732526063919, 0.07325829565525055, 0.002354950411245227, -0.03876606374979019, 0.02189032919704914, -0.040375348180532455, 0.017803261056542397, 0.035358723253011703, -0.006867413409054279, 0.006119184195995331, 0.01425058115273714, 0.0305203665047884, 0.06061085686087608, -0.02316165342926979, 0.023405982181429863, 0.025006849318742752, -0.028686679899692535, 0.06533819437026978, 0.05231093242764473, 0.03344808146357536, 0.02853836491703987, -0.012816410511732101, 0.023742392659187317, -0.04787087067961693, 0.039491452276706696, 0.010781487450003624, -0.020046228542923927, -0.04510929808020592, 0.060908496379852295, -0.03163900226354599, -0.04823371395468712, -0.02710697427392006, -0.005035716108977795, -0.028888575732707977, -0.0086298743262887, 0.07854999601840973, 0.01229438092559576, -0.06248892471194267, -0.059269439429044724, 0.022225242108106613, 0.026071369647979736, 0.03360689803957939, 0.02271064557135105, -0.022089121863245964, -0.07430361211299896, -0.07956215739250183, 0.04079199582338333, -0.00807908270508051, -0.0034921206533908844, 0.03540438041090965, 0.025933802127838135, 0.005625785794109106, 0.0028193830512464046, 0.03510052710771561, -0.007287742104381323, -0.022146841511130333, 0.02781202830374241, -0.02964949980378151, 0.017031732946634293, -0.01578688435256481, -0.043929025530815125, 0.04187127575278282, 0.008955245837569237]], "c82b615a7d0f0ace75840e24251e9e3d": [0.9997901331839792, [0.024717260152101517, 0.01796024478971958, -0.05781141296029091, -0.008344318717718124, 0.06526809930801392, 0.04420291632413864, 0.06826571375131607, -0.0022115849424153566, -0.002932391595095396, -0.036846812814474106, -0.04327644407749176, 0.04617048427462578, 0.05418779328465462, 0.01671254262328148, -0.00715062627568841, -0.01102537102997303, 0.04966117441654205, 0.008799554780125618, -0.05941273272037506, -0.012659178115427494, -0.020218729972839355, -0.023386318236589432, -0.006940707564353943, -0.003215775592252612, -0.02371448092162609, -0.029893534258008003, 0.026299189776182175, -0.013529513962566853, -0.00024747851421125233, 0.00055000587599352, 0.05065058544278145, 0.04001590237021446, 0.01954388990998268, -0.019407762214541435, 0.06593474745750427, 0.061175134032964706, -0.024841787293553352, 0.005038084927946329, -0.004576943814754486, -0.01816459372639656, -0.04803163558244705, 0.03138875216245651, 0.007759635802358389, 0.03929826617240906, 0.05980570614337921, -0.02350730262696743, 0.021319929510354996, 0.02404673956334591, -0.05696134269237518, 0.026914147660136223, 0.010589567944407463, -0.03673424944281578, -0.009147130884230137, 0.01664762571454048, -0.0013127557467669249, -0.0553014874458313, -0.017316434532403946, -0.010593153536319733, -0.0005893217166885734, -0.0020970955956727266, -0.02881203033030033, -0.04512123018503189, -0.037848569452762604, -0.03649551421403885, -0.04609253630042076, 0.00225096195936203, -0.03944192826747894, -0.013038808479905128, -0.02324994094669819, 0.037847988307476044, -0.046692609786987305, 0.053772956132888794, -0.0697278305888176, -0.0060660820454359055, 0.004685018677264452, 0.018346618860960007, 0.023694591596722603, 0.03318813815712929, -0.029749058187007904, 0.0243969839066267, -0.03896472975611687, -0.017970316112041473, 0.06897328048944473, 0.002560385735705495, 0.02507067285478115, 0.029285015538334846, -0.021480051800608635, -0.036956243216991425, -0.017843356356024742, 0.000183704134542495, 0.07727158069610596, 0.05187922716140747, 0.004940743092447519, 0.01672886684536934, 0.08107970654964447, -0.0689300075173378, -0.08932331204414368, -0.08709605038166046, 0.05826946347951889, 0.04346638172864914, -0.0237834881991148, 0.016435470432043076, 0.007421990390866995, -0.06388869881629944, 0.0642944723367691, 0.028195276856422424, 0.0021789586171507835, -0.04003654047846794, -0.05272696912288666, 0.045041780918836594, -0.03326250985264778, -0.023230865597724915, 0.030666377395391464, -0.03874361515045166, -0.013337511569261551, 0.0019495227606967092, 0.0035415650345385075, 0.043066661804914474, -0.03867363557219505, -0.009714946150779724, 0.05081461742520332, 0.05710013210773468, -0.006477993447333574, 0.07506977021694183, 0.04326920211315155, 0.0017482625553384423, 0.031866904348134995, 0.04214072600007057, -0.06699958443641663, -0.012110965326428413, 0.07181578874588013, -0.040588900446891785, -0.019204607233405113, 0.03639059513807297, -0.0206109918653965, -0.07259082049131393, 0.03869166225194931, -0.006503099575638771, 0.010313136503100395, 0.03397751227021217, -0.00013748706260230392, -0.06638849526643753, -0.026258565485477448, 0.02427336387336254, 0.02286202646791935, -0.03789244964718819, 0.038865283131599426, 0.02798604778945446, -0.04112895578145981, 0.011106571182608604, -0.031083248555660248, 0.026558490470051765, 0.024950778111815453, -0.003490978153422475, -0.007844220846891403, 0.004064530599862337, -0.0018771621398627758, -0.04167177900671959, 0.10324657708406448, 0.017497515305876732, 0.03838089108467102, -0.02551223896443844, -0.0032128728926181793, 0.0009637933690100908, -0.051901817321777344, -0.05828094482421875, -0.045499287545681, 0.0034003439359366894, 0.024528518319129944, 0.023279160261154175, 0.01511794701218605, -0.019298240542411804, -0.07263559848070145, -0.05527355894446373, 0.004171750042587519, -0.0005854147020727396, 0.014963828958570957, -0.06310078501701355, -0.04476106911897659, -0.026291821151971817, 0.06315598636865616, 0.03869517147541046, 0.0659247636795044, -0.07690269500017166, 0.01990690641105175, 0.019124645739793777, -0.002391272922977805, 0.06611230969429016, 0.056879714131355286, 0.10341300070285797, -0.015332679264247417, -0.016229549422860146, -0.004933545365929604, 0.04330591857433319, 0.015189476311206818, 0.04311811923980713, 0.047622159123420715, -0.04637117683887482, 0.03151107579469681, 0.01087002083659172, 0.04548158124089241, -0.02607358992099762, 0.01713540218770504, -0.033750101923942566, -0.03137500584125519, 0.0030200814362615347, -0.01320220809429884, 0.016654491424560547, 0.04505537450313568, 0.040771812200546265, 0.019228002056479454, -0.019325466826558113, -0.0037029292434453964, -0.01083679310977459, 0.00039503572043031454, -0.06355620920658112, 0.041063569486141205, 0.018490362912416458, 0.06472798436880112, -0.01471350621432066, 0.0297752283513546, 0.00940835289657116, -0.010908878408372402, 0.05429847538471222, 0.01618393510580063, -0.006316476035863161, 0.01390879601240158, -0.019373852759599686, 0.0030575685668736696, -0.03541836142539978, 0.0031419643200933933, -0.0071305944584310055, 0.02302875742316246, -0.004729520063847303, 0.0016659790417179465, 0.012160597369074821, 0.008539147675037384, -0.04597189649939537, -0.07615888863801956, 0.01357966661453247, -0.035876862704753876, 0.004604512359946966, 0.056498266756534576, -0.049346376210451126, 0.043059319257736206, -0.0014178138226270676, -0.035685379058122635, 0.02019728347659111, -0.03284946829080582, -0.0519859753549099, 0.01458212360739708, -0.034247782081365585, -0.010713528841733932, 0.043481748551130295, -0.03629631921648979, 0.013265971094369888, 0.019564688205718994, -0.05534236878156662, 0.031307708472013474, -0.018512612208724022, 0.08593934774398804, -0.025308819487690926, -0.03684272617101669, -0.04988900199532509, -0.022433174774050713, -0.05442144721746445, -0.04025774076581001, -0.02434248849749565, 0.018626395612955093, -0.032860491424798965, 0.0445103794336319, -0.01368145551532507, -0.060259196907281876, -0.012205518782138824, -0.011749577708542347, 0.01478249579668045, -0.04929114878177643, -0.012756170704960823, -0.007083497941493988, -0.009665187448263168, 0.018165798857808113, 0.006279023829847574, 0.037127841264009476, -0.02585691399872303, 0.016170760616660118, -0.009913446381688118, -0.03864060714840889, 0.01007754635065794, -0.011781994253396988, -0.035985998809337616, 0.05366712436079979, 0.06029468774795532, -0.006636034697294235, -0.022924955934286118, 0.02139432728290558, 0.048415157943964005, 0.06260163336992264, -0.018782109022140503, -0.00792716071009636, -0.030706163495779037, 0.07377032935619354, 0.037977758795022964, -0.02476230263710022, 0.07782027870416641, 0.03263413533568382, 0.0032791662961244583, 0.0011688339291140437, -0.018419945612549782, 0.0009598378092050552, 0.014449034817516804, 0.020204652100801468, -0.011146592907607555, -0.0325697585940361, -0.04778210446238518, -0.015859872102737427, -0.037542879581451416, -0.1491202414035797, -0.049470312893390656, -0.044309116899967194, 0.01354372687637806, -0.01596422679722309, 0.04628072679042816, -0.015128190629184246, -0.056909795850515366, 0.0010405754437670112, 0.013281852006912231, 0.00915459729731083, -0.004410006105899811, 0.02233314700424671, -0.034008827060461044, 0.029083533212542534, -0.003980257548391819, -0.01652434468269348, -0.035131365060806274, 0.04000966623425484, 0.05637359991669655, -0.04082846641540527, 0.03917817771434784, 0.047620829194784164, -0.003813935676589608, -0.04589780792593956, 0.0653538852930069, 0.04411526769399643, 0.058625757694244385, 0.014329018071293831, -0.01512555219233036, 0.0013122872915118933, 0.03146182745695114, -0.02856479585170746, 0.01398482546210289, 0.04686831682920456, 0.00031521174241788685, 0.04046579450368881, -0.0015822445275261998, -0.017033329233527184, 0.05976472049951553, 0.0484992191195488, -0.008550664409995079, -0.01898902840912342, 0.015889130532741547, -0.023999348282814026, -0.02515171281993389, 0.010706759057939053, 0.05247907340526581, 0.01308481115847826, 0.0020422348752617836, 0.04079148545861244, 0.031779393553733826, -0.030555734410881996, 0.01265009306371212, -0.022642483934760094, -0.0046146707609295845, -0.05278316140174866, 0.06006735563278198, 0.022186851128935814, -0.04615247622132301, -0.02094969153404236, -0.009006659500300884, 0.03932979702949524, -0.046291496604681015, -0.015734978020191193, -0.026756858453154564, -0.0004921903600916266, 0.021328188478946686, -0.06957097351551056, 0.10303477942943573, -0.06871775537729263, 0.023497765883803368, -0.030335869640111923, -0.05609643831849098, -0.056855011731386185, 0.04730001464486122, 0.010313494130969048, 0.007121197879314423, -0.07537605613470078, 0.043211378157138824, -0.002429991029202938, 0.0316501222550869, 0.04224266856908798, 0.02685425989329815, 0.02099747024476528, -0.05854717642068863, 0.026636842638254166, -0.01972939260303974, 0.020067445933818817, -0.01477983221411705, 0.06017068028450012, -0.028788574039936066, 0.023252522572875023, -0.01846769079566002, 0.010905683040618896, -0.05405623838305473, -0.01698632538318634, -0.0007920711068436503, -0.012635800987482071, 0.0028345808386802673, -0.01991448737680912, -0.017440857365727425, -0.024012835696339607, 0.06739228963851929, -0.004969730041921139, 0.016190947964787483, 0.07337520271539688, -0.027832690626382828, -0.022403646260499954, -0.006664182059466839, -0.013858199119567871, 0.00015409190382342786, 0.015353544615209103, -0.020993707701563835, -0.0017832792364060879, 0.04725244641304016, -0.009925700724124908, 0.06739003211259842, 0.010559537447988987, 0.0031877225264906883, -0.007726555224508047, -0.06522882729768753, -0.006613371893763542, 0.013052653521299362, 0.0008672915282659233, 0.003566241590306163, 0.042709629982709885, 0.03200441598892212, -0.031403981149196625, 0.01904832199215889, 0.004702717065811157, -0.07212652266025543, -0.04666931554675102, -0.028456691652536392, -0.012445780448615551, 0.014164667576551437, 0.018783709034323692, -0.051099419593811035, -0.11831028759479523, 0.023283038288354874, -0.005964427720755339, 0.00026920996606349945, -0.005990431644022465, 0.00862724520266056, 0.019983608275651932, -0.02676348201930523, 0.03954724967479706, -0.007321239449083805, 0.03328913822770119, -0.00664164125919342, -0.055831629782915115, 0.047183457762002945, 0.024647146463394165, -0.007957440800964832, 0.04581121727824211, -0.014197861775755882, -0.03004005178809166, 0.025605766102671623, 0.03007539175450802, 0.019857751205563545, 0.06429962813854218, -0.02656901627779007, -0.05855681374669075, -0.01510103140026331, -0.030553540214896202, 0.02590661309659481, -0.03499036282300949, -0.030339449644088745, 0.06779639422893524, -0.03902361914515495, -0.04649121314287186, -0.007056352216750383, -0.004055463243275881, -0.040944550186395645, -0.044604361057281494, 0.005456709302961826, -0.04741290211677551, -0.042329441756010056, -0.022289162501692772, 0.013278782367706299, 0.053813472390174866, 0.054441023617982864, 0.03467587009072304, 0.07760241627693176, 0.07912785559892654, 0.005047544836997986, 0.024842798709869385, -0.016775265336036682, -0.03191434219479561, 0.0018611596897244453, -0.0535050667822361, -0.02377077005803585, 0.036913108080625534, 0.017869453877210617, 0.032127898186445236, -0.01230519637465477, 0.03849255293607712, -0.010915265418589115, -0.005927619058638811, 0.06321872770786285, -0.018467213958501816, -0.003100732108578086, 0.004157267045229673, 0.03522384911775589, -0.009776100516319275, -0.050716664642095566, 0.060007862746715546, 0.024003656581044197, -0.0686640813946724, 0.002650034613907337, 0.018802380189299583, 0.025472309440374374, 0.05274517834186554, -0.04285050556063652, 0.0074072713032364845, 0.06935882568359375, 0.01954198069870472, -0.0010500849457457662, 0.05116423964500427, 0.04570406302809715, 0.0007474947487935424, -0.005865223240107298, 0.006119225639849901, 0.019726037979125977, 0.015547563321888447, 0.021823791787028313, 0.026532940566539764, 0.01624782755970955, 0.050911013036966324, 0.005748235620558262, 0.00395658565685153, 0.06450379639863968, 0.01900932379066944, 0.024477913975715637, 0.0020608496852219105, -0.019699841737747192, 0.015993790701031685, 0.06902723014354706, -0.03013499453663826, -0.06130046397447586, -0.0003941435134038329, 0.007610492408275604, 0.01841672509908676, 0.03856392577290535, 0.025678563863039017, -0.019931253045797348, -0.00031183016835711896, 0.012915331870317459, -0.0215058084577322, 0.016366353258490562, -0.02270844765007496, 0.0031156621407717466, -0.0133865587413311, -0.03156532719731331, -0.0145652424544096, -0.008010165765881538, -0.0033138995058834553, -0.027280129492282867, 0.009686604142189026, 0.009852960705757141, -0.004755417816340923, 0.00808895193040371, 0.03191811963915825, -0.008965929970145226, -0.008120476268231869, 0.02806706540286541, 0.06260116398334503, 0.0015772002516314387, -0.06281747668981552, 0.0016987191047519445, 0.04180233180522919, 0.026666276156902313, 0.02145022712647915, -0.013918912969529629, 0.04426971450448036, 0.040889885276556015, -0.012278757058084011, -0.05510275065898895, 0.028842773288488388, -0.01629824750125408, 0.013822288252413273, 0.04363192617893219, -0.008260183036327362, 0.0049407402984797955, -0.001478254678659141, 0.04758494347333908, 0.07138150185346603, -0.03138791769742966, -0.01326740812510252, -0.06825990229845047, 0.011758644133806229, -0.09050047397613525, 0.04693620651960373, -0.0030088250059634447, 0.0017927769804373384, 0.04705604165792465, -0.008767041377723217, 0.00630732299759984, -0.09396745264530182, 0.0008532338542863727, -0.014679036103188992, 0.01827184483408928, 0.03587218374013901, 0.023242861032485962, -0.01214312482625246, 0.010932902805507183, -0.017203476279973984, 0.0036681664641946554, 0.009330649860203266, -0.03025471419095993, 0.010027414187788963, -0.01064184121787548, 0.02411820739507675, 0.04076448827981949, -0.02916022762656212, -0.023046184331178665, 0.035316213965415955, -0.05114785209298134, -0.008403677493333817, -0.03415264934301376, 0.038822147995233536, 0.013164175674319267, 0.024165596812963486, -0.06962645798921585, 0.037140797823667526, 0.04533090069890022, 0.05981616675853729, 0.010185721330344677, -0.04920065402984619, 0.009120522998273373, 0.023432644084095955, 0.03886883705854416, 0.02282274141907692, -0.017687218263745308, 0.017975930124521255, 0.019490908831357956, -0.012386593967676163, 0.04619241878390312, 0.0055324179120361805, -0.0012828459730371833, -0.03298260271549225, 0.03606634587049484, 0.04019280895590782, 0.009547200053930283, -0.03745202347636223, -0.054162222892045975, -0.01710693910717964, 0.04450368881225586, -0.060550183057785034, -0.0029567431192845106, -0.05983508750796318, 0.00781339406967163, -0.018293973058462143, 0.0009353128261864185, -0.012241862714290619, 0.017937369644641876, 0.04721466824412346, 0.04763934016227722, -0.014441057108342648, -0.012724904343485832, 0.0028494896832853556, -0.02941923774778843, 0.02227584645152092, 0.02138606272637844, 0.021608345210552216, -0.025799069553613663, 0.05226295441389084, -0.003183807013556361, 0.055257588624954224, -0.027647685259580612, 0.041069790720939636, -0.06527761369943619, -0.028924867510795593, 0.008581415750086308, -0.0377885103225708, -0.0578155592083931, -0.013653264380991459, 0.022162068635225296, -0.02164384163916111, 0.07282085716724396, -0.03257971629500389, 0.03677816689014435, -0.08127196878194809, -0.04454732686281204, -0.014007940888404846, -0.026177937164902687, -0.007446803618222475, -0.00015305716078728437, 0.023475155234336853, -0.01577058807015419, 0.008764004334807396, 0.020728513598442078, 0.005194835364818573, 0.0379057340323925, -0.014288842678070068, 0.03100801259279251, 0.018476124852895737, 0.010238674469292164, -0.03046109527349472, -0.009188288822770119, -0.004967767279595137, 0.02034328319132328, 0.007293014787137508, -0.020627671852707863, 0.023480011150240898, 0.002834191545844078, 0.034829601645469666, 0.06119902431964874, -0.019783545285463333, 0.008868942968547344, -0.015636270865797997, 0.006653849501162767, -0.040464069694280624, 0.06156880408525467, -0.03102133236825466, -0.04035579785704613, -0.009278192184865475, 0.038765471428632736, -0.012943091802299023, -0.03172944858670235, 0.004372572526335716, -0.05645120516419411, -0.052762072533369064, -0.001245685969479382, 0.007931849919259548, -0.05007778853178024, -0.013420701026916504, -0.0009653759188950062, 0.06445715576410294, 0.051349375396966934, -0.004171061795204878, -0.002425819169729948, -0.01391348335891962, -0.01202850230038166, -0.03339863568544388, -0.04816043749451637, -0.01652435027062893, 0.007176028564572334, 0.06746704131364822, 0.032423678785562515, 0.018339084461331367, 0.0024701873771846294, 0.03585219010710716, 0.022904902696609497, -0.033792901784181595, 0.05738013982772827, -0.055223144590854645, 0.03612557053565979, -0.06269404292106628, -0.014803475700318813, 0.07378262281417847, -0.02187083661556244]]}}}}, "docs": {"docs": {"1": {"title": "<PERSON><PERSON> <PERSON> lị<PERSON> <PERSON> - Cầ<PERSON>ờ - Vũng Tàu", "path": "<PERSON><PERSON> <PERSON> l<PERSON><PERSON> <PERSON> - Cầ<PERSON>ờ - Vũng Tàu.md", "embeddingModel": "text-embedding-004", "ctime": 1739250645483, "mtime": 1739250707398, "tags": [], "extension": "md", "metadata": {"relates": ["[[<PERSON>]]", "[[<PERSON><PERSON><PERSON><PERSON>]]"], "created": "2025/02/11 12:10:45", "modified": "2025/02/11 12:11:47"}, "id": "439623cad75dee7eaeace4bb00794485", "content": "\n\nNOTE TITLE: [[<PERSON><PERSON> du lịch <PERSON><PERSON> - <PERSON>ần Giờ - Vũng <PERSON>àu]]\n\nMETADATA:{\"relates\":[\"[[<PERSON> lịch Huế]]\",\"[[<PERSON><PERSON><PERSON><PERSON>]]\"],\"created\":\"2025/02/11 12:10:45\",\"modified\":\"2025/02/11 12:11:47\"}\n\nNOTE BLOCK CONTENT:\n\n---\nrelates:\n  - \"[[<PERSON> lịch Huế]]\"\n  - \"[[Ph<PERSON>ợt]]\"\n---\nHướng đi: Qua Cần Giờ -> Ph<PERSON> Vũng Tàu  \n  \nHành trang cần chuẩn bị để đi Vũng Tàu  \n* Quần áo: váy maxi, áo kho<PERSON>c mỏng, mũ, gi<PERSON><PERSON>, dép, k<PERSON>h râm, áo phông, quần dài, quần short, áo hai dây,...  \n* <PERSON><PERSON> phẩm: Ke<PERSON> chống nắng, kem chống nắng, son, đ<PERSON> makeup,...  \n* Vật dụng khác: <PERSON><PERSON><PERSON><PERSON>, thẻ ngân hà<PERSON>, <PERSON><PERSON><PERSON>,...  \n  \n<PERSON><PERSON><PERSON><PERSON> s<PERSON>n  \n* thu<PERSON> phòng ở đường <PERSON>r<PERSON>, <PERSON>ăn Cầu hoặc Phan Văn Thị  \n  \nQuán ăn  \n* lẩu cá đuối hay ốc chơi chơi thì ăn quán 40 gần cây xăng nguyễn trường tộ, đối diện quán bar LOX  \n* bánh khọt miền đông ở đường bà triệu  \n* chỗ ngã tư bến đình ăn đêm cũng nhiều thứ, cháo lòng, bún riêu, cơm tấm, bún thịt nướng, xôi, bánh mì... cơm tấm và bún thịt nướng có hàng xe đẩy của chú Cảnh, 35k 40k nhiều thịt, đông quá tự nướng thịt lun, lựa miếng ngon đứng nướng cho cháy xém rồi đưa chú  \n* quán bánh bèo Tuyết Mai  \n* Đa số h các chợ hải sản ko chặt chém nữa thím, nhưng nếu thím muốn rẻ, thì ra chợ ngay ngã tư chí linh, hoặc vựa hải sản nằm ở ngã 3 chí linh (trên đường đi về, cách biển 7km)  \n* Còn thím muốn gần, trong trung tâm thì ra chợ xóm lưới (chỗ này dân du lịch hay mua) , chợ ngã 3 phi trường, chợ bến đình  \n* Hải sản thì vô chợ mua xong nhờ người ta nấu 50k, xong mang ra biển ngồi ăn  \n* Tối ăn cháo hàu vỉa hè ngay ngã ba số 9 thuỳ vân chỗ trước siêu thị 50k/ tô ( gần đoạn dốc lên đồi chúa giang tay)  \n* Bánh khọt tôm mực quán 14 góc hoàng hoa thám nguyễn trường tộ  \n* ăn sáng thì Phở Sơn Hà 3  \n* caffe view đẹp thì thím tìm quán Mi Amor Beach và các quán gần đó, muốn mua hải sản rẻ trung tâm to thì tìm chợ Xóm Lưới  \n  \nỐc  \n* ốc tự nhiên ở trần phú  \n* Gành Hào có 2 cơ sở 1 cái trần phú là đoạn bãi dâu  \n* bình dân hơn thì ra Thành Phát  \n  \nChỗ đi chơi  \n* Hồ Tràm  \n* Đi chơi thì có tượng chúa kito dang tay, hồ mây, hồ đá xanh, ngọn hải đăng, tham quan Thích ca Phật đài, đi đồi con heo  \n* Buổi tối dạo dọc bờ biển, ra Vựa Hải sản Trần Phú  \n* Phước Hải  \n* Từ vũng tàu đi long hải ôm theo đường biển có thể ghé thăm dinh cô và mộ cô rồi theo cái hướng đèo nước ngọt ra hồ tràm hồ cốc.  \n* Từ hồ tràm hồ cốc đi ra lagi cũng là cung đường biển có thể ghé thăm dinh thầy thím, hải đăng lagi, đi đúng mùa người ta mở đèn vườn thanh long buổi tối đẹp lắm.  \n* Từ lagi vào phan thiết rồi ra mũi né , thành phố phan thiết bé tý xíu tham quan các nơi rồi ra mũi né ở rs hoặc phi ra hòn rơm.  \n* Cáp treo  \n  \n---  \n  \nCắm trái  \n  \n[https://voz.vn/t/chia-se-kinh-nghiem-di-camping-cam-trai-cho-nguoi-moi.175142/](https://voz.vn/t/chia-se-kinh-nghiem-di-camping-cam-trai-cho-nguoi-moi.175142/)  \n—  \nCaffe: Nhà của Gạo, photo bike coffee, gọi gió  \nBệnh viện 2 Lâm Đồng  \nCổng trời  \nBún riêu O Lan đối diện nhà thờ Bảo Lộc  \nBánh mì nướng trước Vincom", "embedding": [0.012018117, 0.052909534, -0.05321504, -0.02716614, 0.037649434, 0.015860762, 0.031820044, -0.0028358805, 0.03227552, -0.018314306, -0.0144733805, 0.13709575, 0.06747134, -0.0009117878, 0.05175173, -0.02358897, 0.0044372827, 0.031151183, -0.06112075, -0.038897734, -0.00055446883, 0.021364862, 0.05406118, 0.0069723995, -0.02697382, -0.05412532, -0.018546125, 0.03806098, 0.018370014, -0.09597281, 0.03454909, 0.04531692, -0.007874622, 0.046741046, 0.02137288, 0.035445247, 0.049659293, 0.0034438474, 0.0006497421, -0.0959814, -0.03751415, -0.008309351, 0.014741015, 0.055214174, -0.014371116, -0.07355937, 0.0007462097, 0.018556701, -0.045447133, -0.008703382, 0.035827268, 0.00057459646, 0.02699674, 0.03601803, -0.049563996, -0.0008475424, -0.028477605, -0.069830745, 0.024124218, -0.020976797, -0.028848208, -0.045468647, -0.0011069463, -0.04189262, -0.018139185, -0.026645202, -0.008527622, -0.0062613534, -0.027589358, -0.00625192, -0.05170688, 0.042079955, -0.040167656, 0.0362352, 0.0145480875, -0.018772665, -0.0061397525, 0.023886528, 0.08427055, 0.03222822, -0.07930534, -0.00702211, 0.028876003, 0.056971643, 0.033020746, -0.006755568, -0.030003455, -0.014922112, -0.09478924, -0.026416643, 0.02243341, 0.011415834, -0.0015136234, -0.023235444, 0.061187305, -0.03573246, -0.09457099, -0.09292512, 0.078341156, 0.010358424, -0.003517047, 0.02091104, -0.023610452, -0.03981682, 0.04299613, -0.016210401, -0.0069153863, -0.013242764, -0.0738404, 0.047903642, 0.004852582, -0.038232096, 0.041179452, -0.03697394, -0.024827808, -0.0010169187, -0.00388276, 0.014224992, -0.06523141, -0.02656494, 0.030242324, 0.0019373624, -0.010190159, 0.072292715, 0.013251673, 0.008382192, -0.014399316, -0.035131134, -0.034340717, -0.032956727, 0.039213132, -0.035706826, -0.0394458, 0.017011613, -0.042986922, 0.023454053, 0.049322285, -0.005314822, 0.007846124, 0.018621074, -0.0426487, -0.055325806, -0.048553165, -0.023008695, -0.006092726, -0.018206034, -0.0045495895, 0.09147622, -0.042053845, 0.03126074, -0.05117693, 0.031052606, -0.045100607, -0.007557864, 0.001111803, 0.02343885, 0.019230673, -0.0133863995, 0.035494823, 0.040108062, -0.0017575626, -0.015373836, -0.005076444, 0.03853429, 0.006898269, -0.046081606, -0.01542012, -0.029798212, 0.042915486, -0.01753083, 0.007472935, -0.09354579, -0.046643782, -0.051591713, 0.0060850824, -0.00610979, -2.1663023e-05, 0.055768613, -0.0030567746, 0.025715796, 0.08611199, 0.013492582, -0.017185561, -0.04841711, 0.025243087, -0.010307345, -0.017590847, 0.022324584, 0.020439534, 0.046673845, 0.0065141185, 0.034919627, 0.017490875, 0.033003554, -0.019102318, 0.015996972, 0.09477124, -0.033141293, -0.005944516, 0.0010396586, 0.031121572, 0.010145095, -0.059082635, -0.06884271, -0.038599923, 0.052966803, -0.008116073, -0.024913004, -0.03714907, 0.004168405, -0.07453622, -0.03420556, -0.0062117674, -0.048757616, -0.030037804, -0.0129472995, 0.073455945, -0.0029769526, 0.077155046, -0.06961684, -0.0036041401, -0.014489775, 0.013898352, 0.06426965, 0.03754465, -0.024459466, 0.02284866, -0.01160438, -0.036330063, -0.05224912, 0.016134275, 0.034531273, 0.0037030948, 0.062681064, -0.0037761994, 0.019866668, -0.015958644, -0.05032652, -0.02738983, 0.02974104, 0.0035839567, -0.008778946, 0.049985737, -0.0043062926, 0.018913228, -0.006924585, 0.020495413, 0.051282834, -0.014163144, -0.03724026, -0.016745694, -0.02395106, 0.030281225, -0.03588243, -0.040895704, -0.017641323, 0.050588164, 0.017132219, -0.030878453, 0.009897818, 0.036707573, -0.033347216, -0.010051789, -0.058360722, -0.042728834, -0.03867116, -0.01679714, -0.039890766, 0.04798843, -0.015609534, 0.033246063, -0.07881531, -0.030695146, -0.0029971686, -0.022982951, 0.039712913, -0.039164178, 0.033018447, -0.043923695, -0.045027245, 0.012378249, 0.0021787123, -0.023855232, 0.019040016, 0.018484429, -0.03319255, 0.009764831, 0.005640544, -0.031871177, 0.0052397423, 0.029070009, 0.062727205, 0.00626436, -0.032122597, 0.048522826, -0.008941429, 0.033237096, 0.08797393, -0.0010449438, 0.0010928499, 0.004771893, 0.025393598, 0.009242991, 0.03266303, 0.015527947, -0.0037577539, -0.034771983, 0.021694982, -0.012212024, -0.012462096, 0.08360499, 0.087207995, -0.075591706, 0.00077946164, -0.02074932, 0.012716627, -0.12606502, -0.04508314, 0.0034303449, 0.029568499, 0.028002724, 0.03428996, -0.026191162, 0.012680426, 0.062139418, 0.0023060413, 0.016185166, -0.0067372792, 0.014823699, -0.029646289, 0.019576635, -0.02883388, 0.0065259347, -0.0060645156, 0.013443274, 0.034268443, -0.06505712, 0.018626694, 0.07757189, 0.020175077, 0.049251977, 0.06584809, -0.011065836, 0.02258625, 0.017053982, -0.052277677, 0.022223439, 0.0033152201, 0.0094270455, 0.053221565, 0.07954228, 0.08227805, 0.00035579986, -0.03453236, 0.024461236, 0.03234827, 0.051171303, 0.018564701, -0.005272827, -0.037366875, 0.0075592175, 0.022941263, -0.007383116, 0.065326765, -0.0015356833, -0.00940939, 0.04268577, 0.061402783, -0.033867486, -0.046699308, -0.013494314, 0.0035117522, -0.046864484, 0.022254145, -0.011846657, 0.0022187578, -0.021202905, 0.020240068, 0.033756215, -0.0472876, 0.0052006627, -0.017047979, -0.033428848, 0.030677095, 0.009015095, 0.052027162, -0.0653158, -0.01164536, -0.03485532, 0.013869819, -0.0731154, 0.00080212485, 0.011354097, -0.015030085, -0.009855497, 0.051481176, 0.022643667, 0.022248333, 0.040065058, 0.0328632, -0.013472075, -0.028357971, 0.060780693, -0.016764905, 0.03862507, -0.006693702, 0.0655899, -0.012877171, -0.043928917, 0.0059267036, -0.02313429, -0.05209678, 0.02109327, 0.014386765, -0.031524565, -0.03383322, -0.0041311444, -0.035992544, 0.018860444, 0.009201327, -0.020865226, 0.068904296, 0.06115189, 0.021451702, -0.009535407, -0.054774225, 0.008864977, -0.018296134, -0.034413457, -0.027011052, 0.059995323, 0.056872644, 0.008474387, 0.025731124, -0.032313906, 0.014407226, 0.011114924, -0.034909945, 0.040767595, 0.027549203, 0.00021797592, -0.008641013, 0.043710824, -0.0031752128, -0.0411227, 0.03340287, 0.023950765, 0.031254493, -0.01712195, -0.028938347, -0.0018804468, -0.012576268, 0.01485138, -0.034317918, -0.09005294, 0.018980468, 0.013907869, 0.053721, -0.003789503, -0.0015157181, -0.0056882845, -0.03504513, 0.022034507, 0.0005591215, -0.017611396, -0.023826601, -0.05247041, 0.03024223, -0.019309962, -0.01132232, 0.03376087, 0.018968211, 0.031851124, 0.026615415, 0.033091914, 0.0061261444, 0.0656971, 0.019421896, -0.051974982, -0.020892972, -0.059102867, 0.009006497, -0.04182458, 0.00035559005, 0.036839303, 0.007373192, 0.005376341, 0.017139299, 0.0049837586, 0.016781744, 0.025493946, 0.013451256, -0.01576042, -0.061101004, 0.002179477, -0.0065667476, 0.044141438, -0.005161404, -0.024266228, 0.0042942474, 0.07456024, 0.03936432, 0.0027257977, -0.05276415, -0.020291159, 0.03256581, -0.054191787, -0.05037712, 0.07704597, 0.0024499253, 0.021827424, -0.05458923, -0.027142864, -0.0012915762, -0.038425602, 0.019165896, -0.020254334, -0.004067973, -0.05341643, 0.05225149, 0.049789716, -0.03692034, 0.01872584, -0.020130169, 0.018682744, -0.004240627, 0.019744046, 0.012000543, 0.033149835, -0.010742711, -0.033792075, 0.03400059, 0.06511937, 0.023196643, 0.0013582156, 0.07098738, 0.012483615, -0.022989033, 0.021483673, 0.024602756, 0.026790224, 0.0043168357, -0.04180193, 0.038531322, 0.030283924, 0.024739115, 0.00449802, 0.029812045, -0.018556478, 0.04919313, 0.011465666, -0.036647394, -0.035237335, -0.021188632, -0.0044149244, -0.03943413, 0.04990672, -0.009299849, -0.0350752, -0.008640446, 0.013017522, -0.021813514, 0.026762918, -0.012393971, -0.0066997986, -0.03396729, 0.00058988744, 0.0567796, -0.015573969, -0.038627498, 0.05308408, -0.019165523, 0.006303383, -0.021370335, 0.011007236, -0.058587607, -0.038825892, -0.0007657206, 0.07295974, 9.143389e-05, -0.022956626, 0.015295617, 0.05358565, -0.01432222, -0.028596183, -0.0099794315, -0.018518753, -0.00034282543, 0.020300955, -0.0024005796, -0.0031263782, 0.015072295, -0.0010309815, -0.08397303, 0.04990098, -0.025047222, -0.0144290235, 0.036544975, 0.008961672, -0.007310053, -0.0042161825, -0.002932567, 0.020325834, 0.003820206, -0.008593653, -0.063297115, 0.028044717, -0.072428234, 0.015501706, 0.015495187, -0.023111993, 0.05839634, 0.0038671873, -0.040921956, -0.063013025, 0.033012778, -0.030816255, -0.0010830405, 0.028738266, 0.0458221, 0.022023838, 0.011267545, -0.023859538, 0.008739681, -0.050687734, 0.022081157, 0.026431633, 0.0010581872, 0.00871958, 0.006712131, 0.041171476, -0.015121832, 0.008070612, -0.034975734, -0.01232957, 0.025058778, -0.013661109, -0.013391, 0.0048929695, -0.0658688, -0.0010548895, 0.034288872, 0.068723775, -0.03431878, -0.04920087, 0.012073456, -0.031087434, 0.029902047, -0.029186629, 0.06524054, 0.044089727, 0.038919978, -0.06795853, 0.06889683, -0.022007799, 0.013756997, -0.0010449564, -0.0073294067, -0.019885331, -0.006634279, -0.019614516, -0.0075832624, -0.014011567, -0.004157503, -0.05779877, -0.012539208, -0.064957336, 0.010612813, -0.0005694446, 0.00064520695, -0.015540969, 0.01693838, 0.002839012, 0.058297798, 0.03338094, 0.00050628243, -0.015590475, 0.044756938, -0.024054915, 0.030881, -0.02392382, -0.011828974, 0.018068647, -0.021592895, -0.010187932, 0.032334752, 0.048521068, -0.0022952012, 0.0035789204, 0.007585332, -0.00051543966, -0.05033089, -0.02488323, 0.045838337, -0.020429023, 0.059267737, 0.0009984635, -0.026999513, -0.0137756085, -0.008225553, -0.039388582, 0.009517607, 0.0029774448, -0.073660746, 0.031469587, -0.01752587, 0.07047663, 0.013543643, -0.043668933, 0.043209177, -0.016105467, 0.049624626, 0.048233997, -0.025594838, 0.022451118, 0.042209208, 0.01093991, 0.013436656, -0.045579266, 0.04496844, 0.04934901, -0.025830727, 0.03617865, 0.02361756, 0.048836183, 0.02288853, -0.064559914, 0.0058462345, -0.031428713, 0.07594272, 0.014151893, -0.010155719, -0.04768486, 0.046433534, -0.079902545, -0.024686849, -0.0036596307, -0.014066837, -0.018221542, 0.0005797957, 0.038130533, -0.02111222, -0.044700455, -0.038149852, 0.0077453656, 0.029366383, 0.019819705, -0.017431214, 0.0019253351, -0.064085126, -0.051731344, 0.01300165, 0.010897374, -0.045162678, 0.09036691, 0.03279036, -0.037061334, -0.013495809, 0.027420534, 0.020905606, -0.013995472, 0.040892847, -0.016059076, 0.06745597, -0.017422877, 0.003213586, 0.052152704, -0.03587849], "created_at": 1747110788338, "nchars": 2951}, "2": {"title": "<PERSON><PERSON>", "path": "Viclass.md", "embeddingModel": "text-embedding-004", "ctime": 1733244826412, "mtime": 1731987847000, "tags": [], "extension": "md", "metadata": {"created": "2024/12/03 23:53:46", "modified": "2024/11/19 10:44:07"}, "id": "efdabfec33e46eda7890ba8ca97e2b97", "content": "\n\nNOTE TITLE: [[Viclass]]\n\nMETADATA:{\"created\":\"2024/12/03 23:53:46\",\"modified\":\"2024/11/19 10:44:07\"}\n\nNOTE BLOCK CONTENT:\n\n# 1. Task đã làm không có trong OpenProject\n\n- Refactor NGINX, thêm local NGINX.", "embedding": [0.022437, 0.045646, -0.035821386, -0.0033321164, 0.011381413, -0.0019068632, 0.039403517, -0.0040167053, -0.004122875, -0.015304163, 0.022200761, 0.047468886, 0.06930359, -0.024849107, 0.03526464, -0.033164896, 0.028084813, -0.016550079, -0.0068399073, -0.014614136, -0.04123917, -0.008596298, 0.012768535, -0.012110144, -0.03689855, -0.0036074517, 4.0930776e-05, 0.022546975, -0.003685051, -0.046657696, 0.038073458, 0.025138743, -0.05635922, 0.00990186, 0.04223901, 0.055487674, -0.017847547, 0.07320121, 0.004276789, -0.03310791, -0.05705327, -0.0054649753, 0.005847002, 0.046163026, 0.010265981, -0.002453377, 0.023594584, 0.0035263214, -0.050854217, 0.02057153, 0.028334552, 0.010277223, -0.018446065, 0.04216581, -0.059402313, -0.06662488, -0.055264995, -0.053904254, 0.017931467, 0.012196864, -0.052551303, -0.05813768, -0.060788132, -0.04750675, -0.022028508, 0.009175142, -0.011628219, -0.022606792, -0.012720716, -0.0031070057, -0.048918672, 0.038317434, -0.046902142, 0.010367373, 0.007048201, -0.007440073, -0.022178482, -0.044648986, 0.030902227, 0.042987064, 0.008139675, -0.029001746, 0.05032788, 0.056137405, 0.02393859, -0.0075138495, -0.06293142, -0.0044335444, -0.0016560549, 0.044148244, 0.04748983, 0.02255352, -0.00019806079, -0.023229929, 0.049657717, -0.010832008, -0.05819358, -0.11905542, 0.041125126, 0.0791996, 0.013206951, 0.005277369, 0.0120900525, -0.0826077, -0.0028382107, -0.050023187, -0.04747678, 0.0049167634, -0.104364224, 0.0218856, 0.0023675428, -0.002409499, 0.042317647, 0.011815909, -0.0629185, -0.016847659, -0.0011263439, -0.001014221, -0.0061590704, -0.011760461, 0.05974758, 0.0553739, -0.021665037, 0.044308458, 0.038699582, 0.020264413, 0.004247706, 0.015041349, 0.019309808, -0.028254203, 0.05775149, -0.025782242, -0.0023596445, -0.009706467, 0.008420367, 0.027050871, 0.039102316, -0.0025157614, 0.02972379, 0.07512902, 0.010975043, -0.02499685, -0.05679913, 0.021839468, -0.00388382, -0.05589551, 0.05353257, 0.11114164, 0.033240736, 0.045507886, 0.00582481, -0.027974175, -0.022077331, 0.0112831835, 0.012107834, -0.034238603, 0.031106027, -0.035470378, 0.04528219, 0.015077487, -0.032850817, -0.040043805, -0.0168227, 0.031534664, 0.015905209, -0.06166179, -0.021877196, -0.032163985, 0.0056347246, -0.01793761, -0.01142102, -0.043099243, 0.014598235, -0.035348047, -0.026724938, 0.012718566, -0.031251233, -0.009241755, -0.03592077, -0.025255216, 0.04436356, 0.05358603, -0.009962395, -0.032446492, 0.06375966, 0.012236026, -0.012802372, 0.03093857, -0.0026702853, 0.029886223, 0.03320501, -0.028391914, 0.010790262, 0.045605887, -0.042928394, -0.03841573, 0.02505962, -0.021685693, 0.0051607676, 0.0023379792, 0.011322842, -0.0144764995, -0.04778601, -0.016698826, -0.021869943, -0.020542908, 0.047245495, -0.0076842722, 0.03375389, 0.00627451, -0.0137299355, -0.011884275, 0.013713784, -0.017270694, 0.00016830677, 0.035725117, 0.08803122, 0.05243676, 0.09113943, -0.056790363, 0.040991083, 0.012313093, 0.027878778, 0.03676932, 0.04789291, -0.005843956, 0.01080972, 0.028255768, -0.005264284, -0.03455729, -0.011949748, 0.08123319, 0.02165958, 0.022610012, -0.003451625, 0.00628756, -0.030233596, -0.0004014585, -0.030141015, -0.012525845, 0.04290256, 0.017110895, 0.009294905, 0.0016713333, 0.12291312, -0.040820297, -0.0039661047, 5.5025772e-05, 0.006821457, -0.05650177, 0.044168107, -0.04607245, -0.045560613, -0.03692353, -0.045536768, 0.00057267735, -0.01982147, 0.04174757, 0.0019796384, -0.024380745, 0.07432677, -0.023794359, -0.0029205417, -0.0316318, -0.04775823, -0.012833769, -0.040870637, -0.027306981, 0.023501845, -0.03764704, 0.010568345, -0.07869783, -0.067400075, -0.032378666, -0.039519235, 0.0074125966, -0.011816383, 0.04724602, -0.08023772, -0.0028245163, 0.033221167, -0.013241836, -0.0052089966, -0.030994607, 0.044968467, -0.006999491, -0.035948362, -0.0035747765, -0.053183462, -0.020684402, 0.039823744, 0.07130444, -0.024599819, -0.07077956, -0.0039622104, 0.020528985, 0.029733412, 0.03629077, 0.06536128, 0.014387844, 0.008349694, 0.004446289, 0.0046833237, 0.037855923, -0.029988462, 0.0646336, -0.025314454, -0.010090328, -0.031519506, 0.015388112, 0.06542321, 0.024208749, -0.09063619, 0.0413801, -0.019629696, -0.022506198, -0.12687716, -0.033757746, -0.020615261, 0.0071782316, 0.05870481, 0.0069975182, -0.068350516, 0.016808432, 0.007910675, 0.0024899663, 0.017043693, 0.0073251016, -0.028771788, 0.0064523993, 0.04485556, -0.03459734, -0.02270608, -0.017810684, 0.019414386, 0.022453755, -0.0333693, 0.0048140227, 0.06273076, 0.0435323, -0.003614582, 0.03511334, 0.02975513, 0.033113137, -0.052726414, -0.070996344, -0.016826311, -0.044999264, 0.023068653, 0.039311066, 0.010594923, 0.06847288, 0.029891536, -0.005148157, -0.006474048, 0.029238852, 0.04496986, 0.035888255, 0.018395465, -0.014019922, -0.0021139919, 0.025851298, -0.015689468, 0.03929885, -0.010652398, 0.007327571, 0.047146514, 0.05048713, 0.008391918, -0.032377534, -0.036319613, 0.042691946, 0.012146298, 0.026962534, 0.02886368, -0.08352954, -0.0042544524, 0.015277282, 0.03898433, 0.0026606428, 0.0011918565, 0.008990438, 0.010055943, 0.037614394, 0.02165496, 0.046036903, -0.06324439, -0.008381336, 0.021320397, -0.025537098, -0.060155243, 0.008011414, 0.022073755, -0.008075809, -0.03329141, 0.026324557, -0.020841971, 0.099852614, 0.0038057324, 0.022844719, -0.017845917, -0.051512625, 0.018863993, -0.07477242, -0.0024590823, 0.034092624, 0.011669038, 0.016849818, 0.019503655, 0.008963437, 0.030532103, -0.031179138, -0.0032305003, 0.05022901, -0.019804804, -0.028771106, 0.0010929707, -0.008941158, -0.011733265, 0.0058641494, -0.04050279, -0.006342319, 0.010141877, 0.021532295, -0.026807696, -0.057834223, -0.027231814, 0.035524115, -0.059267946, -0.043812726, 0.050311103, 0.057176296, -0.0061203027, 0.008566862, -0.03178917, 0.050398316, -0.013933849, -0.079149514, -0.02062248, 0.03284907, 0.06738296, 0.009639675, 0.063272595, 0.0052289222, -0.045173045, 0.015630288, 0.01286498, 0.024045734, -0.014157985, -0.035280704, -0.036615655, 0.0013155888, -0.010078096, -0.05715107, -0.058298655, 0.0505274, 0.01822979, 0.044326227, 0.025887594, -0.04293331, 0.0014546585, 0.028035562, 0.019304734, -0.04764591, -0.042762943, 0.011736676, -0.03755361, 0.015136616, 0.017366331, -0.010866754, 0.052489925, 0.0061973254, 0.03515345, 0.024908934, -0.045197524, 0.034098413, 0.035354327, 0.0037387053, -0.0018711123, -0.01492331, -0.002694141, -0.001164639, -0.08007094, 0.014817455, 0.05043009, -0.048851166, 0.001645302, -0.0067849415, -0.023677494, -0.0075809564, -0.005178748, 0.025319051, -0.002191221, -0.031494424, 0.033323623, 0.021578887, -0.011744493, 0.025926292, -0.03883257, 0.046264756, 0.09391603, 0.013982552, -0.020796526, -0.0069741514, -0.043572534, -0.0032936842, -0.02536292, -0.022470843, 0.08004442, 0.033452194, 0.007881987, -0.075184576, 0.01934715, -0.018713705, -0.06370661, 0.044181556, -0.01035621, -0.047943298, -0.04776191, 0.02114642, -0.039780412, -0.059732087, 0.025589032, -0.014477284, -0.0017417206, 0.009075763, 0.011617937, 0.0068319323, 0.046951983, 0.036206167, -0.020493705, 0.054291405, 0.0066610244, 0.051974755, 0.0011671124, 0.047613084, 0.02944144, -0.00889695, 0.0489802, -0.008627702, 0.023741705, 0.0032191204, 0.0010869914, 0.015718862, 0.06323323, 0.03028029, -0.0076399622, 0.049728457, 0.034100305, 0.06616358, -0.009018376, -0.0134076355, -0.06914524, 0.019976666, 0.0011966813, -0.02882468, 0.013220347, -0.015224753, -0.01582337, 0.022588803, -0.03895761, -0.05169247, -0.011884921, -0.043314137, -0.006485682, -0.037228707, -0.028813945, 0.075796016, 0.018847594, -0.0024279477, -0.015003544, -0.013088915, 0.009776156, 0.00050286565, -0.01228078, -0.04616074, -0.03460588, 0.035286862, 0.054040927, 0.010654817, -0.021511283, -0.018043065, 0.06211404, 0.035300825, -0.011508903, 0.01310686, 0.036610603, -0.03512229, 0.049026724, 0.011132642, -0.016033823, -0.022965651, 0.013359282, -0.025131142, 0.047870442, -0.05190246, -0.06979566, -0.033556394, -0.013365335, -0.010574047, 0.006423328, -0.029477779, 0.029846365, -0.009944319, -0.013269794, -0.098689206, -0.030274693, -0.04173593, 0.023499751, 0.034720365, 0.014876959, 0.016507817, -0.030648587, -0.0744579, -0.1191601, 0.0067076874, -0.02319633, -5.3781005e-05, 0.056503538, 0.109167494, 0.018531634, 0.021340277, -0.006571231, -0.0038524636, -0.008283715, -0.031530425, 0.009280278, 0.005204624, 0.024022538, -0.018713867, 0.0067746933, 0.03192008, -0.0024808862, 0.0038535388, -0.012854131, 0.014393021, 0.00432506, -0.015737198, 0.0036486213, -0.040147167, 0.008695575, -0.014024367, 0.07282896, -0.0063275825, -0.036274914, 0.030105565, -0.009374296, -0.00470579, -0.00047773382, 0.03553321, 0.0033338289, 0.06930017, -0.027225204, 0.0054614893, -0.027081542, -0.0131280245, -0.016658057, -0.018504364, 0.0115648545, -0.04885757, -0.01598718, 0.023839448, -0.04163066, -0.04801284, -0.025456363, -0.040514845, -0.06403948, 0.0005130441, -0.019705683, -0.02117085, 0.015488101, 0.025600582, 0.026050031, 0.06997053, -0.007973864, -0.03401048, 0.048846096, 0.027642975, -0.0096600605, 0.03782773, -0.018669039, -0.008065256, 0.022584995, -0.04244331, 0.018561333, 0.042075116, 0.046772838, -0.016899372, -0.02003573, 0.0048896754, 0.02124395, -0.0213716, -0.033157982, 0.040645637, -0.038817286, 0.046892244, -0.048736636, 0.044703107, -0.005361613, -0.032477275, 0.03891739, -0.02067915, -0.041280743, -0.045752432, 0.00031973587, -0.025102817, 0.053362366, 0.06303545, 0.05511716, 0.031621534, -0.058464717, 0.058234483, 0.040688694, 0.020356337, 0.023660028, -0.0140293855, 0.009752743, 0.07996698, -0.02096853, 0.029606149, 0.011312973, -0.025960036, 0.03196172, 0.031707507, 0.043731686, -0.020291684, 0.013698245, 0.014103148, -0.02783108, 0.02192075, 0.0059772283, -0.012113909, -0.040503792, 0.01890846, -0.043876473, -0.032338906, 0.010057543, -0.019094057, 0.0038545672, 0.023287171, 0.05871446, 0.012787647, -0.05535459, -0.08945108, 0.051268764, 0.011920886, 0.001948521, 0.025625445, -0.020867314, -0.025537359, -0.06660349, 0.02416166, 0.016623102, -0.0032608125, -0.020083172, 0.00481245, 0.002426528, -0.00014410279, 0.025624253, -0.0010917612, -0.015982335, 0.043169834, 0.0066243145, 0.061781272, -0.031541105, -0.008508415, 0.07268152, -0.017046323], "created_at": 1747110788354, "nchars": 205}, "3": {"title": "Microservices", "path": "Microservices.md", "embeddingModel": "text-embedding-004", "ctime": 1728769728000, "mtime": 1747076112902, "tags": [], "extension": "md", "metadata": {"relates": ["[[Backend - Back-end]]", "[[Cloud - SaaS]]", "[[Machine learning - Deep Learning - AI - ML - DL]]"], "created": "2024/10/13 04:48:48", "modified": "2025/05/13 01:55:12"}, "id": "fab50ff9baa285f7115a29bc8d7a23de", "content": "\n\nNOTE TITLE: [[Microservices]]\n\nMETADATA:{\"relates\":[\"[[Backend - Back-end]]\",\"[[Cloud - SaaS]]\",\"[[Machine learning - Deep Learning - AI - ML - DL]]\"],\"created\":\"2024/10/13 04:48:48\",\"modified\":\"2025/05/13 01:55:12\"}\n\nNOTE BLOCK CONTENT:\n\n(cont'd) ## 2.2. API Gateway\n\n- Envoy là một proxy mạnh mẽ và linh hoạt, đang ngày càng được ưa chuộng trong các kiến trúc cloud-native hiện đại. Nó tự hào có các số liệu và tính năng nâng cao, cung cấp thông tin chi tiết sâu sắc về luồng lưu lượng và hiệu suất ứng dụng. Ví dụ: Envoy có thể theo dõi các phần trăm độ trễ, tốc độ yêu cầu và mã lỗi, cho ph<PERSON>p gi<PERSON>m sát và khắc phục sự cố chi tiết. <PERSON>ó cũng hỗ trợ các tính năng nâng cao như traffic shadowing (tạo bản sao lưu lượng), fault injection (chèn lỗi) và các quy tắc định tuyến phức tạp dựa trên tiêu đề hoặc các thuộc tính yêu cầu khác. Tuy nhiên, sức mạnh này đi kèm với một cái giá: Envoy có đường cong học tập dốc hơn so với các lựa chọn thay thế đơn giản hơn. Cấu hình Envoy hiệu quả đòi hỏi sự hiểu biết vững chắc về mô hình cấu hình của nó và các khái niệm mạng cơ bản.\n\n- HAProxy, viết tắt của High Availability Proxy (Proxy có tính sẵn sàng cao), là một giải pháp đã được thiết lập và đáng tin cậy cho việc cân bằng tải TCP và HTTP. Nó nổi tiếng với hiệu suất và tính ổn định cao, khiến nó trở thành một lựa chọn phổ biến cho môi trường sản xuất. HAProxy vượt trội trong việc phân phối lưu lượng trên nhiều máy chủ backend, đảm bảo tính sẵn sàng cao và ngăn chặn tình trạng quá tải. Đối với những người tìm kiếm hướng dẫn về cách sử dụng HAProxy để cân bằng tải ứng dụng, các tài nguyên như bài viết Viblo \"Hướng dẫn sử dụng HAProxy cho load balancing ứng dụng\" (có tại [https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA](https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA)) cung cấp những hiểu biết sâu sắc và các ví dụ thực tế. Tài nguyên này, mặc dù đề cập đến Istio, cung cấp một cái nhìn tổng quan tốt về khả năng của HAProxy trong bối cảnh cân bằng tải.\n\n- Kong là một proxy và API gateway đáng chú ý khác, cung cấp một kiến trúc dựa trên plugin cho phép dễ dàng mở rộng và tùy chỉnh. Nó thường được sử dụng để quản lý và bảo mật API, cung cấp các tính năng như xác thực, giới hạn tốc độ và chuyển đổi yêu cầu.\n\n- Pingora, được phát triển bởi Cloudflare và có sẵn trên GitHub ([https://github.com/cloudflare/pingora](https://github.com/cloudflare/pingora)), là một nhân tố tương đối mới trong lĩnh vực proxy. Trọng tâm của nó là hiệu suất và hiệu quả, được thiết kế để xử lý quy mô lớn của mạng Cloudflare.\n\n- Traefik nổi bật với sự dễ dàng cài đặt và tích hợp gốc với Let's Encrypt để quản lý chứng chỉ SSL tự động. Điều này làm cho nó trở thành một lựa chọn đặc biệt hấp dẫn cho các nhà phát triển cần triển khai và bảo mật ứng dụng một cách nhanh chóng mà không cần cấu hình phức tạp. Traefik tự động khám phá và cấu hình chính nó dựa trên cơ sở hạ tầng mà nó đang chạy, chẳng hạn như Docker hoặc Kubernetes, đơn giản hóa đáng kể quy trình triển khai.", "embedding": [0.008662505, 0.031586856, -0.103783414, -0.03494566, 0.0828021, 0.0012805631, 0.03253143, -0.014708179, 0.028908113, -0.00342848, -0.0049188565, 0.07816006, 0.041409224, -0.00036873968, 0.06294935, -0.051725585, 0.011881714, 0.04833816, -0.05243157, -0.025361111, -0.028122323, 0.029939827, 0.012787301, -0.02032957, -0.054605145, -0.039273754, 0.003600235, 0.013272738, -0.021723548, -0.03251133, 0.02074586, 0.020366002, 0.004634654, -0.015948875, -0.018761031, 0.046266984, 0.0459962, 0.05496043, 0.032308273, -0.08511984, -0.031741947, 0.0073840725, -0.01717625, 0.11298621, 0.023848893, -0.05671961, 0.025397392, 0.030383157, -2.7068332e-05, 0.006161926, 0.02864239, -0.034628823, 0.0055561243, 0.030661188, 0.01020122, -0.044838715, -0.018068228, -0.046852622, 0.022291632, 0.04235637, -0.03788784, -0.051876146, -0.020101998, -0.009983707, -0.011935014, 0.008332056, -0.030612854, -0.026967885, -0.061623756, -0.0063037663, -0.0748146, 0.03187772, -0.0053152563, 0.046995144, 0.02495183, 0.034233037, -0.007065403, 0.0025613173, 0.002243117, 0.052730616, -0.061325774, 0.0160706, 0.08909796, 0.014666217, 0.036943108, -0.036309604, -0.04829415, -0.057426855, -0.047435373, -0.01220085, 0.04021228, 0.0043386044, 0.015580573, -0.027486315, 0.032836683, -0.03525031, -0.06738291, -0.1113167, 0.061760575, 0.07247227, -0.0029603622, 0.014821907, -0.007964576, -0.07348859, 0.057671476, 0.008155809, -0.0505436, -0.0005551781, -0.11611317, 0.061029315, -0.0009103371, 0.00548118, 0.05734896, -0.018288001, -0.052096333, -0.012571493, -0.02931847, 0.021273326, -0.028123466, -0.03494346, 0.038773146, 0.058662802, 0.025744231, 0.051817346, 0.03435721, 0.041575048, -0.001088328, -0.014091197, -0.030731672, -0.025553018, 0.062690206, -0.0070067993, 0.000691803, -0.00052556745, -0.01933806, -0.020085806, 0.06936636, -0.03638073, -0.00936778, 0.02458835, 0.014329653, -0.05274379, -0.055678457, -0.0032579543, -0.010403883, -0.044813562, 0.02539099, 0.09264206, 0.009714455, 0.055752914, 0.0089757005, 0.012024243, -0.07808683, -0.0066015157, 0.015471048, 0.0068328925, 0.013270242, -0.050722472, 0.017950205, 0.037967537, -0.027568128, -0.057813086, 0.006504702, -0.027223188, 0.0059067756, -0.07425201, -0.034379315, -0.05181685, 0.03222627, 0.020440236, 0.022518735, -0.061903466, -0.0010205633, -0.0611738, 0.0022467442, 0.025693066, 0.004028285, 0.0042413156, -0.04803259, -0.028193947, 0.07395199, 0.021927567, 0.027141066, -0.091286436, 0.057596687, 0.005307344, 0.005592055, 0.018275397, 0.030898478, 0.036655083, -0.0120382365, 0.017209569, 0.06911622, 0.05553148, -0.0046446263, -0.014455305, 0.034845803, -0.015248393, 0.020545576, 0.039110836, 0.020746164, -0.0030517578, -0.017259361, -0.06540913, -0.05667116, -0.006423609, 0.0068093347, 0.018778272, 0.012837712, 0.01040808, -0.034789782, -0.019710654, -0.02282697, -0.02852573, -0.019006854, 0.03642064, 0.042363018, 0.03364499, 0.097553305, -0.07127206, 0.016185967, -0.0172615, -0.020326797, 0.013663175, 0.055734273, 0.010388759, 0.024624964, 0.0075931577, -0.016545719, -0.059797905, 0.04626652, 0.07114267, 0.006768077, 0.05273262, 0.059758928, 0.010986069, 0.0117097115, -0.06527491, -0.031392433, 0.019822376, -0.020613933, 0.03489633, 0.056136325, -0.019775419, 0.04426993, 0.027707262, 0.005842635, 0.04269424, -0.0015013419, -0.055291325, -0.019189902, -0.03507854, -0.019611795, -0.017019898, -0.059200216, 0.00026440804, 0.032244015, 0.004058929, -0.017864004, -0.011832533, 0.046042304, 0.0036655997, 0.016047185, -0.06086678, -0.048243757, -0.047438543, -0.053340077, -0.051155467, 0.011670951, -0.014365782, 0.04522428, -0.07268421, -0.028861172, -0.018502781, -0.0075207166, 0.052638113, -0.045567546, 0.027011024, -0.048791606, 0.014096469, 0.011564009, -0.00039569824, -0.011059639, 0.018234253, 0.048299953, -0.061781947, 0.01368996, -0.0046395566, -0.0053576794, -0.03409315, 0.07258985, 0.055619646, 0.0008659101, -0.07556885, 0.016935488, 0.011814801, 0.032202654, 0.014453874, -0.025450088, 0.013327966, 0.02196367, 0.031095963, 0.0023867758, 0.03942261, 0.009574002, 0.036665753, -0.018872907, 0.009128845, -0.008356183, -0.01650004, 0.06746536, 0.0516402, -0.07049017, 0.012677528, -0.02545781, -0.013082318, -0.14630105, -0.0392123, -0.040604174, -0.039292607, 0.0598706, 0.049370106, -0.016763693, 0.00081639295, -0.008246243, 0.0015415683, -0.008924403, -0.005482729, -0.011936016, -0.02441406, 0.044450566, -0.029127099, 0.0077869897, 0.02652006, 0.034487344, 0.010794668, -0.06098627, 0.03597297, 0.06512905, 0.013695615, -0.0093732495, 0.04485033, 0.029542938, 0.049709193, 0.004757999, -0.03863925, 0.03693434, 0.017657118, 0.0010822435, 0.025366224, 0.050264716, 0.038955122, -0.01122073, -0.019341307, -0.013608196, 0.031090291, 0.021540565, 0.0019035109, -0.02988357, -0.03739737, 0.0016883804, 0.0020951459, -0.0042446125, 0.03198585, 0.0050761183, 0.0010843802, 0.07086722, 0.05763813, 0.0004606171, -0.023180602, -0.03059051, 0.024183717, -0.03516723, 0.015561145, 0.024140682, -0.051971607, -0.010409331, 0.026855193, -0.0014952031, -0.009704243, 0.0016526373, -0.0223121, 0.006817972, 0.031434763, 0.008228205, 0.04239655, -0.056049287, -0.03651143, -0.0026871576, 0.00544583, -0.061457057, 0.03585847, -0.009109323, -0.0016075566, -0.01222523, -0.014228621, 0.0048427195, 0.04833442, 0.017097464, 0.009019682, 0.01946492, -0.013893696, 0.015630191, -0.062219582, -0.0017818152, 0.003140138, 0.03616264, 0.029877085, 0.0027169518, 0.0060325176, 0.0057764295, -0.04515912, 0.0011451106, 0.03049894, -0.037139226, -0.0021711793, 0.0161327, -0.023355419, 0.04970959, 0.044492353, -0.03954175, 0.012796746, 0.025465537, 0.021856248, 0.020717144, -0.05331542, -0.023537165, 0.0070075057, -0.010134012, -0.02513759, 0.035921473, 0.02288278, 0.015702099, 0.03899235, -0.028565343, 0.066351056, -0.008064467, -0.040583897, -0.042409696, 0.03960509, 0.017359931, 0.0135341715, 0.07151569, -0.016380096, -0.034588072, 0.0150548, 0.015952488, 0.024285816, -0.021255845, -0.041096937, -0.029183079, 0.017780444, -0.013999892, -0.06318222, -0.10182706, 0.039394315, -0.006351498, 0.04108899, 0.017447669, 0.009800139, 0.005937609, -0.0092810355, 0.071662664, 0.010577258, -0.030346254, -0.03551225, -0.037402596, 0.040064365, 0.02205944, 0.024905968, 0.030595185, 0.023699258, 0.022822889, 0.012947252, -0.02793606, 0.0207895, 0.04008461, 0.02978025, -0.036646917, -0.024584755, -0.016808325, 0.042821437, -0.033198964, -0.00411265, 0.007580849, 0.0011864273, 0.0008841309, 0.006752639, -0.009424897, -0.0053665955, 0.0103945965, 0.005341981, -0.048941243, -0.051079914, -0.009375246, -0.002238341, 0.0405561, 0.008291801, -0.035178617, 0.03956019, 0.07376835, 0.026618699, -0.002449441, -0.040297944, -0.0030387766, 0.029609777, -0.044729996, -0.03385843, 0.062338095, -0.004558699, 0.05513567, -0.0649807, 0.0067906845, -0.005188387, -0.031807356, 0.057524506, 0.020852067, 0.019127272, -0.0420947, 0.035480876, 0.0023082164, -0.046390496, 0.033691537, 0.017836748, 0.0088993, -0.0063795047, 0.008029478, 0.027234085, 0.045060437, 0.0023444423, -0.025780564, 0.035029016, 0.0036406703, 0.044169806, -0.015822643, 0.06728525, 0.005312186, 0.013832553, 0.05054729, 0.007802017, 0.023085138, -0.016718378, 0.004530622, 0.0009182255, 0.059163716, 0.008015142, 0.008692785, 0.052764732, 0.022173338, 0.023329023, -0.012152069, -0.011845304, -0.018281683, 0.01780994, 0.02192091, -0.0026224828, 0.0048316107, -0.035379123, 0.020465158, -0.024142964, -0.027204897, -0.0134032145, -0.010601857, 0.0024389548, 0.0083944155, -0.016370853, -0.04810541, 0.06651236, -0.004410922, -0.00727334, 0.026743747, -0.028235592, -0.0103608975, -0.031677913, -0.0039295354, -0.056688875, -0.031244678, 0.009486844, 0.07303136, 0.03224514, -0.0051233554, -0.029156268, 0.04583884, -0.0060774866, -0.012122193, -0.018246695, -0.023877783, -0.011257579, 0.025628297, -0.011724044, -0.019872194, -0.0022794283, 0.033781923, -0.055419996, -0.004686288, -0.051390175, -0.05514203, 0.0020654993, -0.01217043, -0.012498238, -0.0017779581, -0.015208678, 0.03842581, -0.0075145457, -0.030823544, -0.062956706, -0.009604553, -0.08387967, 0.02631506, 0.0054309606, -0.011249839, 0.030978361, -0.012801239, -0.0674276, -0.07652962, 0.01635141, -0.054936934, -0.0065506147, 0.020633986, 0.06396879, 0.020066887, 0.00923081, -0.011080762, 0.013325836, -0.044306226, 0.019596394, 0.024691878, -0.0005675894, 0.024819382, 0.02323414, -0.017249446, -0.017272504, 0.014935341, -0.0120628895, 0.030969083, 0.015782041, 0.0058944714, 0.022817759, -0.018312776, -0.08700606, 0.030389149, 0.027836867, 0.03753869, 0.0039945473, -0.050057244, 0.018288145, -0.037985034, 0.004735824, 0.0029878789, 0.07206621, 0.04221737, 0.04836422, -0.08916485, 0.037672654, -0.010635665, 0.0102039855, -0.024891263, 0.014918422, 0.008320828, -0.012723802, -0.03844502, -0.0201663, -0.044065587, 0.009109898, -0.03620427, -0.046157524, -0.058563385, 0.031978723, 0.006628824, -0.008770901, -0.012232027, 0.026380554, -0.00036633032, 0.0712816, 0.030094497, 0.015175593, 0.007693683, 0.007065695, -0.033286203, 0.025090383, 0.04667492, -0.0069081658, 0.029335177, -0.011375869, -0.022167971, 0.007171852, 0.045442093, -0.03817157, 0.0086717885, 0.02320434, 0.026116941, -0.0195786, -0.019469243, 0.051524967, -0.011410115, 0.08100567, -0.006825463, -0.012081042, -0.037684403, -0.011511576, -0.032335237, 0.005184355, -0.010046622, -0.036731344, -0.0144374715, -0.014974302, 0.071712986, 0.034433547, -0.060434893, 0.014657658, -0.055870567, 0.022220073, 0.027688447, -0.020400194, 0.006114248, 0.008076617, 0.022830974, 0.05328088, -0.02741203, 0.035546083, 0.0638921, -0.029805494, 0.039915245, 0.029773043, 0.0150843905, 0.006098403, -0.049578317, 0.001218876, -0.044192206, 0.028513316, 0.024759967, 0.00024077356, -0.02385053, 0.09142657, -0.031564012, -0.04670481, -0.042639147, -0.022292733, -0.05566782, -0.014938068, 0.06676517, -0.014975691, -0.06129532, -0.047537554, 0.05593863, 0.00646364, 0.028571451, 0.014451909, 0.0069648293, -0.038722944, -0.051209472, 0.036458917, 0.017563105, -0.024196697, 0.018556457, 0.030968571, 0.0016314663, -0.019074092, 0.059380267, -0.03958717, -0.043773644, 0.053198516, -0.019112894, 0.06829867, -0.028914733, -0.052076958, 0.036427084, 0.00042388993], "created_at": 1747110788398, "nchars": 3039}, "4": {"title": "Microservices", "path": "Microservices.md", "embeddingModel": "text-embedding-004", "ctime": 1728769728000, "mtime": 1747076112902, "tags": [], "extension": "md", "metadata": {"relates": ["[[Backend - Back-end]]", "[[Cloud - SaaS]]", "[[Machine learning - Deep Learning - AI - ML - DL]]"], "created": "2024/10/13 04:48:48", "modified": "2025/05/13 01:55:12"}, "id": "c6cfe5ab819dc0a5d9078b44476478af", "content": "\n\nNOTE TITLE: [[Microservices]]\n\nMETADATA:{\"relates\":[\"[[Backend - Back-end]]\",\"[[Cloud - SaaS]]\",\"[[Machine learning - Deep Learning - AI - ML - DL]]\"],\"created\":\"2024/10/13 04:48:48\",\"modified\":\"2025/05/13 01:55:12\"}\n\nNOTE BLOCK CONTENT:\n\n(cont'd) ## 2.13. Development Environments\n\n**1. Daytona: Open Source Dev Environment Manager**\n\n* **Định nghĩa:** Daytona là một trình quản lý môi trường phát triển (Dev Environment Manager) mã nguồn mở. <PERSON><PERSON> gi<PERSON><PERSON> bạn tạo, quản lý và chia sẻ các môi trường phát triển một cách nhất quán và dễ dàng.\n* **Lợi ích chính:**\n    * **Nhất quán:** <PERSON><PERSON><PERSON> bảo mọi thành viên trong nhóm sử dụng cùng một môi trường phát triển, lo<PERSON><PERSON> bỏ các vấn đề \"works on my machine\".\n    * **T<PERSON><PERSON> sử dụng:** <PERSON><PERSON> dàng tạo các môi trường phát triển mới dựa trên các template đã có.\n    * **Tự động hóa:** Tự động hóa quá trình thiết lập môi trường phát triển, tiết kiệm thời gian và công sức.\n    * **Quản lý tài nguyên:** Quản lý tài nguyên (CPU, RAM, disk space) được sử dụng bởi các môi trường phát triển.\n    * **Chia sẻ:** Dễ dàng chia sẻ môi trường phát triển với đồng nghiệp hoặc cộng đồng.\n* **Cách hoạt động:** Daytona thường sử dụng Docker containers để đóng gói môi trường phát triển. Nó cung cấp một giao diện để quản lý các container này.\n* **Ứng dụng:**\n    * Phát triển phần mềm theo nhóm.\n    * Giảng dạy lập trình.\n    * Thử nghiệm các công nghệ mới.\n* **Ưu điểm:** Mã nguồn mở, linh hoạt, dễ tùy chỉnh.\n* **Nhược điểm:** Cần kiến thức về Docker.\n\n**2. Devcontainers (Visual Studio Code)**\n\n* **Định nghĩa:** Devcontainers là một tính năng của Visual Studio Code (VS Code) cho phép bạn sử dụng một container Docker làm môi trường phát triển đầy đủ.\n* **Lợi ích chính:**\n    * **Môi trường phát triển nhất quán:** Đảm bảo mọi người trong nhóm sử dụng cùng một môi trường phát triển, bao gồm các công cụ, thư viện và cấu hình.\n    * **Dễ dàng thiết lập:** VS Code tự động thiết lập môi trường phát triển bên trong container.\n    * **Cách ly:** Cách ly môi trường phát triển khỏi hệ thống máy tính của bạn, tránh xung đột phần mềm.\n    * **Tái tạo:** Dễ dàng tái tạo môi trường phát triển từ một file `devcontainer.json`.\n* **Cách hoạt động:**\n    * Tạo một file `devcontainer.json` trong thư mục gốc của dự án. File này mô tả container Docker cần thiết cho môi trường phát triển.\n    * Khi bạn mở dự án trong VS Code, VS Code sẽ tự động xây dựng và khởi động container.\n    * VS Code sẽ kết nối với container và cho phép bạn làm việc như thể bạn đang làm việc trên máy tính của mình.\n* **Ứng dụng:**\n    * Phát triển phần mềm theo nhóm.\n    * Phát triển các ứng dụng phức tạp với nhiều dependencies.\n    * Thử nghiệm các công nghệ mới.\n* **Ưu điểm:** Tích hợp chặt chẽ với VS Code, dễ sử dụng, cộng đồng lớn.\n* **Nhược điểm:** Yêu cầu VS Code, phụ thuộc vào Docker.\n\n**3. Lapdev: Self-hosted Dev Environment**\n\n* **Định nghĩa:** Lapdev là một giải pháp tự lưu trữ (self-hosted) để tạo và quản lý môi trường phát triển. Nó được phát triển bởi nhóm Lapce (một trình soạn thảo mã nguồn mở).\n* **Lợi ích chính:**\n    * **Tự lưu trữ:** Bạn có toàn quyền kiểm soát dữ liệu và cơ sở hạ tầng.\n    * **Mã nguồn mở:** Bạn có thể tùy chỉnh và mở rộng Lapdev theo nhu cầu của mình.\n    * **Môi trường phát triển nhất quán:** Đảm bảo mọi người trong nhóm sử dụng cùng một môi trường phát triển.\n    * **Cách ly:** Cách ly môi trường phát triển khỏi hệ thống máy tính của bạn.\n* **Cách hoạt động:**\n    * Lapdev sử dụng Docker containers để đóng gói môi trường phát triển.\n    * Bạn có thể sử dụng giao diện web của Lapdev để tạo, quản lý và truy cập các môi trường phát triển.\n* **Ứng dụng:**\n    * Phát triển phần mềm theo nhóm.\n    * Phát triển các ứng dụng phức tạp với nhiều dependencies.\n    * Các tổ chức muốn kiểm soát hoàn toàn cơ sở hạ tầng phát triển của mình.\n* **Ưu điểm:** Tự lưu trữ, mã nguồn mở, linh hoạt.\n* **Nhược điểm:** Cần kiến thức về Docker và quản lý máy chủ, có thể phức tạp hơn so với các giải pháp được quản lý.\n\n**Tóm tắt so sánh:**\n\n| Tính năng   | Daytona                                   | Devcontainers (VS Code)                 | Lapdev                                     |\n| ----------- | ----------------------------------------- | --------------------------------------- | ------------------------------------------ |\n| Loại        | Trình quản lý môi trường phát triển       | Tính năng của VS Code                   | Giải pháp tự lưu trữ môi trường phát triển |\n| Mã nguồn mở | Có                                        | Có (một phần)                           | Có                                         |\n| Tự lưu trữ  | Có (có thể triển khai trên máy chủ riêng) | Không (phụ thuộc vào VS Code và Docker) | Có                                         |\n| Dễ sử dụng  | Trung bình                                | Dễ                                      | Khó hơn (cần kiến thức về máy chủ)         |\n| Tích hợp    | Độc lập                                   | Tích hợp chặt chẽ với VS Code           | Độc lập                                    |\n| Phụ thuộc   | Docker                                    | VS Code, Docker                         | Docker                                     |\n\n## 2.14. Ecommerce\n\n* [Digitalhippo](https://github.com/joschan21/digitalhippo)\n* Medusa: [https://github.com/medusajs/medusa](https://github.com/medusajs/medusa)\n    * Headless CMS\n    * Ecommerce logic\n    * Domain driven", "embedding": [0.0038319135, 0.023478962, -0.049828917, -0.030051544, 0.05247164, -0.009781994, 0.044167463, -0.008660436, -0.0137686515, -0.016352579, -0.0020776833, 0.0815117, 0.063997954, -0.04177353, 0.084779486, -0.020523783, 0.012531402, 0.06852468, -0.04642664, 0.016811585, -0.0367488, 0.0048739067, 0.0011713798, 0.0047261952, -0.047033828, -0.050530713, 0.02301409, -0.041639004, 0.0061659394, -0.010662815, 0.021732073, 0.016252711, 0.025717698, -0.0068219276, -0.009577451, 0.040275697, 0.014701231, 0.019881183, 0.03804511, -0.077963606, -0.044327926, -0.048934966, -0.01509276, 0.08852965, 0.022613006, -0.029502703, 0.025137233, 0.016333256, -0.002254924, -0.014203503, 0.0504322, -0.034880094, 0.017584208, 0.046481777, -0.0005232227, -0.036729533, -0.059788734, -0.04441308, 0.059064534, 0.037478875, -0.03923989, -0.045182448, -0.010246543, -0.022684652, -0.053875115, -0.0061989157, -0.02758362, -0.054532826, -0.023860622, -0.009512949, -0.04619213, 0.016795216, -0.0487, 0.06981322, 0.0006803905, 0.040246177, 0.005124569, -0.03934862, 0.014847923, 0.020455783, -0.017665869, 0.022343561, 0.09508821, 0.020489654, 0.046922076, -0.0032940805, -0.010746801, -0.054113794, -0.050198473, -0.03088824, 0.06180605, 0.02905678, -0.0014711404, -0.019694889, 0.04291969, -0.031204302, -0.0992132, -0.1076599, 0.013789834, 0.04716954, -0.022865232, -0.0074941376, -0.0131334625, -0.08882969, 0.08410654, -0.022308338, -0.036148727, -0.036939897, -0.10340476, 0.0773053, -0.025126263, 0.017761022, 0.05340092, 0.009564415, -0.022444315, 0.021703152, -0.022744114, 0.026834816, -0.06019576, 0.017301157, 0.014179954, 0.028264904, 0.009004954, 0.06465049, 0.03406887, 0.0127743855, 0.022060493, -0.037597403, -0.013447185, -0.019371891, 0.045001853, -0.0149024045, -0.038962677, 0.020144962, -0.018983237, 0.03019381, 0.025684144, -0.03899902, 0.037590113, 0.062998004, -0.013289517, -0.056289997, -0.07083381, 0.01152383, -0.048083626, -0.053424016, 0.04044484, 0.054045692, -0.021242185, 0.03858404, 0.01427389, -0.01360125, -0.024393415, 0.0056890016, 0.004667259, -0.0054929405, 0.005682649, -0.03204645, 0.0481877, 0.05233799, 0.012660132, -0.028759036, 0.01527014, -0.0033858304, -0.016684422, -0.037990462, -0.028895298, -0.037314553, 0.043682545, -0.0086680725, -0.0021796736, -0.053090483, -0.04277186, -0.018585417, 0.0074979425, 0.024278961, -0.0027127715, -0.020937106, -0.077714786, -0.036907293, 0.030700227, 0.04242695, 0.034233127, -0.09097673, 0.06624474, -0.016490918, -0.029369853, 0.02316681, 0.053768996, 0.0658148, -0.010176238, 0.02124418, 0.041739736, 0.048134796, -0.033105206, -0.006345484, 0.07981387, -0.03555867, -0.008089057, -0.012753369, 0.04049838, -0.025921639, -0.028174926, -0.052523486, -0.024526991, 0.01809219, 0.01097799, -0.007551412, 0.007693288, 0.020355275, -0.063489854, -0.03606852, -0.011223746, -0.04116633, -0.034857877, 0.0021094782, 0.055358123, 0.013028579, 0.056665126, -0.081525, 0.0064392253, 0.006801066, -0.011408396, 0.0122110555, 0.060423028, 0.0013483536, 0.040415615, 0.027185662, -0.01724241, -0.025548449, -0.033558883, 0.054639645, 0.02867578, 0.06354223, 0.021504182, 0.033357196, 0.034477588, -0.092670225, -0.059919104, 0.021938955, -0.0025701523, 0.041149113, -0.008906533, 0.0211026, 0.010543958, 0.00085276645, 0.014098658, 0.044805955, 0.0046747457, -0.042965326, 0.003621182, -0.051784158, -0.0015939771, -0.030339293, -0.03378593, -0.00017313045, 0.044380184, -0.010433672, -0.0040352866, 0.006770136, 0.030734325, -0.03976635, -0.031179592, -0.029925484, -0.03543884, -0.0631042, -0.034218606, -0.05956504, 0.0432896, 0.009971132, 0.034554165, -0.08651656, -0.026869385, -0.020714398, -0.029258028, 0.03353263, -0.033595186, 0.032607377, -0.055909, 0.01183091, 0.030491192, -0.0039152657, 0.01173798, 0.013972297, 0.043840315, -0.059201993, -0.029456995, 0.04476189, -0.010902496, -0.062737115, 0.0365262, 0.015684368, -0.027171312, -0.0490019, -0.0029789116, 0.041217443, 0.030941261, 0.01889122, -0.014069141, -0.002954673, 0.0025131733, 0.025788087, -0.053936075, 0.050522637, -0.007813118, 0.017987996, 0.009085832, 0.016765604, -0.018678216, -0.04176703, 0.04378394, 0.062997006, -0.06499021, 0.008759054, -0.02747465, -0.018156068, -0.15371689, -0.045111585, -0.005504971, 0.0030541152, 0.074026756, 0.022174926, -0.05259261, -0.020460527, 0.014480993, -0.0032801395, 0.014790565, -0.011580674, -0.0012720875, -0.027529389, 0.046565305, -0.023920698, 0.0018317334, 0.0022739407, 0.038302697, 0.011186082, -0.0278394, 0.052122608, 0.08569522, 0.026086783, -0.0038838822, 0.039732, 0.031141838, 0.051855482, 0.005910546, -0.06144892, 0.0047833687, 0.011827764, 0.0016996675, 0.055566933, 0.06015257, 0.054474823, -0.0054256837, -0.0132323075, 0.010355387, 0.06328547, 0.008830327, 0.01550908, -0.022089291, -0.046236586, -0.008336123, -0.004622656, -0.005466135, 0.056910023, 0.021607265, -0.01758811, 0.058807094, 0.025445908, -0.025735825, -0.04050626, -0.046466578, 0.02257521, -0.037376177, 0.013944978, -0.0008406277, -0.045455966, -0.056365423, 0.004373394, 0.04340635, -0.026440302, 0.029504023, -0.0081819035, -0.0044170897, 0.05164704, 0.01585255, 0.0670407, -0.07161313, -0.03556143, 0.025347095, 0.03371057, -0.05768926, 0.041268878, 0.027561631, -0.031865332, -0.02354361, 0.00035477296, 0.01416845, 0.05491851, 0.019994687, 0.026602661, -0.0049171215, -0.039399166, 0.02421086, -0.02659619, 0.017091343, -0.007432075, 0.063473016, -0.00013584078, -0.009721949, -0.00052553765, -0.0068416093, -0.0061539593, 0.008606535, 0.027759131, -0.034304284, -0.0184535, 0.017375188, -0.02998736, 0.015349477, -0.008400824, -0.021815073, 0.047374763, 0.055754457, 0.019808589, 0.0044601895, -0.08087838, -0.0044289706, -0.018703477, -0.009819768, -0.020712757, 0.032804593, 0.060293775, -0.024095548, 0.030208182, -0.04856365, 0.025397185, 0.0006076768, -0.04453715, -0.0123236235, 0.033137538, 0.01068023, 0.023276772, 0.051807363, -0.0038734146, -0.01266876, 0.04025803, 0.0129554635, 0.059356693, -0.03869679, -0.020427354, -0.0163059, 0.0149330385, -0.0036783163, -0.034958467, -0.08500928, 0.041267104, 0.009783145, 0.04907895, 0.025215348, 0.021501066, -0.040025678, -0.0061107622, 0.04913799, -0.017208736, -0.019031955, -0.049978938, -0.059619352, 0.02824103, -0.0091772, 0.020113718, 0.04445261, 0.01528236, 0.0062979963, -0.0073034125, -0.0061964407, 0.027650345, 0.05343041, 0.015190494, -0.036428776, -0.0140073765, -0.034321, 0.045006357, -0.011409146, 0.01626564, 0.012503788, 0.0031164896, -0.020325825, 0.013997896, -0.022776388, -0.039719258, 0.0052967328, -0.012185636, -0.02383437, -0.029224819, 0.017187404, -0.03243563, 0.04458817, -0.012471027, -0.006593565, 0.011125573, 0.04366461, 0.007558588, 0.028951678, -0.02729764, -0.01449476, 0.02411814, -0.053277396, -0.018756421, 0.09067825, 0.00571283, 0.008950244, -0.074401274, -0.015601161, 0.008046322, -0.049585875, 0.03689747, 0.007822141, -0.041359853, -0.061665904, 0.04975178, 0.02166648, -0.04611431, 0.0364629, 0.005382846, 0.014945674, -0.012196945, -0.005177993, 0.015224508, 0.037131306, -0.02268213, -0.02704327, 0.047341894, -0.0012563103, 0.0018790663, -0.024042286, 0.05785229, 0.004655424, 0.009326701, 0.04332756, 0.010254244, -0.0042421995, 0.017571867, 0.0037591695, 0.0011180708, 0.061778445, 0.01676615, 0.03293006, 0.06303081, 0.016805302, 0.059691284, 0.016885728, 0.004992956, -0.035729464, 0.010331215, -0.001113968, -0.021784607, 0.018607248, 0.026945608, -0.026762994, 0.008603711, 0.018427674, -0.0015706639, -0.004255052, -0.025356347, -0.002774165, -0.012185611, -0.051986065, 0.052255034, -0.01973812, -0.04217667, 0.0010317276, -0.022924077, -0.0041525303, -0.003682286, -0.0056435415, -0.049944356, -0.034893256, 0.0034438365, 0.07910564, 0.035173368, -0.015640406, 0.024852859, 0.06027958, 0.0063065253, -0.018892635, -0.0024108125, -0.006179117, -0.022385651, 0.025798189, -0.013362206, -0.013974951, -0.0230225, -0.0013429641, -0.050994594, 0.022318913, -0.025160134, -0.039647553, 0.042128146, -0.019837383, -0.028240405, -0.008481644, -0.01178374, 0.03355969, -0.013473285, -0.052337073, -0.072383836, 0.016911207, -0.07497084, 0.0028322693, 0.011681308, -0.022258006, 0.0405512, 0.013403308, -0.03744819, -0.098519064, 0.0140126655, -0.04812159, -0.00012224904, 0.025164293, 0.06740358, 0.028710512, 0.016709417, -0.0048389877, 0.027942773, -0.04557839, -0.0012881656, -0.011037824, -0.010737512, 0.082720004, 0.035735417, -0.016671948, -0.0017612411, 0.008382454, -0.012871259, 0.0059732343, -0.011056529, 0.0010341712, 0.04655574, -0.0013412236, -0.064783454, 0.0001509958, 0.042986576, 0.054770615, -0.021288358, -0.035587404, 0.03083893, -0.042346768, -0.014492464, -0.002325588, 0.035581302, 0.026718779, 0.049670223, -0.050802905, 0.078909196, -0.020009782, -0.00023554818, -0.04409912, -0.0033276863, 0.0028807698, -0.039249696, -0.012955523, -0.02780424, -0.0336056, -0.013239712, -0.074821495, -0.0124309, -0.07884947, 0.014594418, 0.008774677, -0.007151301, -0.004121327, 0.057643645, -0.005354705, 0.037009723, 0.022901159, 0.010677117, -0.024071576, -0.0011420854, -0.023414295, 0.009358457, 0.008017638, -0.00553906, 0.016905278, -0.03875464, 0.021689175, 0.02689224, 0.017560944, -0.028743288, 0.03379147, -0.0026118655, -0.010727852, -0.0409602, -0.012351152, 0.03129627, -0.027801026, 0.04510178, -0.0022804844, -0.029361824, -0.048510317, -0.0009409887, -0.028270867, 0.0044412715, -0.033257235, -0.037172064, 0.0141300885, -0.015227325, 0.073258296, 0.0023549504, -0.038766064, 0.02189033, -0.04037535, 0.017803261, 0.035358723, -0.0068674134, 0.006119184, 0.014250581, 0.030520367, 0.060610857, -0.023161653, 0.023405982, 0.02500685, -0.02868668, 0.065338194, 0.052310932, 0.03344808, 0.028538365, -0.0128164105, 0.023742393, -0.04787087, 0.039491452, 0.010781487, -0.020046229, -0.045109298, 0.060908496, -0.031639002, -0.048233714, -0.027106974, -0.005035716, -0.028888576, -0.008629874, 0.078549996, 0.012294381, -0.062488925, -0.05926944, 0.022225242, 0.02607137, 0.033606898, 0.022710646, -0.022089122, -0.07430361, -0.07956216, 0.040791996, -0.008079083, -0.0034921207, 0.03540438, 0.025933802, 0.005625786, 0.002819383, 0.035100527, -0.007287742, -0.022146842, 0.027812028, -0.0296495, 0.017031733, -0.015786884, -0.043929026, 0.041871276, 0.008955246], "created_at": 1747110788402, "nchars": 5373}, "5": {"title": "Algorithms & Data Structures CheatSheet", "path": "Algorithms & Data Structures CheatSheet.md", "embeddingModel": "text-embedding-004", "ctime": 1733244767993, "mtime": 1728671255000, "tags": [], "extension": "md", "metadata": {"created": "2024/12/03 23:52:47", "modified": "2024/10/12 01:27:35"}, "id": "c82b615a7d0f0ace75840e24251e9e3d", "content": "\n\nNOTE TITLE: [[Algorithms & Data Structures CheatSheet]]\n\nMETADATA:{\"created\":\"2024/12/03 23:52:47\",\"modified\":\"2024/10/12 01:27:35\"}\n\nNOTE BLOCK CONTENT:\n\n# 1. Arrays\n\nIn javascript you’d think this `[]` is an array. Well you’re wrong, it’s actually an Array List.\n- An Array is just a contiguous memory space (contiguous = non breaking)\n- It’s just a chunk of memory that is understood as an array\n\nHere is how you could build an array in node:\n\n```ts\nconst a = new ArrayBuffer(6)\n// ArrayBuffer { [Uint8Contents]: <00 00 00 00 00 00>, byteLength: 6 }\nconst a8 = new Uint8Array(a) // numbers between 0 and 255. Creates a view into the array buffer, does not create anything new\na8[0] = 45\na8[2] = 45\n// ArrayBuffer { [Uint8Contents]: <2d 00 2d 00 00 00>, byteLength: 6 }\nconst a16 = new Uint16Array(a) // numbers between 0 and 255. Creates a view into the array buffer,\na16[2] = 0x4545\n// ArrayBuffer { [Uint8Contents]: <2d 00 2d 00 45 45>, byteLength: 6 }\n// because I look at it in 16bit unity, I can only walk in 16 units at a time, this is why the 45 45 was placed in the last bits\n\n```\n\n## 1.1. Arrays vs linked lists\n\n- I’s simple\n- You can’t add a value because there is only override so you’ll have to shift over your values to write something in between\n- O(1) everything for Arrays\n- Memory has to be allocated in advance for arrays. Linked lists have better memory usage but more computational\n- In a list you have to always traverse the list (linear search)\n- Linked Lists are usually the better choice for queues, Array Lists for stacks, or Array Buffer for both\n## 1.2. Array lists\n\nArray access with the ability to grow:\n\n```\n[1, , ] Length: 1, Capacity 3 => .push(2) => [1,2, ] Length: 2, Capacity 3\nGetting: if index is >= length throw/undefined\nPush: is length in capacity? Great just add and increment length. No? Create new bigger array and move items over.\nPop: length -1 get the value, decrement the length (might need to zero out the data).\nEnqueue: Grow capacity, move over all values one to the right\ndequeue: move over all values one to the left\n\n```\n\n- Get O(1)\n- Really good for push and pop O(1)\n- Really bad for enqueue and dequeue O(n)\n- Getting something is great just add/remove on the beginning is not so great (but it is in a linked list)\n\n## 1.3. ArrayBuffer / RingBuffer\n\n- Head & Tail are not in the beginning nor end of the array, the items are just within the head and the tail\n\n```\n[ , ,1,2, , ]\nDequeue: +1 to the head\nEnqueue: -1 to the head\nPush: +1 to the tail\nPop: -1 to the tail\n```\n\n- Push, pop, enqueue, dequeue are all O(1)\n- If you max out the head or the tail you can, i.e. move the tail over to the front before the head, as long as there is space. Which is why it’s called \"RingBuffer\" (this.tail % length gives you then the position of the tail). I.e. if array length is 10 and you move the tail to position 12, you’ll find it at 12 % 10 = 2 (remaining)\n- If the tail is at the head you need to resize: create a new buffer with more capacity and write the old to the new one\n\n# 2. Searching\n\n## 2.1. Linear Search\n\nSearching a list linearly in JavaScript:\n\n```ts\nexport default function linear_search(\n    haystack: number[],\n    needle: number,\n): boolean {\n    for (let i = 0, il = haystack.length; i < il; i++)\n        if (haystack[i] === needle) return true;\n\n    return false;\n}\n```\n- Time Complexity is `O(N)`\n\n## 2.2. Binary Search\n\nAs a recursive method\n\n```ts\nexport default function bs_list(haystack: number[], needle: number): boolean {\n  const midpoint = Math.floor(haystack.length / 2)\n  const value = haystack[midpoint]\n  if (value === needle) return true\n  if (haystack.length === 1) return false\n  if (value < needle) return bs_list(haystack.slice(midpoint), needle)\n  if (value > needle) return bs_list(haystack.slice(0, midpoint), needle)\n\n  return false\n}\n\n```\n\nwith a loop\n\n```ts\nexport default function bs_list(haystack: number[], needle: number): boolean {\n  let low = 0;\n  let high = haystack.length\n\n  do {\n    const midpoint = Math.floor(low + (high - low) / 2)\n    const value = haystack[midpoint]\n    if (value === needle) return true\n    else if (value > needle) high = midpoint\n    else low = midpoint + 1\n  } while (low < high)\n\n  return false\n}\n```\n\n- Time Complexity is `log(N)`\n\n## 2.3. Crystal Balls Exercise\n\nGiven 2 crystal balls that will break when dropped from high enough. Determine the exact spot where they will break in the best way.\n\n- Going one by one is O(n) : [1, 2, BREAK*, …]\n- Going with a binary search is O(n) : B1: […, BREAK_1, …] | B2: [1, 2, BREAK_2*, …, BREAK_1, …] we still need to loop for the second one since we only have 2 balls\n- So the most optimized way would be to jump in X steps\n\n```ts\nexport default function two_crystal_balls(breaks: boolean[]): number {\n\n  const jumps = Math.sqrt(breaks.length)\n  let ball1 = 0\n\n  for (; ball1 < breaks.length; ball1 += jumps)\n    if(breaks[ball1]) break\n\n  for (let ball2 = ball1 - jumps; ball2 < ball1; ball2++)\n    if(breaks[ball2]) return ball2\n\n  return -1\n\n}\n```\n\n- Time Complexity is `O(√N)`\n\n# 3. Sorting\n\n## 3.1. Bubble sort\n\n- It just goes to the next element in the list and asks \"hey are you smaller than me?\" if yes, switch positions.\n- A singular iteration will always put the largest item at the last spot. So next bubble sort does not need to include the last position of the biggest element.\n- The runtime is `O(n^2)`\n\n```ts\nexport default function bubble_sort(arr: number[]): void {\n  for (let searched = 1; searched < arr.length; searched++)\n    for (let i = 0, il = arr.length - searched; i < il; i++)\n      if (arr[i] > arr[i + 1]) {\n        const temp = arr[i]\n        arr[i] = arr[i + 1]\n        arr[i + 1] = temp\n      }\n}\n```\n\n## 3.2. Divide and Conquer\n\n- Split the input into sub-sets, go over the sub-sets to solve it faster.\n- An array of 1 element is always sorted", "embedding": [0.02471726, 0.017960245, -0.057811413, -0.008344319, 0.0652681, 0.044202916, 0.068265714, -0.002211585, -0.0029323916, -0.036846813, -0.043276444, 0.046170484, 0.054187793, 0.016712543, -0.0071506263, -0.011025371, 0.049661174, 0.008799555, -0.059412733, -0.012659178, -0.02021873, -0.023386318, -0.0069407076, -0.0032157756, -0.02371448, -0.029893534, 0.02629919, -0.013529514, -0.0002474785, 0.0005500059, 0.050650585, 0.040015902, 0.01954389, -0.019407762, 0.06593475, 0.061175134, -0.024841787, 0.005038085, -0.004576944, -0.018164594, -0.048031636, 0.031388752, 0.007759636, 0.039298266, 0.059805706, -0.023507303, 0.02131993, 0.02404674, -0.056961343, 0.026914148, 0.010589568, -0.03673425, -0.009147131, 0.016647626, -0.0013127557, -0.055301487, -0.017316435, -0.010593154, -0.0005893217, -0.0020970956, -0.02881203, -0.04512123, -0.03784857, -0.036495514, -0.046092536, 0.002250962, -0.03944193, -0.0130388085, -0.023249941, 0.03784799, -0.04669261, 0.053772956, -0.06972783, -0.006066082, 0.0046850187, 0.018346619, 0.023694592, 0.03318814, -0.029749058, 0.024396984, -0.03896473, -0.017970316, 0.06897328, 0.0025603857, 0.025070673, 0.029285016, -0.021480052, -0.036956243, -0.017843356, 0.00018370413, 0.07727158, 0.051879227, 0.004940743, 0.016728867, 0.08107971, -0.06893001, -0.08932331, -0.08709605, 0.058269463, 0.04346638, -0.023783488, 0.01643547, 0.0074219904, -0.0638887, 0.06429447, 0.028195277, 0.0021789586, -0.04003654, -0.05272697, 0.04504178, -0.03326251, -0.023230866, 0.030666377, -0.038743615, -0.013337512, 0.0019495228, 0.003541565, 0.04306666, -0.038673636, -0.009714946, 0.050814617, 0.057100132, -0.0064779934, 0.07506977, 0.043269202, 0.0017482626, 0.031866904, 0.042140726, -0.066999584, -0.012110965, 0.07181579, -0.0405889, -0.019204607, 0.036390595, -0.020610992, -0.07259082, 0.038691662, -0.0065030996, 0.0103131365, 0.033977512, -0.00013748706, -0.066388495, -0.026258565, 0.024273364, 0.022862026, -0.03789245, 0.038865283, 0.027986048, -0.041128956, 0.011106571, -0.031083249, 0.02655849, 0.024950778, -0.0034909782, -0.007844221, 0.0040645306, -0.0018771621, -0.04167178, 0.10324658, 0.017497515, 0.03838089, -0.025512239, -0.003212873, 0.00096379337, -0.051901817, -0.058280945, -0.045499288, 0.003400344, 0.024528518, 0.02327916, 0.015117947, -0.01929824, -0.0726356, -0.05527356, 0.00417175, -0.0005854147, 0.014963829, -0.063100785, -0.04476107, -0.026291821, 0.06315599, 0.03869517, 0.06592476, -0.076902695, 0.019906906, 0.019124646, -0.002391273, 0.06611231, 0.056879714, 0.103413, -0.015332679, -0.01622955, -0.0049335454, 0.04330592, 0.015189476, 0.04311812, 0.04762216, -0.046371177, 0.031511076, 0.010870021, 0.04548158, -0.02607359, 0.017135402, -0.033750102, -0.031375006, 0.0030200814, -0.013202208, 0.016654491, 0.045055375, 0.040771812, 0.019228002, -0.019325467, -0.0037029292, -0.010836793, 0.00039503572, -0.06355621, 0.04106357, 0.018490363, 0.064727984, -0.014713506, 0.029775228, 0.009408353, -0.010908878, 0.054298475, 0.016183935, -0.006316476, 0.013908796, -0.019373853, 0.0030575686, -0.03541836, 0.0031419643, -0.0071305945, 0.023028757, -0.00472952, 0.001665979, 0.012160597, 0.008539148, -0.045971896, -0.07615889, 0.013579667, -0.035876863, 0.0046045124, 0.056498267, -0.049346376, 0.04305932, -0.0014178138, -0.03568538, 0.020197283, -0.03284947, -0.051985975, 0.014582124, -0.034247782, -0.010713529, 0.04348175, -0.03629632, 0.013265971, 0.019564688, -0.05534237, 0.03130771, -0.018512612, 0.08593935, -0.02530882, -0.036842726, -0.049889002, -0.022433175, -0.054421447, -0.04025774, -0.024342488, 0.018626396, -0.03286049, 0.04451038, -0.0136814555, -0.060259197, -0.012205519, -0.011749578, 0.014782496, -0.04929115, -0.012756171, -0.007083498, -0.009665187, 0.018165799, 0.006279024, 0.03712784, -0.025856914, 0.01617076, -0.009913446, -0.038640607, 0.010077546, -0.011781994, -0.035986, 0.053667124, 0.060294688, -0.0066360347, -0.022924956, 0.021394327, 0.048415158, 0.06260163, -0.018782109, -0.007927161, -0.030706163, 0.07377033, 0.03797776, -0.024762303, 0.07782028, 0.032634135, 0.0032791663, 0.0011688339, -0.018419946, 0.0009598378, 0.014449035, 0.020204652, -0.011146593, -0.03256976, -0.047782104, -0.015859872, -0.03754288, -0.14912024, -0.049470313, -0.044309117, 0.013543727, -0.015964227, 0.046280727, -0.015128191, -0.056909796, 0.0010405754, 0.013281852, 0.009154597, -0.004410006, 0.022333147, -0.034008827, 0.029083533, -0.0039802575, -0.016524345, -0.035131365, 0.040009666, 0.0563736, -0.040828466, 0.039178178, 0.04762083, -0.0038139357, -0.045897808, 0.065353885, 0.044115268, 0.058625758, 0.014329018, -0.015125552, 0.0013122873, 0.031461827, -0.028564796, 0.013984825, 0.046868317, 0.00031521174, 0.040465795, -0.0015822445, -0.01703333, 0.05976472, 0.04849922, -0.008550664, -0.018989028, 0.01588913, -0.023999348, -0.025151713, 0.010706759, 0.052479073, 0.013084811, 0.0020422349, 0.040791485, 0.031779394, -0.030555734, 0.012650093, -0.022642484, -0.0046146708, -0.05278316, 0.060067356, 0.022186851, -0.046152476, -0.020949692, -0.0090066595, 0.039329797, -0.046291497, -0.015734978, -0.026756858, -0.00049219036, 0.021328188, -0.06957097, 0.10303478, -0.068717755, 0.023497766, -0.03033587, -0.05609644, -0.05685501, 0.047300015, 0.010313494, 0.007121198, -0.075376056, 0.04321138, -0.002429991, 0.031650122, 0.04224267, 0.02685426, 0.02099747, -0.058547176, 0.026636843, -0.019729393, 0.020067446, -0.014779832, 0.06017068, -0.028788574, 0.023252523, -0.01846769, 0.010905683, -0.05405624, -0.016986325, -0.0007920711, -0.012635801, 0.0028345808, -0.019914487, -0.017440857, -0.024012836, 0.06739229, -0.00496973, 0.016190948, 0.0733752, -0.02783269, -0.022403646, -0.006664182, -0.013858199, 0.0001540919, 0.015353545, -0.020993708, -0.0017832792, 0.047252446, -0.009925701, 0.06739003, 0.010559537, 0.0031877225, -0.007726555, -0.06522883, -0.006613372, 0.0130526535, 0.0008672915, 0.0035662416, 0.04270963, 0.032004416, -0.03140398, 0.019048322, 0.004702717, -0.07212652, -0.046669316, -0.028456692, -0.01244578, 0.014164668, 0.018783709, -0.05109942, -0.11831029, 0.023283038, -0.0059644277, 0.00026920997, -0.0059904316, 0.008627245, 0.019983608, -0.026763482, 0.03954725, -0.0073212394, 0.03328914, -0.0066416413, -0.05583163, 0.047183458, 0.024647146, -0.007957441, 0.045811217, -0.014197862, -0.030040052, 0.025605766, 0.030075392, 0.019857751, 0.06429963, -0.026569016, -0.058556814, -0.015101031, -0.03055354, 0.025906613, -0.034990363, -0.03033945, 0.067796394, -0.03902362, -0.046491213, -0.007056352, -0.0040554632, -0.04094455, -0.04460436, 0.0054567093, -0.047412902, -0.04232944, -0.022289163, 0.013278782, 0.053813472, 0.054441024, 0.03467587, 0.07760242, 0.079127856, 0.005047545, 0.024842799, -0.016775265, -0.031914342, 0.0018611597, -0.053505067, -0.02377077, 0.036913108, 0.017869454, 0.0321279, -0.012305196, 0.038492553, -0.010915265, -0.005927619, 0.06321873, -0.018467214, -0.003100732, 0.004157267, 0.03522385, -0.0097761005, -0.050716665, 0.060007863, 0.024003657, -0.06866408, 0.0026500346, 0.01880238, 0.02547231, 0.05274518, -0.042850506, 0.0074072713, 0.069358826, 0.01954198, -0.001050085, 0.05116424, 0.045704063, 0.00074749475, -0.0058652232, 0.0061192256, 0.019726038, 0.015547563, 0.021823792, 0.02653294, 0.016247828, 0.050911013, 0.0057482356, 0.0039565857, 0.0645038, 0.019009324, 0.024477914, 0.0020608497, -0.019699842, 0.01599379, 0.06902723, -0.030134995, -0.061300464, -0.0003941435, 0.0076104924, 0.018416725, 0.038563926, 0.025678564, -0.019931253, -0.00031183017, 0.012915332, -0.021505808, 0.016366353, -0.022708448, 0.0031156621, -0.013386559, -0.031565327, -0.014565242, -0.008010166, -0.0033138995, -0.02728013, 0.009686604, 0.009852961, -0.004755418, 0.008088952, 0.03191812, -0.00896593, -0.008120476, 0.028067065, 0.062601164, 0.0015772003, -0.06281748, 0.0016987191, 0.04180233, 0.026666276, 0.021450227, -0.013918913, 0.044269715, 0.040889885, -0.012278757, -0.05510275, 0.028842773, -0.016298248, 0.013822288, 0.043631926, -0.008260183, 0.0049407403, -0.0014782547, 0.047584943, 0.0713815, -0.031387918, -0.013267408, -0.0682599, 0.011758644, -0.090500474, 0.046936207, -0.003008825, 0.001792777, 0.04705604, -0.008767041, 0.006307323, -0.09396745, 0.00085323385, -0.014679036, 0.018271845, 0.035872184, 0.023242861, -0.012143125, 0.010932903, -0.017203476, 0.0036681665, 0.00933065, -0.030254714, 0.010027414, -0.010641841, 0.024118207, 0.04076449, -0.029160228, -0.023046184, 0.035316214, -0.051147852, -0.0084036775, -0.03415265, 0.038822148, 0.013164176, 0.024165597, -0.06962646, 0.037140798, 0.0453309, 0.059816167, 0.010185721, -0.049200654, 0.009120523, 0.023432644, 0.038868837, 0.022822741, -0.017687218, 0.01797593, 0.019490909, -0.012386594, 0.04619242, 0.005532418, -0.001282846, -0.032982603, 0.036066346, 0.04019281, 0.0095472, -0.037452023, -0.054162223, -0.01710694, 0.04450369, -0.060550183, -0.0029567431, -0.059835088, 0.007813394, -0.018293973, 0.0009353128, -0.012241863, 0.01793737, 0.04721467, 0.04763934, -0.014441057, -0.012724904, 0.0028494897, -0.029419238, 0.022275846, 0.021386063, 0.021608345, -0.02579907, 0.052262954, -0.003183807, 0.05525759, -0.027647685, 0.04106979, -0.06527761, -0.028924868, 0.008581416, -0.03778851, -0.05781556, -0.013653264, 0.022162069, -0.021643842, 0.07282086, -0.032579716, 0.036778167, -0.08127197, -0.044547327, -0.014007941, -0.026177937, -0.0074468036, -0.00015305716, 0.023475155, -0.015770588, 0.008764004, 0.020728514, 0.0051948354, 0.037905734, -0.014288843, 0.031008013, 0.018476125, 0.0102386745, -0.030461095, -0.009188289, -0.0049677673, 0.020343283, 0.007293015, -0.020627672, 0.023480011, 0.0028341915, 0.0348296, 0.061199024, -0.019783545, 0.008868943, -0.01563627, 0.0066538495, -0.04046407, 0.061568804, -0.031021332, -0.040355798, -0.009278192, 0.03876547, -0.012943092, -0.03172945, 0.0043725725, -0.056451205, -0.052762073, -0.001245686, 0.00793185, -0.05007779, -0.013420701, -0.0009653759, 0.064457156, 0.051349375, -0.004171062, -0.0024258192, -0.013913483, -0.012028502, -0.033398636, -0.048160437, -0.01652435, 0.0071760286, 0.06746704, 0.03242368, 0.018339084, 0.0024701874, 0.03585219, 0.022904903, -0.0337929, 0.05738014, -0.055223145, 0.03612557, -0.06269404, -0.014803476, 0.07378262, -0.021870837], "created_at": 1747110793728, "nchars": 5856}}, "count": 5}}