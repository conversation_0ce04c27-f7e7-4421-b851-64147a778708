{"main": {"id": "0eb5ba693c2fec5c", "type": "split", "children": [{"id": "d91c3a14c317a5f9", "type": "tabs", "children": [{"id": "5f64a192e00bee6b", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "ngosangns - home.md", "mode": "source", "source": false, "backlinks": true, "backlinkOpts": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}}, "pinned": true, "icon": "lucide-file", "title": "ngosangns - home"}}]}], "direction": "vertical"}, "left": {"id": "e1e33e21f7a65547", "type": "split", "children": [{"id": "0d1187136fcd93c5", "type": "tabs", "children": [{"id": "f75e099d7066d2bd", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "cfe929892045ce75", "type": "leaf", "state": {"type": "search", "state": {"query": "tag:business--management", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}], "currentTab": 1}], "direction": "horizontal", "width": 300}, "right": {"id": "dcfe546b23c80c5b", "type": "split", "children": [{"id": "dc7145cf924f24cc", "type": "tabs", "children": [{"id": "117eb978fdf1a1b5", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Working.md", "collapseAll": true, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Working"}}, {"id": "f9dd7167b7b1ec1d", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Working.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Working"}}, {"id": "304e3123d495ccc9", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "f4cc6f4a6f847c60", "type": "leaf", "state": {"type": "outline", "state": {"file": "ngosangns - home.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of ngosangns - home"}}, {"id": "c3bb31753dcb6f09", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-archive", "title": "All properties"}}], "currentTab": 3}], "direction": "horizontal", "width": 445.5}, "left-ribbon": {"hiddenItems": {"graph:Open graph view": false, "command-palette:Open command palette": false, "canvas:Create new canvas": false, "workspaces:Manage workspace layouts": false, "obsidian-excalidraw-plugin:New drawing": false}}, "active": "5f64a192e00bee6b", "lastOpenFiles": ["code/detailed_categorization_report.py", "code/improved_tagging_system.py", "code/apply_tags_to_files.py", "code/analyze_markdown_files.py", "code", "Business.md", "ngosangns - home.md", "Final_Analysis_Report.md", "Category_Index.md", "Tag_Index.md", "apply_tags_to_files.py", "improved_tagging_report.json", "improved_tagging_system.py", "detailed_categorization_report.py", "markdown_analysis_report.json", "analyze_markdown_files.py", "Working.md", "Tạo video bằng các tool AI.md", "Excalidraw/Drawing 2025-07-29 11.46.50.excalidraw.md", "Cân Bằng Giữa P&L và Chất Lượng Dự Án.md", "Testing.md", "Microservices.md", "Untitled 1.canvas", "Excalidraw/<PERSON> - Load classroom coordinator states.md", "Excalidraw/<PERSON> - editor.geo.md", "viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md", "viclass - 752 - Make it easier to create account for user to experience the beta system.md", "Excalidraw/Viclass - 514 - Synchronize mouse position & mouse shape of the presenter.md", "Inceptionlabs - Viclass.md", "Inceptionlabs.md", "Severless.md", "SaaS.md", "Solutions & System Designs & Design Patterns.md", "Machine learning - Deep Learning - AI - ML - DL.md", "AI support for coding.md", "React - Next.md", "MCP - Model Context Protocol.md", "Untitled.canvas", "Untitled 7 1.png", "Untitled 11.png", "react roadmap.jpg", "MonitoringAlerting dùng PrometheusAlertmanager, Logging thì EL<PERSON>, con Sentry cho Tracing.png", "Pasted image **************.png", "Pasted image **************.png", "Pasted image **************.png", "Pasted image **************.png", "Pasted image **************.png", "Pasted image **************.png"]}