
"smart_sources:37 Tips from a Senior Frontend Developer.md": {"path":"37 Tips from a Senior Frontend Developer.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"d6a12944a913bf6c88e687fba4bf926f734a5b29b553d6ee2c7b1897c7709456","at":1743662830268},"class_name":"SmartSource","outlinks":[{"title":"Work","target":"Work","line":3},{"title":"Kho chung IT","target":"Kho chung IT","line":4},{"title":"@abbeyperini","target":"https://dev.to/abbeyperini","line":11},{"title":"post","target":"https://dev.to/abbeyperini/12-tips-from-a-mid-level-developer-29bk","line":11},{"title":"T-shaped","target":"https://www.forbes.com/sites/lisabodell/2020/08/28/futurethink-forecasts-t-shaped-teams-are-the-future-of-work/","line":316}],"metadata":{"relates":["[[Work]]","[[Kho chung IT]]"]},"blocks":{"#---frontmatter---":[1,5],"#":[7,14],"#1. Master the fundamentals":[15,28],"#1. Master the fundamentals#{1}":[17,20],"#1. Master the fundamentals#{2}":[21,21],"#1. Master the fundamentals#{3}":[22,22],"#1. Master the fundamentals#{4}":[23,24],"#1. Master the fundamentals#{5}":[25,28],"#2. Understand how the web works":[29,38],"#2. Understand how the web works#{1}":[31,38],"#3. Get familiar with Data Structures & Algorithms":[39,52],"#3. Get familiar with Data Structures & Algorithms#{1}":[41,52],"#4. Learn by doing rather than reading/watching":[53,62],"#4. Learn by doing rather than reading/watching#{1}":[55,62],"#5. Ask for help when stuck":[63,72],"#5. Ask for help when stuck#{1}":[65,72],"#6. Ask for help the proper way":[73,86],"#6. Ask for help the proper way#{1}":[75,78],"#6. Ask for help the proper way#{2}":[79,79],"#6. Ask for help the proper way#{3}":[80,80],"#6. Ask for help the proper way#{4}":[81,82],"#6. Ask for help the proper way#{5}":[83,86],"#7. Don't copy/paste code you don't understand":[87,98],"#7. Don't copy/paste code you don't understand#{1}":[89,90],"#7. Don't copy/paste code you don't understand#{2}":[91,91],"#7. Don't copy/paste code you don't understand#{3}":[92,92],"#7. Don't copy/paste code you don't understand#{4}":[93,94],"#7. Don't copy/paste code you don't understand#{5}":[95,98],"#8. Don't blindly apply every piece of advice found online":[99,117],"#8. Don't blindly apply every piece of advice found online#{1}":[101,110],"#8. Don't blindly apply every piece of advice found online#{2}":[111,111],"#8. Don't blindly apply every piece of advice found online#{3}":[112,113],"#8. Don't blindly apply every piece of advice found online#{4}":[114,117],"#9. Assume good intent: people want you to succeed ❤️":[118,131],"#9. Assume good intent: people want you to succeed ❤️#{1}":[120,131],"#10. Done is better than perfect":[132,144],"#10. Done is better than perfect#{1}":[134,135],"#10. Done is better than perfect#{2}":[136,136],"#10. Done is better than perfect#{3}":[137,137],"#10. Done is better than perfect#{4}":[138,138],"#10. Done is better than perfect#{5}":[139,140],"#10. Done is better than perfect#{6}":[141,144],"#11. Always break tasks into manageable ones":[145,160],"#11. Always break tasks into manageable ones#{1}":[147,154],"#11. Always break tasks into manageable ones#{2}":[155,155],"#11. Always break tasks into manageable ones#{3}":[156,156],"#11. Always break tasks into manageable ones#{4}":[157,158],"#11. Always break tasks into manageable ones#{5}":[159,160],"#12. Be trusted to reach out when you need help":[161,176],"#12. Be trusted to reach out when you need help#{1}":[163,168],"#12. Be trusted to reach out when you need help#{2}":[169,169],"#12. Be trusted to reach out when you need help#{3}":[170,170],"#12. Be trusted to reach out when you need help#{4}":[171,172],"#12. Be trusted to reach out when you need help#{5}":[173,176],"#13. Show enthusiasm for the work":[177,188],"#13. Show enthusiasm for the work#{1}":[179,188],"#14. Stay open to learning new things/tools/methods":[189,198],"#14. Stay open to learning new things/tools/methods#{1}":[191,198],"#15. Master your dev tools 🛠️":[199,211],"#15. Master your dev tools 🛠️#{1}":[201,204],"#15. Master your dev tools 🛠️#{2}":[205,205],"#15. Master your dev tools 🛠️#{3}":[206,206],"#15. Master your dev tools 🛠️#{4}":[207,207],"#15. Master your dev tools 🛠️#{5}":[208,209],"#15. Master your dev tools 🛠️#{6}":[210,211],"#16. Focus on delivering value":[212,228],"#16. Focus on delivering value#{1}":[214,217],"#16. Focus on delivering value#{2}":[218,218],"#16. Focus on delivering value#{3}":[219,219],"#16. Focus on delivering value#{4}":[220,220],"#16. Focus on delivering value#{5}":[221,222],"#16. Focus on delivering value#{6}":[223,228],"#17. Advocate for your work: it won't speak for itself":[229,243],"#17. Advocate for your work: it won't speak for itself#{1}":[231,232],"#17. Advocate for your work: it won't speak for itself#{2}":[233,234],"#17. Advocate for your work: it won't speak for itself#{3}":[235,237],"#17. Advocate for your work: it won't speak for itself#{4}":[238,243],"#18. Prefer writing dumb code over clever code":[244,255],"#18. Prefer writing dumb code over clever code#{1}":[246,255],"#19. Your manager is your best ally":[256,267],"#19. Your manager is your best ally#{1}":[258,267],"#20. Make your manager's life easier":[268,281],"#20. Make your manager's life easier#{1}":[270,273],"#20. Make your manager's life easier#{2}":[274,274],"#20. Make your manager's life easier#{3}":[275,275],"#20. Make your manager's life easier#{4}":[276,277],"#20. Make your manager's life easier#{5}":[278,281],"#21. Understand the big picture behind your tasks":[282,297],"#21. Understand the big picture behind your tasks#{1}":[284,289],"#21. Understand the big picture behind your tasks#{2}":[290,290],"#21. Understand the big picture behind your tasks#{3}":[291,291],"#21. Understand the big picture behind your tasks#{4}":[292,293],"#21. Understand the big picture behind your tasks#{5}":[294,297],"#22. Contribute to the team (documentation, tech talk, demos, etc.)":[298,307],"#22. Contribute to the team (documentation, tech talk, demos, etc.)#{1}":[300,307],"#23. Become the \"go-to-person\" in a specific area":[308,319],"#23. Become the \"go-to-person\" in a specific area#{1}":[310,319],"#24. Develop your communication skills":[320,334],"#24. Develop your communication skills#{1}":[322,325],"#24. Develop your communication skills#{2}":[326,326],"#24. Develop your communication skills#{3}":[327,327],"#24. Develop your communication skills#{4}":[328,328],"#24. Develop your communication skills#{5}":[329,330],"#24. Develop your communication skills#{6}":[331,334],"#25. Take breaks when you're stuck on a problem":[335,346],"#25. Take breaks when you're stuck on a problem#{1}":[337,346],"#26. Work from your strengths, not your weaknesses":[347,358],"#26. Work from your strengths, not your weaknesses#{1}":[349,358],"#27. Take ownership of your career path":[359,368],"#27. Take ownership of your career path#{1}":[361,368],"#28. Hang with other devs":[369,385],"#28. Hang with other devs#{1}":[371,378],"#28. Hang with other devs#{2}":[379,379],"#28. Hang with other devs#{3}":[380,380],"#28. Hang with other devs#{4}":[381,381],"#28. Hang with other devs#{5}":[382,383],"#28. Hang with other devs#{6}":[384,385],"#29. Mentor younger devs":[386,397],"#29. Mentor younger devs#{1}":[388,391],"#29. Mentor younger devs#{2}":[392,392],"#29. Mentor younger devs#{3}":[393,393],"#29. Mentor younger devs#{4}":[394,395],"#29. Mentor younger devs#{5}":[396,397],"#30. Diversify the problems you solve":[398,409],"#30. Diversify the problems you solve#{1}":[400,403],"#30. Diversify the problems you solve#{2}":[404,404],"#30. Diversify the problems you solve#{3}":[405,405],"#30. Diversify the problems you solve#{4}":[406,407],"#30. Diversify the problems you solve#{5}":[408,409],"#31. Find mentors":[410,425],"#31. Find mentors#{1}":[412,413],"#31. Find mentors#{2}":[414,414],"#31. Find mentors#{3}":[415,415],"#31. Find mentors#{4}":[416,417],"#31. Find mentors#{5}":[418,425],"#32. Commit to a JavaScript framework & master it":[426,439],"#32. Commit to a JavaScript framework & master it#{1}":[428,439],"#33. Constantly think of the user experience":[440,452],"#33. Constantly think of the user experience#{1}":[442,445],"#33. Constantly think of the user experience#{2}":[446,446],"#33. Constantly think of the user experience#{3}":[447,447],"#33. Constantly think of the user experience#{4}":[448,448],"#33. Constantly think of the user experience#{5}":[449,450],"#33. Constantly think of the user experience#{6}":[451,452],"#34. Be comfortable saying no":[453,462],"#34. Be comfortable saying no#{1}":[455,462],"#35. Continuously invest in your skills":[463,470],"#35. Continuously invest in your skills#{1}":[465,470],"#36. When faced with too much work, reduce the features vs. the quality of the code.":[471,480],"#36. When faced with too much work, reduce the features vs. the quality of the code.#{1}":[473,480],"#37. Strive to understand your collaborators (designers, Backend developers, etc.)":[481,487],"#37. Strive to understand your collaborators (designers, Backend developers, etc.)#{1}":[483,487]},"last_import":{"mtime":1743045798094,"size":13425,"at":1743662830271,"hash":"d6a12944a913bf6c88e687fba4bf926f734a5b29b553d6ee2c7b1897c7709456"}},
"smart_sources:37 Tips from a Senior Frontend Developer.md": {"path":"37 Tips from a Senior Frontend Developer.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08225498,-0.04194194,-0.02567822,-0.07422557,0.03308891,0.00954021,-0.06396301,-0.02250626,0.00037766,-0.02261464,-0.00715273,0.06656592,0.01153353,0.01934898,0.02761272,0.04452212,-0.0078963,0.0662242,-0.05248937,0.00498486,-0.00624548,0.02012376,-0.03032314,-0.04251404,0.04720077,0.0136259,-0.03259518,-0.0615053,-0.02614412,-0.19995736,-0.024774,-0.03836494,0.08747859,-0.0454624,-0.01213497,-0.02192,0.04733173,0.01060553,-0.05312601,0.00820223,0.01432481,0.04991345,-0.00191941,-0.02695624,-0.04018476,-0.0334215,0.00040782,-0.02497683,-0.03904903,-0.03365698,-0.0546662,-0.06719396,-0.02805766,-0.00115013,0.01200144,0.05136583,0.01169964,0.0693385,-0.02582875,0.06341934,0.05412192,-0.00025215,-0.15501319,0.11628125,0.07468443,0.06042917,-0.01729256,-0.01626774,0.02826123,0.04353917,-0.04899602,0.00161405,-0.00666557,0.0910593,0.00295764,-0.03685658,-0.03053548,-0.01832666,0.0358388,0.01722789,0.01761472,0.00008608,-0.00299734,0.04178251,0.02389291,0.00918609,0.01696181,0.05087728,0.06438721,-0.00445303,0.00948852,-0.09531074,0.03436683,-0.01742038,0.03288911,0.0179844,0.05199724,0.01568935,-0.07762114,0.09151766,-0.06997308,0.00744223,0.06376871,-0.02709641,0.03383231,-0.02753871,0.05526531,-0.00169948,0.00892959,0.02218532,-0.06751915,-0.04671407,-0.03731104,-0.03160551,0.03449847,-0.05510249,-0.00601625,0.02563985,0.02067197,0.01391678,0.05807396,0.00230826,0.04258076,0.00656282,0.04803814,-0.02385398,-0.0280382,0.03562227,-0.00240979,0.05642114,0.02331779,0.06323037,-0.07026152,-0.02431039,-0.02688734,0.00699994,0.03859618,0.0094865,0.03444682,-0.01455055,-0.04469838,-0.01757844,0.04023457,-0.03787608,-0.01477605,0.09301508,0.00574359,0.05439978,-0.01889173,-0.04608445,0.01649992,0.04488451,-0.01781666,-0.03381142,-0.00313322,0.02285299,0.03182758,0.00149627,-0.05025123,-0.00491682,-0.01593409,-0.01817651,-0.09157977,0.09021755,0.02351972,-0.12403667,-0.03559094,0.0033183,0.01539675,-0.02230366,0.00343398,0.02239974,0.01360674,-0.02642523,0.09253193,-0.04192547,-0.05197371,-0.031224,0.05448548,0.08268324,0.05840008,0.02819622,-0.00984521,0.0465913,0.01380706,-0.07485583,0.0064659,-0.04472005,0.03696886,-0.00333908,-0.08664794,0.00643944,-0.04526106,0.00447016,-0.00727969,0.0259479,-0.00114388,-0.02480089,0.05837111,0.0120761,0.04679517,0.02470863,0.02754571,0.04370021,-0.10024853,0.0374144,0.03962782,-0.06991065,0.0846532,0.03136367,-0.05799189,0.00302183,0.08790957,0.03126247,0.03220632,0.00349676,0.00862967,0.12442412,-0.00755572,0.06587974,0.01825006,0.0359419,-0.08575159,-0.22072671,-0.00844402,0.02778392,-0.06221154,0.01667609,-0.05355011,0.08290147,-0.0388011,-0.02531994,0.0588147,0.09714764,-0.04662992,-0.05328239,-0.02019101,-0.04221669,-0.00058831,0.03315689,0.00097334,-0.09273218,-0.00924768,-0.00177312,-0.01567278,-0.02018829,-0.126505,0.0233557,-0.00593166,0.12337637,-0.04187825,0.02258849,-0.09196831,0.0115379,-0.04395843,-0.00641865,-0.08014605,0.05076917,0.00077027,0.02351802,-0.06163848,0.01639032,-0.00164795,0.01820304,0.003392,0.01857032,-0.07448437,-0.10988757,-0.02421086,-0.05268424,-0.11625364,-0.00330282,-0.02809821,0.00847468,0.00457553,0.04364039,0.10269122,-0.02039371,-0.02991628,-0.05263436,0.00430877,0.01009617,0.05857987,-0.00538915,-0.03076064,0.00440097,-0.03012024,0.05338568,0.00810296,-0.03353737,0.00020066,0.10419653,-0.06715806,-0.05027112,0.12063797,0.00300751,-0.01207952,0.0230137,0.03055008,-0.03618615,-0.01652449,0.07023062,0.00275788,-0.02812396,-0.04147615,0.07887158,0.00666528,0.02224671,0.01990147,0.02312366,-0.04877478,0.03420737,-0.04423686,-0.0764094,0.01607288,-0.05476622,-0.02227737,0.01493765,0.00883617,-0.24666013,-0.02911884,0.04808973,-0.04341359,-0.01348775,0.0195284,0.08766848,-0.10081863,-0.00313368,0.0190496,-0.01325356,0.04104856,0.05737956,-0.06833162,0.06901976,0.04074512,0.06122013,0.01496547,0.03630947,-0.04849921,0.02266536,0.05228029,0.21957454,-0.00773029,0.02727738,0.04252183,0.01599138,0.01084124,0.11216666,0.03650988,0.02083994,0.02642267,0.07385217,-0.01498259,-0.01310912,0.02670612,-0.0366962,0.01275322,-0.0181498,0.0074545,0.05004285,-0.01560956,-0.00794501,0.00842935,0.05151869,-0.02673084,-0.008467,-0.08530974,-0.02302161,-0.01048924,-0.09949989,0.01475386,0.00479634,-0.00345759,0.01205174,0.04211566,0.01769242,0.00432041,-0.04520072,-0.00820107,0.00533709,0.00771551,0.0413936,0.09034251,0.03715095],"last_embed":{"hash":"d6a12944a913bf6c88e687fba4bf926f734a5b29b553d6ee2c7b1897c7709456","tokens":468}}},"last_read":{"hash":"d6a12944a913bf6c88e687fba4bf926f734a5b29b553d6ee2c7b1897c7709456","at":1743662881990},"class_name":"SmartSource","outlinks":[{"title":"Work","target":"Work","line":3},{"title":"Kho chung IT","target":"Kho chung IT","line":4},{"title":"@abbeyperini","target":"https://dev.to/abbeyperini","line":11},{"title":"post","target":"https://dev.to/abbeyperini/12-tips-from-a-mid-level-developer-29bk","line":11},{"title":"T-shaped","target":"https://www.forbes.com/sites/lisabodell/2020/08/28/futurethink-forecasts-t-shaped-teams-are-the-future-of-work/","line":316}],"metadata":{"relates":["[[Work]]","[[Kho chung IT]]"]},"blocks":{"#---frontmatter---":[1,5],"#":[7,14],"#1. Master the fundamentals":[15,28],"#1. Master the fundamentals#{1}":[17,20],"#1. Master the fundamentals#{2}":[21,21],"#1. Master the fundamentals#{3}":[22,22],"#1. Master the fundamentals#{4}":[23,24],"#1. Master the fundamentals#{5}":[25,28],"#2. Understand how the web works":[29,38],"#2. Understand how the web works#{1}":[31,38],"#3. Get familiar with Data Structures & Algorithms":[39,52],"#3. Get familiar with Data Structures & Algorithms#{1}":[41,52],"#4. Learn by doing rather than reading/watching":[53,62],"#4. Learn by doing rather than reading/watching#{1}":[55,62],"#5. Ask for help when stuck":[63,72],"#5. Ask for help when stuck#{1}":[65,72],"#6. Ask for help the proper way":[73,86],"#6. Ask for help the proper way#{1}":[75,78],"#6. Ask for help the proper way#{2}":[79,79],"#6. Ask for help the proper way#{3}":[80,80],"#6. Ask for help the proper way#{4}":[81,82],"#6. Ask for help the proper way#{5}":[83,86],"#7. Don't copy/paste code you don't understand":[87,98],"#7. Don't copy/paste code you don't understand#{1}":[89,90],"#7. Don't copy/paste code you don't understand#{2}":[91,91],"#7. Don't copy/paste code you don't understand#{3}":[92,92],"#7. Don't copy/paste code you don't understand#{4}":[93,94],"#7. Don't copy/paste code you don't understand#{5}":[95,98],"#8. Don't blindly apply every piece of advice found online":[99,117],"#8. Don't blindly apply every piece of advice found online#{1}":[101,110],"#8. Don't blindly apply every piece of advice found online#{2}":[111,111],"#8. Don't blindly apply every piece of advice found online#{3}":[112,113],"#8. Don't blindly apply every piece of advice found online#{4}":[114,117],"#9. Assume good intent: people want you to succeed ❤️":[118,131],"#9. Assume good intent: people want you to succeed ❤️#{1}":[120,131],"#10. Done is better than perfect":[132,144],"#10. Done is better than perfect#{1}":[134,135],"#10. Done is better than perfect#{2}":[136,136],"#10. Done is better than perfect#{3}":[137,137],"#10. Done is better than perfect#{4}":[138,138],"#10. Done is better than perfect#{5}":[139,140],"#10. Done is better than perfect#{6}":[141,144],"#11. Always break tasks into manageable ones":[145,160],"#11. Always break tasks into manageable ones#{1}":[147,154],"#11. Always break tasks into manageable ones#{2}":[155,155],"#11. Always break tasks into manageable ones#{3}":[156,156],"#11. Always break tasks into manageable ones#{4}":[157,158],"#11. Always break tasks into manageable ones#{5}":[159,160],"#12. Be trusted to reach out when you need help":[161,176],"#12. Be trusted to reach out when you need help#{1}":[163,168],"#12. Be trusted to reach out when you need help#{2}":[169,169],"#12. Be trusted to reach out when you need help#{3}":[170,170],"#12. Be trusted to reach out when you need help#{4}":[171,172],"#12. Be trusted to reach out when you need help#{5}":[173,176],"#13. Show enthusiasm for the work":[177,188],"#13. Show enthusiasm for the work#{1}":[179,188],"#14. Stay open to learning new things/tools/methods":[189,198],"#14. Stay open to learning new things/tools/methods#{1}":[191,198],"#15. Master your dev tools 🛠️":[199,211],"#15. Master your dev tools 🛠️#{1}":[201,204],"#15. Master your dev tools 🛠️#{2}":[205,205],"#15. Master your dev tools 🛠️#{3}":[206,206],"#15. Master your dev tools 🛠️#{4}":[207,207],"#15. Master your dev tools 🛠️#{5}":[208,209],"#15. Master your dev tools 🛠️#{6}":[210,211],"#16. Focus on delivering value":[212,228],"#16. Focus on delivering value#{1}":[214,217],"#16. Focus on delivering value#{2}":[218,218],"#16. Focus on delivering value#{3}":[219,219],"#16. Focus on delivering value#{4}":[220,220],"#16. Focus on delivering value#{5}":[221,222],"#16. Focus on delivering value#{6}":[223,228],"#17. Advocate for your work: it won't speak for itself":[229,243],"#17. Advocate for your work: it won't speak for itself#{1}":[231,232],"#17. Advocate for your work: it won't speak for itself#{2}":[233,234],"#17. Advocate for your work: it won't speak for itself#{3}":[235,237],"#17. Advocate for your work: it won't speak for itself#{4}":[238,243],"#18. Prefer writing dumb code over clever code":[244,255],"#18. Prefer writing dumb code over clever code#{1}":[246,255],"#19. Your manager is your best ally":[256,267],"#19. Your manager is your best ally#{1}":[258,267],"#20. Make your manager's life easier":[268,281],"#20. Make your manager's life easier#{1}":[270,273],"#20. Make your manager's life easier#{2}":[274,274],"#20. Make your manager's life easier#{3}":[275,275],"#20. Make your manager's life easier#{4}":[276,277],"#20. Make your manager's life easier#{5}":[278,281],"#21. Understand the big picture behind your tasks":[282,297],"#21. Understand the big picture behind your tasks#{1}":[284,289],"#21. Understand the big picture behind your tasks#{2}":[290,290],"#21. Understand the big picture behind your tasks#{3}":[291,291],"#21. Understand the big picture behind your tasks#{4}":[292,293],"#21. Understand the big picture behind your tasks#{5}":[294,297],"#22. Contribute to the team (documentation, tech talk, demos, etc.)":[298,307],"#22. Contribute to the team (documentation, tech talk, demos, etc.)#{1}":[300,307],"#23. Become the \"go-to-person\" in a specific area":[308,319],"#23. Become the \"go-to-person\" in a specific area#{1}":[310,319],"#24. Develop your communication skills":[320,334],"#24. Develop your communication skills#{1}":[322,325],"#24. Develop your communication skills#{2}":[326,326],"#24. Develop your communication skills#{3}":[327,327],"#24. Develop your communication skills#{4}":[328,328],"#24. Develop your communication skills#{5}":[329,330],"#24. Develop your communication skills#{6}":[331,334],"#25. Take breaks when you're stuck on a problem":[335,346],"#25. Take breaks when you're stuck on a problem#{1}":[337,346],"#26. Work from your strengths, not your weaknesses":[347,358],"#26. Work from your strengths, not your weaknesses#{1}":[349,358],"#27. Take ownership of your career path":[359,368],"#27. Take ownership of your career path#{1}":[361,368],"#28. Hang with other devs":[369,385],"#28. Hang with other devs#{1}":[371,378],"#28. Hang with other devs#{2}":[379,379],"#28. Hang with other devs#{3}":[380,380],"#28. Hang with other devs#{4}":[381,381],"#28. Hang with other devs#{5}":[382,383],"#28. Hang with other devs#{6}":[384,385],"#29. Mentor younger devs":[386,397],"#29. Mentor younger devs#{1}":[388,391],"#29. Mentor younger devs#{2}":[392,392],"#29. Mentor younger devs#{3}":[393,393],"#29. Mentor younger devs#{4}":[394,395],"#29. Mentor younger devs#{5}":[396,397],"#30. Diversify the problems you solve":[398,409],"#30. Diversify the problems you solve#{1}":[400,403],"#30. Diversify the problems you solve#{2}":[404,404],"#30. Diversify the problems you solve#{3}":[405,405],"#30. Diversify the problems you solve#{4}":[406,407],"#30. Diversify the problems you solve#{5}":[408,409],"#31. Find mentors":[410,425],"#31. Find mentors#{1}":[412,413],"#31. Find mentors#{2}":[414,414],"#31. Find mentors#{3}":[415,415],"#31. Find mentors#{4}":[416,417],"#31. Find mentors#{5}":[418,425],"#32. Commit to a JavaScript framework & master it":[426,439],"#32. Commit to a JavaScript framework & master it#{1}":[428,439],"#33. Constantly think of the user experience":[440,452],"#33. Constantly think of the user experience#{1}":[442,445],"#33. Constantly think of the user experience#{2}":[446,446],"#33. Constantly think of the user experience#{3}":[447,447],"#33. Constantly think of the user experience#{4}":[448,448],"#33. Constantly think of the user experience#{5}":[449,450],"#33. Constantly think of the user experience#{6}":[451,452],"#34. Be comfortable saying no":[453,462],"#34. Be comfortable saying no#{1}":[455,462],"#35. Continuously invest in your skills":[463,470],"#35. Continuously invest in your skills#{1}":[465,470],"#36. When faced with too much work, reduce the features vs. the quality of the code.":[471,480],"#36. When faced with too much work, reduce the features vs. the quality of the code.#{1}":[473,480],"#37. Strive to understand your collaborators (designers, Backend developers, etc.)":[481,487],"#37. Strive to understand your collaborators (designers, Backend developers, etc.)#{1}":[483,487]},"last_import":{"mtime":1743045798094,"size":13425,"at":1743662830271,"hash":"d6a12944a913bf6c88e687fba4bf926f734a5b29b553d6ee2c7b1897c7709456"}},"smart_blocks:37 Tips from a Senior Frontend Developer.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10511867,-0.02763507,-0.01332953,-0.11749963,-0.00096336,-0.01164228,-0.02687246,-0.00839529,0.00442443,-0.00794358,-0.00235789,0.00592291,0.00412329,-0.01429329,-0.01921202,0.03980172,0.00739568,0.02702655,-0.05938457,-0.00428687,-0.0087167,0.01133103,0.0139428,-0.0268257,0.06269184,0.00421526,-0.02020894,-0.06910274,-0.03071913,-0.17668597,0.01007457,0.00096468,0.0486708,-0.0546184,-0.03955869,0.01703316,0.03661837,-0.00181981,-0.04158204,0.00764408,0.00389245,0.02759207,0.00292864,-0.06359741,-0.00507718,-0.00423841,0.01378229,-0.03116384,0.01459693,-0.02072337,-0.03602856,-0.01509689,-0.01074116,-0.00181949,-0.00787437,0.03923394,0.00561344,0.03931615,-0.02845138,0.06450664,0.02342184,-0.00722356,-0.18557979,0.09203879,-0.00247322,0.0404918,-0.03513982,-0.03285125,-0.02952032,0.02836013,-0.03683191,-0.00255987,-0.00196325,0.07082298,-0.01789136,-0.00340587,-0.02368999,0.01348279,0.01814694,-0.02666156,0.05850404,0.04287617,-0.02494299,0.04932758,0.03654475,-0.0022874,0.07008486,0.06211143,0.06925891,-0.03394889,0.02019177,-0.06175435,0.04758052,0.0087042,-0.00443576,0.01645894,0.05025782,0.02569941,-0.07875054,0.12518427,-0.08791061,0.00234602,0.01665787,0.00740669,0.04788996,-0.00881156,0.04523026,-0.00163953,0.0127344,0.06245368,-0.04452081,-0.02750596,0.00550714,-0.0573659,0.03642406,-0.00131868,0.02542091,0.03622011,0.00130869,0.00207161,0.03037428,0.0136282,0.02509356,0.00261146,0.01807864,0.00474857,-0.00866146,0.02208381,-0.03246023,0.04395365,-0.00190843,0.0814958,-0.08990558,-0.03395769,-0.02360317,-0.00306276,0.03020062,-0.03644598,0.04880822,-0.03605276,-0.05841406,-0.06390148,0.06432909,-0.04394314,-0.02810982,0.07790314,0.03331311,0.02700188,0.01123552,-0.00142671,0.00785835,0.0395932,0.01396386,-0.04197595,0.03622225,0.01558844,0.06319172,0.0475149,-0.08651499,0.00789449,0.01965978,-0.08371232,-0.05157145,0.04814727,0.01157923,-0.12355806,-0.01922986,0.00738853,0.02331284,0.03401019,0.02425673,0.00980589,0.00285914,-0.0280902,0.10848479,-0.03912869,-0.00732654,-0.05671676,0.04470747,0.08065082,0.03269297,0.00509346,-0.00773202,0.03994807,0.00202078,-0.06837886,-0.01689281,-0.05736908,0.04547491,0.02759088,-0.10233121,0.01596615,-0.04547564,0.04792864,-0.02434863,0.02804568,-0.04067229,0.01983226,0.07216199,-0.00615979,0.01017438,-0.00247981,-0.00280127,0.00894664,-0.08730964,0.01833737,0.04867091,-0.05632157,0.10564889,0.02701262,-0.05996929,0.01804522,0.08303298,0.0384515,0.01615137,0.04923711,0.00257115,0.10850533,-0.00302849,0.03413406,0.06976819,0.03295812,-0.1009312,-0.25559011,0.03544807,0.03288172,-0.05948246,0.0599709,-0.04993016,0.07722087,-0.0202051,0.03280214,0.04330363,0.11854143,-0.05493244,-0.06978071,-0.00810686,0.01153113,0.0143772,0.05423808,0.00433367,-0.06787232,-0.03218703,0.00232092,-0.02821657,-0.03383087,-0.09891374,0.04444171,-0.01819556,0.13351592,0.00135285,0.03611057,-0.08627949,0.01822701,-0.05378326,-0.00719987,-0.11239098,0.03791165,0.01936956,0.00998783,-0.05720563,0.0224807,-0.02859306,0.02062672,0.06308442,-0.02415435,-0.08339659,-0.1122616,-0.05378267,-0.02675134,-0.07985032,0.00825529,0.00392269,0.02023488,0.01161796,-0.00591168,0.07157923,0.01765818,-0.07735953,-0.12081901,-0.0238086,-0.00599043,0.10061131,-0.02012164,-0.01022121,-0.01183605,-0.04804154,0.02095169,-0.0107843,-0.05247404,0.00144457,0.11269414,-0.07327469,-0.03132081,0.09476039,-0.01230232,-0.01528327,0.02026835,0.01136318,0.00227057,-0.01522352,0.04490193,0.01758744,-0.01705025,-0.11738885,0.07162649,0.03174664,0.01548272,0.04098562,-0.01215736,-0.02093272,0.0371497,-0.01434451,-0.05773809,0.00610398,-0.06107258,-0.02861473,0.03377847,0.01329552,-0.25165203,-0.0101822,0.07053407,-0.03035388,-0.01225802,0.05720842,0.08289684,-0.08040863,-0.02520506,0.02533304,-0.00820794,0.02898564,0.07030996,-0.04234416,0.06908082,0.02073193,0.03252478,0.01255312,0.03540951,-0.04285549,0.02049663,0.03718536,0.18017137,0.02444801,0.01792193,0.03484787,-0.04004547,0.03703477,0.08034357,0.00340946,0.00126337,0.02254051,0.01725388,-0.03113863,0.01122571,0.06888653,-0.03538864,0.02578411,-0.01594448,0.01056299,0.0388491,-0.00307492,-0.01954518,0.0323952,0.05634334,-0.02859049,-0.0286269,-0.05464347,-0.03397035,0.00889166,-0.08827247,0.01089632,-0.01047214,0.01480003,0.0390589,0.04529816,0.04383237,-0.028593,-0.04669572,-0.01869113,-0.00279631,0.01217217,0.06152036,0.07531048,0.05109878],"last_embed":{"hash":"f7bdaa143d6d01d9157301fcc03c1aeb43fcca176a66d21455205dfe1062a31e","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"f7bdaa143d6d01d9157301fcc03c1aeb43fcca176a66d21455205dfe1062a31e","at":1743662881199},"key":"37 Tips from a Senior Frontend Developer.md#","lines":[7,14],"size":317,"outlinks":[{"title":"@abbeyperini","target":"https://dev.to/abbeyperini","line":5},{"title":"post","target":"https://dev.to/abbeyperini/12-tips-from-a-mid-level-developer-29bk","line":5}],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#1. Master the fundamentals": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02596054,-0.01202303,0.00576634,-0.06488506,0.01530138,0.01570097,-0.05023102,-0.00432324,0.0170203,-0.00281057,-0.03034019,0.05206596,0.02433487,0.02377226,0.04193465,0.04746763,-0.01639123,0.07200079,-0.04283433,0.02555051,-0.01835948,0.02502216,-0.0609489,-0.07232229,0.01845196,-0.02887373,-0.01512627,-0.05359456,-0.0346786,-0.17232428,-0.05549532,-0.0788959,0.04703666,-0.03750428,0.02851272,-0.01680093,0.01934753,0.06151459,-0.02118137,-0.00025431,0.03441175,0.03968142,0.00446133,-0.05411802,-0.01988978,-0.03931743,0.00320206,-0.03665016,-0.01764837,-0.04881079,-0.01684288,-0.08512466,-0.03120198,-0.01617367,0.05149632,0.05230667,0.01616661,0.05873505,-0.02278618,0.06934018,0.03638066,0.0085433,-0.17907336,0.09204786,0.12706973,0.03486475,-0.00050243,-0.00117184,0.01072178,0.06437639,-0.02706279,0.01009168,0.00643807,0.06351768,0.01194334,-0.05210391,-0.02199911,-0.05366469,0.04229205,0.0383756,-0.00048408,-0.01723875,-0.0253997,-0.01389156,-0.02961881,-0.00380179,0.01439951,0.00109965,0.02778699,0.01181866,0.0403921,-0.08025045,0.03703284,-0.01779752,0.01633244,0.00401288,0.02979105,-0.01359682,-0.09007937,0.12736039,-0.055879,0.05910885,0.07567701,-0.0192702,0.05428258,0.00252087,0.0351194,0.00631425,0.00712531,0.01794953,-0.04743861,-0.04168792,-0.01158519,-0.02448448,0.01507329,-0.05685239,-0.00916029,-0.00983822,0.02952524,0.00834802,0.10737041,0.01548469,0.0655131,-0.01617431,0.04502889,-0.02615584,-0.02311107,0.02946911,0.00041912,0.02315862,-0.01330254,0.01974465,-0.03800542,-0.00650647,0.01577536,0.03160521,0.02803952,0.0648541,0.05657237,0.02775819,-0.02651089,-0.01773799,0.01294439,-0.02366413,0.00847276,0.07348914,-0.0251041,0.07519536,-0.03220015,-0.04646777,-0.02642441,0.00736274,-0.04187431,-0.02953662,-0.01846105,0.0569269,0.02541682,-0.01723662,-0.01998485,-0.00339634,-0.02130726,-0.01325554,-0.07571192,0.05594013,0.01820557,-0.11172117,-0.10554026,0.0054426,-0.00011454,-0.02682409,-0.01926984,0.00948963,-0.00909017,-0.0352655,0.10033879,-0.04480377,-0.01280663,0.01010892,0.02947531,0.03599396,0.07937121,-0.00827332,-0.00781313,0.02485893,-0.00170801,-0.09342674,0.04247506,-0.02701577,0.0551955,-0.03483313,-0.08531427,-0.00883967,-0.04188662,-0.01369461,-0.00604996,0.03427564,-0.00108438,-0.02737813,0.08483624,0.00329113,0.03844485,0.01867268,0.02112461,0.04414158,-0.09449314,0.04700848,0.04044991,-0.07565111,0.1146734,0.04173345,-0.08399507,-0.01646358,0.08939496,-0.00735248,0.0454509,-0.01418661,0.00423123,0.10617431,-0.00093048,0.0412977,0.00752588,0.06995046,-0.06853168,-0.22598793,0.00235908,0.00089035,-0.08854587,0.01275387,-0.04193028,0.10767308,-0.03545063,-0.04557535,0.04340114,0.08157589,-0.05371833,-0.02661573,-0.01421282,-0.06342524,0.02414661,-0.00111348,-0.01855157,-0.06726505,-0.01708897,-0.00708444,0.00987435,-0.05022944,-0.12724271,0.03788197,0.02055826,0.11067824,-0.02909366,-0.00887854,-0.13728763,0.01067178,-0.01019167,0.00871824,-0.0724836,0.05735769,0.00124753,-0.00816079,-0.0793272,-0.03438047,0.00174789,0.04281096,-0.00686854,0.03132217,-0.04112327,-0.10008616,-0.01608966,-0.05118379,-0.08867783,-0.0150386,0.00291734,-0.00376639,-0.01920722,0.06868111,0.08158977,-0.02723496,0.0045232,-0.05626525,0.06756807,0.02837188,0.07498799,-0.01512278,-0.04378763,0.03838171,-0.05652577,0.05400555,0.03595551,-0.04900821,-0.03119344,0.11178097,-0.04252892,-0.01932051,0.09340381,0.0189541,-0.01190249,0.01392111,0.00951029,-0.00851683,-0.00186523,0.03700433,-0.01430747,-0.0293205,-0.03936725,0.07581057,0.01990106,0.04792804,0.02685088,0.02715884,-0.03997744,0.01315375,-0.06472422,-0.08556421,0.06809475,-0.06811085,0.01245838,0.00103904,0.01444783,-0.23093468,0.00882532,0.06095896,-0.01424385,-0.028939,0.03184783,0.05827902,-0.0262636,-0.03328556,0.02913829,0.01174211,-0.0120892,0.04624267,-0.05469538,0.03172602,-0.00178808,0.05608125,0.01328681,0.05653138,-0.09429182,0.02793105,0.07263975,0.20708607,0.01216931,-0.00419726,0.06625769,0.01190329,0.04073847,0.11524878,0.03757469,0.01459547,-0.00122781,0.08117378,-0.00563686,0.02648691,-0.00288711,-0.02765779,-0.00460697,-0.04066685,-0.00318883,0.04745141,-0.02220778,-0.01369911,0.0202752,0.04540887,-0.0696686,0.03280431,-0.02312536,-0.06618775,-0.04234657,-0.08446742,0.029913,0.03562414,-0.01857593,0.01740525,0.03733158,0.01418201,0.01786333,-0.05309451,-0.00439792,0.01361279,0.04512205,0.06187441,0.04050452,0.05730569],"last_embed":{"hash":"fe233dac90c17c2fdd21694848540a7a8698708956478c9439b85a2be55e0cf4","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"fe233dac90c17c2fdd21694848540a7a8698708956478c9439b85a2be55e0cf4","at":1743662881214},"key":"37 Tips from a Senior Frontend Developer.md#1. Master the fundamentals","lines":[15,28],"size":417,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#2. Understand how the web works": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0769499,-0.05387992,-0.02790084,-0.06254309,0.0221104,0.00676834,-0.05682319,-0.02986383,-0.03299172,-0.05007501,0.01485879,0.05074207,-0.02673933,0.00132375,0.0390933,0.05138609,-0.00114737,0.05225391,-0.06039995,-0.02794132,0.01469058,0.0256906,-0.02142871,-0.0531925,0.00765195,0.0362458,-0.05082938,-0.04302526,0.00620838,-0.15977716,-0.00741446,-0.07250213,0.0618422,-0.00013035,0.0011818,-0.06053103,0.03739979,-0.00070804,-0.0577936,0.0116294,0.01177219,0.0346397,-0.02370984,-0.03483175,-0.02718458,-0.04255285,-0.00329372,-0.04154428,-0.03893626,-0.03013187,-0.01038664,-0.04265391,-0.00103131,0.00067306,0.00959353,0.06914444,0.01080072,0.07381538,-0.0431702,0.04515733,0.04299212,-0.01026356,-0.14820828,0.11206461,0.03502315,0.06572331,-0.01716881,-0.01176679,0.06452429,0.05455869,-0.04159143,-0.01066324,-0.02912436,0.09407238,0.00711261,-0.04463306,-0.00079054,-0.02381533,0.0427522,-0.01823471,-0.00579664,-0.01660913,-0.01912943,0.02258893,0.0078702,0.00594213,0.05949237,0.03152508,0.06672791,0.00748464,-0.01577682,-0.09550804,0.02627416,-0.02581955,0.04272492,0.00839365,0.0454404,0.00722199,-0.06167053,0.1251535,-0.05755736,-0.0052182,0.06914546,-0.03464963,0.03978997,-0.00419377,0.02058449,0.01631473,0.00794643,0.01423562,-0.06008025,-0.04355145,-0.03431582,-0.02515739,0.0438025,-0.07897625,-0.03561363,-0.00251225,0.0487394,0.0233341,0.05027704,-0.00453742,0.05301812,0.0161566,0.02756627,-0.02230787,0.008195,0.05480477,-0.0337776,0.05957873,0.04960868,0.03414495,-0.07006139,0.00680398,-0.02558512,-0.00405318,0.07635226,0.00502131,0.01321572,0.03938514,-0.00686995,-0.03251915,0.00610483,-0.05220645,0.02744726,0.06787842,-0.00637321,0.07194229,-0.00220335,-0.02190727,0.01085222,0.08290201,-0.0521767,-0.02791544,-0.00342952,0.0308649,0.04377185,-0.04548538,-0.02274573,-0.01240731,0.00026573,-0.02844459,-0.07068378,0.09541479,0.00484926,-0.11812053,-0.06857827,0.02506769,0.03074234,-0.05885157,-0.01077653,0.02473142,0.0098587,-0.05509034,0.08888391,0.00341708,-0.07572185,-0.00209154,0.05550734,0.06307323,0.09369797,0.00119178,-0.00202872,0.0544766,-0.05741909,-0.0719524,0.05081637,-0.05923188,0.03701749,0.02787198,-0.04147802,-0.01407634,-0.03643684,-0.01930679,0.02278913,0.06341112,-0.02574479,-0.04163291,0.05093857,-0.02413007,0.09469771,0.07166854,-0.00126902,0.03069668,-0.07765278,0.0481119,0.03616454,-0.07702567,0.05948867,-0.00665361,-0.09333762,0.01848001,0.08404198,0.03629302,0.03104666,0.00286039,0.02101164,0.11262079,0.02388885,0.05846456,0.04789107,0.03222138,-0.06860591,-0.1852926,-0.03231934,0.02488964,-0.05810396,0.00607169,-0.03423189,0.05432022,-0.02782078,-0.04295439,0.07907185,0.11363137,-0.01995283,-0.01785718,-0.01047169,-0.03725057,0.03384492,-0.00297312,-0.01304499,-0.08541308,0.00333711,0.00569053,-0.04699461,-0.02514493,-0.09878749,0.05523321,-0.00547503,0.11181419,-0.00275918,0.00326125,-0.12269673,0.0202383,-0.0104238,-0.03689157,-0.09187782,0.07771225,0.00917351,0.01612939,-0.10307787,0.03382412,0.0048027,0.01955957,-0.0132043,0.0338163,-0.05131505,-0.06416099,0.01187399,-0.05552999,-0.12708721,-0.00940558,-0.03201763,-0.01091603,0.01327825,0.03980661,0.12358989,-0.05607981,-0.01102857,-0.00321854,0.02414338,-0.01145222,0.04822727,0.00116623,-0.02882769,-0.0062981,-0.04750565,0.06308915,0.05176313,-0.03929998,-0.01614031,0.09883107,-0.07052091,-0.06535141,0.11256958,0.00238386,0.00469953,-0.01817013,0.02981452,-0.02257802,-0.01508184,0.09460949,0.0085552,-0.01117698,-0.0079921,0.06596845,-0.00989019,0.00160304,-0.02584356,0.00824472,-0.09096745,0.00488525,-0.04750154,-0.05723902,0.00734548,-0.05538169,-0.05046231,0.02674604,0.00507986,-0.21137618,-0.012064,0.0591154,-0.00648284,-0.02507102,0.01708462,0.09567234,-0.09028094,-0.04037279,0.01515107,0.04006448,0.03447487,0.04167406,-0.05510816,0.05762074,0.04206447,0.06256244,-0.00339321,0.03896794,-0.06976632,0.01969817,0.03109175,0.22366399,-0.02539034,0.05106572,0.04464403,0.01984773,-0.01697826,0.09539133,0.02844132,0.04060233,0.03284129,0.07297858,-0.00565461,-0.02497678,0.02585283,-0.06119019,0.01104228,-0.0312117,0.00441546,0.01241012,-0.00996295,-0.02409324,-0.0337377,0.03830135,-0.05248479,0.00285985,-0.07668027,-0.03956456,-0.05246768,-0.06549295,0.00149597,-0.00625897,0.00089474,0.00935052,0.04854479,0.0144372,0.00225767,-0.03651568,0.02679086,0.0020184,0.02150626,0.02769206,0.11388851,0.06036625],"last_embed":{"hash":"3941b52350deaebd56c1a3875ffc3565f30195e0134455495489ed61ca789611","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"3941b52350deaebd56c1a3875ffc3565f30195e0134455495489ed61ca789611","at":1743662881228},"key":"37 Tips from a Senior Frontend Developer.md#2. Understand how the web works","lines":[29,38],"size":434,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#2. Understand how the web works#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07684111,-0.05788267,-0.02654678,-0.05701797,0.01992278,0.00786827,-0.05802699,-0.03063122,-0.03341035,-0.0530154,0.01830476,0.05221764,-0.02898301,0.00304182,0.04100414,0.04991556,-0.00242919,0.0487706,-0.06110168,-0.02727339,0.01820576,0.02482655,-0.01927243,-0.05143943,0.00629856,0.03883846,-0.04879437,-0.04219488,0.00954162,-0.15845092,-0.0078029,-0.07492308,0.06369931,0.00237005,0.00188669,-0.06357259,0.03714522,-0.00107373,-0.05898472,0.01463285,0.0143836,0.0342071,-0.02370354,-0.0330461,-0.02924805,-0.0412707,-0.00195303,-0.04066714,-0.0387737,-0.03141057,-0.00942445,-0.04226807,0.00024381,0.00301,0.00828754,0.06855332,0.01188614,0.07684623,-0.04285007,0.04115852,0.04385873,-0.00764746,-0.14939107,0.11316503,0.03578763,0.06681266,-0.01671086,-0.00937696,0.06588776,0.05753479,-0.0435775,-0.00957946,-0.03206762,0.09415787,0.00658868,-0.0479155,0.00004873,-0.02355095,0.03923278,-0.01721098,-0.00693233,-0.01917657,-0.01724872,0.02172741,0.00529077,0.0072495,0.0589722,0.02862575,0.06816814,0.00813706,-0.02075383,-0.09543512,0.02348811,-0.02338521,0.04397725,0.00705069,0.04390622,0.00887101,-0.05681579,0.12291963,-0.05752082,-0.00582424,0.0698214,-0.03594458,0.03812647,-0.00150041,0.01755592,0.01520936,0.00837218,0.01345322,-0.05872145,-0.04441122,-0.03691334,-0.02457025,0.04337624,-0.08156209,-0.0365307,-0.00603635,0.05015324,0.02301142,0.04875287,-0.00237428,0.05445671,0.01734442,0.0293624,-0.02347814,0.00924966,0.05455092,-0.03297015,0.05769583,0.05338974,0.03318741,-0.06982242,0.00774482,-0.02799837,-0.00348111,0.07843787,0.0070274,0.01248652,0.04098953,-0.00327836,-0.03224969,0.00860907,-0.05541372,0.0275132,0.06687894,-0.00700093,0.07154915,-0.0026494,-0.02067625,0.01033713,0.08377952,-0.05524634,-0.02818903,-0.00686129,0.03005307,0.04609108,-0.04787044,-0.02251124,-0.01289408,-0.00296588,-0.02507986,-0.07106537,0.09782817,0.00624156,-0.11561248,-0.06829811,0.02727685,0.02859334,-0.06466642,-0.01274983,0.02586892,0.01034007,-0.05294001,0.08521774,0.00275909,-0.07865658,-0.00223628,0.05150056,0.06340878,0.09595305,0.00105287,-0.00244051,0.05690264,-0.05982788,-0.06783596,0.05060067,-0.05768141,0.0393003,0.02694715,-0.03788683,-0.01201767,-0.03611723,-0.01978286,0.02438192,0.06473975,-0.02631515,-0.04409491,0.04884681,-0.02391814,0.09804849,0.07036264,0.00094551,0.03155172,-0.07588265,0.04746002,0.03258222,-0.07629619,0.06003518,-0.00699468,-0.09345336,0.02261669,0.08084022,0.03453362,0.03108568,0.00222422,0.02250682,0.11308164,0.02659552,0.05881374,0.04512322,0.02831969,-0.06640853,-0.18218528,-0.03702394,0.02337241,-0.0563585,0.00671974,-0.03358662,0.05092659,-0.02594056,-0.04500879,0.08106404,0.11252797,-0.01928085,-0.01527949,-0.00973292,-0.03946087,0.03317652,-0.00436213,-0.01300499,-0.08796307,0.00605319,0.00302711,-0.04628158,-0.02542582,-0.09784377,0.05265237,-0.00957881,0.11206133,-0.0044196,0.00434126,-0.12128112,0.02160504,-0.00842302,-0.04027556,-0.09116209,0.07688624,0.00996643,0.01725656,-0.1062278,0.03611924,0.00454489,0.01868291,-0.01564009,0.03370581,-0.05026608,-0.06449198,0.01545418,-0.05651996,-0.12720479,-0.01072187,-0.03239448,-0.01165541,0.01521398,0.03774417,0.12690403,-0.05898279,-0.00853485,0.00086514,0.02515269,-0.01195672,0.04379445,0.00338465,-0.02709948,-0.00825841,-0.04856032,0.06529026,0.05431088,-0.03887813,-0.01469514,0.09632393,-0.06856622,-0.06517299,0.11321457,0.00061605,0.00660527,-0.01903276,0.03115887,-0.02336089,-0.01612381,0.09546557,0.00709851,-0.00875942,-0.00635734,0.06346832,-0.0099197,0.00051984,-0.02814975,0.00760314,-0.09478513,0.00462666,-0.04866672,-0.05191233,0.00488242,-0.05549445,-0.04792933,0.0269927,0.00655071,-0.21147966,-0.01155273,0.05534073,-0.00303162,-0.0266897,0.01357599,0.09316909,-0.08898387,-0.03826771,0.01651665,0.04329613,0.03265519,0.03794857,-0.05597769,0.0567864,0.04461249,0.06247455,-0.00343789,0.03931586,-0.06544223,0.02150652,0.03150108,0.22194855,-0.02789122,0.05400107,0.04201062,0.02405745,-0.01900614,0.09148127,0.03006722,0.04220364,0.03253939,0.0757375,-0.00346611,-0.02644598,0.0251558,-0.0624617,0.0103875,-0.03077503,0.0042568,0.01067272,-0.00966725,-0.02552836,-0.03693781,0.03989264,-0.05181234,-0.00038468,-0.08061523,-0.03923963,-0.05176122,-0.0632458,0.00084026,-0.00521644,0.00202495,0.00830143,0.04871667,0.01475451,0.0020871,-0.03706234,0.0270631,0.00167104,0.02100957,0.02542014,0.11203291,0.05653674],"last_embed":{"hash":"b33da5d0e68f1ba48b8f8dd90daf7313838d7522df61d7765bdbd4518c782853","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"b33da5d0e68f1ba48b8f8dd90daf7313838d7522df61d7765bdbd4518c782853","at":1743662881245},"key":"37 Tips from a Senior Frontend Developer.md#2. Understand how the web works#{1}","lines":[31,38],"size":399,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#3. Get familiar with Data Structures & Algorithms": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08860762,-0.02291092,-0.02421104,-0.01634872,0.0244485,0.04622798,-0.02446512,0.00864396,0.01508773,-0.01991698,-0.03439906,0.03593729,0.02134656,0.01319633,0.01621802,0.05069279,-0.01564957,0.0719045,-0.0168041,-0.01531076,0.07304684,-0.03120996,-0.04657026,-0.03233461,0.07368708,0.07611548,-0.06466305,-0.0625881,-0.03661313,-0.24281617,-0.03137583,-0.0187536,0.11196656,-0.03822227,-0.02322736,-0.01685117,0.01495489,0.03395708,-0.08108209,0.01482849,0.00239209,0.06154605,-0.02139758,0.00041132,0.00253778,-0.02762486,-0.03585966,0.00498184,-0.01473742,-0.04781002,-0.05546177,-0.04796217,-0.04146419,0.03819989,0.01923146,0.03626065,0.06663261,0.03782161,0.03284437,0.00965616,0.04921076,-0.01350569,-0.14003891,0.0667173,0.09064648,0.06194932,-0.03266268,-0.03329333,0.03386488,0.07446203,-0.01643465,-0.02101634,0.00825147,0.04934421,0.03124485,-0.00936611,-0.00536874,-0.03701628,-0.00288921,0.03733582,-0.04850484,-0.03190378,0.00879836,0.03964301,-0.00881025,-0.03734152,-0.06689943,0.04129688,0.04353596,0.01741158,0.01510184,-0.0443899,0.02417918,0.04069947,0.01721808,0.03264536,0.0391309,0.00359709,-0.05354216,0.13419738,-0.04205954,0.02261952,0.04560269,0.02827229,-0.01143037,-0.04657648,0.07460973,0.00220134,-0.06338251,-0.04275957,-0.03215377,-0.04155103,0.01790838,-0.07312357,0.01795021,-0.03306295,0.00247045,0.07777395,-0.03147711,0.02758582,-0.00420139,-0.01966762,0.0308093,0.03086152,0.07052604,-0.072793,-0.0468585,0.06962615,0.02940298,0.07863687,0.04268516,0.04497281,-0.04065504,-0.0220873,0.00808922,-0.02148335,0.04553892,0.0412026,-0.00172318,-0.04599344,-0.01782079,-0.01418297,0.05806121,-0.04004803,-0.00876306,0.09114704,-0.02490003,0.04019689,-0.03225181,-0.04934149,-0.01328424,0.00807428,-0.02103929,-0.05275624,-0.01136534,0.00388191,0.03459182,0.01822384,-0.06063991,0.02929768,-0.03793487,0.02937586,-0.06233784,0.13133857,-0.01862954,-0.05073934,0.00978427,-0.00273186,0.03534862,-0.04516776,0.00950944,0.06072336,-0.00457586,0.01865025,0.05432108,-0.02581976,-0.10150536,-0.06434823,0.03455801,0.02155939,0.01678436,0.02346862,-0.04236583,-0.00138421,0.0944489,-0.01034326,-0.01869746,-0.02466786,-0.01207762,-0.01086248,-0.08990474,-0.01401597,-0.03646529,0.00718617,-0.07006887,-0.02629632,0.05687057,-0.02340552,0.01053466,0.01680465,0.00231261,0.03932314,0.04661863,0.10760041,-0.08588023,-0.02037522,0.02181024,-0.05631265,0.04454158,0.02539216,0.00229866,-0.02535071,0.0101231,0.02668779,-0.00962015,-0.05170597,0.04781991,0.07218876,-0.05600878,0.05484317,0.0316604,-0.03529827,-0.05286797,-0.19330263,-0.02533255,0.04857867,0.00306641,-0.01782255,-0.05010419,0.04723476,-0.04251147,-0.05630708,0.08069719,0.04583652,-0.01465178,-0.05502052,-0.01305945,-0.02529863,0.00206699,0.02165087,0.00484385,-0.03878925,-0.01653421,-0.00298703,0.04190275,0.00485852,-0.09874262,0.01647673,0.01750958,0.12584738,-0.07206088,0.05977282,0.04296958,-0.01513657,-0.03081911,-0.02229697,-0.0775534,0.08154133,-0.02566191,0.0136397,-0.03537465,0.04164737,-0.02099823,-0.03696577,-0.01613433,-0.01992832,-0.06491301,-0.0532628,-0.00635748,-0.03170146,-0.03978334,-0.03760754,0.01069308,0.03282743,-0.03713041,0.09029425,0.05476319,0.01819359,-0.02434516,-0.05739436,-0.02434833,-0.0153232,0.03807115,0.02518735,-0.02842895,-0.04423287,-0.00071029,0.05345269,-0.0231191,-0.00032663,-0.01184378,0.02045072,-0.03265893,-0.00393496,0.13567707,0.039378,-0.09975218,0.02044847,0.01609173,-0.06671123,-0.05183221,0.05639327,-0.03463042,0.00268057,-0.0296565,0.04691936,0.03936097,0.03546113,0.02810499,0.06392911,0.0265098,-0.00819521,-0.02892477,-0.04433797,0.02955203,-0.0279104,-0.0608907,0.01934234,0.02020574,-0.26168308,-0.00074541,0.02711701,-0.03327499,0.03319988,-0.00934662,0.07702766,-0.11271933,0.04509044,-0.00362221,-0.02119956,0.08998117,0.01182347,-0.10246537,0.0314928,0.06296564,0.09693814,-0.00913469,0.03844455,0.00556579,0.04549938,0.04103174,0.2291486,-0.00645609,0.02938055,0.01846582,0.00144609,-0.00939244,0.04155783,0.06300792,0.05332821,-0.00366781,0.10701268,-0.02171643,0.0106317,0.06596213,0.01876003,0.02541135,-0.00060042,0.02773876,0.05913416,-0.03572983,-0.02992027,0.03885631,0.09722371,0.01584762,-0.03732708,-0.12740792,-0.00727878,0.0470113,-0.08331794,-0.00757891,-0.01317264,-0.0019063,-0.02073195,0.01828022,-0.04670426,-0.01999003,-0.03259556,-0.01787666,0.01344141,-0.04769545,0.00822154,0.0660149,0.01887111],"last_embed":{"hash":"e5e62cb7a3b3d894ffed95aebae3885fcde8a2a22ffb57e7abdfaf484f9a48ee","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"e5e62cb7a3b3d894ffed95aebae3885fcde8a2a22ffb57e7abdfaf484f9a48ee","at":1743662881260},"key":"37 Tips from a Senior Frontend Developer.md#3. Get familiar with Data Structures & Algorithms","lines":[39,52],"size":536,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#3. Get familiar with Data Structures & Algorithms#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08718921,-0.02427461,-0.02466122,-0.00988403,0.02347268,0.04877479,-0.022824,0.00707472,0.01677472,-0.02217475,-0.03310848,0.03635738,0.02093291,0.01240087,0.01826389,0.05036958,-0.01629696,0.06762444,-0.01510932,-0.01370244,0.07343018,-0.0328925,-0.04511472,-0.02803767,0.07157998,0.0772275,-0.06524355,-0.06298965,-0.03459592,-0.24323878,-0.03255282,-0.0195099,0.11328388,-0.03685397,-0.02612887,-0.01959976,0.01246613,0.03424487,-0.08010348,0.01567866,0.00315665,0.0605088,-0.02163242,0.00284533,0.00194198,-0.02694812,-0.0343446,0.00844111,-0.0106774,-0.04937287,-0.05413637,-0.04361299,-0.0413675,0.0428425,0.02016188,0.03570126,0.06766433,0.03784186,0.03343057,0.00762802,0.04944986,-0.01004101,-0.13979919,0.06617734,0.08830918,0.06203127,-0.03180958,-0.03033538,0.03386972,0.07576244,-0.01753417,-0.02177594,0.00960676,0.05053427,0.0319568,-0.01062572,-0.00298536,-0.04034049,-0.00770969,0.0397058,-0.05034624,-0.03191628,0.00971811,0.03838897,-0.0117267,-0.03743679,-0.07375325,0.03944466,0.04191538,0.0169223,0.01211116,-0.04053925,0.02465719,0.04573295,0.01679758,0.02891772,0.03744852,0.00455615,-0.04608317,0.13206163,-0.04112346,0.02314693,0.04783095,0.03044881,-0.01421267,-0.04674275,0.07285862,0.002021,-0.06503476,-0.04310139,-0.03102523,-0.04148556,0.01579169,-0.07425988,0.01669699,-0.03517389,0.00055818,0.07722638,-0.03105216,0.02543862,-0.00831173,-0.01872429,0.03152112,0.03188671,0.07179316,-0.07581358,-0.04673791,0.06897986,0.03568675,0.07747568,0.04495616,0.04152316,-0.04061766,-0.02350338,0.00591707,-0.02092949,0.04513458,0.04307552,-0.00072431,-0.04712869,-0.01472927,-0.01665703,0.06059969,-0.03930444,-0.00948581,0.09150669,-0.0278343,0.0406129,-0.034341,-0.0500167,-0.01434567,0.00684865,-0.02293946,-0.05071374,-0.01268752,0.00243187,0.03447601,0.01773166,-0.05930932,0.0261208,-0.0405458,0.03209899,-0.06229168,0.13167073,-0.01630866,-0.04852956,0.010961,-0.00439324,0.03254242,-0.04607182,0.011175,0.06382833,-0.00179633,0.02354839,0.05068955,-0.02465573,-0.10461667,-0.0683559,0.03166177,0.02147222,0.01662781,0.02269176,-0.0442933,-0.00239116,0.09936028,-0.00547016,-0.02350607,-0.02252302,-0.00971818,-0.01362703,-0.08990739,-0.01102071,-0.03326023,0.00733842,-0.07102537,-0.02570843,0.05597742,-0.02452836,0.00931213,0.02014868,0.00077574,0.03552923,0.04653839,0.10748359,-0.08398326,-0.02522724,0.01847435,-0.05460545,0.04376048,0.02543484,0.00361355,-0.02510581,0.00679051,0.0245894,-0.01146611,-0.05502306,0.0503374,0.07028041,-0.05513275,0.05397006,0.02939245,-0.04137797,-0.04885871,-0.18990427,-0.02770629,0.04574434,0.00499727,-0.01802995,-0.04947075,0.04489949,-0.04085607,-0.05939313,0.08315189,0.04250804,-0.01290607,-0.05266894,-0.01041718,-0.02809229,0.0035157,0.02172986,0.00503224,-0.03703293,-0.01638035,-0.00650434,0.04686517,0.0053232,-0.09533999,0.01419091,0.01910377,0.12613802,-0.07428385,0.05926159,0.04772095,-0.01671838,-0.03054746,-0.02444815,-0.07524068,0.07831659,-0.0272797,0.01469613,-0.03276589,0.04419114,-0.02521859,-0.04140306,-0.01929622,-0.02145314,-0.05996023,-0.0522384,-0.00429396,-0.02903119,-0.03591506,-0.04158119,0.01350101,0.03196867,-0.03461632,0.0908782,0.05154666,0.01852809,-0.02248777,-0.05732857,-0.02649777,-0.01235796,0.03573781,0.02768894,-0.02649298,-0.04698544,-0.00314143,0.0557784,-0.02657885,0.00114148,-0.01111378,0.01473496,-0.02599017,-0.00256693,0.13760138,0.03712073,-0.0990923,0.02138838,0.01562058,-0.06635319,-0.05241543,0.05406926,-0.0365086,0.00442356,-0.02880505,0.04556119,0.04145934,0.03572085,0.02786424,0.06577767,0.02872134,-0.00778017,-0.02831474,-0.03923156,0.02846062,-0.02676089,-0.05632678,0.01880406,0.022008,-0.26210877,0.00163267,0.02326252,-0.02944091,0.03339202,-0.012262,0.07584568,-0.1109358,0.05184042,-0.00261031,-0.01943554,0.09205354,0.00901521,-0.10384832,0.02743482,0.06472467,0.0975276,-0.00858826,0.03679383,0.00911019,0.0478688,0.04007376,0.22767366,-0.00733504,0.02842708,0.01256611,0.00543988,-0.01115964,0.03650971,0.06500527,0.05421587,-0.00435799,0.10899934,-0.02259418,0.01254133,0.06670499,0.01969404,0.0241588,0.00119374,0.0271353,0.05756708,-0.03622729,-0.03474194,0.03791969,0.0990769,0.01677162,-0.04081147,-0.1282407,-0.00618302,0.05274316,-0.08102304,-0.00701256,-0.01302161,-0.00119695,-0.02245232,0.01539861,-0.05013764,-0.02130351,-0.03284339,-0.01864592,0.01553692,-0.05060501,0.00357295,0.06351516,0.01516677],"last_embed":{"hash":"16ee11bcd0a65911777646751881a3a7c3b61e1951219d7c322152acf3e05090","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"16ee11bcd0a65911777646751881a3a7c3b61e1951219d7c322152acf3e05090","at":1743662881280},"key":"37 Tips from a Senior Frontend Developer.md#3. Get familiar with Data Structures & Algorithms#{1}","lines":[41,52],"size":483,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#4. Learn by doing rather than reading/watching": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03222813,-0.0239883,0.00247916,-0.07226729,0.00947408,0.01144191,-0.01088948,-0.03066624,0.0012021,0.00320862,-0.00440199,0.02737849,0.02090715,0.03107498,-0.00795269,0.01995523,-0.03615172,0.05748512,-0.05666079,-0.00599418,-0.031276,0.03601244,-0.01699521,-0.03259853,0.05624478,0.01080752,-0.02741513,-0.08688603,-0.02915669,-0.153708,-0.03019255,0.00076939,-0.01557217,-0.01898548,-0.01816191,0.01560101,-0.02865309,0.04225912,-0.02478834,-0.01805795,0.03129676,0.01570534,-0.01118226,-0.0703037,0.00043305,-0.02144231,0.02421998,-0.04015689,0.07580491,-0.04134022,-0.04246883,-0.05643303,0.0018917,-0.01353529,-0.00706013,0.02738067,0.02328415,0.05242038,0.0075753,0.04214342,0.05509147,0.00987919,-0.16847624,0.08880133,-0.02785944,0.04256891,-0.00180126,0.00935246,0.03667486,0.06556257,-0.02200724,0.00477606,-0.03464223,0.08335217,0.06412506,-0.00354496,-0.02168639,-0.00805763,0.04777149,0.00488147,0.03355136,0.00905781,0.0112175,-0.00521376,-0.00947985,-0.01454378,0.05005191,-0.03957921,0.06584337,0.00195952,0.02935509,-0.05476318,-0.02960666,0.01675037,0.06147411,-0.00393318,0.04915101,0.02722364,-0.1009749,0.12666997,-0.06940817,0.05910731,0.05479686,0.03710298,0.01729505,-0.04860803,0.0538407,0.03546421,0.01499903,0.029814,-0.04213604,-0.03687687,0.02410565,-0.03204915,-0.00715946,0.0424564,0.00234261,0.01776317,-0.02001005,0.03642898,0.03172253,0.01040084,0.0623384,-0.01406353,0.02616942,-0.08452369,0.0027325,0.08004712,-0.02349226,0.0558329,0.02927501,-0.00028238,-0.10190843,0.04153405,0.01634622,0.01817507,0.01311056,-0.01633175,0.03094844,0.00316967,-0.02216305,0.02513783,0.00195119,-0.08202484,-0.02133962,0.0684949,-0.00863531,0.07153908,0.01789695,-0.03146364,0.0086238,0.02111223,-0.03084627,-0.04239872,0.02566921,-0.00291085,0.1216037,-0.00189442,-0.06016008,-0.00140258,-0.03401547,-0.07072975,-0.06423514,0.00623842,0.04402885,-0.02919104,-0.02283794,-0.02206812,0.00935007,-0.03093872,0.002744,-0.01316592,-0.03692948,-0.03579868,0.14096665,-0.02646159,-0.02571564,-0.05629015,0.05007036,0.03332721,0.09038332,-0.01766885,0.01062697,0.02680732,0.00798583,-0.00885271,0.03973751,-0.05048669,0.04835027,0.01733572,-0.04907691,-0.00591244,-0.09143724,-0.01025977,-0.03474277,0.00394152,-0.02014256,-0.03237871,0.01195793,-0.01005541,-0.05694832,0.01274315,-0.01118453,0.02189994,-0.07711359,0.06861892,0.07066417,-0.06772558,0.1506785,-0.00742085,-0.10108347,-0.00536708,0.09347638,-0.01604019,-0.01286303,-0.00152571,0.01711303,0.11983167,-0.07703189,0.04583102,0.04699436,0.0578954,-0.05796413,-0.22500671,-0.0324811,0.06137978,-0.08594577,0.04637885,-0.02518984,0.08804891,0.0348892,0.01526089,0.03979693,0.06859258,-0.08070726,-0.03036738,-0.0041909,0.0298568,0.00007483,0.00070799,0.03406438,-0.04634371,0.01921955,-0.0163839,-0.02397009,-0.00665214,-0.13760939,0.05522968,0.0451408,0.09147573,0.01530763,0.0293143,-0.09062368,0.03036335,-0.022977,0.01829633,-0.11948307,0.0561439,-0.03941553,0.03559196,-0.04641246,-0.01419815,-0.05497279,0.04323266,0.03563243,0.00129625,-0.09420801,-0.11426048,-0.0184099,-0.0498908,-0.02848434,-0.00764742,-0.01785275,0.01216219,-0.01582607,0.05168984,0.04022802,-0.08877945,-0.02429125,-0.08298805,0.05194739,-0.05210319,0.06251505,-0.0303268,-0.02658382,0.01879332,-0.03609431,0.0474593,0.00841823,-0.03253746,0.02358247,0.12048726,-0.02942107,-0.01917268,0.11550529,0.04703743,-0.01619772,-0.03410309,-0.00419071,-0.01475681,-0.02040595,0.02323963,0.00124041,-0.03975742,-0.11071336,0.05360207,0.03973624,0.02187678,-0.02467602,0.04053623,-0.07700386,0.01169393,-0.03461562,-0.0775127,0.0708487,-0.07490227,-0.05260069,-0.01853394,-0.01078821,-0.20154387,0.01080614,0.05517209,0.01105752,-0.04762455,0.017166,0.06885368,-0.06683207,-0.04132193,0.01842209,-0.02365439,0.00593387,0.04162799,-0.04990947,0.02436112,0.03709852,0.08270177,0.00074199,0.04949265,-0.09400291,0.02318571,0.07299682,0.19443086,0.01691636,0.05338407,0.02741904,-0.04945192,0.02979355,0.10507947,-0.00391925,0.03211576,0.00190665,0.01573401,0.01882713,-0.00093834,0.04596243,-0.02710163,0.05504276,-0.01751021,-0.00322185,0.0610429,-0.03353304,-0.00007491,0.00137686,0.03579932,-0.02580661,0.04512561,-0.08641412,-0.07099804,-0.0407435,-0.05476596,0.02589368,0.02744544,0.01701375,0.01299459,0.06584246,0.00094898,-0.01224812,-0.04527325,-0.03143406,0.0324329,-0.01947847,0.08750117,0.04286636,0.06319741],"last_embed":{"hash":"6f0d5bed0549a271001b98d2ea83abb9d76868a4c1aaf59bc476a6b6c613c5f5","tokens":79}}},"text":null,"length":0,"last_read":{"hash":"6f0d5bed0549a271001b98d2ea83abb9d76868a4c1aaf59bc476a6b6c613c5f5","at":1743662881293},"key":"37 Tips from a Senior Frontend Developer.md#4. Learn by doing rather than reading/watching","lines":[53,62],"size":319,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#4. Learn by doing rather than reading/watching#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0301786,-0.02521734,0.004281,-0.06122084,0.00828219,0.01887642,-0.01095199,-0.03489845,0.00273301,0.0056578,0.0054926,0.02576139,0.01613012,0.03858012,-0.00763902,0.01454984,-0.04380454,0.05259926,-0.06037942,-0.00961856,-0.02509527,0.04054515,-0.0103211,-0.03056768,0.05414785,0.01636385,-0.0217321,-0.08788539,-0.02473897,-0.15204681,-0.03269023,-0.00562143,-0.01529296,-0.01327417,-0.01978458,0.01116511,-0.03963454,0.04371323,-0.02334638,-0.01691417,0.03524566,0.00901725,-0.01531886,-0.07410883,0.00152449,-0.02187553,0.03530138,-0.03285477,0.08593082,-0.03933856,-0.04278918,-0.05470996,0.00994343,-0.01374976,-0.00943647,0.02469031,0.0267815,0.05671097,0.01120976,0.03280976,0.05775389,0.01895382,-0.16720752,0.09041931,-0.0387139,0.04295307,-0.00247577,0.01214352,0.0383397,0.07037848,-0.02142251,0.00924851,-0.03828765,0.08515386,0.07263073,-0.01014671,-0.01770047,-0.00798379,0.04017053,0.00504913,0.03579953,0.00820403,0.01328511,-0.01644032,-0.0095125,-0.01685179,0.04771885,-0.05428958,0.0637325,0.00358019,0.02434766,-0.04709355,-0.04404888,0.02555852,0.0637174,-0.00584194,0.04138384,0.0347144,-0.09289937,0.12025376,-0.07278199,0.06637672,0.06047349,0.03767322,0.01785822,-0.0513554,0.05395845,0.03720599,0.01849023,0.02992545,-0.03705535,-0.03391254,0.02028953,-0.03497771,-0.01166472,0.0510723,0.00310022,0.01075066,-0.02256422,0.03059918,0.03168958,0.01360057,0.06874286,-0.01676642,0.03062965,-0.09677672,0.00651387,0.08039784,-0.02351306,0.05158156,0.0289539,-0.00846204,-0.10068583,0.04455924,0.01423293,0.0173537,0.01432113,-0.00979227,0.03582752,-0.00030883,-0.01779423,0.02601718,0.00272482,-0.0949057,-0.02378392,0.06783728,-0.01247337,0.07184668,0.01519806,-0.03700086,0.00021466,0.01891169,-0.0377765,-0.04019914,0.01898851,-0.00702357,0.13042367,-0.00075563,-0.06056677,-0.00256231,-0.04475897,-0.06313341,-0.06472715,0.00053984,0.05194838,-0.0106233,-0.02157244,-0.02661823,0.00854841,-0.03540923,-0.0014664,-0.01381057,-0.0400883,-0.02436646,0.13722877,-0.02837448,-0.03041149,-0.05973442,0.04582081,0.0299245,0.0968008,-0.02599209,0.01187419,0.03087224,0.01223984,0.00304201,0.03629291,-0.0474703,0.04927291,0.01359962,-0.04491636,-0.00870202,-0.09000441,-0.01515092,-0.03056016,0.00174459,-0.02002353,-0.03188121,-0.00018496,-0.00505771,-0.06429978,0.00181985,-0.00350802,0.01994484,-0.0748537,0.07039884,0.0662908,-0.06322139,0.15406981,-0.01101546,-0.106282,-0.00047082,0.08724803,-0.02545592,-0.01218911,-0.00313124,0.02121422,0.11545231,-0.07649828,0.04868441,0.04052491,0.05439221,-0.05088773,-0.222021,-0.03696309,0.06312228,-0.08272832,0.05229148,-0.02787334,0.08202481,0.04387433,0.01858144,0.03757767,0.05836962,-0.08524562,-0.02537799,-0.00230198,0.02900096,-0.00756654,-0.00490394,0.03756118,-0.04577934,0.02898992,-0.02529061,-0.0170316,-0.0029541,-0.14149925,0.05233499,0.04555621,0.09284848,0.01623425,0.02749126,-0.07986413,0.02843845,-0.02320245,0.01163317,-0.12155256,0.0521534,-0.03910912,0.04354249,-0.04395242,-0.0123915,-0.06408393,0.0435228,0.03456113,-0.00594767,-0.09391724,-0.10925897,-0.02031005,-0.04090063,-0.01972841,-0.01432069,-0.02364172,0.01472939,-0.01417367,0.05313118,0.03575553,-0.09274944,-0.0222974,-0.08355761,0.05285228,-0.05592767,0.05898501,-0.03056163,-0.01864169,0.01986993,-0.03976624,0.051473,0.01274682,-0.02429215,0.02430825,0.11420672,-0.01712179,-0.01349118,0.11757199,0.04667208,-0.01905963,-0.03225982,-0.00275327,-0.01103301,-0.02021488,0.02033045,0.00060121,-0.03420497,-0.11591571,0.04545093,0.03868465,0.02211971,-0.03059033,0.03396329,-0.08006329,0.01356061,-0.03503782,-0.07042288,0.06916662,-0.07026295,-0.04564823,-0.01697634,-0.00751694,-0.20207873,0.01225708,0.05298393,0.02055281,-0.05283363,0.01188325,0.06485461,-0.06288063,-0.04090177,0.01746196,-0.0236932,0.00014788,0.04208022,-0.04424268,0.01486833,0.03678676,0.08611856,-0.00163603,0.05365598,-0.08542954,0.03008038,0.07357256,0.19148906,0.01666311,0.06199848,0.01696165,-0.04850421,0.02751938,0.09643649,-0.00952479,0.0304408,-0.00717665,0.01446517,0.01921359,-0.00279132,0.04799118,-0.02757599,0.0580879,-0.01282015,-0.00779093,0.06359037,-0.03715069,-0.00168719,-0.00210462,0.04279974,-0.02497459,0.04817165,-0.08740382,-0.06899363,-0.03488315,-0.0451035,0.02682064,0.03086902,0.02004312,0.01381163,0.06463274,-0.00416854,-0.00916118,-0.04511669,-0.03544447,0.03004682,-0.02077549,0.09010698,0.03471248,0.05552255],"last_embed":{"hash":"cc9f6a1766d38f05ba984ec06674c7546ec8c25578fb7d4aa616d6e86db60f59","tokens":79}}},"text":null,"length":0,"last_read":{"hash":"cc9f6a1766d38f05ba984ec06674c7546ec8c25578fb7d4aa616d6e86db60f59","at":1743662881306},"key":"37 Tips from a Senior Frontend Developer.md#4. Learn by doing rather than reading/watching#{1}","lines":[55,62],"size":269,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#5. Ask for help when stuck": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04438887,-0.02135278,0.03456269,-0.07382509,0.03814049,-0.02116135,0.0319212,-0.02235299,-0.03330168,-0.02198892,-0.01295484,0.0161101,0.01796007,0.00539557,-0.00448978,0.02047752,-0.00016107,0.06303787,-0.06020664,0.00908096,-0.04545778,-0.01911054,-0.03667927,-0.00559578,0.02894633,-0.03855235,-0.01738076,-0.10384647,-0.07432164,-0.14335871,-0.02035499,-0.02619074,-0.0024668,-0.02497089,0.01870455,0.04372289,0.02942269,0.03410921,-0.01325541,0.00895075,0.05546351,0.04930488,-0.00047262,-0.05793736,-0.01084901,-0.04271282,-0.01588753,-0.05379691,0.02226727,-0.07083179,0.01934609,-0.04169429,-0.05148493,-0.03171039,-0.00230023,0.0612946,0.02368333,0.04252842,-0.01514238,0.06386109,0.06146282,-0.03613789,-0.15970269,0.07080738,0.05821348,0.02096921,0.00804766,-0.0246098,-0.01350147,0.05419504,-0.02355595,-0.01462531,-0.00861118,0.08342499,0.03150267,0.01371729,-0.00759319,-0.02746599,0.06595126,0.04296361,0.00574408,0.05597918,-0.03175465,0.03670568,-0.01110473,-0.01443573,0.07224431,0.05015503,0.06569288,0.0108669,0.06116437,-0.03445545,0.02295381,-0.00998566,0.01702829,-0.0234533,0.08492568,-0.00810268,-0.17429741,0.16239524,-0.05167796,0.04076882,0.03069235,-0.00044461,0.05308186,0.00666884,0.05085773,0.02900762,0.01296062,0.03804253,-0.00555138,-0.01938333,0.02057172,-0.0316781,0.03213458,0.03067734,-0.00528925,0.03737096,-0.02659156,-0.01236302,0.0317371,0.01401514,0.03642365,0.00166189,-0.00351883,-0.05547559,0.00697736,0.08742877,-0.03550474,0.04716818,0.01514823,-0.01792239,-0.08869345,-0.02233796,-0.02687948,-0.00645121,0.01465377,0.0026322,0.04609147,-0.01331081,-0.00975134,-0.09924673,0.00062158,-0.03553614,-0.01518849,0.10245413,-0.00964278,0.07724664,-0.0037095,-0.05907664,0.00768557,0.0209933,-0.02206,-0.0335853,0.04446315,0.02847905,0.05431023,0.00546771,-0.04005339,0.02379587,-0.00054346,-0.08933788,-0.07641154,0.0236016,0.00375119,-0.0806344,-0.0528004,-0.01875521,-0.00276307,0.00729583,-0.02134759,0.0201069,-0.01741569,-0.01936023,0.1482075,-0.00564745,-0.03540451,-0.01429649,0.05257066,0.05969127,-0.00102299,0.03504165,0.03204136,-0.00347354,-0.00068446,-0.08750267,0.02790451,-0.04957828,0.04233548,-0.0035468,-0.09034456,-0.00529167,-0.04311384,0.0071621,-0.02893807,-0.00645904,-0.03650294,0.00137074,0.05633881,-0.00771076,-0.00604374,-0.00900863,-0.00732078,0.02341454,-0.07445254,0.05747109,0.04934171,-0.09191841,0.13324498,-0.0152252,-0.05876479,-0.01128059,0.11522771,0.02643505,-0.01125077,0.01359258,0.02122018,0.07154124,-0.02145399,0.04826459,0.0894955,0.07668905,-0.0548031,-0.23096171,-0.0075182,-0.01499613,-0.06204779,0.02968876,-0.02269076,0.07984116,-0.01340368,-0.02526812,0.10935195,0.11124748,-0.06777675,-0.04770159,0.01791015,0.04010701,0.04090533,0.01797199,0.00780895,-0.04295485,-0.04210341,0.0141007,-0.06047821,0.01686669,-0.08373961,0.03020561,0.05618436,0.15479223,0.00720605,-0.01086566,-0.12845659,-0.00460255,-0.05262214,-0.00008083,-0.12055264,0.07235317,-0.01193813,-0.03484224,-0.06134573,0.00417449,-0.01784413,-0.01852714,0.02091321,0.01685661,-0.0515967,-0.07797317,0.01513552,-0.02590399,-0.03013985,0.002983,-0.01923991,0.01512657,-0.02432219,-0.0029376,0.00160216,0.005949,-0.02073826,-0.05087807,0.00158444,-0.00872037,0.0892861,-0.04090079,-0.02962569,0.05691122,-0.03196221,0.06001803,-0.0124161,-0.05952411,0.0208658,0.09081816,-0.05009662,-0.01209205,0.09450126,-0.02525696,0.01754256,-0.00861792,0.01163478,-0.01896703,-0.02123049,0.06049009,0.01835332,-0.00065642,-0.11752137,0.04866822,0.02932786,0.04575557,0.00375552,-0.01053736,-0.00553862,0.02489375,-0.01760718,-0.05094638,0.03085694,-0.07609721,-0.05043716,0.01941371,-0.05045135,-0.20937902,0.04387968,0.03938844,-0.10176256,0.00540256,0.05571586,0.08175389,-0.03396519,-0.01289079,0.04228208,-0.01819706,0.07421636,0.01212074,-0.07685309,0.04741272,0.00346601,0.04124994,-0.00877649,-0.01344465,-0.10007871,0.022494,0.04890944,0.20170666,0.03402556,0.03382194,0.04026519,-0.01940235,0.00182107,0.10552943,-0.03246773,0.01232246,0.01016208,0.01784831,-0.04843336,0.03840083,0.01091981,-0.05907868,0.04694336,-0.00849921,-0.01192872,0.02453495,-0.03168603,-0.00709719,-0.0115987,0.06462286,-0.02245794,0.00629666,-0.03641446,-0.05001188,-0.02387045,-0.08247988,0.00835494,0.03355693,-0.00851038,0.02900033,0.03684285,0.01540514,-0.01539848,0.00143807,0.01024202,0.00599133,-0.00119224,0.07051735,0.03881993,0.05714888],"last_embed":{"hash":"40dd06b432e27b1fe490869585e20611115274a740865668aeb93bf8cfd3558c","tokens":82}}},"text":null,"length":0,"last_read":{"hash":"40dd06b432e27b1fe490869585e20611115274a740865668aeb93bf8cfd3558c","at":1743662881317},"key":"37 Tips from a Senior Frontend Developer.md#5. Ask for help when stuck","lines":[63,72],"size":306,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#5. Ask for help when stuck#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04056725,-0.02364841,0.03708014,-0.06725067,0.03487807,-0.02116493,0.03441279,-0.02447213,-0.03651989,-0.02589971,-0.01028217,0.01702799,0.01644363,0.00924789,-0.00401611,0.02181399,-0.00174574,0.06504947,-0.06304958,0.01066871,-0.04704037,-0.01727402,-0.0388498,-0.00207836,0.02492863,-0.0402421,-0.01406542,-0.10910291,-0.07348923,-0.14015642,-0.02246772,-0.02803127,-0.00687219,-0.02276466,0.01860116,0.04704719,0.0294397,0.03592234,-0.0094026,0.01241052,0.05838146,0.05108717,0.00060539,-0.06204192,-0.00830679,-0.04196529,-0.01463358,-0.05499604,0.02694267,-0.07001595,0.01850301,-0.04095913,-0.05212576,-0.02835002,-0.00056647,0.06367008,0.02663144,0.04659184,-0.01464966,0.06168611,0.06279708,-0.03579976,-0.15879171,0.07367869,0.05885956,0.018588,0.01482612,-0.02080053,-0.01655756,0.05512663,-0.02097661,-0.01041904,-0.01015058,0.08668898,0.03378031,0.01571364,-0.0040007,-0.02919891,0.06586515,0.04075439,0.00222782,0.05868264,-0.03132265,0.0317284,-0.01466071,-0.01186585,0.07119431,0.04453237,0.06394143,0.01353941,0.05772939,-0.02301026,0.02188089,-0.00541356,0.01644626,-0.02882395,0.08355437,-0.00622838,-0.18211627,0.15980224,-0.04824354,0.04108842,0.02992485,-0.00145864,0.0497039,0.0068666,0.05251692,0.03168706,0.01324568,0.03470257,0.00022551,-0.01803307,0.01973702,-0.02879174,0.027947,0.03794908,-0.00483822,0.0347617,-0.02860295,-0.01619625,0.02866609,0.01823797,0.03969149,0.00264101,-0.00448775,-0.06049106,0.0049111,0.08916309,-0.03380795,0.04394308,0.02060459,-0.02416671,-0.0885922,-0.02413884,-0.03356476,-0.00775789,0.01569528,0.00490743,0.04918152,-0.01880201,-0.00325082,-0.10868196,0.00347673,-0.03542387,-0.01505128,0.09802903,-0.01701526,0.08088331,-0.00666092,-0.06302686,0.00431639,0.01718436,-0.02756535,-0.03462272,0.04299648,0.02736392,0.06084636,0.00382447,-0.04157476,0.02598811,-0.0018571,-0.08934898,-0.07570205,0.02102373,0.00679577,-0.07165899,-0.05823359,-0.01870828,-0.01003192,0.00648662,-0.01951717,0.02721467,-0.0177564,-0.01071031,0.14460151,-0.00929491,-0.03671236,-0.0147481,0.04816013,0.06055686,-0.00578076,0.0381335,0.03422577,-0.00401689,-0.00048513,-0.08462815,0.02390588,-0.04934001,0.04465263,-0.00647793,-0.08688755,-0.00234555,-0.03644332,0.00784418,-0.02832061,-0.01058669,-0.04155185,-0.00019714,0.04979573,-0.0078322,-0.00211378,-0.01701607,-0.00553728,0.0234335,-0.07191312,0.05774258,0.04668205,-0.09019086,0.13500752,-0.01707793,-0.0582478,-0.01003866,0.11712139,0.02183163,-0.01348768,0.01677252,0.02258015,0.06328023,-0.01967395,0.05054246,0.08923109,0.07012498,-0.04917909,-0.22792698,-0.00905351,-0.0186808,-0.05582248,0.02902301,-0.02291285,0.07945476,-0.00750336,-0.02721328,0.11451264,0.11139873,-0.06783144,-0.04618155,0.02320612,0.0401063,0.03613153,0.01866562,0.00920049,-0.04294399,-0.04154982,0.01062505,-0.0608344,0.01957474,-0.07787958,0.02734581,0.05586742,0.15804242,0.00453496,-0.01747805,-0.13248247,-0.0068361,-0.04936334,-0.00097553,-0.12507299,0.07136098,-0.01477263,-0.04038536,-0.06135032,0.00984612,-0.01957839,-0.02182216,0.01855327,0.01513595,-0.0466543,-0.07379771,0.01525139,-0.01983884,-0.02306079,-0.00150795,-0.02140385,0.01686097,-0.02253458,-0.00666424,-0.00320418,0.00511959,-0.01270666,-0.04609653,-0.00103764,-0.00688373,0.09015588,-0.04473541,-0.02585248,0.06189804,-0.03523717,0.06151668,-0.01630463,-0.05901667,0.02314804,0.08713005,-0.04484359,-0.01070767,0.09480283,-0.03034104,0.02296045,-0.00895627,0.01684686,-0.01428428,-0.02491253,0.05687561,0.01208015,0.00364995,-0.12448505,0.04497284,0.02956001,0.0482381,0.00206456,-0.01573155,-0.00442988,0.02926483,-0.01337318,-0.04153955,0.02784889,-0.0775056,-0.04541466,0.0207528,-0.05587655,-0.20898016,0.0486107,0.03735609,-0.10427713,0.00766866,0.05620831,0.07585686,-0.02892797,-0.01300086,0.04284045,-0.01819314,0.07445674,0.01037933,-0.07872588,0.04162706,0.00115142,0.04009422,-0.01244937,-0.01868014,-0.09987767,0.02477844,0.05250498,0.19693224,0.02995302,0.03527141,0.03699499,-0.01626983,-0.00131233,0.10422964,-0.03776441,0.00938274,0.00734145,0.01824357,-0.05036925,0.04340114,0.0102296,-0.06528603,0.0518172,-0.00355603,-0.0141853,0.02324899,-0.03003448,-0.0082795,-0.01612893,0.0682077,-0.01694405,0.00415659,-0.03589816,-0.05227142,-0.01683557,-0.07791129,0.0077176,0.03640113,-0.00786265,0.02856768,0.03483574,0.01344935,-0.01213953,0.00226982,0.01205651,0.00550297,-0.00503238,0.07410113,0.03150111,0.05304177],"last_embed":{"hash":"b623428b4643e06f792e054f08339f9f7684a64c78206f4aeba9369f8ced378c","tokens":82}}},"text":null,"length":0,"last_read":{"hash":"b623428b4643e06f792e054f08339f9f7684a64c78206f4aeba9369f8ced378c","at":1743662881330},"key":"37 Tips from a Senior Frontend Developer.md#5. Ask for help when stuck#{1}","lines":[65,72],"size":276,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#6. Ask for help the proper way": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01749181,-0.02509123,0.00215672,-0.08453751,-0.00973875,-0.01092597,0.02047288,0.00807964,-0.02158929,-0.05276924,-0.01162052,0.01295195,0.01330217,0.02063894,0.0283082,0.06705794,0.01422738,0.04501488,-0.02958266,0.03383736,-0.01814675,0.00595503,-0.03344487,0.02553094,0.02856226,-0.01778091,-0.02604247,-0.10323196,-0.07533304,-0.17874566,-0.00863248,-0.02770619,0.02500995,-0.05086971,-0.00527957,0.04414461,-0.00160787,0.03486916,-0.02383327,0.01089682,0.07344888,0.03395228,-0.02217301,-0.05726574,0.00942066,-0.02042696,-0.01295988,-0.03470154,0.03539314,-0.08905865,0.00032745,-0.02649616,-0.01296902,-0.05451774,-0.02094021,0.04874171,0.03829701,0.06139131,0.01273025,0.06228798,0.07563757,-0.03605052,-0.18349342,0.09252409,0.03812166,0.02529169,0.00578312,-0.00701203,-0.01128441,0.06762443,-0.02753242,-0.0084089,-0.03741446,0.09821973,0.00463979,0.02609697,-0.015088,-0.03400466,0.06726393,0.00365412,-0.01985016,0.04100734,0.00606838,0.01565305,0.01571974,-0.04247861,0.0796743,0.04994397,0.06009042,0.02625634,0.08937446,-0.0271394,0.02726091,-0.00381845,-0.01518269,-0.00555794,0.04840648,0.02800119,-0.17672542,0.14364757,-0.03254344,0.04435712,0.04414176,-0.0062941,0.02943194,-0.03933357,0.035071,0.0115917,0.00457387,0.03098304,0.00287114,-0.01288567,0.03205634,-0.02618102,0.03146738,0.0125747,0.02698077,-0.01281849,-0.03877406,-0.05094145,0.020497,0.02118719,0.05627923,-0.01613007,-0.00582255,-0.04511034,-0.00998544,0.07403767,-0.01121608,0.05359682,0.04159913,-0.0105393,-0.1154931,-0.02395521,-0.02869843,0.01200183,0.02824953,-0.012471,0.02915459,-0.03433631,-0.01323016,-0.10474884,-0.00337684,-0.0213494,-0.03192752,0.1066909,-0.06503893,0.05749312,-0.02694407,-0.10815512,0.03020586,0.0149958,-0.02991588,-0.03762954,0.04821933,0.02013966,0.08660817,0.00222426,-0.03941203,0.002622,0.01429374,-0.03625841,-0.09391063,0.0534947,0.02592902,-0.09079438,-0.05549425,-0.0315949,-0.02477827,-0.01055958,0.01476308,0.01306003,-0.00401758,-0.05442754,0.07918914,-0.03099195,-0.06158929,-0.00471064,0.03085531,0.04380054,0.03689205,0.0377609,0.02337466,-0.00416589,-0.01010625,-0.09562982,0.05488443,-0.06879357,0.05045607,-0.02815575,-0.06637558,-0.02885021,-0.01024105,0.01426818,-0.03052642,0.02002774,-0.06485702,-0.05825569,0.05741219,-0.02748448,0.06131506,0.02332656,0.00339552,0.03083453,-0.06794717,0.0657894,0.03515981,-0.0701733,0.13806161,0.00322239,-0.07038348,-0.00141951,0.09933413,0.02740603,-0.01883994,0.01902949,-0.00018482,0.08082724,-0.02681109,0.03153677,0.08195995,0.02519948,-0.05708811,-0.21989881,-0.03748902,-0.00652051,-0.02606837,0.00400257,-0.04246569,0.08960626,0.00625623,-0.02590856,0.11219592,0.13362686,-0.00946941,-0.04343865,-0.00936757,-0.00565789,0.02900947,0.04381923,0.03806644,-0.00815848,-0.01267646,-0.01857862,-0.04203913,0.01398747,-0.10242238,0.04456572,0.04579791,0.12138365,0.03795946,-0.01965818,-0.09359284,0.01162602,-0.02391258,-0.00142474,-0.15051883,0.11414167,-0.0176065,-0.02807461,-0.02315449,0.01971146,-0.04067016,-0.04381337,-0.01559956,-0.02239356,-0.03792369,-0.07430461,0.03840454,-0.04371857,-0.07063823,-0.05925364,0.01275291,-0.0259016,0.02059824,0.01245303,0.03111648,-0.00437553,-0.01656402,-0.0678904,-0.01709913,0.00279984,0.04791011,-0.04797283,-0.01792158,0.03715019,-0.03252546,0.04721297,-0.01454424,-0.05900494,0.03638748,0.0823271,-0.03858265,-0.01817603,0.09251162,-0.01883209,0.06520235,-0.02524177,0.03359884,0.03152736,-0.07866269,0.05254541,-0.04768777,-0.02306712,-0.07145528,0.04318859,-0.00524824,0.04242691,0.04219346,-0.02115352,0.01299859,0.07750788,-0.02112865,-0.02913426,0.02590047,-0.05769368,-0.04495822,0.04184956,-0.01578045,-0.17926739,0.021097,0.0411454,-0.03396251,-0.05848973,0.03628297,0.08662266,-0.01763171,-0.05344677,0.0462325,-0.02217823,0.04623355,0.01248661,-0.06779743,0.01738803,0.0022206,-0.00892107,0.00317209,0.01145311,-0.09352744,0.03414497,0.01533473,0.17955443,0.03329816,0.03950156,0.0223821,0.03874638,-0.01330868,0.10540095,-0.01555628,0.0136179,-0.00678307,0.03808958,-0.00660087,0.0445677,-0.00189285,-0.02470476,0.02107166,-0.00112377,-0.01533828,0.03053522,0.00952538,0.02817465,-0.01386075,0.07412302,-0.02221113,0.02775509,-0.01632551,-0.06968576,-0.01117763,-0.05783188,-0.017323,0.04680619,-0.02133949,0.00580464,0.00938901,0.01872986,-0.00255115,-0.01264772,0.02046945,0.01400556,-0.01881463,0.11066009,0.03173233,0.06329833],"last_embed":{"hash":"e16e4460b006b5d7ef9dc8da3213d7b93d952ecd5802cadf8f23c2aed280cc3a","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"e16e4460b006b5d7ef9dc8da3213d7b93d952ecd5802cadf8f23c2aed280cc3a","at":1743662881343},"key":"37 Tips from a Senior Frontend Developer.md#6. Ask for help the proper way","lines":[73,86],"size":392,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#7. Don't copy/paste code you don't understand": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0398871,-0.01762048,-0.05406372,-0.07202249,0.00189861,-0.0447945,0.03556534,0.03199574,-0.0314341,-0.00201617,-0.0280406,-0.00603536,0.03169574,0.01211775,0.01152644,0.0428643,0.00755581,0.00272518,-0.05404695,-0.00171673,0.00146726,-0.03037972,-0.01769896,-0.03370052,0.04702201,0.02861168,-0.04301225,-0.04531576,-0.06276736,-0.18949839,-0.00126806,-0.03059649,0.02397261,-0.03989277,0.02716685,0.01181745,0.0056219,0.02603889,-0.01093253,0.00986584,0.01753103,0.00823086,0.00804832,-0.04066488,0.01659415,-0.08309133,-0.01895681,-0.04651383,0.01355323,-0.02636817,0.0163961,-0.01683646,-0.02632621,-0.01367801,-0.03329299,0.03305179,0.02985275,0.09158391,-0.03447366,0.04217189,-0.00721933,-0.01218105,-0.15667817,0.1003307,0.05008732,0.08747129,-0.02049368,0.00371569,0.01069399,0.00356223,-0.02776696,0.02602707,-0.01375659,0.08810367,0.03165943,0.00655667,0.00877123,0.02385284,-0.001955,0.05503071,-0.01019341,0.00150376,0.03032923,0.0478046,0.00036531,-0.04653725,0.07358737,0.03540277,0.06046047,-0.03416484,0.03739665,-0.07036668,0.08491035,-0.00551822,-0.01442645,0.03519675,0.05055579,0.03815035,-0.10649349,0.13098453,-0.06158779,0.02139195,-0.04013264,0.02250304,0.05248813,-0.04457811,0.04627019,-0.01869664,-0.02031777,0.02192689,-0.03730975,0.00801667,0.05770325,-0.0077249,0.0351932,-0.01085981,0.07085307,-0.01400848,0.00817502,0.05234918,0.03197279,0.03462196,0.04027661,-0.00784517,0.04037391,0.01045058,-0.01857303,0.03682286,0.00247741,0.05854031,0.01383234,0.035018,-0.04215612,0.0172402,0.00660624,0.017077,0.00017593,0.01261369,0.03331075,-0.0548427,0.01429508,-0.0636121,0.04247546,0.01671261,-0.01407921,0.06612983,-0.03260003,0.00906331,-0.00099099,-0.07214118,0.00151915,0.02623816,-0.00967955,-0.0338644,0.04953079,-0.01177183,0.05825444,0.03973217,-0.04825285,0.00059666,0.07632077,-0.03513343,-0.09125187,0.06338702,0.0243755,-0.06950051,-0.04529563,-0.00272415,0.02378929,-0.0568138,0.00827856,-0.01398532,-0.01335303,-0.02593045,0.03534202,-0.04534369,-0.06304061,0.02020289,0.04883428,0.05311786,0.00345921,-0.01348858,-0.00757242,0.03455758,-0.00098704,-0.10706856,0.02567386,-0.02094756,0.08149762,0.02759179,-0.06987952,-0.0146428,-0.08509624,0.04893001,-0.08519209,-0.0033816,-0.03218792,-0.00461771,0.03615395,-0.05132653,0.03248259,0.02114861,0.01296564,0.03359415,-0.10163753,0.00964107,0.05995786,-0.11695749,0.11777778,0.01132296,-0.05414214,0.02211778,0.03792034,0.01186149,-0.00861837,0.00162488,0.00371592,0.11176267,0.04279553,0.01859123,0.0105413,0.07283621,-0.04835991,-0.25084108,-0.08315355,0.01395624,-0.01844764,0.01591879,-0.08578714,0.09537639,-0.01447233,0.04530304,0.05752054,0.14995253,0.02506784,-0.05873936,-0.0588453,-0.02925026,0.04190723,0.01539052,0.02121973,-0.00237391,-0.03261551,-0.02612768,0.00740159,-0.02698751,-0.11200731,0.07042909,-0.00236256,0.14345782,0.00824101,0.02362908,-0.0276721,0.01109796,-0.00742555,0.04086339,-0.1766205,0.10033866,0.02461846,-0.00783202,0.00990074,0.02425686,-0.02531528,-0.00597021,-0.00450374,-0.0194734,-0.10626298,-0.02163994,-0.0175323,-0.07197835,-0.08766086,-0.04301808,0.03389908,0.02958882,0.02672547,0.0308538,0.1013801,0.01030106,-0.03255786,-0.05314324,0.01371298,0.03282682,0.05919502,-0.04663437,-0.00640454,0.02215444,-0.05798772,0.00215535,0.02477123,-0.04406784,-0.01240375,0.10181738,0.00832441,0.00144136,0.14741588,0.00969985,0.00097998,-0.03822567,0.02975451,-0.00273184,-0.00641916,0.00584916,-0.00091842,0.02666076,0.00906177,0.06010412,0.02192217,-0.03437211,0.02757622,-0.01070154,-0.01264751,0.05801572,-0.01847287,-0.0398991,0.03708777,-0.04623089,-0.05700098,0.02653071,0.00006501,-0.24416392,-0.0679539,0.00483591,-0.03674335,-0.00121676,0.03420855,0.10436755,-0.08788538,-0.09230459,0.02066116,0.00317027,0.03530228,0.02985155,-0.10684279,0.02730062,0.01831082,0.00641958,-0.04902586,0.00081734,-0.0357201,0.00135957,-0.0225466,0.18760137,0.04037418,0.03043547,0.02839118,-0.00715018,0.04364339,0.07845982,-0.00513642,-0.01457564,0.04856417,0.05063867,-0.03585609,-0.02184168,-0.00974415,-0.02827585,-0.01996236,0.00372253,-0.02412642,0.04173765,0.00550891,-0.00789301,-0.05966928,0.03585333,-0.01647574,-0.02849885,-0.02065597,-0.04611195,-0.02893658,-0.08018582,0.00834209,0.01483315,-0.04494935,0.03104363,-0.00314025,0.00075708,0.01606763,-0.03551024,0.03032648,0.01993919,-0.01678482,0.04071607,0.0907084,0.01465268],"last_embed":{"hash":"bb58e294d5037a6263dbbad15876be23b480096649cf19924b130ec884eefede","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"bb58e294d5037a6263dbbad15876be23b480096649cf19924b130ec884eefede","at":1743662881362},"key":"37 Tips from a Senior Frontend Developer.md#7. Don't copy/paste code you don't understand","lines":[87,98],"size":383,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#8. Don't blindly apply every piece of advice found online": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03162202,-0.01214643,0.00654363,-0.00974211,0.04056939,-0.02083415,0.06110805,0.00605306,0.01947221,-0.04557273,-0.01641858,0.02348437,0.05313896,0.04325207,-0.00274569,0.04098134,-0.01178232,0.0133725,-0.00246663,0.00457473,0.02917542,0.01431719,-0.01819776,-0.00082696,0.06552646,-0.00804349,-0.04597436,-0.04811419,-0.05050525,-0.18672612,-0.03979499,-0.00936672,0.04822807,-0.01377171,-0.00650305,0.03028804,0.02820868,0.04923581,-0.0127523,0.033,-0.03393506,0.05353104,-0.00831929,-0.02956096,-0.01298312,-0.03074871,-0.00453175,-0.00032761,0.00068307,-0.00385606,0.0025818,0.01124719,-0.01229825,-0.00488104,-0.01731547,0.03644492,0.0532141,0.05199006,0.00026341,0.04569205,0.08250961,-0.03655615,-0.171185,0.09303308,0.08032031,0.02826195,-0.02812437,-0.01351083,-0.01415289,0.06854306,-0.05290175,0.00927685,-0.03496658,0.09109966,0.01396862,-0.00299363,0.00679123,0.01402786,0.04627514,0.00235291,-0.01725753,-0.01713016,-0.06086944,-0.00851561,0.01437806,-0.02484079,0.03212616,0.0112574,0.03353258,0.02089291,0.05163359,-0.05310364,0.04177173,0.00962981,-0.00662239,0.01462898,0.08154327,0.03593805,-0.09310898,0.12776524,-0.05394404,0.02222244,0.02702785,0.06249138,0.05301701,-0.04937178,0.03831042,0.04842113,0.02517778,0.00964832,0.00381765,-0.00941627,0.02025646,-0.04001623,0.06419061,0.05059221,0.03474649,0.05292723,0.01751565,-0.01222826,0.04608005,0.0053398,0.03185624,0.03679625,-0.02880401,-0.06191934,-0.00361632,0.0734816,-0.03018918,0.03126543,0.02073017,-0.01732197,-0.08999829,-0.000994,0.04064447,0.04915815,0.03853041,0.01009267,0.07759348,0.02520829,-0.02014041,-0.05305256,-0.03431886,0.01385712,-0.02569032,0.10094754,-0.02715023,0.06726721,-0.01349919,0.01242823,-0.00388295,0.02422668,-0.06177037,-0.0248258,0.03675571,0.00959685,0.06348429,-0.03483035,-0.07521471,0.02007446,-0.02733054,-0.01096633,-0.02330396,0.01585006,-0.01158896,-0.08404761,-0.05089445,0.00010888,0.06681549,-0.05947545,-0.03487566,-0.02780054,-0.01779745,-0.05592442,0.11167158,-0.01799467,-0.00805926,-0.02842328,-0.00056666,0.07569195,0.04737886,0.01225105,-0.00658856,-0.00382537,-0.01304515,-0.09698296,-0.01712572,-0.01925671,0.07415792,0.02664352,-0.06405561,-0.06661706,-0.10334807,-0.01268764,-0.03686149,0.02518722,-0.00608357,-0.04173097,0.06953927,-0.01777242,0.01085962,0.02659334,-0.00867166,0.02647654,-0.05056072,0.08416929,0.02416753,-0.12475409,0.10184329,0.01350273,-0.0947525,0.00688059,0.0709212,0.03649207,-0.00076215,-0.01557704,0.0241005,0.09898505,-0.01612627,-0.02121841,0.09660129,0.00962571,-0.06258862,-0.22451553,0.01522831,0.00328424,-0.02485883,0.00927077,-0.08482102,0.06429829,-0.01524418,-0.01077924,0.06102034,0.03383639,-0.03497094,-0.01177211,-0.03172961,-0.02077599,-0.01570975,0.01557947,0.01483765,-0.04027709,-0.01566625,-0.05327739,-0.05192722,-0.02783995,-0.137923,0.08762469,-0.02724616,0.13949187,0.06436803,-0.01683633,-0.04062466,-0.00822962,-0.04835418,0.0212788,-0.15131377,0.06746415,-0.02538499,-0.01350324,-0.07114671,0.03140451,-0.0437873,-0.03265107,-0.03492826,-0.01461247,-0.04173688,-0.06479091,-0.05570518,-0.05883158,-0.05615992,-0.01980173,0.03662844,0.02940934,0.00495317,0.01944158,0.0649633,-0.01736482,-0.06907316,-0.07830808,0.022685,-0.02743379,0.04685001,-0.03357,-0.03820933,0.01858753,-0.03778777,0.00832855,0.01952779,-0.01926172,-0.03458698,0.07636158,-0.00237778,-0.05542037,0.16387871,-0.03424859,-0.04882001,0.01224809,0.00267331,-0.04157799,-0.01254152,0.04128352,0.01370216,-0.02043042,-0.08143744,0.05124555,0.03506028,-0.00387233,-0.01749298,0.05336565,-0.07369176,-0.00402514,0.00431421,-0.06144529,-0.0026244,-0.05977589,-0.01418543,0.03274304,-0.03409032,-0.21167774,0.016203,0.03578036,0.00010726,0.02130417,-0.00678375,0.08243582,-0.0675026,-0.04830327,0.02970525,-0.04390968,0.05248364,0.02226553,-0.0111392,0.03848512,0.00946649,0.05742093,0.04700416,0.05057766,-0.0965056,0.00019996,-0.01333754,0.20308262,0.05108241,0.0065413,0.03628601,0.01824012,0.02528079,0.12584655,0.02941736,-0.01824633,-0.0084315,0.01219572,-0.02054099,-0.00216625,-0.03944618,-0.02178108,0.00675394,0.02721705,0.01353012,0.02638683,-0.01414415,0.03426759,-0.00496626,0.08415168,0.00259202,0.01842898,-0.07563233,-0.09838622,-0.04482021,-0.0611576,0.00448585,-0.01158466,-0.03725773,0.0441368,0.04459061,-0.00204993,-0.04340101,-0.06352495,-0.00506328,0.05237161,0.03402552,0.08314522,0.09532706,0.07007731],"last_embed":{"hash":"270358205d8e7fcf93fa2fc4dc49f62a199976a4c9b0c4954a8cee72ce71e26c","tokens":124}}},"text":null,"length":0,"last_read":{"hash":"270358205d8e7fcf93fa2fc4dc49f62a199976a4c9b0c4954a8cee72ce71e26c","at":1743662881373},"key":"37 Tips from a Senior Frontend Developer.md#8. Don't blindly apply every piece of advice found online","lines":[99,117],"size":472,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#8. Don't blindly apply every piece of advice found online#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03119816,-0.01927795,0.01067172,-0.01318135,0.04906807,-0.0035878,0.04667294,0.00366996,0.0262559,-0.0408749,-0.0184371,0.03745322,0.06063924,0.05073903,-0.02246288,0.03856511,-0.01340015,0.00712724,-0.01443833,0.01270591,0.02650761,0.01728917,-0.01891395,-0.00328513,0.05203751,-0.00851198,-0.03921387,-0.04475787,-0.04896128,-0.18829316,-0.04813027,-0.02025676,0.04844289,-0.02132362,-0.00432362,0.02555847,0.03587013,0.03891138,-0.01024165,0.03240661,-0.03252039,0.05581756,-0.01028957,-0.04404975,-0.02424124,-0.03234523,0.00915761,-0.00247117,0.03462978,-0.00404305,0.00851783,0.02121491,-0.02534377,0.00059609,-0.01184031,0.02838852,0.04625167,0.06111149,0.00229625,0.04934936,0.07406418,-0.04247084,-0.16189,0.07572176,0.07726958,0.04581488,-0.044332,-0.01878456,-0.01201606,0.06968583,-0.06082339,0.00269851,-0.042411,0.09662998,0.01873978,-0.00113832,0.01486876,0.01865457,0.04839319,-0.00999756,-0.0070558,-0.01663278,-0.06240482,-0.01052313,0.0052783,-0.03537165,0.0349726,0.01798591,0.03363169,0.02086081,0.05157572,-0.04508474,0.05192252,0.01633327,-0.0097813,0.0212991,0.07949933,0.02703799,-0.06770927,0.14075764,-0.06328234,0.0151748,0.02651708,0.04511342,0.03941569,-0.04297778,0.03665343,0.05630598,0.01512179,0.01229629,0.00960045,-0.01073944,0.03227925,-0.04454927,0.07137842,0.04789716,0.01372949,0.0591175,0.00702804,-0.01286789,0.05495806,0.01026426,0.03859955,0.03993534,-0.01508668,-0.05093149,-0.00206845,0.07539043,-0.03171058,0.03440059,0.01517798,-0.01387325,-0.08070742,0.00122018,0.02873596,0.04684702,0.04065165,0.00942132,0.07235013,0.00443602,-0.02384376,-0.04912029,-0.04304586,0.01631535,-0.02011094,0.07918797,-0.01860226,0.06725191,-0.00190249,0.00354578,0.00050914,0.02162348,-0.05171601,-0.02602934,0.03178766,0.00152551,0.06637225,-0.0312998,-0.07132704,0.01904193,-0.01413294,-0.01154092,-0.02379511,-0.00094112,-0.01310897,-0.06680465,-0.04656686,0.00996759,0.05927166,-0.0662207,-0.03587368,-0.01903505,-0.01467327,-0.06613944,0.09655507,-0.01928747,-0.00346115,-0.02421069,0.00096819,0.07848193,0.03933651,0.00978631,-0.0119454,0.0024377,-0.00763017,-0.10517617,-0.00143878,-0.01376331,0.0789566,0.04434437,-0.05376365,-0.06480201,-0.10698957,-0.00406895,-0.03532971,0.02294617,0.00134201,-0.04016851,0.08956025,-0.02143448,0.02319843,0.03161531,-0.00925388,0.02384089,-0.05447897,0.08108486,0.03355866,-0.11850157,0.09313198,0.0055499,-0.11105547,0.00423784,0.06802053,0.03104721,-0.01295373,-0.01496044,0.01372826,0.10478672,-0.01837477,-0.02943049,0.10354831,-0.00333117,-0.07964471,-0.22106741,0.015609,-0.00131336,-0.03588477,-0.00765141,-0.10292778,0.07506604,-0.02167735,0.00088516,0.07547861,0.03573876,-0.0292882,0.00892045,-0.04024612,-0.03422188,-0.01871649,0.02768172,0.01590071,-0.04812763,-0.0229492,-0.06645866,-0.0571545,-0.02309671,-0.13557965,0.09495492,-0.02854595,0.15025607,0.0425087,-0.00001255,-0.05601112,-0.00303663,-0.05357227,0.02738166,-0.14262521,0.08211786,-0.01713809,-0.02763909,-0.08788403,0.02327501,-0.0433647,-0.0204044,-0.04664936,-0.00964524,-0.05584709,-0.0540649,-0.06256458,-0.0705801,-0.06670552,-0.02110998,0.05195858,0.03298007,-0.00455107,0.01645309,0.06990559,-0.0185439,-0.05666554,-0.06841662,0.02417162,-0.03051604,0.04246741,-0.01839232,-0.04563575,-0.00935766,-0.03930973,0.01143233,0.02975195,-0.03472783,-0.03976995,0.08573522,0.00093767,-0.04398507,0.18055609,-0.03103278,-0.05755247,0.01169991,-0.00387383,-0.04091208,-0.02787307,0.02878005,0.01886263,-0.02844961,-0.06884216,0.05838951,0.04525726,-0.00470465,-0.01006283,0.05273059,-0.06396902,-0.00180078,0.0086697,-0.06492917,0.00800876,-0.06490797,-0.02647502,0.03748515,-0.02703914,-0.19909568,0.0132603,0.02746452,0.00493415,0.02537777,-0.00423794,0.0728686,-0.06155317,-0.03443057,0.03800423,-0.05696059,0.04920762,0.0290545,-0.00370889,0.04529407,0.02626065,0.04055496,0.05438242,0.05520448,-0.08055124,0.00238484,-0.02137909,0.2052836,0.04224835,0.00470052,0.03332109,0.00617669,0.02707347,0.11254448,0.03256422,-0.01961418,-0.00808909,0.01055122,-0.01903389,0.00209202,-0.03038842,-0.02599297,0.00449781,0.01384886,0.02065611,0.00952822,0.00526504,0.03366265,0.00110001,0.08644578,-0.01741533,0.01198405,-0.07782825,-0.08069516,-0.04546054,-0.06020155,-0.00057986,-0.00541184,-0.03219055,0.03101905,0.0438791,-0.00147822,-0.03940414,-0.06420096,-0.01063262,0.06112331,0.03366411,0.07724536,0.07578808,0.05745794],"last_embed":{"hash":"c8d51eba86a890c5bf1196fc798539d0cd404e03bf5edc68879e8459e7bfa5f8","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"c8d51eba86a890c5bf1196fc798539d0cd404e03bf5edc68879e8459e7bfa5f8","at":1743662881390},"key":"37 Tips from a Senior Frontend Developer.md#8. Don't blindly apply every piece of advice found online#{1}","lines":[101,110],"size":281,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#9. Assume good intent: people want you to succeed ❤️": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02548383,0.00428199,-0.01692125,-0.08846037,0.0033447,0.0086659,0.10117754,-0.04671912,-0.0004027,-0.01549833,0.029744,0.02666749,0.02109851,-0.04367053,-0.0303498,0.03353798,-0.04001921,0.00589189,-0.06795526,0.01082232,-0.06840649,-0.01403704,-0.03960252,-0.0158375,0.04683521,-0.03234868,-0.03367306,-0.08817593,-0.08551911,-0.12561859,-0.02966727,-0.02430696,0.02945125,-0.02741276,0.0198601,0.03434529,-0.02780425,0.03380444,-0.01355561,0.00401959,-0.00336211,0.00529929,-0.02080072,-0.04401701,-0.00928341,-0.02758023,0.01606102,-0.05689892,0.0243645,-0.00176484,-0.02199682,-0.03616811,-0.04263067,-0.00437872,0.01448259,0.04336531,0.06785392,0.05437828,-0.029815,0.02942451,0.05188721,-0.03193134,-0.14580882,0.04041145,0.05861907,-0.00906085,-0.03984785,0.01201368,-0.05835559,0.09494171,-0.00272009,-0.05102878,-0.01981051,0.06773984,0.01312437,0.04823567,-0.00747781,-0.00493495,0.05409293,0.04488249,0.04579427,0.00289613,-0.03233045,0.02507791,0.00862628,0.00263613,0.07659805,0.01779597,0.07556672,0.00833786,0.07211288,-0.02274879,-0.03595126,-0.03615921,-0.00040163,-0.00563386,0.07072685,0.02317769,-0.14104442,0.1295698,-0.01832252,0.0559411,0.02478576,0.03250971,0.06822096,-0.033769,0.02400863,0.04242452,0.04103294,0.00332446,-0.0088236,-0.00603963,0.00655824,-0.03286893,0.03930375,0.01893492,0.00010615,0.03203952,-0.04584609,-0.02494901,0.03152421,0.00554639,0.02910777,0.01950236,-0.03179945,-0.11074477,0.04509622,0.07929465,-0.02366258,-0.00374007,0.02715439,-0.0187871,-0.11074059,-0.00538307,0.01455056,0.04419889,-0.01450966,0.0253508,-0.01258804,0.00894814,0.03007316,-0.05285671,0.01675247,-0.05574903,0.00521499,0.10636557,0.01379611,0.05081376,-0.00825674,0.01317595,-0.02804311,0.00116328,-0.02386438,-0.04474506,0.0210425,-0.00617933,0.04124904,0.04562051,-0.02732922,-0.01369349,0.00093892,-0.02805157,-0.08607926,-0.02622558,-0.00489222,-0.04419725,-0.04291138,-0.03485309,0.04231357,0.01113854,-0.03197629,-0.04114332,-0.02353237,-0.03177484,0.08027826,-0.02473335,-0.04527703,-0.02177084,0.016088,0.02691028,0.10421545,0.04830536,0.00588157,-0.004372,0.02039932,-0.07225876,0.0218119,-0.03825574,-0.00243124,0.01787665,-0.08443423,0.04042378,-0.07217344,0.00397299,-0.01923796,-0.00050332,-0.01107778,-0.00957894,0.02712622,-0.04209084,-0.07659144,0.00586835,-0.02941553,0.03524296,-0.05750768,0.08472694,0.06886227,-0.10308355,0.17706406,-0.00986414,-0.09653125,0.0115602,0.11825734,0.06351729,0.02748024,0.00684886,0.02631579,0.13418138,-0.0262438,0.01994099,0.12039465,0.06902165,-0.02897711,-0.20323199,-0.00548158,-0.02493541,-0.03090205,-0.00290439,0.03440135,0.07793152,0.03137725,-0.00282815,0.06147397,0.09209288,-0.07165197,-0.04179996,0.03112457,0.03593059,0.00714828,-0.023547,0.03572481,-0.02639289,-0.02159653,-0.02599845,0.00443619,-0.0502501,-0.0985265,0.06639656,0.04043154,0.12317249,0.02800237,-0.01666045,-0.08004528,0.02507644,-0.01420816,0.00292379,-0.14038169,0.08118003,-0.02703402,0.00190086,-0.1013578,-0.02408201,-0.01460317,-0.02045693,0.01489168,-0.03075278,-0.05317947,-0.10167825,-0.01252687,-0.08408966,0.00095831,-0.04943889,0.02148863,0.02025643,-0.01002996,0.03083355,0.06222115,0.01659383,-0.05766322,-0.08817243,0.04554462,-0.02620084,0.10168568,-0.0190123,-0.03139412,-0.01413759,-0.01323379,0.06379861,-0.02647746,-0.04992793,-0.01080278,0.08774551,-0.02127804,-0.03478664,0.09762593,0.00974109,-0.05002731,0.01589224,-0.02499127,-0.06098815,-0.01060773,0.0270493,-0.01680627,-0.00578678,-0.09678181,0.0536123,0.05010041,0.06080966,0.00406192,0.04552276,-0.00503886,0.0414913,-0.01848659,-0.06309768,0.02299454,-0.07064534,-0.07727266,0.01737142,-0.01099228,-0.20508556,-0.00086653,-0.01557174,0.016807,-0.00992266,-0.0164645,0.06561323,-0.04082017,-0.06032646,0.03610068,-0.01409211,0.01171856,0.04169021,-0.04700936,0.03634398,0.02589867,0.08750416,0.0030502,0.02813898,-0.04929203,-0.01947194,0.03140362,0.19609784,0.03385621,0.04298994,0.03634892,-0.02232731,0.02569098,0.07602287,-0.00055547,0.01575441,-0.01011418,-0.00010156,-0.02645586,0.0095909,0.0239109,-0.04317494,0.05144748,-0.00535507,0.00721229,0.04641373,-0.01570226,0.04822413,0.01952334,0.09530316,-0.03558686,0.00061535,-0.03034095,-0.03611877,-0.02735892,-0.09703474,0.03047735,0.01466207,-0.03882378,-0.01440438,0.06659646,0.01054623,-0.0506264,-0.01291819,0.00593629,0.01114621,0.01842879,0.0842512,0.10129289,0.06258254],"last_embed":{"hash":"4603915c9908b1160203e1212deaccd715b584edffc3919a87ed35ef62c367e8","tokens":128}}},"text":null,"length":0,"last_read":{"hash":"4603915c9908b1160203e1212deaccd715b584edffc3919a87ed35ef62c367e8","at":1743662881403},"key":"37 Tips from a Senior Frontend Developer.md#9. Assume good intent: people want you to succeed ❤️","lines":[118,131],"size":498,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#9. Assume good intent: people want you to succeed ❤️#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02320304,0.00302973,-0.01662787,-0.08026087,-0.00505049,0.01355342,0.10830534,-0.04981375,0.00270306,-0.01252573,0.03600422,0.025372,0.0185251,-0.0446623,-0.03794609,0.03775357,-0.04483288,-0.00043362,-0.07083187,0.01248652,-0.06777432,-0.01440452,-0.03305688,-0.0112034,0.04748626,-0.03023517,-0.02821135,-0.09026262,-0.08134464,-0.12053181,-0.03121925,-0.02515504,0.03138287,-0.02471597,0.01555152,0.03155011,-0.03351597,0.03606002,-0.01091076,0.00950788,-0.00231465,0.00163797,-0.02240933,-0.04484671,-0.00989995,-0.02491589,0.02279919,-0.0589602,0.02508762,0.00013487,-0.02020589,-0.03218648,-0.0456026,-0.00024458,0.01744105,0.0437135,0.0750159,0.05808289,-0.02709872,0.02581924,0.04858058,-0.02643117,-0.14629281,0.03399312,0.05502664,-0.01191735,-0.04260815,0.01948496,-0.06855143,0.09746042,0.00124557,-0.04827303,-0.01835086,0.0721613,0.01346554,0.04857001,-0.00417736,-0.00633332,0.05150168,0.04502587,0.04626911,0.00105596,-0.03223028,0.02275515,0.00611563,0.003932,0.07406653,0.01402651,0.07440091,0.00968759,0.07441595,-0.01313959,-0.0499418,-0.03284362,0.00146255,-0.00776186,0.06420374,0.02415763,-0.13878147,0.12500033,-0.01876329,0.05811083,0.02146885,0.03551428,0.06836101,-0.03618784,0.01873997,0.04271466,0.04670988,-0.00594874,-0.00368808,-0.00748166,0.00250991,-0.02983977,0.03855771,0.02641079,0.00662596,0.03121174,-0.04576143,-0.02678353,0.02850221,0.01007315,0.03279872,0.01911823,-0.03839607,-0.11863222,0.04760107,0.08300541,-0.02665855,-0.01283096,0.03251646,-0.02534422,-0.10869358,-0.00951188,0.01264481,0.0497721,-0.01537344,0.03125556,-0.01681754,0.00677643,0.03877266,-0.05793555,0.01634073,-0.06130531,0.00332653,0.10722017,0.0126287,0.0522128,-0.01404972,0.0143775,-0.03495876,-0.00673024,-0.02754579,-0.04239087,0.01866378,-0.01132217,0.04419799,0.04673724,-0.02921841,-0.0201734,-0.00440608,-0.01947728,-0.08875199,-0.03007728,-0.01149241,-0.0294562,-0.04333387,-0.03884498,0.04389397,0.01630477,-0.03498955,-0.04026458,-0.02479341,-0.01903517,0.07262657,-0.0186498,-0.049793,-0.02313107,0.00950775,0.01946995,0.10859758,0.04903743,0.00482359,-0.00928116,0.02402997,-0.06555856,0.01620464,-0.03506539,-0.0019197,0.01785324,-0.08449584,0.04508698,-0.06669534,0.0018886,-0.01832405,-0.00452345,-0.01214189,-0.01194268,0.01638993,-0.04301812,-0.08095756,0.00296262,-0.02803367,0.03187027,-0.05367282,0.0852135,0.06632663,-0.10406673,0.18315288,-0.01593603,-0.09927554,0.0161717,0.11350393,0.06381205,0.02899827,0.00215086,0.03307932,0.13132815,-0.02216477,0.01989837,0.12277211,0.06827791,-0.02067507,-0.19598657,-0.0075038,-0.02814642,-0.02459917,-0.00343155,0.04183961,0.07583044,0.03938943,-0.00427231,0.06274217,0.08966582,-0.0729589,-0.038673,0.03926783,0.03677843,0.0030714,-0.02910609,0.03397384,-0.02158578,-0.02070639,-0.03085644,0.01345372,-0.05030904,-0.09873436,0.06565256,0.04222818,0.12352636,0.0266366,-0.02128422,-0.07295449,0.0284339,-0.00722477,-0.00334965,-0.14371966,0.08149929,-0.02681337,0.00030956,-0.10187745,-0.02122356,-0.01457404,-0.02229474,0.01511359,-0.03605229,-0.04858569,-0.10071886,-0.01231425,-0.08082295,0.01491907,-0.05612092,0.02493566,0.02720448,-0.00379111,0.02479056,0.06204178,0.01564642,-0.05274431,-0.08829626,0.05008749,-0.0254495,0.09809522,-0.0163378,-0.02640813,-0.02220558,-0.01234438,0.06683359,-0.02611879,-0.0432216,-0.01627235,0.08013986,-0.01343868,-0.03497924,0.09740087,0.00235014,-0.05221714,0.0211368,-0.02675035,-0.06426089,-0.01543997,0.02336315,-0.02369624,0.00420877,-0.09504905,0.04834788,0.04763998,0.06545673,0.00075966,0.04590835,-0.00385901,0.04177151,-0.02195586,-0.05258356,0.0169881,-0.07404634,-0.07479125,0.01693013,-0.01018083,-0.20733431,-0.00167724,-0.02460876,0.02399801,-0.00892528,-0.02296626,0.05944685,-0.03643437,-0.06433144,0.03354166,-0.01611444,0.00851389,0.04121315,-0.04540638,0.03021726,0.0249831,0.08862932,0.0013894,0.02963116,-0.04560892,-0.01360659,0.02974565,0.19370918,0.03191954,0.04940526,0.03017145,-0.01937791,0.02044401,0.06928667,-0.00425499,0.01212427,-0.01685124,0.0016178,-0.02599902,0.00624775,0.02683028,-0.04532455,0.05045522,-0.00170325,0.00426092,0.04211989,-0.01585043,0.05295574,0.01866557,0.10232189,-0.03351195,-0.00511676,-0.02763995,-0.03308506,-0.01968365,-0.09124993,0.03273139,0.01267639,-0.04247358,-0.01331608,0.06627518,0.00716933,-0.04970343,-0.0111732,0.00766301,0.00589004,0.01863588,0.08943101,0.09820174,0.05525704],"last_embed":{"hash":"994f87a4e211e2480e84d0f596813a5d9747dd8f863f1c59ed6483f8155f9e23","tokens":128}}},"text":null,"length":0,"last_read":{"hash":"994f87a4e211e2480e84d0f596813a5d9747dd8f863f1c59ed6483f8155f9e23","at":1743662881415},"key":"37 Tips from a Senior Frontend Developer.md#9. Assume good intent: people want you to succeed ❤️#{1}","lines":[120,131],"size":442,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#10. Done is better than perfect": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03112027,-0.00319268,0.03800904,-0.10466734,-0.00446064,-0.00660003,-0.02076103,-0.00968348,0.02282219,-0.0418727,-0.03306991,-0.00572061,0.00280072,0.01861784,-0.02153032,0.05311102,0.00499851,0.07147526,-0.03520086,0.01421341,0.02457883,-0.02042112,-0.01144589,-0.0193549,0.08884012,-0.01124479,-0.04762272,-0.11326439,-0.0598724,-0.21589868,-0.02855234,-0.02988243,0.02795615,-0.02175007,0.00695757,-0.00920994,0.01420085,0.05475719,-0.06455757,-0.02097614,0.04206224,0.03628539,-0.0422188,-0.01684912,-0.00901579,-0.0254949,-0.01742877,-0.03686361,0.02005363,-0.03209722,-0.00380571,-0.04640691,-0.0330207,0.00879114,0.02674064,0.04086475,0.0505424,0.05901255,0.01063159,0.00524284,0.04398897,-0.02634411,-0.18118165,0.05212336,0.07555696,0.01035544,-0.00575938,-0.02956464,-0.07419755,0.09283233,-0.0185119,-0.05197047,0.01291901,0.09954327,0.00546012,0.00146885,-0.00401081,-0.02450285,0.04281596,0.05432964,0.03611357,-0.01894119,-0.00743334,0.01975548,-0.01596632,-0.00288694,0.02936219,0.05238289,0.103981,-0.01762637,0.05776548,-0.05465131,0.00676313,0.01068539,0.00249373,-0.03167181,0.04087139,0.00406911,-0.06242479,0.14314774,-0.07499979,0.05122344,0.02396019,-0.01228848,0.05713146,-0.02919504,0.06949686,0.01814851,-0.0119775,0.00137798,-0.02027372,-0.02561151,0.06932282,-0.03869537,0.03871744,0.05563156,0.02353518,0.02515326,-0.00918453,-0.02749701,0.04959005,-0.00360065,0.05815537,-0.03287413,-0.06975616,-0.07602733,0.05516253,0.04688574,-0.03612471,0.03168325,-0.00085517,0.02112917,-0.06301408,0.01708614,0.00178939,-0.00556233,0.02811963,-0.02596033,0.03778943,-0.04303194,-0.04963223,0.01719817,0.00949896,-0.07437021,0.00343889,0.10781513,0.0050845,0.03730953,0.02448931,-0.03289403,-0.04362872,0.02636034,-0.0274239,-0.08070702,0.01431421,0.00271459,0.05696462,-0.01748966,-0.02409901,0.00258044,-0.00474482,-0.01373193,-0.05108357,0.10078194,-0.00734946,-0.07468349,-0.00922054,-0.00418601,-0.00554766,0.01597656,-0.00856623,-0.03271422,-0.0122497,-0.00733764,0.0751785,-0.0033443,0.00408153,0.00257735,0.03733463,0.02872617,0.063432,-0.0082318,0.00921072,0.03655625,0.03351724,-0.07553287,0.02505484,-0.00320405,0.06905278,-0.03578549,-0.07202692,-0.03621372,-0.06236568,0.03768025,-0.04818306,-0.02788288,0.00611056,0.00855027,0.02315008,-0.07517663,-0.03277338,-0.02290216,0.01713813,0.00267764,-0.01207575,0.07866289,0.02822357,-0.08883636,0.14425373,0.06143636,-0.10097865,-0.03409879,0.10907337,0.03404751,-0.03655616,0.01781438,0.00438798,0.09457517,-0.03199042,0.01941447,0.05449865,0.06116741,-0.04040017,-0.21180886,-0.00641672,0.03360436,-0.02124638,0.08575179,-0.01578435,0.05090563,0.03807249,-0.02713438,0.0048691,0.04267313,-0.07446821,-0.06208017,0.00494248,0.00892258,0.01076662,0.02644989,-0.01357958,-0.0215938,-0.03058346,0.00603731,0.00260234,-0.03500068,-0.03732985,0.05118433,-0.00087938,0.13026018,-0.01344941,0.03728008,0.00920589,0.02271604,-0.0254062,0.01011395,-0.12035311,0.07030343,-0.01485371,0.06293096,-0.07683457,-0.00122879,0.00273436,0.00039571,0.01785425,0.02881963,-0.10620192,-0.06593234,-0.00353149,-0.06781859,-0.04088253,-0.01565269,0.03831217,0.02718997,-0.00456532,0.02806119,0.04635062,0.01614624,-0.00506713,-0.07389782,0.00529079,-0.03728317,0.05346781,-0.02908986,-0.04985051,0.0090798,0.03105154,0.0231926,0.03422489,-0.03606226,0.00815238,0.06461307,-0.0541587,0.00986534,0.08391695,-0.06916948,-0.07661256,0.01340395,-0.00135748,-0.06541718,0.0096052,0.03855841,-0.03754117,0.01246836,-0.05243689,0.05373769,0.04610765,0.0557817,-0.00352724,0.05238576,-0.00622331,0.00626553,-0.00928576,-0.05084343,0.02876329,-0.07255284,-0.01779252,0.0427754,-0.02056426,-0.26116192,-0.01171782,0.02315059,-0.0413843,-0.02283131,0.02589744,0.05401302,-0.0279836,-0.07209802,0.05356319,-0.05785783,0.01568694,0.04505795,-0.04757774,0.0275629,0.01934052,0.05253268,-0.02386435,0.06409238,-0.1066561,0.00224723,0.02086493,0.20903654,0.0058016,0.00454936,0.07571365,0.01940235,0.04128486,0.11149111,-0.01012632,0.0304525,0.00302572,0.00735961,0.00343158,-0.01362626,0.03068649,-0.04236721,0.05178054,-0.02251242,-0.04545171,0.05668478,-0.03470627,0.03031816,0.0029477,0.11115403,-0.05241449,-0.0274309,-0.05954139,-0.05343842,-0.03925416,-0.08136662,0.03057093,0.00975313,-0.03445273,-0.01346602,0.06575634,0.01645979,-0.02024194,-0.02320964,0.02365383,-0.00506491,0.01876321,0.08433279,0.08104607,0.06508625],"last_embed":{"hash":"429e36481c652b0592cdd55336cd8e988135beb7670450799e7544e744982bce","tokens":64}}},"text":null,"length":0,"last_read":{"hash":"429e36481c652b0592cdd55336cd8e988135beb7670450799e7544e744982bce","at":1743662881427},"key":"37 Tips from a Senior Frontend Developer.md#10. Done is better than perfect","lines":[132,144],"size":213,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#11. Always break tasks into manageable ones": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04051803,-0.03483104,0.01679175,-0.10286491,-0.00320987,-0.01246595,0.01334578,-0.0500677,0.0134907,-0.01856242,-0.03200852,0.01206502,0.04016873,0.03373212,0.03692767,0.0157458,0.02618943,0.05300955,-0.08555819,0.03317394,-0.03851211,-0.00598687,-0.03968957,0.02016882,0.02436394,0.00604367,-0.05857811,-0.08611405,-0.06352812,-0.1802465,0.00177956,0.00267117,0.03931941,-0.01628562,0.02528624,0.03572741,-0.0293018,0.02412067,-0.02903463,0.0112967,0.0548595,0.05007486,-0.00348228,-0.07359233,-0.02357968,-0.05784885,-0.05458522,-0.07945589,0.07646742,-0.03500375,0.01069964,-0.03208322,0.01348564,0.01986626,-0.01654231,0.03270289,0.02081118,0.0622347,-0.0362117,0.06442563,0.09466233,-0.03942158,-0.12233534,0.0334468,0.04940222,0.0333278,-0.03965294,0.0016733,-0.00324655,0.13516149,-0.05240003,0.01427063,-0.02790549,0.11684191,0.01870232,-0.02758982,0.00047666,-0.01650162,0.03617014,0.08777842,-0.05814746,0.02433156,0.00082517,0.00972904,-0.04235008,0.00625054,0.03031543,-0.01302525,0.07023867,-0.00813263,-0.00506626,-0.07860246,0.03914018,-0.01492902,0.01467122,-0.02536381,0.02823629,-0.00805067,-0.11378587,0.16542505,-0.07634193,0.04731933,0.06601083,-0.00893113,0.02921465,-0.02426421,0.03833657,0.03202718,-0.04072811,0.02826497,-0.01023788,-0.02114086,0.06220686,-0.06395563,0.04536843,-0.01883794,0.03294619,-0.01301646,-0.02889899,0.01899933,0.031585,0.02695659,0.01787393,-0.0014179,0.01335727,-0.01727265,0.03181832,0.03750256,-0.0017912,0.0065631,0.02670181,0.02235219,-0.1305209,0.02207376,0.01720052,-0.01202912,0.04118539,-0.02842895,0.03673597,0.01753613,0.0016868,-0.01483639,0.03513524,-0.08822444,-0.03182403,0.1587871,-0.03837993,0.04154342,0.01042002,-0.07111898,-0.02261421,0.02270348,-0.02560948,-0.06878969,0.03560634,-0.00312661,0.06412396,0.0050705,-0.02766395,0.02050382,0.02617269,-0.05015638,-0.06468842,0.01312979,-0.00439522,-0.07023615,-0.0330577,0.00052592,-0.01377592,-0.03372826,-0.01658665,-0.00054161,-0.04340233,-0.03827097,0.10209513,-0.02616208,0.0050768,-0.03209519,0.07811531,0.0517882,0.02723099,-0.01684023,-0.00857689,-0.01124721,-0.00075088,-0.0963527,0.08731785,-0.05226707,0.04560367,-0.01222082,-0.04830041,-0.00475948,-0.00603032,0.00893,-0.04892339,0.03375592,-0.02047488,0.0290786,0.01094148,0.00576605,-0.00976355,0.03643271,-0.02430171,0.04944035,-0.00080322,0.06477967,0.01273045,-0.05579888,0.12045425,0.05761904,-0.06047292,0.01174089,0.11179081,0.02916782,-0.00993928,0.03001211,0.03883381,0.11168654,-0.01946737,0.03003764,0.07340936,0.06107703,-0.06393854,-0.1976655,0.04220909,0.01808504,-0.04663665,0.02063444,0.00801346,0.09600084,-0.06170529,-0.00257722,0.0240316,0.08509655,-0.08392758,-0.07380234,-0.0127036,0.0223711,-0.00355164,0.04141704,-0.02376823,-0.06305941,-0.02870719,0.04155022,-0.03889325,-0.0443261,-0.12400659,0.01153977,0.02920226,0.11921857,-0.03013468,-0.0200117,-0.02711398,-0.01014339,-0.01403763,0.02357359,-0.154689,0.07352194,-0.00232425,0.00298074,-0.1039075,-0.00996804,0.00893435,-0.02589573,0.01474664,0.00197895,-0.07541974,-0.07503857,-0.0399845,-0.05284271,-0.05215427,-0.00740416,-0.05631413,0.02341014,-0.00132185,0.0370827,0.01246922,-0.0370401,-0.02119105,-0.07611027,-0.00420257,-0.05642986,0.02007739,-0.05849053,-0.06613664,0.00250334,-0.00117076,0.04847289,0.01931505,-0.04501139,0.01647034,0.02976653,-0.04187616,-0.01201586,0.06348724,0.00416679,-0.043654,-0.02160353,-0.01827811,-0.03419571,-0.01613738,0.02507246,-0.0237092,-0.02228662,-0.08385169,0.03507911,0.05345774,-0.00485544,0.00510324,-0.00067977,0.00756673,0.01848761,-0.0221647,-0.00358959,0.05647333,-0.05594223,-0.04156041,0.06916482,0.01607218,-0.18804206,0.05289592,0.0325917,-0.02317492,-0.00625837,0.07595205,0.06722756,-0.01505724,-0.00671614,0.0284801,0.00335993,0.06479348,0.03187801,-0.06867049,0.03153922,0.01522024,-0.00026946,0.03746902,0.06032478,-0.05260946,0.00842452,0.00493199,0.21157917,-0.01469018,0.0560003,0.04778666,-0.00064031,-0.00021889,0.0394071,0.03277658,0.02257589,-0.028644,0.01801928,-0.00416507,0.0352202,0.03217371,-0.03480637,0.05565123,0.01295786,0.01871598,0.0754609,-0.04673054,0.02173529,-0.01975582,0.08455177,-0.11183234,-0.03999022,-0.06181319,-0.09312974,-0.05185303,-0.05395669,0.01939723,-0.01011781,-0.01858493,0.04930122,0.05229654,0.04848241,0.01114673,0.00130526,-0.01541321,0.01746327,0.05236864,0.06413712,0.0444006,0.10841221],"last_embed":{"hash":"d584f234a0b61d77a0435784ed67ff0c8df24675c58b6c72ef4259eb606808b5","tokens":73}}},"text":null,"length":0,"last_read":{"hash":"d584f234a0b61d77a0435784ed67ff0c8df24675c58b6c72ef4259eb606808b5","at":1743662881438},"key":"37 Tips from a Senior Frontend Developer.md#11. Always break tasks into manageable ones","lines":[145,160],"size":299,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#12. Be trusted to reach out when you need help": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02191227,-0.03881961,-0.00902325,-0.12168063,0.0016684,0.01168204,0.07396266,-0.01366982,-0.04524601,-0.04418912,-0.02126232,-0.01011032,0.03230228,0.01945888,0.01561732,0.02499267,-0.00179002,0.0492522,-0.02717211,-0.01599437,-0.02941038,-0.00350623,-0.04379044,0.00598094,0.02862619,-0.02197479,-0.0312258,-0.05303005,-0.10257145,-0.13860039,-0.05150234,-0.08017583,0.03869426,-0.03262993,0.03548327,0.02616349,0.01604157,0.0396468,0.00004921,-0.05348191,0.01915902,0.03980369,0.00255983,-0.07007312,-0.01758769,-0.05202707,0.01704677,-0.03181074,0.02217399,-0.02986251,0.01096112,-0.01209709,-0.0442443,0.04325907,0.00073194,0.08755653,0.03466414,0.02029878,-0.01718403,0.04467262,0.03045052,-0.0209594,-0.15242158,0.02676572,0.02788197,0.03033341,-0.02437721,-0.01550561,-0.04001994,0.06944288,-0.04343293,-0.03082964,-0.01392415,0.0565094,0.04794005,-0.01932771,-0.01426788,-0.0233442,0.0250868,0.07379747,0.00544979,0.02738007,0.02537832,0.00206239,-0.0279968,-0.0120857,0.06594288,0.00446061,0.06155867,-0.02261879,0.08416678,-0.02615383,-0.02043838,-0.01851027,0.01653531,-0.01458908,0.0209068,0.01440443,-0.16068952,0.17281537,-0.04879641,0.02411287,0.02875383,0.01117471,0.04084025,0.00570083,0.02846099,0.04469643,-0.00568425,0.06697784,-0.02657634,-0.03038524,0.01624171,-0.03557669,0.06192845,0.04097675,0.0229196,-0.0004435,-0.02491284,-0.01553743,0.03657543,0.0106097,0.0732113,0.01578817,-0.00056347,-0.06464332,0.04998907,0.06113867,-0.00763491,0.03776068,-0.01208495,-0.01049799,-0.12340901,0.00641163,0.01895658,0.0193362,0.0077076,-0.02302366,0.03287347,0.00638694,-0.04981241,-0.06083143,-0.01201961,-0.06954976,0.00672956,0.10410867,0.01263008,0.0347957,-0.02330319,-0.04354468,0.00350226,0.03801037,-0.00304139,-0.05975632,0.06133082,-0.01340109,0.05510107,-0.03393395,-0.0269568,0.01641586,0.01295812,-0.07151524,-0.06564841,0.0250701,-0.01319891,-0.06553176,-0.02916216,-0.02870117,0.02423974,-0.03082122,-0.03890839,-0.00982503,-0.02342312,-0.03134975,0.06509793,-0.05507644,-0.01193008,-0.00727525,0.02405465,0.00854343,0.07135356,0.01878294,-0.00267381,0.02649291,-0.01551752,-0.07744157,0.06247648,-0.06662744,0.01391618,-0.02237024,-0.07567086,0.03783012,-0.01258711,-0.00361038,-0.00709517,0.00007175,-0.05590925,0.00862885,0.02191449,-0.02586374,-0.02729482,0.00268548,0.03109942,0.0072507,-0.07693949,0.0513913,0.02642193,-0.08368266,0.14216919,0.03718315,-0.05864703,-0.00280724,0.1086357,0.01763385,-0.0272909,0.05539551,0.03527311,0.10601316,0.02782168,0.03035067,0.08288306,0.10522736,-0.07103993,-0.21528576,-0.01153397,-0.03069925,-0.0045759,0.00678344,-0.00067804,0.09577267,-0.03533451,-0.01218731,0.04631981,0.12970208,-0.08424577,-0.05348225,-0.01545972,0.0186917,0.05427463,-0.0330641,0.01754276,-0.04656311,-0.00265483,-0.03114014,-0.01119013,-0.05868527,-0.09940603,0.05151997,-0.00391342,0.11892016,-0.02587711,-0.03026213,-0.06252132,-0.02301832,-0.0303741,0.00466306,-0.12270017,0.08347858,-0.01780188,-0.05392413,-0.07279988,-0.02185631,-0.00721964,0.02543323,0.02444332,-0.01048922,-0.02788818,-0.14688423,-0.01477991,-0.04568832,-0.01354971,-0.03082332,-0.03460844,-0.02346627,0.02109247,0.05148347,0.01193143,-0.00550185,-0.0521517,-0.063136,0.01363881,-0.00711524,0.0648798,-0.03544743,-0.02587314,-0.00223415,-0.02565691,0.05675807,-0.02104771,-0.06090388,-0.00679069,0.12665939,-0.02408263,0.01413143,0.06635445,0.00157167,-0.0015322,0.00364622,-0.00138158,-0.00863835,-0.03547593,0.0511364,0.00474756,-0.02830938,-0.070956,0.05947879,0.06293376,0.03029632,0.03136845,0.00920569,0.05500787,0.05567447,-0.00262701,-0.05124101,0.0154112,-0.06023666,-0.1025936,0.03826858,-0.03421278,-0.19491032,0.02844445,-0.01154813,-0.00798178,-0.02723135,-0.00663431,0.06374151,0.00286772,-0.05251513,0.04292583,0.00869608,0.04773168,0.01119976,-0.02406701,0.03245918,0.03529298,0.04441034,-0.01187388,0.0388066,-0.07633592,0.01584426,0.04367023,0.22106273,0.03551544,0.05965998,0.06619911,0.01370497,0.01605663,0.11364413,0.01566489,0.05889294,-0.0166254,0.00178496,-0.02339841,0.04049532,0.06159206,-0.01329157,0.05220535,-0.0029953,0.01745993,0.03892169,0.00076268,-0.00300255,-0.00665619,0.09444454,-0.07455683,-0.02160673,-0.03033379,-0.06835633,-0.02663179,-0.10512933,0.00028001,0.05546182,-0.03927403,0.02567591,0.06441445,0.03459961,0.01007974,-0.01111766,0.01491436,0.0167472,0.04829057,0.10069737,0.05581746,0.06502873],"last_embed":{"hash":"ef12d6ad67acd392befa3e266928a15054829bec8197783d411416c7dc1ced63","tokens":103}}},"text":null,"length":0,"last_read":{"hash":"ef12d6ad67acd392befa3e266928a15054829bec8197783d411416c7dc1ced63","at":1743662881451},"key":"37 Tips from a Senior Frontend Developer.md#12. Be trusted to reach out when you need help","lines":[161,176],"size":419,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#13. Show enthusiasm for the work": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0336752,0.01962889,0.00556753,-0.07495369,0.03044446,-0.02725733,0.01997873,-0.02936414,0.00357264,-0.03991268,0.01505904,0.01929223,0.00877063,-0.00191405,-0.0155227,0.03869661,0.01741051,0.05390583,-0.05594833,0.00755421,-0.04900786,0.01672308,0.01170221,-0.07044593,0.09853568,-0.01696195,-0.01365641,-0.07137312,-0.03949,-0.11708485,-0.02281925,-0.0064891,0.07447393,-0.01685862,0.00256714,0.05674421,0.01314852,0.0644619,-0.04684411,0.0161578,0.01783912,0.00621175,-0.00980779,-0.06857818,-0.04447155,-0.06536413,-0.04334367,-0.05682452,0.01270164,-0.00523643,-0.03113191,-0.08021099,-0.03056109,0.00978923,-0.01234082,0.04547342,0.05814217,0.01932356,-0.03162552,0.03545765,0.06265469,-0.01418735,-0.14827718,0.01306806,0.0510326,0.01792839,-0.04446067,0.00989535,-0.01826328,0.02869723,-0.00010625,-0.01516167,0.01315573,0.07056808,0.02335472,-0.01238608,-0.0331839,-0.0138672,0.05076022,0.0069448,0.01331962,-0.00403225,-0.02080883,0.03829921,0.00510715,-0.02356649,0.05127509,-0.00361035,0.05791572,0.02834734,-0.00851608,-0.06455104,-0.02926498,-0.02211855,-0.00729422,-0.0350722,0.04472436,-0.00266687,-0.12528345,0.15018098,-0.06627092,0.05839797,0.05280882,0.01144061,0.06833632,-0.06140247,0.05195535,0.05436078,0.00611522,0.03907064,-0.00603471,-0.0255995,-0.00513468,-0.04117873,0.01413236,0.01417329,-0.07203895,0.0132025,-0.01203768,-0.02554459,0.02347484,0.01481062,0.04640012,0.0046807,-0.02256646,-0.0452664,0.01767378,0.0768245,0.01818631,0.05032268,0.01383577,0.03406971,-0.08087953,0.01038801,0.01555989,-0.02025353,0.03008814,-0.06387696,-0.0380902,0.06192931,0.02155318,-0.02636276,0.01101661,-0.09254368,0.00021932,0.10502721,-0.00052736,0.09083029,0.00674645,0.01139501,-0.01591901,-0.00664091,-0.02819814,-0.0097823,0.04872018,-0.00455735,0.04841454,0.04087475,-0.04572822,0.02984255,-0.00164167,-0.04127535,-0.08090767,-0.04125949,0.0207765,-0.09252881,-0.00143858,-0.00038751,0.00304094,0.01078604,0.00181437,-0.03717576,-0.03905067,-0.0054253,0.12425146,-0.04583323,-0.03772314,-0.02481664,0.02992097,0.06733213,0.10211103,0.00439431,0.05354303,0.05393073,-0.02428737,-0.05580429,0.01450792,-0.05777752,0.03523513,0.00027506,-0.0768825,0.00727365,-0.08556196,0.02400712,-0.00620531,0.02422742,-0.01036093,0.01379239,0.02661443,-0.03449558,-0.07681891,0.05296632,0.00769814,0.04546863,-0.04659238,0.0468442,0.05292831,-0.04522017,0.07745738,0.01334291,-0.10769024,-0.00458262,0.14223388,0.06705724,-0.01813451,0.0002576,0.03154793,0.12107025,-0.02139248,0.06196416,0.07481865,0.06610687,-0.02922094,-0.24207908,0.01373203,0.02019806,-0.06193885,0.03660607,0.00059951,0.12587972,-0.02110955,0.01377286,0.05327202,0.06523115,-0.03788356,-0.03815492,-0.00056095,0.02725969,0.03254667,-0.01852971,0.03312957,-0.02290484,-0.01936287,0.04401948,0.0029477,-0.03385509,-0.09962308,-0.00071108,0.01343162,0.10483783,0.01490179,-0.03301793,-0.09923968,0.00270049,-0.03629436,0.0038219,-0.14498673,0.06887165,0.00661997,0.01488443,-0.07297473,0.00165298,0.00618263,0.00605324,0.01996118,-0.01624093,-0.02592896,-0.07761204,-0.00965552,-0.01243843,-0.01611696,-0.01678489,0.03584772,0.01748877,-0.0272072,0.08122747,0.05506922,0.00093905,-0.0342663,-0.10874924,0.03502049,-0.00837593,0.06955047,-0.01984309,-0.04180604,-0.01891117,-0.00012386,0.03469932,0.01803226,-0.05540248,0.03954769,0.05610183,-0.06025749,-0.03517009,0.0675959,0.02178298,0.0202775,0.02502717,-0.02924381,-0.06925815,-0.03126261,0.04264612,-0.0075556,-0.07052762,-0.089862,0.01355151,0.07651901,0.00954799,-0.01863059,0.03187191,-0.00200464,-0.00928447,-0.02863559,-0.08527277,0.03501154,-0.05677073,-0.04866414,0.03182386,0.02007047,-0.22363825,-0.00599594,0.07930417,-0.08852217,-0.05242575,0.05905973,0.09366418,-0.06634612,-0.07280131,0.03048682,0.00606394,0.01678409,0.0176147,-0.02284885,0.04534678,0.03675463,0.02848426,-0.01215609,0.00422453,-0.13566209,0.0206947,0.03160664,0.19007909,0.02417159,0.0601403,0.04237374,-0.05324682,0.01595439,0.07307515,-0.05040468,0.01783063,-0.00074595,0.03856338,-0.05712265,-0.00974202,0.02909394,-0.0565011,0.02646724,-0.0244107,-0.01019912,0.05646646,0.01207598,0.03517599,-0.02862955,0.07192025,-0.06322894,0.02845418,-0.02990721,-0.04127989,-0.02331349,-0.06455626,0.07044181,0.02139606,-0.01577826,-0.00223729,0.04133276,0.01130934,-0.02191419,0.00413768,-0.02167715,0.01479494,0.07164088,0.04927333,0.09264824,0.06733712],"last_embed":{"hash":"0dbb1464738f579f0b9fdadec677d39e65dca8f6b1b537943fcbf113b5c68783","tokens":84}}},"text":null,"length":0,"last_read":{"hash":"0dbb1464738f579f0b9fdadec677d39e65dca8f6b1b537943fcbf113b5c68783","at":1743662881470},"key":"37 Tips from a Senior Frontend Developer.md#13. Show enthusiasm for the work","lines":[177,188],"size":336,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#13. Show enthusiasm for the work#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03006396,0.01955158,0.00704043,-0.06543807,0.02606788,-0.02400428,0.01826238,-0.02982062,0.00508344,-0.03853538,0.01659938,0.02160591,0.00779009,0.00274294,-0.01787913,0.03883002,0.01784667,0.0500433,-0.05718508,0.00811067,-0.04527788,0.02259194,0.01749757,-0.07346382,0.1025354,-0.01369173,-0.00842431,-0.07021543,-0.03350993,-0.11409427,-0.02609517,-0.00979749,0.07908136,-0.01081522,0.00269289,0.05799082,0.0107564,0.06723221,-0.0513624,0.0180785,0.01936486,0.00333442,-0.01049408,-0.07245576,-0.04913113,-0.06762916,-0.04419113,-0.0594288,0.01316396,-0.00080768,-0.03083515,-0.08087716,-0.02841185,0.01657527,-0.01283795,0.04292573,0.06062807,0.01951707,-0.03418732,0.03178957,0.06378629,-0.01380617,-0.14981583,0.0075966,0.04749506,0.01794931,-0.04807959,0.01154678,-0.02036448,0.02823176,0.00215666,-0.01342814,0.01269041,0.07102045,0.02649464,-0.01403918,-0.03558654,-0.01496166,0.04603552,0.00278351,0.01061267,-0.00632784,-0.02124656,0.03583769,0.00658052,-0.02453882,0.04697412,-0.00786929,0.05779008,0.03072702,-0.01374531,-0.06362497,-0.03899484,-0.01927711,-0.01141368,-0.0376993,0.04108755,0.00036753,-0.12181876,0.14678146,-0.06431168,0.05981216,0.05344701,0.0111083,0.06843973,-0.06246151,0.05342679,0.06007335,0.00559509,0.03863546,-0.00172105,-0.02597412,-0.00994526,-0.04279857,0.01176371,0.02025029,-0.07798361,0.01084589,-0.01309889,-0.02970339,0.02487973,0.01972279,0.04626013,0.00444845,-0.0249467,-0.04754644,0.02260771,0.07814059,0.02086388,0.04746074,0.01476667,0.03288388,-0.07552093,0.01204321,0.01556381,-0.02508598,0.03036594,-0.0623008,-0.04436752,0.06685503,0.03229961,-0.02634747,0.01259787,-0.09756956,-0.00076206,0.10632492,-0.00462695,0.09293033,0.00315553,0.01345551,-0.02011337,-0.01239195,-0.0285265,-0.00980018,0.04505951,-0.00766037,0.05181134,0.04470563,-0.04623836,0.02908963,-0.00607264,-0.03794784,-0.07944663,-0.04527436,0.01950011,-0.08293158,0.000147,0.00015507,0.00412656,0.01001582,-0.00068363,-0.03607699,-0.04185997,0.0027349,0.12006468,-0.04812087,-0.04157287,-0.02448633,0.02840563,0.06659858,0.10599931,-0.00150052,0.05542287,0.05956255,-0.02848403,-0.04766897,0.01223908,-0.05653578,0.03416348,-0.0001849,-0.07577357,0.00823944,-0.08410273,0.02711293,-0.00298358,0.01814915,-0.0097278,0.01554619,0.01924284,-0.0348464,-0.07965042,0.05052847,0.01078083,0.04615944,-0.04130085,0.0456745,0.04996644,-0.03871855,0.07500163,0.0122861,-0.11273291,-0.00257343,0.1419668,0.06599382,-0.01918542,-0.00150408,0.0328882,0.11847261,-0.01665916,0.06417775,0.07421143,0.06514941,-0.02225409,-0.24215899,0.0139974,0.01971578,-0.06132329,0.03780315,0.00159214,0.12637354,-0.01908649,0.01521313,0.05600579,0.0608061,-0.03884091,-0.03696006,0.0017105,0.02412876,0.02858478,-0.02376324,0.03136375,-0.02146908,-0.01751648,0.04729228,0.0057003,-0.03307539,-0.09812229,-0.00755078,0.01144327,0.10618009,0.009492,-0.03670349,-0.09490498,0.00582861,-0.03490308,-0.00057371,-0.1497968,0.06820843,0.00970248,0.01537207,-0.07148574,0.00313173,0.0068331,0.0048727,0.01948601,-0.02143284,-0.02216216,-0.07203673,-0.0098359,-0.00686887,-0.00840143,-0.02285052,0.04132803,0.02155956,-0.02212851,0.08181676,0.05729903,0.00333575,-0.02874488,-0.10870049,0.03815184,-0.00711704,0.06483991,-0.01852336,-0.04013435,-0.02446627,0.00220947,0.03604032,0.02171755,-0.05335579,0.03962781,0.04866637,-0.05223037,-0.03557429,0.06371072,0.0213428,0.01907796,0.0294682,-0.0300902,-0.07330102,-0.03476152,0.04160221,-0.01019644,-0.06876205,-0.09051037,0.00774719,0.07633511,0.00975068,-0.02077339,0.02925031,-0.00413047,-0.01013878,-0.02941844,-0.0820882,0.03328323,-0.05395857,-0.04220091,0.03375514,0.02455935,-0.22696139,-0.00602404,0.07756542,-0.08823454,-0.05715772,0.05714291,0.08858712,-0.06414741,-0.07630289,0.02811597,0.00446245,0.01131816,0.0154472,-0.0201237,0.04467361,0.03808377,0.02623036,-0.01625527,0.00477843,-0.13520034,0.02782936,0.03297978,0.18768634,0.02115268,0.06414585,0.04077315,-0.0541354,0.01370832,0.06854898,-0.05488514,0.01641948,-0.00712799,0.04100102,-0.0575825,-0.01306638,0.033838,-0.0615497,0.02226343,-0.02326201,-0.01451107,0.05801683,0.01221036,0.03329499,-0.03072327,0.0753039,-0.06228704,0.02821632,-0.02852009,-0.03971875,-0.02075741,-0.06001172,0.07374461,0.02284783,-0.0168885,-0.00129974,0.0408118,0.01122568,-0.01761845,0.00562613,-0.02429692,0.01399183,0.07674975,0.04983009,0.0926018,0.063894],"last_embed":{"hash":"6577a411eca95715979e3a1b452a983d6e708d025f842d05d102c374dd3ac5cf","tokens":84}}},"text":null,"length":0,"last_read":{"hash":"6577a411eca95715979e3a1b452a983d6e708d025f842d05d102c374dd3ac5cf","at":1743662881482},"key":"37 Tips from a Senior Frontend Developer.md#13. Show enthusiasm for the work#{1}","lines":[179,188],"size":300,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#14. Stay open to learning new things/tools/methods": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.033395,-0.03818955,-0.00284207,-0.07991533,0.03705223,0.00720761,-0.00731168,-0.02737074,-0.00509046,-0.01895382,0.00776755,0.02083826,0.03246506,0.00437829,0.01749047,0.04631874,-0.01650291,0.02159486,-0.02177703,-0.02293698,-0.01565374,0.02477661,-0.00286414,-0.04193756,0.03343739,0.02713296,-0.02686674,-0.06730965,-0.03288232,-0.14907081,-0.02245207,0.00957145,0.02960186,-0.03937085,-0.01063634,0.01184306,0.0134036,-0.00306443,-0.03758192,0.00945667,0.01964267,0.02347748,-0.01801105,-0.08084428,-0.02832165,-0.02995201,-0.01992091,-0.05662859,0.0271789,-0.04456725,-0.00509468,-0.03984,-0.02838304,-0.0300476,-0.01123489,0.03054073,0.00824419,0.03800743,-0.0257182,0.04192995,0.09568475,-0.01456939,-0.14510997,0.06842948,0.0286159,0.03259615,-0.02701856,-0.01906811,0.04199937,0.03341442,-0.01298556,-0.00457861,-0.0193852,0.037125,0.02861147,0.01761521,-0.03660936,-0.01994718,0.05428311,0.01408545,0.05490376,0.01413492,-0.01501817,0.01377258,-0.04565708,-0.03221266,0.06919734,-0.00389757,0.05323471,-0.00632194,-0.00401272,-0.08732941,0.0146581,-0.01901349,0.01882595,0.00923578,0.07885511,0.0194502,-0.11596277,0.13938431,-0.07709275,0.0309012,0.05551364,0.04385503,0.03641804,-0.01736409,0.03652338,0.00908278,0.00879756,0.03591513,-0.0344674,-0.04675289,0.00431068,-0.0159352,0.00038223,-0.01503171,-0.02174942,0.01634835,-0.00745818,0.02093264,0.02005264,0.01044033,0.02724657,-0.01014489,0.0142372,-0.04425279,0.01858651,0.07009391,-0.05112594,0.0027371,0.01488793,0.04034782,-0.12536564,0.0240625,0.02422817,0.03390329,0.00099628,-0.0410263,0.01004143,0.02987445,-0.02492864,0.01038793,0.04980834,-0.09174908,0.01524475,0.10652038,0.0066023,0.06246281,0.03917291,-0.03261671,0.02609738,0.01263239,-0.00447397,-0.03925231,0.03044697,0.01207469,0.02619771,-0.0147181,-0.01723425,0.0248893,-0.00345736,-0.06182996,-0.07325697,0.04769917,0.00517359,-0.07020933,-0.0296355,-0.02202801,0.02947122,-0.01542123,0.00911381,0.02952247,-0.04780836,-0.04283111,0.15411161,-0.0396452,-0.03145729,-0.02514273,0.02359572,0.00663754,0.04550987,0.01420925,0.00368556,0.02400182,-0.00962309,-0.08390491,0.07946914,-0.06882423,0.0708077,0.05873627,-0.04871683,0.05753477,-0.04950147,0.02121141,-0.0146978,0.01361863,-0.00380254,-0.01652472,0.06444599,-0.03262775,-0.05450635,0.05306406,-0.00727233,0.03968747,-0.07199567,0.06241156,0.08663387,-0.10707808,0.15137589,-0.00265311,-0.091072,-0.01975139,0.11863034,0.04234907,-0.0110058,0.00398431,0.01837505,0.08718744,-0.05486907,0.0418185,0.06027193,0.08941776,-0.04879371,-0.22331572,0.01935051,0.03225762,-0.08223318,0.03564882,-0.01722066,0.07934391,-0.03600268,-0.01193435,0.0457751,0.06856965,-0.03468461,-0.04800386,0.01621512,-0.00196705,0.02895451,0.0213593,0.01065362,-0.04686076,0.00835423,0.03408098,-0.03176002,-0.01321198,-0.12920538,0.03278069,0.01003942,0.12364008,-0.0298456,0.0262008,-0.10121474,0.01450457,-0.02032326,-0.01114637,-0.14266026,0.0786542,0.00728211,0.01959121,-0.03125854,-0.0246124,-0.02451211,0.0329168,0.02699161,0.01912039,-0.08833565,-0.06365938,0.00792407,-0.05446699,-0.05845761,-0.00120431,-0.02697995,-0.00240404,-0.01157745,0.04972948,0.04990583,-0.0638834,-0.07181837,-0.04980598,0.04374108,-0.03523443,0.07249336,-0.03908632,-0.03402579,-0.03297353,-0.06518043,0.05488979,-0.00848003,-0.06621493,0.00417638,0.08244918,-0.09990141,-0.04123097,0.09964702,0.01077146,-0.00074782,-0.03723573,-0.00105491,-0.02702096,0.01800422,0.04063589,0.03727404,-0.0410875,-0.07241674,0.06333831,0.06735431,-0.00645526,-0.00720464,0.03935091,-0.06677726,0.00088668,-0.03678302,-0.05908251,0.03043276,-0.05742416,-0.08682038,0.02000434,0.00544342,-0.20889065,0.04029648,0.05863961,-0.02222285,-0.01140541,0.06368068,0.09749693,-0.06114132,0.00411655,0.03085425,-0.02347613,0.00538446,0.06544913,-0.03457046,0.04395186,0.02687551,0.11102726,0.00182543,0.02760931,-0.11174247,0.01746251,0.05555524,0.21837349,0.01466207,0.05243706,0.02046864,-0.05479537,0.01818533,0.06554501,-0.01083174,0.03415859,-0.00155457,0.00777948,-0.00769259,-0.01364029,0.05975215,-0.02681829,0.02447044,-0.02118897,-0.00086282,0.03541454,-0.00342404,-0.02198331,0.01191484,0.04276273,-0.06447864,0.0460814,-0.06465653,-0.05704204,-0.05767165,-0.06069267,0.03372752,-0.00191773,-0.00818449,0.01531121,0.02892425,0.05454544,-0.00824948,-0.03446775,-0.01868161,0.04004212,0.01406778,0.06165159,0.05997016,0.09225689],"last_embed":{"hash":"a15b2465fc3791a74deb98a18a9d96fff964bdf8b97fa869ba26945ae7701018","tokens":67}}},"text":null,"length":0,"last_read":{"hash":"a15b2465fc3791a74deb98a18a9d96fff964bdf8b97fa869ba26945ae7701018","at":1743662881494},"key":"37 Tips from a Senior Frontend Developer.md#14. Stay open to learning new things/tools/methods","lines":[189,198],"size":252,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#15. Master your dev tools 🛠️": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05949141,-0.0052812,0.00799674,-0.07578985,0.01565945,-0.02759939,0.00487958,-0.03857437,-0.00426244,0.01000305,-0.03403156,-0.02861698,0.04607832,-0.00666396,-0.0001442,0.03601139,-0.00645318,0.0262592,-0.01107537,-0.02950841,0.03551985,0.01598898,-0.04351802,-0.04372592,-0.00421737,0.04130686,0.02529271,-0.08227962,-0.03049036,-0.15660614,0.00706874,-0.04510996,0.07522837,-0.04681212,-0.01294231,-0.03226718,-0.02907377,0.01750537,-0.05461599,-0.03488251,0.02997522,0.05357029,-0.03124731,-0.0648044,0.01972671,-0.02006095,0.02249663,-0.01046322,0.00212513,-0.01976069,0.0166766,-0.00054199,0.00857967,0.02737463,-0.02044321,0.01940352,0.02897618,0.04493776,0.00588368,0.0354244,0.07059489,-0.00621064,-0.18384129,0.09426521,0.04082861,0.06198968,-0.01349106,-0.06283604,-0.00845127,0.03360404,-0.07501516,-0.0095256,-0.02573628,0.05460583,0.01048653,-0.00879752,-0.00841118,0.00780698,0.06513724,0.00134734,-0.01620377,0.01517894,-0.04507599,0.010469,-0.03197933,0.00847721,0.05743451,0.045249,0.13332383,-0.01565923,0.03218965,-0.12572607,0.06830137,0.01982206,-0.00002671,0.0346991,0.0682869,0.03852932,-0.1057244,0.16279233,-0.0812563,0.02176521,-0.01666638,-0.01392812,0.05601715,-0.02030535,0.01937229,0.02579976,-0.00692021,0.04720977,-0.01597169,0.00288703,0.00270156,-0.07372891,0.05894304,-0.00391229,-0.03158094,0.0288662,0.01903463,0.03432592,0.04986207,-0.01501108,0.04795708,0.00340239,0.05352344,-0.01090553,0.0347062,0.07272469,-0.03568656,0.0430108,-0.01173421,0.04714266,-0.06572813,0.02246445,-0.0029451,0.02297612,-0.00075298,-0.02497628,0.02594788,-0.01706109,-0.08849861,0.01581371,0.01104534,-0.00352296,0.03553614,0.06603508,-0.00344451,0.02649264,-0.04723629,-0.09733775,-0.00587347,0.04251162,-0.02681956,-0.00526805,-0.01424298,0.04509728,0.08491993,-0.02783341,-0.02645209,-0.01433329,0.03571354,-0.02579037,-0.04564472,0.10482753,0.05070309,-0.08004612,-0.04520538,0.03497575,0.01253044,-0.03480811,-0.04690315,-0.00722089,-0.04408025,-0.02505806,0.10995908,-0.06813969,-0.03571062,0.04281867,0.01455087,0.04387302,0.0942801,-0.00773992,-0.01233646,0.04745184,-0.02119114,-0.10170395,0.03712767,-0.04416245,0.0153332,0.02419963,-0.0817313,0.02632348,-0.00193284,0.03630793,0.00908707,0.00992461,-0.043599,0.00037634,0.02825535,-0.07098648,0.03620983,0.05032248,0.00911258,0.01559666,-0.08815079,0.03119246,-0.02204083,-0.07271066,0.08393331,0.03090986,-0.11132208,-0.02276036,0.14720209,0.0016784,0.00190623,-0.01363444,-0.03316836,0.08072737,-0.01929014,0.09757999,0.00666079,0.06308752,-0.07127951,-0.21337508,0.00737839,0.02219783,-0.03811355,0.02335246,-0.04930744,0.02165932,-0.03847609,0.02614645,0.06637593,0.10778587,-0.02496279,-0.01151982,0.00604725,-0.01252379,0.05451903,0.04186796,-0.01633475,-0.03726472,-0.03508104,0.02262921,-0.02940965,0.018764,-0.07794205,-0.00925812,0.00463377,0.12210406,-0.02845169,0.05342864,-0.09932839,0.00864589,-0.00199014,0.02182803,-0.14487647,0.0338113,0.0191513,0.00300979,-0.0448109,0.01519492,0.01154994,-0.00934312,-0.01322019,0.01113583,-0.08380243,-0.04795444,-0.05717193,-0.03546989,-0.08736978,-0.00424895,-0.05096988,0.01063623,-0.02016029,0.06295878,0.03555125,-0.05400926,-0.02221368,-0.06252936,0.05400214,-0.01958926,0.07067631,0.01349308,-0.03356471,-0.0557316,-0.07754533,0.05052669,0.00436368,-0.05010701,-0.03132862,0.09586082,-0.02047002,-0.01935531,0.07595925,0.01082016,0.01990235,-0.01977016,0.01072979,-0.00182625,-0.02287187,0.03826142,-0.00343264,-0.05748035,-0.03332404,0.05703827,0.02277825,0.0272791,0.00095013,-0.00686664,-0.01343497,0.03178473,-0.00841613,-0.03938588,0.02308533,-0.07029288,-0.10300672,-0.01385737,-0.01127775,-0.17983885,0.07713184,0.06472372,-0.02774781,-0.00807482,0.00638051,0.07648618,-0.08756598,-0.056934,-0.01658091,-0.01490659,0.02865222,0.04407924,-0.01893133,0.07766373,0.01907156,0.06585222,-0.0293742,0.04325715,-0.07070317,-0.00832012,0.01707858,0.20238778,0.08732799,0.04930095,0.08297619,-0.00617908,0.01385967,0.1185073,-0.01212498,0.07204912,0.01664117,0.0885876,-0.0259138,0.00647419,0.09328291,-0.06634481,0.03963361,-0.0292748,0.02521454,0.01853992,-0.05381226,-0.03034843,0.01553678,0.01812587,-0.06756023,-0.02047644,-0.04880596,-0.04623084,-0.00993964,-0.05197279,0.02361029,-0.02482558,-0.00144428,0.02839625,0.0000944,0.03494877,-0.00767357,-0.02314358,-0.00756307,-0.00572162,-0.01030648,0.07172514,0.01955241,0.01908391],"last_embed":{"hash":"08d38386b461cff0757483142eeca3f01f7a2758b50bdf28190d7db4def33249","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"08d38386b461cff0757483142eeca3f01f7a2758b50bdf28190d7db4def33249","at":1743662881511},"key":"37 Tips from a Senior Frontend Developer.md#15. Master your dev tools 🛠️","lines":[199,211],"size":238,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#16. Focus on delivering value": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05220019,0.02559827,-0.01160986,-0.11135831,0.01798183,0.00448294,0.05512204,0.04596319,0.0131696,-0.01023674,-0.00006057,0.04073836,0.01849633,-0.0246299,-0.02375296,0.0362359,-0.01639138,0.03811296,-0.07310146,-0.00756418,0.03074178,-0.07973938,0.01098283,-0.02175132,0.03318736,0.00432705,-0.07251877,-0.03201493,-0.08233655,-0.15349758,-0.06502245,-0.02654879,0.06091416,-0.04630654,0.00100047,0.03603436,-0.0026815,0.0142836,-0.03451713,-0.00670161,0.03936907,0.02913683,0.0100695,-0.02426147,-0.05527374,-0.03298436,0.03169155,-0.08624159,0.00494323,-0.01325186,0.00626828,-0.02601996,-0.08149391,0.04642549,-0.03575953,0.03997953,-0.01489279,0.03533142,-0.03610789,0.0568193,0.06750145,-0.02822101,-0.19641398,0.07347094,0.06035129,0.04555158,-0.04558378,-0.02317036,-0.0374494,0.05612454,-0.01088052,-0.0379821,-0.01404104,0.07686549,0.06089439,0.01025404,-0.01230969,-0.01836639,0.00000262,-0.01046383,-0.01752974,-0.00185701,-0.00988541,0.06773648,-0.00681821,-0.00349274,0.06790685,0.00088176,0.07648981,-0.03561407,0.05815974,-0.10873571,-0.02773659,-0.00328425,-0.00698469,0.00437262,0.08252694,-0.03235624,-0.07597928,0.16486199,-0.03680818,0.00381715,-0.03093506,-0.03255018,0.08131938,-0.05203413,0.04597975,0.03303656,-0.03056248,0.04331087,-0.08214065,0.0315506,-0.02284173,-0.02192575,0.04289976,0.06486601,0.00936229,0.0376182,-0.01323205,-0.03193447,0.02723992,-0.01269997,-0.00467107,-0.00028233,0.00553129,-0.04189377,0.05201025,0.1107482,-0.02121155,0.04337494,0.02746062,0.00251797,-0.0796155,-0.00177885,-0.01824916,0.00112336,0.01388857,0.02905885,0.01992919,0.01658141,0.03786543,0.01915484,-0.04120553,-0.01361998,0.03158736,0.09243601,-0.02016408,0.00664801,-0.01827428,-0.03103055,-0.03050521,0.0372006,-0.03087202,-0.03267285,0.02993554,0.05254521,0.00662044,-0.02635479,-0.05010499,-0.04705391,0.03634846,-0.02681158,-0.10872614,0.0918415,-0.05927538,-0.07093695,0.01129629,0.00682878,0.05390597,-0.00837313,-0.02876752,-0.01508008,-0.03722844,-0.09342542,0.09294891,-0.01338371,-0.02747677,0.03403033,0.04222032,0.05511085,0.05686517,0.04175977,0.00415068,-0.03772319,0.03791007,-0.06652173,-0.0235259,-0.03528096,0.00721795,0.04198249,-0.07368862,0.00343028,-0.05166166,-0.01254437,-0.04349002,0.01937908,0.00529481,0.01553035,0.04450273,-0.03496768,-0.00500221,0.05429969,0.04717805,0.04183966,-0.01104754,0.05803028,0.0931877,-0.04656054,0.03542662,-0.00497396,-0.0625195,0.01749498,0.09483358,0.05483182,-0.06723363,0.03316612,-0.01262039,0.12181341,-0.00514211,-0.0138407,0.03891709,0.01297547,-0.01783665,-0.21174404,0.01674164,0.0266636,-0.06609038,0.01672288,-0.03671002,0.03918482,-0.01292372,-0.03628183,0.0100024,0.11704751,-0.09028723,-0.04717096,-0.03300381,-0.00100668,0.02229836,0.04054894,-0.02981735,-0.00663715,-0.00323579,-0.00398633,-0.05108554,0.0092716,-0.08501924,0.08700619,-0.01488496,0.11473541,-0.02850861,0.06955076,-0.0339565,-0.01041652,-0.03314265,0.00245121,-0.10332914,0.05768397,0.00592812,-0.04361466,-0.00466376,-0.00960963,-0.01073511,0.02299939,0.00211131,-0.03412505,-0.06137476,-0.04997106,0.01983839,-0.08693359,-0.09050036,-0.01350788,0.04336556,0.02197664,-0.02672963,0.0161485,0.07548218,0.01774771,-0.04259666,-0.04489485,0.04547405,-0.01906683,0.05786052,0.00746273,-0.02707153,0.03497891,-0.02474651,0.02837411,0.00258483,-0.02904468,0.0051355,0.08195496,-0.02302309,-0.01416104,0.09146243,-0.009915,-0.03858647,-0.01148012,0.0241608,-0.02246571,0.04542785,0.06837855,0.02878628,0.02384907,0.00808498,0.07034512,0.04029005,0.07911677,0.04860906,0.03376361,0.01933672,-0.00372228,-0.00909986,-0.09182044,-0.00059775,-0.02781018,-0.05530451,-0.04941003,-0.03856804,-0.23503396,-0.00811494,0.01302171,-0.03050913,-0.00268573,0.01977011,0.07482935,-0.09191205,-0.11636598,0.07080209,0.01592826,0.03750611,0.00015159,-0.05092854,0.03410408,0.00228466,0.04766304,-0.01851881,0.04038709,-0.04298414,0.06466522,0.00171691,0.22295524,0.03374451,0.03653734,0.00716098,-0.03145884,0.0517331,0.12059639,0.03958346,0.0462171,0.00502476,0.07710167,0.01674069,-0.00443596,0.015912,-0.05083375,0.03396995,-0.00411094,0.00696883,0.02126007,-0.01069981,0.01382014,-0.0314918,0.06010823,0.02183605,0.00900061,-0.0789501,-0.06761496,-0.04917156,-0.10475469,0.04035686,0.00773292,-0.00428643,0.0231827,0.00730805,0.02960042,-0.05275389,-0.04975954,0.03266381,0.01216685,0.01601127,0.02591511,0.04595994,0.03536419],"last_embed":{"hash":"62edf2003d89534496263825a2aca4fbc9defa47ffb108da12fbfd4f90b84610","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"62edf2003d89534496263825a2aca4fbc9defa47ffb108da12fbfd4f90b84610","at":1743662881524},"key":"37 Tips from a Senior Frontend Developer.md#16. Focus on delivering value","lines":[212,228],"size":317,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#17. Advocate for your work: it won't speak for itself": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0517582,-0.00850775,-0.00096963,-0.1132916,0.0201541,-0.05420524,0.03580262,0.02298858,0.00114185,-0.04592108,-0.02021523,0.01617926,0.00564669,-0.03055737,0.00114778,0.01973102,-0.00275128,0.05654756,-0.01558717,0.0001835,-0.03507078,0.01493349,0.00551333,-0.0236848,0.0690229,-0.01127637,-0.02986339,-0.05661533,-0.04480814,-0.17169702,-0.04920933,-0.00287747,0.04676514,-0.04058781,0.01548756,0.02885219,-0.00336224,0.02343662,-0.03707038,-0.00544816,0.01989913,0.03401363,-0.03291237,-0.03387179,0.00219466,-0.02009182,0.00959411,-0.05540941,-0.0052986,-0.04773057,-0.01837463,-0.05102981,-0.01822167,-0.00004326,-0.01242842,0.05089004,0.0416716,0.07144142,-0.00057499,0.04114431,0.06951471,-0.04524591,-0.1638602,0.07696468,0.07531425,0.02592054,-0.02202543,-0.02575302,-0.02739198,0.03560376,-0.02660724,-0.01766486,0.00558606,0.10065222,0.02566698,0.02795476,0.00462159,-0.00227898,0.03355762,0.02412249,0.00252679,0.01847991,-0.03899522,0.03564105,0.0102151,-0.0299442,0.07568619,0.05335455,0.06438299,-0.01457374,0.06195174,-0.07368463,0.02553731,-0.00067304,0.00299106,0.00933268,0.0748578,-0.01194025,-0.10931879,0.15920642,-0.0434397,0.01573559,0.03870403,0.0205788,0.02650902,-0.0414555,0.01960422,-0.01254004,0.00224424,0.04709548,-0.01983146,0.01330858,-0.00562218,-0.01616773,0.04320023,0.02635504,0.02880643,0.05941949,-0.04815432,-0.0185993,0.01693859,-0.00355969,0.02719542,0.00088817,-0.00855622,-0.04799309,0.04891803,0.08184824,0.0034008,0.05418121,0.00096258,0.02449119,-0.05043243,0.01855232,0.02742877,0.03126868,0.02125985,0.00758534,0.0396654,-0.00054257,0.02585782,-0.00674787,-0.02752768,-0.03295257,-0.01544151,0.11424424,-0.01938272,0.039244,0.01020979,-0.01796809,-0.03479418,0.06674662,-0.0235316,-0.01797327,0.05923234,0.00718112,0.08191602,-0.02304173,-0.04763215,0.01377608,0.05352136,-0.04711162,-0.0591006,0.06384457,0.02979368,-0.0777288,-0.03090695,-0.00484707,0.02398933,0.00382121,-0.0197994,-0.03846774,-0.01421599,-0.0657716,0.11793248,-0.03362083,-0.00393222,-0.00781147,0.03588698,0.04496539,0.04630868,0.03084081,0.00648457,0.03334697,0.01936016,-0.1144137,0.0355701,-0.04020634,0.07748474,0.01910489,-0.05637729,0.0139546,-0.05746358,0.00606941,-0.03239191,0.01727135,-0.01314066,-0.01997478,0.06713255,-0.03184376,-0.0651409,0.05371759,0.00587671,0.02344054,-0.0488726,0.05755207,0.05017502,-0.07040504,0.11420529,0.02456007,-0.12865669,0.01141707,0.09931259,0.0764241,0.02867435,0.0217725,0.0077238,0.0988159,-0.01883814,0.00184149,0.05996294,0.03989115,-0.07459968,-0.25388676,-0.01600434,-0.0010472,-0.04001983,0.0216215,-0.01673942,0.07940418,0.01039356,0.00285763,0.09000525,0.06724702,-0.05425137,-0.04507168,-0.01278333,0.01624227,0.02675504,-0.01647577,0.01260582,-0.04371836,-0.01892716,-0.02029246,-0.02776098,0.00230201,-0.11806963,0.0520463,0.0338814,0.11929817,0.01192427,0.03009434,-0.07421548,0.01748284,-0.04621109,0.03536504,-0.15804604,0.03201242,0.02298083,-0.01210957,-0.05384578,-0.05231754,-0.01408319,-0.00021259,0.00601221,-0.02720709,-0.09158174,-0.08182915,0.01509165,-0.13349786,-0.05281984,-0.03181588,0.00083303,-0.00151715,0.00704353,0.02826667,0.07156207,-0.02221071,-0.03066385,-0.09427649,0.03349454,-0.03437796,0.07599742,-0.01432674,-0.0516715,0.02397345,-0.03165279,0.03775815,0.0197109,-0.05564077,-0.01999784,0.07686586,-0.05234506,-0.02545831,0.10118569,0.0014933,-0.09312224,0.00257942,-0.00159344,-0.04967649,-0.04554947,0.06168737,0.01685353,-0.04222185,-0.05420669,0.052981,0.01513813,0.0260449,0.05708836,0.00470557,-0.02034118,0.0211564,-0.0673977,-0.08337648,0.01945542,-0.04984362,-0.04678752,0.01685042,-0.00390036,-0.20615877,-0.02316414,0.06870549,-0.05796826,-0.0263252,-0.00596303,0.07561426,-0.09807893,-0.05736353,0.05584876,-0.01624983,0.0219295,0.01879741,-0.04549766,0.05167167,0.03129198,0.06478978,0.02658886,0.02403207,-0.09512576,0.02516309,0.04131046,0.18598516,0.00541284,0.01176078,0.03690543,-0.01710852,0.02942103,0.04408551,0.00180102,0.0394358,-0.01691256,0.05471307,-0.00951768,0.01602818,0.00062947,-0.05304367,-0.01898768,-0.01243129,0.01682523,0.07179564,-0.02700657,0.02752592,0.00286037,0.08151221,-0.05068016,0.00007955,-0.04710935,-0.06588693,-0.0421746,-0.08143468,0.04673428,-0.01195673,0.00520853,0.02664024,0.02498957,0.03123908,-0.03414239,-0.01698953,0.00879641,0.01660884,0.0313555,0.0436996,0.06622703,0.07993796],"last_embed":{"hash":"3fef5e419b3de452bfcba0d545a075e96bab40d97d55f7c3abe64bdc135a9b95","tokens":90}}},"text":null,"length":0,"last_read":{"hash":"3fef5e419b3de452bfcba0d545a075e96bab40d97d55f7c3abe64bdc135a9b95","at":1743662881543},"key":"37 Tips from a Senior Frontend Developer.md#17. Advocate for your work: it won't speak for itself","lines":[229,243],"size":325,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#18. Prefer writing dumb code over clever code": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05102204,-0.00576191,-0.04788601,-0.04533134,0.0383075,0.02027646,0.00349822,0.0269982,-0.01306294,-0.02900213,-0.03432361,0.04895909,0.02443766,0.01173275,-0.03386369,0.04974315,-0.01973096,0.02061677,-0.03753623,-0.03618486,0.04996936,-0.00572893,-0.03907542,0.04276189,0.03274468,0.03456163,-0.05346536,-0.02120965,-0.08414851,-0.15014336,-0.0294456,0.03289416,0.06963426,-0.01468476,-0.01377515,0.00224257,0.00533345,0.03969589,-0.03603815,-0.02213103,0.00577878,0.02402156,-0.00452431,0.00200682,-0.02729935,-0.0120238,-0.01758583,-0.04512325,0.0005546,-0.01009636,-0.0548445,-0.01487746,-0.01667396,0.00874992,-0.02591315,0.03603426,-0.01694149,0.04579253,-0.01345693,0.01035782,0.05147755,-0.01590245,-0.1679903,0.07399573,0.05198155,0.04358645,-0.02768352,-0.04269346,0.00813157,0.02631803,-0.00047624,0.00304862,-0.04690048,0.09587032,0.01446094,0.00559258,-0.03305303,-0.03990848,0.02774677,0.03871502,0.00257579,-0.00796994,0.0120971,0.02872432,-0.01460358,-0.02568204,0.02785331,0.03099145,0.08736124,-0.02715454,0.04375532,-0.07028469,-0.01863169,0.03930677,0.05175753,0.00444518,0.07508135,-0.02319906,-0.10899742,0.13036583,-0.07172575,-0.01042472,0.00101433,-0.00948498,0.04833704,-0.05077296,0.09169856,0.00464979,-0.00470846,-0.00770251,-0.04827391,-0.03923048,0.05474354,0.00364991,0.06976283,0.02603415,0.02396316,0.04405891,-0.01004403,0.06363807,0.03898593,-0.03165724,-0.0169683,0.00556531,-0.00161714,-0.04227655,0.01163045,0.10210451,-0.01676169,0.0805259,0.06661897,0.03001971,-0.05856935,-0.00842283,0.00391002,0.00397019,-0.00597213,-0.00115008,0.00192458,-0.07557392,0.03296816,0.01714271,0.01956207,-0.0326678,0.04053229,0.07394047,-0.02906301,-0.0209817,0.00786619,-0.05445622,-0.02713169,0.0082246,-0.01685911,-0.05047916,0.01066179,0.03305021,0.06802933,-0.00782643,-0.05664096,-0.0030594,0.01856686,0.01650785,-0.06799729,-0.00471008,-0.03326902,-0.07657282,-0.00897438,-0.00210143,0.00865939,-0.04674202,0.04052872,-0.00279658,-0.04116791,-0.02847595,0.05580335,-0.00584955,-0.01920777,-0.00305376,0.04480332,0.05608216,0.05633158,-0.00185821,0.00413418,0.00774323,0.02703514,-0.0481038,0.02538972,-0.04242636,0.02057142,0.01347046,-0.09043942,-0.00853151,-0.05326538,0.0088266,-0.02668422,0.01136082,-0.01381403,-0.01683514,0.02890948,-0.04354721,-0.06816602,0.06536751,0.00136604,0.05572359,-0.02519597,0.00683382,0.08436113,-0.09391028,0.04816306,-0.01442989,-0.09157054,-0.03366048,0.09451775,0.03718575,-0.04343239,-0.04311198,-0.02364652,0.10415403,-0.01733033,0.00087702,0.02587685,-0.03637026,-0.05723188,-0.21379437,-0.03838649,0.05335896,-0.09009486,0.04263945,-0.06605237,0.03844934,-0.05321255,-0.02228836,0.06022804,0.08751459,-0.06461728,-0.03095412,-0.04111679,-0.00026792,0.07913788,-0.00474454,-0.005161,-0.00791725,0.01208492,0.02313459,-0.00207422,0.01375761,-0.1314477,0.02921113,0.02556266,0.12165415,-0.02953577,0.1141766,0.0084973,0.00770515,-0.03962402,0.04612173,-0.10358118,0.09529594,0.02104635,0.00686966,-0.01335719,-0.03172264,-0.04657285,0.00228461,0.00337797,-0.04496194,-0.07558279,-0.10278888,-0.01739949,-0.10457334,-0.10094352,-0.04117436,0.03320243,0.00257482,-0.00666124,0.00410929,0.07388356,-0.03604702,0.00083344,-0.08095767,0.02177256,-0.02752204,0.09758034,0.03422301,-0.03023579,0.00392981,-0.00495576,0.01684666,0.04987012,-0.02481182,0.01424682,0.08223882,0.04884488,-0.01664343,0.15255083,-0.00100887,-0.03581079,-0.04882159,0.03399136,-0.01445036,0.01709101,0.0384018,-0.01908417,0.00130092,0.03355527,0.04798857,0.05699109,0.03578921,-0.00131308,0.02537807,-0.00583926,0.0249608,-0.0114519,-0.08384538,0.05453253,-0.04468074,-0.04138582,-0.01976584,-0.0078322,-0.24764426,0.00333076,0.03136015,-0.02415539,0.00492768,0.00104269,0.08605798,-0.09592932,-0.08362439,0.05627717,-0.00343193,0.04917491,-0.00040147,-0.04595977,0.05184658,-0.03209941,0.08563177,0.00049098,0.04346861,-0.07514109,0.0819753,0.05291336,0.22670636,0.02972065,-0.00518392,0.02234788,0.01988304,0.07812329,0.10429737,0.04067088,0.06348089,0.03808405,0.04759551,-0.01295759,0.00337337,0.05078221,-0.0407007,0.01733371,0.0127616,0.01567789,0.03251714,0.0236493,-0.04428692,-0.02166117,0.0505966,0.01802665,-0.06525316,-0.08860142,-0.04641188,-0.02628523,-0.07236926,0.02186969,-0.03704616,-0.01614855,0.05465885,-0.02660296,0.03084313,-0.04856192,-0.0432748,0.02067464,0.05053622,-0.03962802,0.07784521,0.08697215,0.02466463],"last_embed":{"hash":"6d333877f6cd5646a304a2e57442778a6ff60856919ba83e2d6bcfc5497db7df","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"6d333877f6cd5646a304a2e57442778a6ff60856919ba83e2d6bcfc5497db7df","at":1743662881562},"key":"37 Tips from a Senior Frontend Developer.md#18. Prefer writing dumb code over clever code","lines":[244,255],"size":311,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#18. Prefer writing dumb code over clever code#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04986463,-0.00860132,-0.0488735,-0.04067486,0.03681308,0.0236005,0.00344231,0.02763626,-0.01234547,-0.02975369,-0.03391066,0.0497453,0.02287401,0.01339028,-0.03424707,0.04903982,-0.01980854,0.01675246,-0.03771367,-0.03347171,0.05245769,-0.00655773,-0.03703587,0.04568876,0.03138427,0.03558332,-0.05308535,-0.02127961,-0.08544374,-0.15018991,-0.03081128,0.03297963,0.07078166,-0.01299406,-0.01791562,-0.00079362,0.00411614,0.03803929,-0.03398763,-0.0213118,0.00524484,0.02340956,-0.00401746,0.00308608,-0.02904435,-0.01003856,-0.01735655,-0.04436461,0.00450937,-0.00825959,-0.05526069,-0.0114476,-0.01516924,0.01265203,-0.02809746,0.03310208,-0.0190368,0.04693452,-0.01438014,0.00637874,0.0504697,-0.01325273,-0.16828769,0.07366525,0.04713681,0.04277468,-0.02676854,-0.04051687,0.00708333,0.02586145,0.0016462,0.0056011,-0.04971918,0.09408645,0.01474639,0.00389605,-0.0335608,-0.04111821,0.02447858,0.0385056,0.00334026,-0.00889866,0.01262712,0.02527537,-0.01647932,-0.02859964,0.02441421,0.02950314,0.08543447,-0.02872208,0.04290638,-0.06738757,-0.02024641,0.04235081,0.05215995,0.00319784,0.07398967,-0.02222438,-0.10806094,0.1273344,-0.07167791,-0.01150216,-0.00169972,-0.00729428,0.0490081,-0.0498973,0.09352327,0.00545361,-0.00510939,-0.00776955,-0.0462961,-0.03867158,0.05720026,0.00491744,0.07115245,0.02966663,0.02514144,0.04440036,-0.01005022,0.06343122,0.03548652,-0.03268562,-0.0160948,0.00439289,-0.00224119,-0.04585797,0.01267215,0.10385305,-0.01244709,0.08155409,0.0685808,0.02800262,-0.05702883,-0.00920597,0.00431824,0.00440623,-0.0056938,0.0008968,0.00226445,-0.07949667,0.03764053,0.01764498,0.02204692,-0.03317027,0.04336442,0.07435173,-0.03274187,-0.02343113,0.00446732,-0.05638925,-0.02890993,0.00766542,-0.01784655,-0.05065165,0.00703755,0.03340375,0.06972023,-0.00912617,-0.05799647,-0.00446816,0.01649463,0.01947206,-0.06731962,-0.0080303,-0.03445987,-0.07051904,-0.00762629,-0.00262589,0.00628128,-0.04815077,0.04547087,-0.0022596,-0.0421399,-0.02255973,0.05210791,-0.00379145,-0.02049673,-0.00330287,0.04173557,0.05888442,0.05627645,-0.0024479,0.00541025,0.00880549,0.02911402,-0.04356914,0.02208098,-0.03952328,0.0211416,0.01162249,-0.09016369,-0.00931908,-0.05029313,0.00912536,-0.02694592,0.01189356,-0.01434854,-0.01721666,0.02556563,-0.04375038,-0.07111035,0.06296544,0.00233254,0.05513722,-0.02149462,0.00625164,0.08389296,-0.09309286,0.04883528,-0.01719427,-0.09343567,-0.03214953,0.09235685,0.03497146,-0.04591852,-0.04487985,-0.02376156,0.10194249,-0.01534414,-0.0009088,0.0238323,-0.04109211,-0.05581395,-0.21151562,-0.03973394,0.0532064,-0.08857958,0.04570873,-0.0687331,0.03436901,-0.05183955,-0.02268792,0.06268886,0.08372337,-0.0648949,-0.0295945,-0.04196503,-0.00234571,0.07912594,-0.00650817,-0.00593331,-0.00617905,0.01438269,0.02165122,0.00098706,0.01331022,-0.12916254,0.02804732,0.02543132,0.12309453,-0.03301528,0.11511605,0.01469197,0.00991448,-0.03950262,0.04664554,-0.10258748,0.09618465,0.02316311,0.00583688,-0.01275562,-0.03211338,-0.05041562,0.00025593,0.0020661,-0.04813565,-0.0746727,-0.10262039,-0.016463,-0.10368363,-0.09995475,-0.04298769,0.03557603,0.00413909,-0.00485462,0.00205512,0.07472618,-0.03697291,0.00362772,-0.08218734,0.0198846,-0.02931707,0.09668794,0.0364849,-0.02917396,0.00469059,-0.00600765,0.01699859,0.05003263,-0.02382598,0.01442364,0.07704244,0.05805397,-0.01426345,0.15507658,-0.00232034,-0.03799034,-0.04966928,0.03526551,-0.01385271,0.01858193,0.03690734,-0.0222482,0.00456165,0.03603477,0.04617041,0.06086599,0.03443398,-0.00245992,0.02578857,-0.004754,0.02541551,-0.00876135,-0.08125839,0.05483162,-0.04298538,-0.03674953,-0.02060518,-0.00805539,-0.24869452,0.00172035,0.0286227,-0.0203645,0.00682723,-0.00379076,0.08562624,-0.09257492,-0.08287175,0.05881204,-0.00179252,0.04937863,-0.00260246,-0.04548401,0.05139903,-0.03594749,0.08441155,-0.0000144,0.04445223,-0.07342795,0.08753069,0.05187932,0.22533335,0.02912048,-0.00662402,0.02093201,0.02386631,0.08006146,0.1007526,0.04142931,0.06351835,0.03614012,0.04977564,-0.01289848,0.0040175,0.05244057,-0.04351916,0.01653206,0.01374214,0.01459706,0.03288503,0.02391462,-0.0478534,-0.02440414,0.05421507,0.02117267,-0.06957016,-0.09067138,-0.04526504,-0.02353073,-0.06900592,0.02267728,-0.03800385,-0.01501179,0.0552427,-0.02779079,0.02935412,-0.05076576,-0.04297836,0.02145261,0.05166673,-0.04200469,0.07677333,0.08555797,0.02036922],"last_embed":{"hash":"ded358aad5fa0077c99ec49ae31299b1099c877fd370c146712f91859052f28b","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"ded358aad5fa0077c99ec49ae31299b1099c877fd370c146712f91859052f28b","at":1743662881579},"key":"37 Tips from a Senior Frontend Developer.md#18. Prefer writing dumb code over clever code#{1}","lines":[246,255],"size":262,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#19. Your manager is your best ally": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03426692,-0.01860703,-0.0209157,-0.06957252,0.00894448,-0.02358393,0.06994864,-0.01762066,-0.00414958,-0.04813961,0.00359362,0.0116643,0.04157718,0.00500955,-0.02413908,0.03035203,-0.03023706,0.08270415,-0.08305863,0.00007296,-0.10470219,-0.02824002,-0.05438272,-0.00304097,0.05783368,-0.02443003,-0.04235298,-0.03141412,-0.10020649,-0.14370574,-0.01134197,-0.05266995,0.02458996,-0.03826414,0.04588175,0.05989545,-0.01184557,0.04335938,-0.03126437,0.00400303,0.05981067,0.06742549,0.01514958,-0.05268341,-0.00879597,-0.0493862,-0.00154002,-0.03189678,0.00465821,-0.03441542,0.0465893,-0.02321809,-0.07447267,0.05674007,-0.00694903,0.13269222,0.04949299,0.03831995,-0.03247988,0.0779183,0.02248627,-0.00935875,-0.14230797,0.02240112,-0.00131814,0.0419326,-0.03196278,0.0222458,-0.03666983,0.07917994,-0.00808343,-0.03542572,-0.03412827,0.03249248,0.03428496,0.00407939,-0.00104974,-0.02850921,0.04954358,0.07076955,-0.00556472,0.02869078,-0.03250022,-0.01923714,0.02705175,-0.02667522,0.05183309,-0.00114294,0.05997984,0.01292613,0.04129195,-0.03434552,0.027691,-0.04376903,0.02919706,-0.03175293,0.05227507,-0.02538264,-0.15300383,0.18299468,-0.03950647,0.05422822,0.00932514,0.0020962,0.03975232,-0.00484637,0.00862309,0.05512726,0.03455832,0.04569528,-0.00014122,-0.05695618,0.00906871,-0.04133899,0.06075976,0.01199848,-0.00605953,-0.00546947,-0.03334576,0.00503508,0.01576962,0.03375175,0.05124849,0.02884032,-0.01019512,-0.03989578,0.02751143,0.06247253,-0.01570957,-0.00018612,0.02025137,0.0050658,-0.12450621,-0.0285242,0.01192014,-0.01813271,0.01567601,-0.05551462,0.01135697,0.04527211,-0.01236014,-0.07223845,0.04888459,-0.07192186,0.01485755,0.15577278,0.00857414,0.01623815,-0.06190113,0.00075626,-0.02035517,0.01142857,-0.003638,-0.08403297,0.03470529,0.00608813,0.02767626,-0.00830672,-0.02937396,0.02585497,-0.00347554,-0.03300598,-0.04561759,-0.00732467,-0.01618692,-0.08518237,-0.04629631,0.01897545,0.01348179,-0.02123153,-0.00911974,-0.04602853,-0.011553,-0.01883562,0.11326383,-0.02770654,-0.04621636,-0.04910102,0.01540991,0.01255892,0.05818081,0.0007528,0.01744236,0.00366546,-0.01362181,-0.06728483,0.02178845,-0.08050183,0.01038286,-0.01141918,-0.05653029,0.05264363,-0.05284838,-0.01761973,-0.01209165,0.01487203,-0.03303713,-0.00702721,0.03565694,-0.02464505,-0.053258,0.03151939,-0.01280849,0.00205746,-0.06761783,0.03198685,0.04011076,-0.08175498,0.13265833,0.00351316,-0.07545701,-0.03382844,0.09856233,0.06156539,-0.01427244,0.028627,0.02481637,0.1245125,-0.00115687,0.02573261,0.08231542,0.09565666,-0.06594945,-0.20612969,0.007301,-0.05276156,-0.06021832,-0.04888925,0.01370387,0.08188008,-0.00148366,0.00837812,0.06328318,0.13683726,-0.07370418,-0.04136537,0.00190001,0.00910567,0.00788991,-0.034457,0.08067068,-0.01012425,-0.03868534,0.01725572,-0.01189364,-0.03358684,-0.05253108,0.08657067,0.03512804,0.13007508,0.03274101,-0.00681311,-0.05010983,-0.00249653,-0.01021137,-0.01915062,-0.11050133,0.06140749,-0.0153239,-0.00981114,-0.11954381,-0.02971224,0.00193994,-0.00797585,0.03299446,-0.00500156,-0.05684223,-0.07538691,0.00027603,-0.04064965,-0.00189613,-0.00894063,0.00458308,-0.032229,-0.00544928,0.06827359,0.02656158,0.01334984,-0.0160264,-0.08687077,0.04646407,0.0038882,0.07961722,-0.03432463,-0.03226855,-0.00543766,-0.0438983,0.06319384,-0.03122241,-0.04792187,0.02503462,0.07670762,-0.05340176,0.01055347,0.01770226,0.01100451,0.0248576,0.0021978,0.00398481,-0.02340458,-0.02841632,0.0172709,0.0382314,-0.00406718,-0.11283568,0.06493399,0.0596837,0.02109588,0.02426726,-0.00382606,0.04360507,0.04633209,-0.05129281,-0.05412639,0.01836228,-0.0414358,-0.07313363,0.0138444,-0.01749115,-0.19223356,0.02356998,0.03140142,-0.04643602,-0.01166648,0.01130778,0.06116424,-0.02743702,-0.08752698,0.02667267,0.03571089,0.01969163,-0.01651314,-0.04295891,0.03776367,0.0201354,0.02684766,-0.01106438,0.0555765,-0.0889798,0.00160404,0.02659842,0.19754995,0.03797028,0.01718461,0.10236444,0.01159026,0.00255105,0.05326626,-0.00389308,0.05374189,0.02872491,0.03647326,-0.03885551,0.05059323,0.01988718,-0.02099454,0.03076898,0.01003029,0.03480505,0.05295313,0.0163209,0.01324024,-0.00649237,0.07330503,-0.07538869,-0.00230446,-0.01939131,-0.07926153,-0.01526993,-0.10875288,0.01975967,0.02373681,-0.04785693,0.01921447,0.01942741,0.03993484,-0.03030497,0.0237874,0.01308872,-0.00239396,0.01415193,0.05003527,0.07396055,0.07851011],"last_embed":{"hash":"7bc3170bb3df21a7ee90751036de091396de3c5f6f7d61ff31467169515baf1a","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"7bc3170bb3df21a7ee90751036de091396de3c5f6f7d61ff31467169515baf1a","at":1743662881587},"key":"37 Tips from a Senior Frontend Developer.md#19. Your manager is your best ally","lines":[256,267],"size":408,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#19. Your manager is your best ally#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03276642,-0.02289399,-0.01863208,-0.06156399,0.00649286,-0.02396405,0.07340356,-0.01583607,-0.00401798,-0.04855749,0.00867654,0.0115817,0.04079382,0.00919288,-0.02468457,0.02642313,-0.03149152,0.08373594,-0.08449745,0.00098792,-0.10384066,-0.02783992,-0.05366395,0.00249237,0.05699059,-0.02074325,-0.03826546,-0.02508663,-0.10182948,-0.14170852,-0.01014044,-0.05390831,0.02520102,-0.03580843,0.04517214,0.06264343,-0.01574599,0.04494116,-0.02926193,0.00517067,0.0659081,0.06706855,0.01468753,-0.05703615,-0.00928822,-0.04806727,0.00033045,-0.03025339,0.00278879,-0.03329756,0.05389284,-0.02116105,-0.07662472,0.06373397,-0.00567463,0.13498075,0.05359588,0.03603185,-0.0362737,0.07882424,0.01928321,-0.0063108,-0.14266668,0.02046925,-0.00936266,0.04228,-0.03198211,0.02487081,-0.04398047,0.08123427,-0.00494824,-0.0334073,-0.03776069,0.02668198,0.0350678,0.00046116,0.00216612,-0.03040589,0.04697179,0.07613357,-0.00987809,0.0268302,-0.03086924,-0.02779764,0.02961286,-0.0284299,0.04352714,-0.00635167,0.05542252,0.01587736,0.03993541,-0.02931367,0.02721983,-0.04327047,0.03085766,-0.03529826,0.04535353,-0.0254913,-0.15668805,0.18302369,-0.03923803,0.05429749,0.00594877,0.00113524,0.03918105,-0.00213431,0.00280726,0.05765912,0.03705258,0.04649095,0.00630538,-0.06150362,0.01074678,-0.04359623,0.06300183,0.0137181,-0.00038664,-0.01162909,-0.03494142,0.00504841,0.012976,0.03609361,0.05706996,0.03078908,-0.01049245,-0.04439127,0.02876713,0.06021788,-0.01173523,-0.00674607,0.02380414,-0.00280953,-0.12900198,-0.03280302,0.01263668,-0.02248696,0.01635923,-0.05850888,0.0148355,0.04501559,-0.01198775,-0.07948471,0.05129707,-0.07534563,0.0158463,0.15967242,0.00501239,0.01232327,-0.07410282,0.00421576,-0.02658498,0.00769628,-0.00509051,-0.08625696,0.03410099,0.00620233,0.02819736,-0.00499433,-0.0279406,0.02905028,-0.00876476,-0.0320828,-0.03908752,-0.01190641,-0.0187302,-0.07935652,-0.04419108,0.01974734,0.01278937,-0.02333892,-0.00984141,-0.04951684,-0.015169,-0.00946156,0.10842212,-0.02984587,-0.04479282,-0.05122046,0.00949004,0.00915416,0.05992291,-0.00185129,0.01896055,0.00567691,-0.01158425,-0.06084607,0.01651118,-0.08341401,0.00541308,-0.011296,-0.05661606,0.05659642,-0.04394924,-0.02131896,-0.00871066,0.01242416,-0.03390533,-0.00606261,0.02802095,-0.024152,-0.05419265,0.02838354,-0.00991438,-0.00235798,-0.06369554,0.02886087,0.03337041,-0.08027859,0.13343449,0.00613865,-0.07512601,-0.03295944,0.09471274,0.05694317,-0.01295859,0.02571902,0.02913065,0.12506372,0.00450361,0.02598801,0.07916559,0.09927582,-0.06540694,-0.20286295,0.00607787,-0.06120754,-0.05422845,-0.05340545,0.01438378,0.08073174,-0.00006604,0.00890097,0.06565035,0.12924446,-0.07568453,-0.03732442,0.00448791,0.00807916,0.0043056,-0.03749853,0.08475955,-0.00825272,-0.03733724,0.01656642,-0.00616169,-0.03548002,-0.04567528,0.08504844,0.0308399,0.13426588,0.03145786,-0.01152801,-0.03908351,-0.00314501,-0.0083767,-0.02116377,-0.11115286,0.06017023,-0.01364918,-0.01228374,-0.12418462,-0.02461845,-0.00042518,-0.01034155,0.0337088,-0.00634287,-0.05310087,-0.0780347,0.00004894,-0.03304716,0.0049558,-0.01406496,0.00326943,-0.03642029,0.00024563,0.06691474,0.02217001,0.01575149,-0.00920546,-0.08636537,0.04790835,0.00530593,0.07396273,-0.03328398,-0.03364072,-0.00651059,-0.04360492,0.0613377,-0.03246953,-0.04356427,0.02554629,0.06797598,-0.04987049,0.01218518,0.01208601,0.01283953,0.02701434,0.00491609,0.00341451,-0.02248314,-0.02750491,0.01092481,0.03585708,-0.00027912,-0.11422937,0.06353553,0.06038877,0.01917007,0.02186234,-0.0110945,0.04606752,0.05046265,-0.05129124,-0.04835628,0.01423778,-0.04125455,-0.06889549,0.01509841,-0.01581931,-0.18988228,0.02710139,0.02652139,-0.0429975,-0.01056011,0.00968781,0.05241436,-0.02156116,-0.08868222,0.0256426,0.04375742,0.01682782,-0.02340509,-0.03887945,0.03334089,0.01829874,0.02408618,-0.01554632,0.06172376,-0.08573165,0.00506912,0.02539073,0.19241849,0.03572422,0.01794025,0.10726658,0.01533681,0.00304778,0.05221276,-0.00180046,0.0560493,0.02779794,0.04230576,-0.04318065,0.05908253,0.01733357,-0.01669443,0.03165879,0.01376201,0.03742791,0.05332101,0.01951117,0.00930538,-0.00870136,0.07850862,-0.07547602,-0.01020774,-0.01665682,-0.07832132,-0.00923864,-0.10963549,0.02028692,0.0239924,-0.04667054,0.02041955,0.02320921,0.03888632,-0.03172745,0.02679569,0.01445105,-0.00261774,0.01441128,0.05223877,0.07207689,0.07367446],"last_embed":{"hash":"1da5046eca46cb51f463cccc9074b2294c761cf593aa3b576382ef8596efd29f","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"1da5046eca46cb51f463cccc9074b2294c761cf593aa3b576382ef8596efd29f","at":1743662881626},"key":"37 Tips from a Senior Frontend Developer.md#19. Your manager is your best ally#{1}","lines":[258,267],"size":370,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#20. Make your manager's life easier": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06195363,0.00363314,0.01310062,-0.0711742,0.0352989,-0.01306155,0.00870093,0.00091984,-0.05802123,-0.04030802,-0.00498592,0.03655792,0.00169312,-0.01736503,0.00860733,0.00275818,0.0048163,0.07661473,-0.0344494,-0.01895886,-0.04044821,0.0015324,-0.05318799,0.02842606,0.01856588,-0.02221559,-0.07780946,-0.02523683,-0.09128399,-0.17383166,-0.02544661,-0.08694563,0.08553495,-0.02317931,0.04892671,0.02270137,-0.01994708,0.04398761,-0.0202719,-0.03504597,0.04277903,0.03399672,-0.00508511,-0.07285754,-0.03839394,-0.06698484,0.02040649,-0.0806224,-0.00102993,-0.02812375,0.01517078,-0.02370626,-0.04026593,0.04355237,-0.03222487,0.04300237,0.05057164,0.02462404,-0.02807499,0.03916969,0.02199504,-0.01313386,-0.13557917,0.04006769,0.00193203,0.04536898,-0.06548338,0.00901975,0.00668697,0.08521912,-0.07273106,-0.05861281,-0.01860963,0.03316751,0.04414386,-0.00686884,-0.0056004,-0.00711023,0.04059661,0.00360633,-0.01054917,0.02792165,-0.01445061,0.00994026,0.00151669,-0.03970809,0.05126039,0.01517755,0.09052337,-0.02498803,0.05769891,-0.0423922,0.02270212,-0.00884138,0.00570994,-0.03292311,0.0706973,-0.02035781,-0.1100818,0.18334292,-0.01408487,0.01824427,0.04218481,-0.01002236,0.03402013,-0.00450234,0.02112415,0.07046904,-0.03290186,0.05253532,-0.02504406,-0.01956731,-0.03796807,-0.05797482,0.06995951,0.01768389,0.01123371,0.01799492,-0.0274978,-0.01210074,0.06396357,0.01048824,0.04643687,0.03404435,-0.01584506,-0.00940217,0.06039734,0.05549442,-0.01328001,0.02880346,0.02577517,-0.01196718,-0.10436043,0.01081613,0.03847209,-0.00915577,0.04656157,-0.01251144,0.0229909,0.08119029,-0.0052682,-0.03356457,-0.0140329,-0.06186093,0.02721167,0.1421247,0.00676805,0.03266174,-0.03354756,-0.05202527,-0.00883355,0.05673564,-0.03093532,-0.06444965,0.05488261,0.00375173,0.08250621,-0.01115329,-0.00662295,0.02014325,0.02077285,-0.0884226,-0.02811007,0.05196349,0.01738568,-0.08446889,-0.02898775,-0.00544026,0.00929257,-0.03513651,-0.01644169,-0.02525575,-0.0344476,-0.02564723,0.09574688,-0.0077747,-0.05841638,-0.02286237,0.03811774,0.00706754,0.06052997,-0.00547157,0.02401469,0.01052441,0.00276533,-0.07909732,0.04004932,-0.02670861,-0.00812862,0.00179657,-0.03730141,0.03062714,-0.05366912,-0.01830567,-0.00591297,-0.00221118,-0.0349587,-0.00501649,0.03839174,-0.04191587,-0.02765221,0.01624883,0.04251718,0.00358598,-0.07240637,0.01814424,0.0337465,-0.06937716,0.14655681,-0.02328678,-0.05909349,-0.02049211,0.13081193,0.04382631,-0.00733126,0.05593481,0.00457577,0.09411699,-0.00511679,-0.03055636,0.0726387,0.09363255,-0.03584976,-0.20992297,0.00615054,-0.02219238,-0.04021442,-0.04873351,-0.01913914,0.0883114,-0.01156978,-0.0140176,0.06289988,0.1126231,-0.0920872,-0.04713016,-0.0159666,0.01191382,0.04090429,-0.00739899,0.03570025,-0.03385952,0.00772966,0.01675959,-0.02237389,-0.0368437,-0.06505438,0.08327208,0.02215679,0.13448018,-0.0378128,-0.0091549,-0.07623004,-0.04716391,-0.0233484,-0.03903066,-0.13574532,0.05171715,0.00738241,-0.07189742,-0.10959982,0.00582061,0.01671934,-0.0097369,0.01101033,0.00584071,-0.04203901,-0.10491145,0.05472144,-0.05643673,-0.04301461,-0.03049784,0.00101863,-0.0707493,0.01889512,0.01419549,0.02888061,0.00594253,-0.04606802,-0.06167654,0.06084741,-0.02994434,0.06870036,-0.01013582,-0.04202154,0.00998677,-0.04509819,0.0611155,-0.00706053,-0.10846855,-0.00632957,0.09129997,-0.04128434,0.01644173,0.06785344,0.01724513,0.01600344,0.02460681,0.02119504,0.00439359,-0.0373069,0.05959799,0.0001478,-0.04864718,-0.06437245,0.06597163,0.04813607,0.00184281,0.0482785,-0.02673629,0.02451104,0.02656179,-0.03195082,-0.07303644,0.0332377,-0.00732449,-0.03366435,0.02056755,-0.00158684,-0.19540335,0.03605551,0.07224465,-0.0197834,-0.02193582,0.00534699,0.03790019,-0.04628423,-0.05389564,0.01952873,0.01951303,0.04172564,-0.01085045,-0.02465913,0.05403778,0.05461943,0.00823752,-0.01798309,0.04038025,-0.09116375,0.01515467,0.05136102,0.21040754,0.03492881,-0.00681037,0.03745391,0.00886431,0.0234014,0.12844646,-0.00175653,0.03469708,-0.00724695,0.03124675,-0.00045442,0.03314925,0.04197705,-0.0437229,0.04256132,-0.00936548,0.0491718,0.04127794,-0.00801949,-0.04381245,-0.0484519,0.05968472,-0.06614178,0.01818857,-0.04699879,-0.06891584,0.00879393,-0.0843086,-0.00202168,0.02752895,0.00602161,0.03257263,0.03928816,0.03756221,0.00483751,-0.01121566,0.01178291,0.01059346,0.02733923,0.06281711,0.08431379,0.0682592],"last_embed":{"hash":"d6a77fb8a8b2ab55e13af5fbbf0765afef728065d84f8ee7a627cfee598a70c7","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"d6a77fb8a8b2ab55e13af5fbbf0765afef728065d84f8ee7a627cfee598a70c7","at":1743662881646},"key":"37 Tips from a Senior Frontend Developer.md#20. Make your manager's life easier","lines":[268,281],"size":450,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#21. Understand the big picture behind your tasks": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04778016,-0.0019607,-0.04079933,-0.13439141,0.04594591,0.01432145,0.09322993,-0.00791671,0.00787647,-0.02955306,-0.00546427,0.02456807,0.04095221,-0.00442571,-0.00532464,0.04390651,-0.04408181,0.03160143,-0.07916451,-0.04059971,-0.01208341,-0.02738197,0.007065,-0.01196547,0.07080029,0.03960435,-0.07525416,-0.07127456,-0.0369527,-0.18035294,-0.01978977,-0.0285161,0.07737885,-0.02192809,-0.00534799,0.0380883,-0.01445096,-0.0160038,0.02083446,0.01316716,0.04726319,0.03854779,-0.0130837,-0.0569763,-0.04203853,-0.07931815,-0.00007513,-0.0682044,-0.0109233,-0.04901227,-0.01979816,-0.05298589,-0.04377292,0.02096635,-0.02025462,0.07893593,0.02658999,0.02851253,-0.03168303,0.05183768,0.08100002,-0.05639029,-0.16297142,0.06444353,0.0563939,0.03533673,-0.04283201,0.00156677,-0.03441946,0.08458953,-0.03165545,-0.04560061,0.00200693,0.06590576,0.03733055,0.01272306,0.01337701,-0.02584412,0.04424007,-0.01153661,0.02751453,0.04172112,-0.00498789,0.04228263,-0.04132828,0.04011172,0.07048305,0.00314757,0.07237352,-0.01833978,0.05420855,-0.06913842,-0.03277612,-0.02220535,0.01520044,-0.00371012,0.08896855,0.04880584,-0.08345279,0.15569112,-0.03992337,0.0170061,-0.00615581,0.00721452,0.05297017,-0.01358967,0.07846916,0.03765227,0.04094013,0.0553082,-0.03764025,-0.01784271,-0.04224005,-0.00495486,0.04602266,0.00926041,-0.02538097,0.02489431,-0.00114698,-0.04027847,0.02718906,-0.00539861,0.02479731,-0.0074535,-0.02432117,-0.0282173,0.02026243,0.06526631,-0.009548,-0.01151666,0.05972242,-0.02194428,-0.06424396,0.03225204,0.02722955,0.0003726,0.02793585,0.01468898,-0.01426418,0.00797395,-0.02520699,0.02575175,0.03238921,-0.03862629,-0.02355204,0.12063002,-0.02256048,0.03594291,-0.00240323,-0.03311866,-0.03093209,0.04566449,-0.01707323,-0.02766972,-0.00176803,0.00855102,0.0573842,-0.02226918,-0.04649699,-0.00251612,0.00260733,-0.06899484,-0.09918584,0.04297331,0.02656653,-0.08003153,-0.01146015,-0.02108214,0.03878186,-0.01243244,-0.00915353,-0.03267786,-0.03915031,-0.05395059,0.12742382,-0.01171724,-0.0509289,-0.0129475,-0.0001725,0.03897615,0.03453396,-0.0309085,-0.01305924,-0.03026079,-0.03770991,-0.08868647,0.02831827,-0.05837725,0.02163838,-0.02454621,-0.0690298,0.05326605,-0.01976031,-0.00525231,-0.01207682,0.00285565,-0.00412649,-0.02181348,0.03203528,-0.03040983,-0.05191354,0.05371941,0.03502132,0.03641157,-0.09236754,0.02149358,0.06993266,-0.0622844,0.14375909,0.02779209,-0.08824664,0.01230608,0.08205654,0.01523602,0.00966216,0.00439412,0.00429792,0.10039421,-0.02686063,0.01337691,0.03152096,0.05227697,-0.03564547,-0.22011685,0.01469219,0.00795364,-0.07789687,-0.0413869,-0.01392426,0.07754176,-0.00430796,-0.00482989,0.01783025,0.12203198,-0.05326623,-0.02751703,-0.0236711,-0.01022084,0.03201354,0.01397974,0.04130487,-0.01454203,-0.00170037,0.03459772,-0.03446554,0.01723005,-0.09907557,0.00862769,-0.00438936,0.12777005,0.00325483,0.02740268,-0.03443524,0.01925533,-0.07667164,-0.01627278,-0.10206894,0.06629249,-0.02975384,-0.0080947,-0.07773589,-0.03905317,0.01818563,0.00436637,0.00163087,-0.00256571,-0.07137906,-0.11600892,-0.00218164,-0.05460415,-0.05966779,0.01940162,-0.00394616,0.03985883,-0.01472028,0.01341389,0.03535591,-0.01125058,-0.03451457,-0.0818232,-0.01008435,-0.04706789,0.08411983,-0.04341884,-0.02082652,-0.01950364,-0.0011853,0.02611751,0.03391762,-0.03374198,-0.02035395,0.08756537,-0.04361977,0.00522509,0.08767434,0.02180034,-0.0528063,-0.0049361,-0.02025393,0.00222286,-0.00402156,0.0795736,0.01245132,-0.02025538,-0.04819732,0.05377656,0.02175948,0.07527525,-0.00082479,0.03651749,-0.02332612,0.01820207,-0.02281069,-0.05361614,0.02638722,-0.04092451,-0.09971254,-0.00303773,-0.00528616,-0.20968479,0.00691835,0.06659541,-0.02353699,-0.01235491,0.01508367,0.1093779,-0.07451733,-0.03812975,0.04352779,0.0074535,0.04652654,0.03110588,-0.04366606,0.03170885,0.04135485,0.0882097,0.0317045,0.03578109,-0.02319925,0.02574849,0.03836523,0.20662844,0.00421465,0.05356494,0.03285156,-0.02357092,0.01584574,0.10118541,0.00514867,0.08514866,0.00693501,0.10974364,-0.02855688,-0.00931649,0.05328258,0.00545624,0.02970395,0.03392819,0.0126083,0.08124891,-0.03942819,-0.02394054,-0.0168971,0.08625261,-0.01137233,-0.0205832,-0.08696363,-0.0238505,-0.0515964,-0.06977201,-0.01770747,-0.02601366,-0.03547028,0.01532609,0.02188379,0.05730122,-0.01857718,-0.00328749,-0.00624543,0.0359988,-0.01826285,0.06353138,0.03328011,0.03001788],"last_embed":{"hash":"ba11be63a6241484ce50e711bdbb54f6cc8d0d3c49920c56d0754200e2818530","tokens":94}}},"text":null,"length":0,"last_read":{"hash":"ba11be63a6241484ce50e711bdbb54f6cc8d0d3c49920c56d0754200e2818530","at":1743662881659},"key":"37 Tips from a Senior Frontend Developer.md#21. Understand the big picture behind your tasks","lines":[282,297],"size":395,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#22. Contribute to the team (documentation, tech talk, demos, etc.)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05161638,0.0156536,0.00425724,-0.1076478,0.04807941,-0.02717728,0.04384694,0.00536311,-0.02618867,-0.0022636,-0.02293912,0.01352011,0.04024998,-0.02851849,-0.00451934,0.0309897,-0.01478324,0.04892789,-0.05791016,-0.05579939,-0.08012301,-0.02202041,0.00865125,-0.01651234,0.06717134,-0.03616443,-0.06615188,-0.09479844,-0.07289013,-0.13612667,-0.02929331,-0.03080265,0.04878873,-0.01235115,0.00347885,0.0304996,-0.01555538,-0.00179061,-0.04813446,0.00460612,0.03211148,0.02211419,-0.03313559,-0.05006713,-0.01977717,-0.05693307,-0.01478432,-0.05337496,-0.01089538,-0.01169682,0.01945063,-0.0284609,-0.04626079,0.00520341,-0.01453276,0.07094851,0.01593866,0.0320874,-0.01573997,0.01448841,0.0474872,-0.008229,-0.16639246,0.09151313,0.01632302,0.02239857,-0.07103829,0.00588993,0.01501037,0.03154634,-0.05486608,-0.02444256,0.01569023,0.06231854,0.07659079,0.04764451,0.00431868,-0.03458394,0.02411762,-0.03240809,0.0222818,0.00648161,-0.03480028,0.03297508,0.00964459,-0.02694839,0.07338264,0.03718443,0.05682072,-0.01783486,0.0402823,-0.00837859,-0.00065979,-0.00152785,-0.00407277,-0.02767125,0.06558449,0.00326391,-0.09179129,0.17702834,-0.06269117,0.05011003,0.03777927,-0.00100061,0.0406024,-0.03115172,0.05914013,0.04382645,0.00339536,0.0552438,-0.03192364,0.0114251,-0.06863972,-0.0102135,0.07735637,0.01598142,-0.03165308,0.03432721,-0.01663739,-0.04488814,0.05591566,0.0085107,0.03509548,0.01729353,-0.01459989,-0.02995622,0.04682217,0.0725882,0.00521883,0.05654714,0.0397442,0.03636907,-0.07771744,0.01363516,0.01956509,-0.02114505,0.00843498,-0.03301458,-0.01349516,0.05838709,0.0119766,0.01398525,0.0307843,-0.09985839,-0.02168563,0.1182166,0.00707872,0.06282915,-0.0067362,-0.02333497,-0.00733597,0.05422855,0.00054805,-0.03320805,0.02138817,0.01673706,0.06825804,-0.019764,-0.02694664,0.02693825,0.0111231,-0.02036383,-0.07473258,0.06690249,0.01861312,-0.12731408,-0.05846296,-0.00010591,0.01891979,0.00796835,-0.03163743,-0.02012062,-0.03604056,-0.02777509,0.09486158,-0.01067923,-0.0535676,-0.02648993,0.04101172,0.00010464,0.06227773,0.01804865,-0.01887393,0.00980161,0.04332752,-0.07012098,0.03676514,-0.04690929,0.01276636,0.03684586,-0.08884074,0.03606962,-0.0735549,0.02315251,-0.02879351,0.04918185,-0.01305147,-0.01559809,0.03251299,-0.0351674,-0.05768398,0.01044478,-0.0108543,0.04117499,-0.05001986,0.05184654,0.03760835,-0.06856749,0.1326343,-0.00779173,-0.10332394,-0.03518956,0.11246242,0.08099214,-0.02670337,0.02734032,-0.01027765,0.10578858,-0.03058384,0.02675631,0.06052052,0.08339881,-0.045189,-0.20536359,0.02075394,0.0132722,-0.02416443,0.00752477,-0.00184418,0.08416933,0.00496473,0.02480411,0.03221686,0.15437974,-0.06807932,-0.07825524,-0.0167451,-0.00680947,0.0146966,0.02367605,0.02775232,-0.01082008,-0.00836944,0.02945533,-0.02491585,0.01071766,-0.08200768,0.06755108,0.01504367,0.12535807,-0.00224795,0.01537524,-0.03874912,-0.01190617,-0.01248208,-0.01261902,-0.14709617,0.05552798,0.00993915,-0.01716522,-0.05933293,-0.01939395,-0.0193713,0.00351045,0.01962769,-0.00907691,-0.11212593,-0.05389653,0.00352058,-0.09136398,-0.05527827,-0.01202832,-0.01301117,0.01143892,-0.0038982,0.03518589,0.03571525,0.0093086,-0.05325887,-0.06458167,0.06218994,-0.05223273,0.08171782,-0.02499711,-0.04172407,-0.0325813,-0.04429022,0.05801213,-0.00954634,-0.07276373,0.02754261,0.09328666,-0.05268929,-0.02347447,0.07215247,0.01225933,0.00857697,-0.01979498,0.01193551,0.00960567,-0.04542434,0.06726233,0.00127318,-0.03618351,-0.070701,0.05299878,0.0286645,0.03259163,0.03438363,0.03054524,-0.01304637,0.02101597,-0.03659719,-0.07187422,0.02076235,-0.0359374,-0.03788106,0.0113023,-0.01881195,-0.23023748,0.01544787,0.05621975,-0.04575432,-0.03512149,-0.00006018,0.062677,-0.06648396,-0.05706911,0.03661624,-0.00788236,0.05159359,0.02463653,-0.03131442,0.05157659,0.05630313,0.07017265,-0.01218424,0.03835326,-0.0699353,-0.00614304,0.03214413,0.20102167,0.02801602,0.03648663,0.0589502,-0.02339663,0.00899104,0.06189412,-0.040971,0.00480518,-0.00878189,0.01527393,-0.01314506,-0.02820939,0.0642231,-0.03495106,0.02145991,-0.03264755,0.03181649,0.03703394,-0.00848989,0.01939213,-0.04853588,0.07500463,-0.02613289,0.01445497,-0.07425074,-0.07247701,-0.04665428,-0.07667102,0.03716363,0.00643524,-0.04224171,0.03211411,-0.00951029,0.04440776,0.00429273,0.00019191,-0.00000561,0.01129815,0.02197198,0.03513484,0.08780589,0.09218542],"last_embed":{"hash":"f81c25b4cad6e818772f18ecc2d46a8a6e30c09179537524949d366334a4ff12","tokens":87}}},"text":null,"length":0,"last_read":{"hash":"f81c25b4cad6e818772f18ecc2d46a8a6e30c09179537524949d366334a4ff12","at":1743662881674},"key":"37 Tips from a Senior Frontend Developer.md#22. Contribute to the team (documentation, tech talk, demos, etc.)","lines":[298,307],"size":337,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#22. Contribute to the team (documentation, tech talk, demos, etc.)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04869607,0.01273392,0.00393145,-0.10416025,0.04773171,-0.02797741,0.04774218,0.00984987,-0.02915385,0.00172407,-0.02105964,0.00887001,0.04134468,-0.02967794,-0.00552032,0.02910364,-0.01898362,0.04706949,-0.05833963,-0.05636479,-0.08365753,-0.0247123,0.01552259,-0.0109371,0.06481175,-0.03581169,-0.06914235,-0.09839755,-0.07047022,-0.13298689,-0.02786988,-0.03100709,0.05232655,-0.00618912,0.00135132,0.02929167,-0.01783071,-0.00862648,-0.04764681,0.00766759,0.03376107,0.01957647,-0.03547635,-0.05227565,-0.01760867,-0.05922076,-0.01428718,-0.05381145,-0.00853903,-0.00928299,0.0252443,-0.02415705,-0.04664166,0.01176255,-0.01751528,0.07352448,0.0143746,0.03414451,-0.0113274,0.01077001,0.04480369,-0.00304084,-0.16771919,0.09179932,0.01127167,0.02515625,-0.07352903,0.01088459,0.01570876,0.02904704,-0.05900197,-0.02120044,0.01592671,0.06052339,0.08376137,0.05108922,0.01051839,-0.0395621,0.02183338,-0.03853846,0.02138855,0.00718437,-0.03529045,0.0320739,0.00868998,-0.02575365,0.07342748,0.03503724,0.05103209,-0.01566176,0.03939779,-0.00046653,-0.00215122,0.00408062,-0.00733002,-0.03298995,0.06275448,0.00569795,-0.09174237,0.17491235,-0.06274289,0.05320805,0.03712515,0.00004028,0.03890914,-0.03068099,0.06057517,0.0475419,0.00416764,0.05781735,-0.02894406,0.01534469,-0.07909837,-0.00827312,0.08295643,0.01830924,-0.03608072,0.0334171,-0.02125877,-0.0503133,0.0558758,0.01097746,0.03776721,0.02156661,-0.01790524,-0.03201152,0.04894567,0.07508633,0.01086269,0.06007226,0.04322881,0.03504985,-0.07969239,0.01505929,0.0177161,-0.02520735,0.00778697,-0.03604958,-0.01721988,0.06343424,0.02046784,0.01212883,0.03429862,-0.10303211,-0.023723,0.12042978,0.00209727,0.0615604,-0.01004492,-0.0252442,-0.01330033,0.05578979,-0.00057827,-0.03292629,0.01525431,0.01504344,0.07158823,-0.02718324,-0.02633376,0.02823139,0.00768133,-0.01572455,-0.07433469,0.06683632,0.01933876,-0.12140035,-0.06197234,-0.00124209,0.01455442,0.00819673,-0.03331113,-0.01945435,-0.03798507,-0.01877386,0.09091114,-0.00792187,-0.0587296,-0.02655146,0.03758242,-0.00401797,0.0616776,0.01814011,-0.019788,0.01190674,0.04973139,-0.06162483,0.0316841,-0.04652269,0.00904326,0.03684521,-0.08847664,0.0430103,-0.07233932,0.02354316,-0.0290051,0.05218498,-0.01397528,-0.01765028,0.02693645,-0.03433567,-0.06147674,0.00739078,-0.01040365,0.03839778,-0.04784853,0.04922752,0.03391889,-0.06613299,0.13652003,-0.01200756,-0.10628112,-0.03435542,0.11036163,0.07996576,-0.02974425,0.0327094,-0.01106919,0.10210645,-0.02350141,0.02908061,0.05855694,0.08011654,-0.04203703,-0.19885059,0.02078919,0.01339072,-0.01979434,0.00427891,-0.00063732,0.08065046,0.01040395,0.02208874,0.03385175,0.15446357,-0.06686246,-0.07794297,-0.01381657,-0.01396559,0.0117266,0.02279661,0.03025878,-0.00702465,-0.00685627,0.02801245,-0.02148273,0.01118632,-0.07631616,0.06701758,0.01690538,0.12876745,-0.00536361,0.00924257,-0.02977871,-0.01202339,-0.01233071,-0.01836992,-0.15100512,0.05116507,0.01003966,-0.01950687,-0.05815116,-0.0165423,-0.02017283,-0.00055097,0.01936252,-0.01400332,-0.1105722,-0.05081712,0.00646174,-0.09129948,-0.05048545,-0.01530463,-0.01178358,0.01157102,0.00034558,0.03146956,0.03504306,0.00921437,-0.04946997,-0.06260936,0.06634905,-0.05580521,0.07826512,-0.02392462,-0.03821632,-0.03788888,-0.04896244,0.06253541,-0.01286566,-0.07534847,0.03036889,0.08702063,-0.04610014,-0.02147184,0.06742218,0.00905051,0.01363082,-0.01886117,0.01169172,0.01219916,-0.05134002,0.06692631,-0.0021147,-0.03342941,-0.07213118,0.05088644,0.02787288,0.03628796,0.03609581,0.02842202,-0.00913849,0.02239148,-0.03583298,-0.06781884,0.01428774,-0.03121753,-0.02633766,0.01121283,-0.01899605,-0.23165937,0.01562465,0.05465119,-0.04320766,-0.03600612,-0.00533785,0.05911363,-0.06404801,-0.05873449,0.03556456,-0.00491942,0.05083628,0.02266579,-0.02672626,0.05030549,0.06181226,0.06903122,-0.01790868,0.03926201,-0.06796715,-0.00336784,0.03169996,0.19705349,0.02602136,0.03921398,0.05633603,-0.02156489,0.00606788,0.0554538,-0.04595919,0.00259215,-0.01498581,0.0188429,-0.01245579,-0.02912411,0.06566247,-0.03789319,0.0192723,-0.0303169,0.03380508,0.03519428,-0.0089422,0.0196507,-0.0558384,0.08420876,-0.02218079,0.01194666,-0.08171622,-0.07672399,-0.04233145,-0.07220077,0.03916579,0.0100056,-0.04272225,0.02938318,-0.01602539,0.04448497,0.00832441,0.00168202,0.00118825,0.00835084,0.01858107,0.03174433,0.08930975,0.08688197],"last_embed":{"hash":"8832298915f0b1b3773b7334ad379a7f4db72bcbde0b4fbb36af55017176c99a","tokens":87}}},"text":null,"length":0,"last_read":{"hash":"8832298915f0b1b3773b7334ad379a7f4db72bcbde0b4fbb36af55017176c99a","at":1743662881690},"key":"37 Tips from a Senior Frontend Developer.md#22. Contribute to the team (documentation, tech talk, demos, etc.)#{1}","lines":[300,307],"size":267,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#23. Become the \"go-to-person\" in a specific area": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01557412,-0.00879385,0.01188042,-0.05754597,-0.02113996,0.01410749,0.05883443,-0.02750495,-0.02867203,-0.00957585,-0.01635918,0.01097715,0.00091615,-0.02046081,0.02053039,0.04497034,-0.03454909,0.02683883,-0.02226416,-0.00929832,-0.03383789,0.00736902,-0.0109051,-0.02893045,0.07251683,-0.02151944,-0.03811621,-0.07305167,-0.06789892,-0.1434197,-0.02914997,0.03025556,0.01505465,-0.01874808,-0.03055671,0.00971741,-0.00405776,0.03453727,-0.01083388,0.00312865,0.03505031,0.01360529,0.00319537,-0.0610108,-0.04713706,-0.01674837,-0.0342594,-0.03029264,-0.03147611,-0.01520544,0.0165528,-0.0600426,-0.04948987,0.01636639,-0.00896389,0.03407571,-0.01101747,0.05786394,-0.03085202,0.06399563,0.0673561,-0.00493319,-0.19419347,0.07334783,0.02045366,0.03640832,-0.03516795,-0.02761122,-0.00888265,0.05370021,0.01141649,-0.02796633,-0.03603162,0.0347239,0.06897953,0.01813361,-0.03600052,0.01168161,0.07609919,0.02456069,0.0223448,0.00416387,-0.03207938,0.00896852,-0.05092652,-0.00704167,0.04985435,0.01007365,0.03633357,-0.02132048,0.030913,-0.07185557,-0.02492992,-0.0077357,0.02824548,-0.02369301,0.07314154,0.01087746,-0.05693632,0.10826868,-0.06025399,0.01786097,0.07803027,0.03183365,0.02512701,-0.04435913,0.03614071,0.02384279,0.02229219,0.04026206,-0.03702032,-0.03195623,-0.02433615,-0.0227739,0.04470036,0.06299103,-0.01257039,0.06972384,-0.00891527,-0.03138033,0.04280354,0.020079,0.01400992,0.0278558,-0.02690176,-0.02261941,0.02661297,0.06325163,0.01013876,0.0328206,0.01160401,0.02043251,-0.12627175,0.02308272,0.03703252,0.02754097,0.02664913,0.01326405,-0.00131733,0.05580783,-0.01441104,0.00469786,0.04976507,-0.04265479,0.01184681,0.15406542,-0.00420451,0.04276334,-0.00995363,-0.02244787,-0.03450396,0.01996781,-0.0178052,-0.03029627,0.01542276,0.00341268,0.04639097,-0.00658309,-0.08156101,0.00149552,-0.00787029,-0.04801522,-0.05872098,0.08443058,0.02119622,-0.11870598,-0.02743763,0.00334498,0.0235498,-0.00948359,0.0298217,-0.02556705,-0.0524275,0.00041847,0.14244483,-0.01369304,-0.07024442,-0.01325025,0.00841588,0.02990042,0.07773262,0.01406236,0.01415171,-0.04161659,-0.00464948,-0.06207765,0.04546048,-0.04569694,0.04386077,-0.02061054,-0.11593515,0.04830596,-0.05646471,0.01588554,-0.00387437,0.03468124,-0.04199298,-0.02716747,0.08318183,-0.02833606,-0.07855495,0.00798888,-0.02694672,0.02677623,-0.06776126,0.05301593,0.08050686,-0.07964263,0.12134515,0.00256059,-0.07809474,-0.06727835,0.11494361,0.03435406,0.03455118,0.02706709,0.03695514,0.11441812,-0.06181969,0.00762734,0.04545435,0.03812085,-0.0638497,-0.22092785,-0.00985283,-0.0046855,-0.03780684,0.00319857,0.02275634,0.06585785,0.00890797,0.01462258,0.06688637,0.09353054,-0.05899113,-0.07455168,0.01435547,0.00465516,0.04534942,0.01121815,0.04632081,-0.01489043,0.00287698,0.02553512,0.03702502,-0.0835139,-0.11617532,0.03240394,0.03431361,0.10782404,-0.01545306,-0.00164003,-0.05475705,0.00220319,-0.0122853,0.00828229,-0.06970633,0.07389308,-0.01190778,0.00914616,-0.05369352,-0.01297469,-0.04077878,-0.00511828,0.04809148,-0.01286098,-0.07149244,-0.13771105,-0.03213582,-0.06272402,0.00442828,0.02721837,0.01158604,0.04496425,-0.05714283,0.04267569,0.03814674,0.0211156,-0.06821187,-0.07568344,0.04983464,-0.0431798,0.07457794,-0.01048784,-0.04832067,0.01888949,-0.01223797,0.05387178,-0.01912096,-0.06736015,0.04439152,0.05881544,-0.1033683,-0.01474364,0.07975695,0.02278163,-0.00417171,-0.04717513,-0.02040024,-0.02152712,0.01360951,0.02407655,0.01855634,-0.00182402,-0.1201335,0.0305309,0.02902654,0.02258568,-0.00094628,0.03110579,-0.03639053,0.01232313,-0.01207036,-0.0512559,0.00621554,-0.0495239,-0.02151135,0.00177622,-0.05732725,-0.23433612,0.0150144,0.04369818,-0.04233357,-0.00719634,0.01937525,0.06560512,-0.03735137,-0.04254603,0.01535888,0.02916916,0.00017043,0.0859445,-0.00277715,0.01618909,0.00186293,0.06619771,0.02513825,0.02240187,-0.06945698,-0.00387324,0.03580001,0.21075203,0.04841391,0.0605648,0.0172934,-0.05988595,-0.01630713,0.05787757,0.00690457,0.04523017,0.0146564,0.02828496,-0.04455932,0.01290716,0.06232387,-0.01755296,0.04437945,-0.05130433,0.00078865,0.04272601,-0.03286811,-0.01159078,0.02622251,0.09175038,-0.00539454,0.00573784,-0.04728394,-0.07972681,-0.08628967,-0.07269779,0.05361305,0.00117455,-0.06686843,0.02188239,0.06630155,0.06717234,-0.00248123,-0.00979041,-0.00467907,0.01394589,0.02070845,0.03023434,0.08684218,0.08143198],"last_embed":{"hash":"91f267fb2bbe67e1f13a106562237b87e1a798bfd059f63fe156aefe2df64263","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"91f267fb2bbe67e1f13a106562237b87e1a798bfd059f63fe156aefe2df64263","at":1743662881703},"key":"37 Tips from a Senior Frontend Developer.md#23. Become the \"go-to-person\" in a specific area","lines":[308,319],"size":528,"outlinks":[{"title":"T-shaped","target":"https://www.forbes.com/sites/lisabodell/2020/08/28/futurethink-forecasts-t-shaped-teams-are-the-future-of-work/","line":9}],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#23. Become the \"go-to-person\" in a specific area#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00834949,-0.01268442,0.01409511,-0.0498628,-0.03483069,0.01721171,0.06600759,-0.02592995,-0.03172719,-0.00763835,-0.01332611,0.00864169,-0.00093823,-0.01781777,0.01677702,0.04540651,-0.03801709,0.02146999,-0.02342899,-0.00710069,-0.03328537,0.00969438,-0.0089289,-0.02721876,0.06929833,-0.01708579,-0.03594521,-0.07494453,-0.06727281,-0.13750185,-0.02927089,0.03477431,0.00979243,-0.01292962,-0.03777206,0.00516386,-0.00517915,0.03093987,-0.0098569,0.00718982,0.03591403,0.01067561,0.00533587,-0.06351867,-0.04568227,-0.01469965,-0.03258964,-0.02570584,-0.02934476,-0.0132072,0.01698755,-0.05391522,-0.05050132,0.01856868,-0.01215386,0.0340848,-0.0108629,0.06264612,-0.02996032,0.05873712,0.06385703,-0.00199421,-0.19690564,0.0708605,0.0152159,0.03698704,-0.03346629,-0.02610694,-0.01135618,0.05567004,0.01219021,-0.02371983,-0.03704319,0.03311462,0.06983635,0.01424877,-0.0356783,0.01245507,0.07852675,0.02727056,0.02329396,0.00632224,-0.03339833,0.0032492,-0.05649315,-0.00480539,0.04344384,0.00190737,0.03130734,-0.0201385,0.02524578,-0.06455323,-0.02950379,-0.00015807,0.03071857,-0.02830927,0.0709968,0.01366262,-0.05020099,0.10320547,-0.06295263,0.01893746,0.08023655,0.03485214,0.02501643,-0.04516876,0.03469866,0.02737467,0.02563062,0.03708559,-0.03690273,-0.03078485,-0.02701521,-0.02336182,0.04367982,0.06929389,-0.01235426,0.07228287,-0.00876044,-0.03202828,0.04822315,0.02184035,0.01181748,0.03126042,-0.02542184,-0.02660018,0.02660768,0.06344602,0.01362452,0.02844531,0.01795607,0.01740039,-0.13289285,0.02578241,0.03876009,0.02722012,0.02684849,0.01791098,-0.00582157,0.05437424,-0.00565048,0.00550382,0.05020474,-0.04197339,0.01360195,0.15610196,-0.00905165,0.04357677,-0.01360517,-0.02065219,-0.0387823,0.01832649,-0.0207885,-0.03186515,0.01021781,-0.00060124,0.0518116,-0.00836392,-0.08769482,-0.00199912,-0.01088536,-0.04813915,-0.05392346,0.0828874,0.02547396,-0.11164753,-0.02585464,0.00766772,0.01871776,-0.00647577,0.03430357,-0.02378049,-0.05750231,0.00866655,0.14358519,-0.01136325,-0.07168054,-0.01288785,0.00087308,0.02823074,0.08229522,0.01394095,0.01202148,-0.05051357,-0.00263326,-0.05395044,0.04492345,-0.04418518,0.04236522,-0.021554,-0.11744867,0.05638866,-0.05079914,0.00891714,0.00094967,0.03292488,-0.04973056,-0.03296741,0.07903558,-0.02813544,-0.07533333,0.00598163,-0.02863567,0.02593908,-0.06549501,0.05027293,0.0792674,-0.07881088,0.12329281,0.00244861,-0.07866698,-0.06996474,0.11658859,0.03447592,0.03313544,0.02474455,0.04041697,0.11208118,-0.05907109,0.00787755,0.04228788,0.03145838,-0.06323888,-0.21800396,-0.0119555,-0.00327206,-0.03183078,0.0029893,0.02480024,0.06047126,0.01082104,0.01341652,0.07036486,0.09360997,-0.06069538,-0.07356054,0.024299,0.00494236,0.0468021,0.01114671,0.0491735,-0.01159094,0.00725573,0.02396355,0.04231488,-0.08760133,-0.11430874,0.02772825,0.03361407,0.11054895,-0.01733395,-0.001837,-0.04762431,0.00247818,-0.01193083,0.00915311,-0.07231515,0.07272254,-0.01236464,0.00833049,-0.0565946,-0.0100667,-0.0418212,-0.00735597,0.05016411,-0.01534391,-0.06882376,-0.13592568,-0.03207453,-0.06216041,0.01330503,0.02592296,0.014143,0.0528274,-0.05797173,0.03770981,0.03582614,0.02218559,-0.0693106,-0.0728218,0.05105308,-0.04344586,0.07266922,-0.01414426,-0.04440862,0.01634379,-0.01316671,0.05488366,-0.02302369,-0.06621294,0.04528921,0.05316267,-0.10255352,-0.01159521,0.0762162,0.02043482,-0.00266093,-0.04728857,-0.02006476,-0.02497987,0.01387069,0.01951302,0.01319758,-0.00136759,-0.12330901,0.02554496,0.02775231,0.02476158,-0.00362806,0.02788425,-0.03884375,0.00723679,-0.00979564,-0.04356755,0.00680035,-0.05084033,-0.01393871,-0.00143514,-0.06264161,-0.24156797,0.01586667,0.04009498,-0.03480939,-0.00425417,0.01758472,0.0634645,-0.03084982,-0.04476056,0.01444019,0.0367081,-0.00679658,0.08944251,0.00307216,0.01392262,-0.00254144,0.06444155,0.02827296,0.01894537,-0.06679066,0.00132856,0.03543098,0.20622568,0.04665441,0.06397185,0.01198085,-0.06097021,-0.02033444,0.05284546,0.00572257,0.04771195,0.01341174,0.03456664,-0.04854994,0.01334948,0.0654029,-0.01824469,0.04114926,-0.05115501,-0.00331914,0.03980672,-0.03965848,-0.01074686,0.02843974,0.09813377,-0.00417883,0.00277868,-0.04798343,-0.08441138,-0.08220761,-0.06902845,0.05701038,0.00090597,-0.06877983,0.02198398,0.06821101,0.06524878,0.00211489,-0.0070329,-0.00880916,0.01214303,0.02113409,0.02907996,0.08576361,0.07783458],"last_embed":{"hash":"518b5310c6f0cadf8224d70f28776ead02dc41090801ed4d3395a9264c74d017","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"518b5310c6f0cadf8224d70f28776ead02dc41090801ed4d3395a9264c74d017","at":1743662881718},"key":"37 Tips from a Senior Frontend Developer.md#23. Become the \"go-to-person\" in a specific area#{1}","lines":[310,319],"size":476,"outlinks":[{"title":"T-shaped","target":"https://www.forbes.com/sites/lisabodell/2020/08/28/futurethink-forecasts-t-shaped-teams-are-the-future-of-work/","line":7}],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#24. Develop your communication skills": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03800776,-0.02330709,-0.00823815,-0.10006187,-0.01758946,-0.00046796,0.02547326,0.02679211,-0.03578748,0.00031956,-0.0350779,-0.03532273,0.0256298,0.02753923,0.04999265,0.09467713,0.04279418,0.03023965,0.01224802,0.01661556,0.0144831,0.0317247,-0.04159057,0.00317389,0.08051486,-0.02179452,-0.02565958,-0.05958325,-0.03274018,-0.12926717,-0.00369989,0.00691181,0.05454718,-0.01783023,-0.03090612,0.00368103,-0.00853188,-0.02614875,-0.07248688,0.00736047,0.01710642,-0.00613252,0.04345223,-0.0618319,-0.00345394,-0.07069764,-0.04100192,-0.04828849,0.01463899,-0.09676414,0.01874448,-0.05391184,-0.01538923,0.01060027,0.00008133,0.03170308,0.06554733,0.04609574,-0.01060457,0.04519165,0.04290845,0.00095761,-0.19517983,0.09077002,-0.0033261,0.03541581,-0.02810715,0.01736213,-0.01387554,0.02852861,-0.02881063,-0.03936896,-0.01344734,0.05248534,0.02353868,0.02807775,-0.03060781,-0.03035462,0.0833385,0.01243817,0.00963262,0.03544676,-0.00596835,0.03857483,-0.01130813,-0.04415639,0.08134788,0.01598747,0.00947147,-0.0155084,-0.01900727,-0.04007532,-0.00952156,-0.00168947,-0.02457289,-0.01194124,0.04687614,0.01351771,-0.09724207,0.17936862,-0.05758151,-0.00028532,0.00080392,-0.0188218,0.05365442,-0.02714125,0.01780011,0.01227566,-0.00943959,0.09983912,-0.05569872,-0.00311065,-0.01444675,-0.01318342,0.0346908,0.02361296,0.00628706,0.03523894,-0.02252264,0.00812856,0.04901709,-0.02305273,0.0327835,0.0105615,0.05854043,-0.05660627,0.03561531,0.05651512,-0.00837165,0.08011557,0.03431544,0.05638976,-0.06027228,0.00900357,0.02724432,0.02768027,-0.00317047,-0.01017882,-0.0077454,0.06001542,0.00478576,-0.05395899,0.01146426,-0.03285719,0.02546512,0.10189486,-0.03332376,0.06350646,-0.00914167,-0.00648287,0.00968667,0.02501913,0.005308,-0.02925533,0.0173915,-0.00489241,0.05085994,-0.00219316,-0.02826378,0.0150115,0.03587162,-0.06559773,-0.03426633,0.09477953,-0.03455576,-0.11283529,-0.04627939,-0.02194518,0.03856812,-0.0380715,-0.01559998,-0.00291431,-0.00124493,-0.04876365,0.06790086,-0.04502561,-0.04187485,0.00593603,0.01121574,0.03656297,0.01345207,0.03798482,0.02071621,0.02385993,-0.03730239,-0.07981367,0.03302492,-0.03658447,0.07887168,-0.00108979,-0.10595673,0.01755526,-0.03906064,0.01683192,-0.04438799,0.0732237,0.00232935,-0.00490688,0.03156593,-0.01859559,-0.03759609,0.01474666,-0.02320813,0.058689,-0.07559244,0.03680406,0.06188715,-0.06735487,0.08574396,0.03412712,-0.07799485,-0.02309504,0.10632465,0.01766374,0.0018016,0.03224256,0.03555178,0.12567994,-0.03671776,0.03384743,0.05180975,0.04905318,-0.07163083,-0.21935423,-0.02873828,0.04735424,-0.07088142,0.0933025,-0.02949717,0.09736579,0.0446013,0.0178481,0.08422179,0.11154888,0.01682084,-0.06411048,-0.04380985,0.01000432,0.04596397,-0.02663321,-0.00017556,-0.03138026,0.00921379,-0.01622563,-0.02711824,-0.01684938,-0.08034175,0.0312951,0.02771685,0.08290142,-0.05007195,-0.00327308,-0.05160628,0.02793811,-0.0056726,-0.00310367,-0.11898566,0.04979391,-0.00482095,-0.02753842,-0.00039846,0.00667847,-0.0003698,-0.00702744,0.00342234,0.0117932,-0.08247172,-0.07909455,-0.02119543,-0.07334333,-0.1065595,-0.04335458,-0.02754304,0.01959518,0.0050053,0.0749821,0.06156573,-0.02005094,-0.01784113,-0.0710014,-0.04299471,-0.05075196,0.05520963,-0.04400292,-0.00231224,-0.00466987,-0.04090289,0.03725442,0.06396312,-0.01758009,0.00043437,0.11037964,-0.03994524,-0.00390019,0.11380415,0.00353806,-0.03349214,-0.04265315,0.00367486,-0.05822667,-0.04480846,0.03204745,0.00005766,0.00505312,-0.09303067,0.06914211,0.03886007,0.03330589,0.04590882,0.00732021,0.02016544,0.02245376,-0.00573521,-0.0828798,0.04144799,-0.01286067,-0.06723585,-0.01389628,-0.04955802,-0.22341909,-0.02609353,0.08257004,-0.04894793,-0.03866334,0.03372644,0.08641135,-0.0535767,-0.13859649,0.06514581,0.04357178,-0.00964341,0.05439571,-0.01570009,0.03808791,0.06638273,0.04065448,-0.02774259,-0.01532912,-0.10353017,0.00586432,0.01820568,0.16228524,0.0266646,0.05301742,0.0324993,-0.06054886,0.04770089,0.05382724,-0.03952544,0.06253789,0.00367998,0.0324183,-0.03215027,-0.01905931,0.03376114,-0.03164823,0.00084598,-0.04934638,-0.01185568,0.03874023,-0.01551676,0.02124298,-0.01583056,0.04267142,0.0400136,-0.00172198,-0.05324218,-0.04490876,-0.04334001,-0.10240782,0.04569138,0.02694756,-0.02654551,0.00775678,0.0250487,0.04357823,-0.03922153,-0.03648487,-0.00288161,0.01933189,0.03742498,0.05564762,0.08117582,0.07593472],"last_embed":{"hash":"51bc1ea254b4309e94b61385fd99d02924feb135ee690a42b36f1635374b80b9","tokens":71}}},"text":null,"length":0,"last_read":{"hash":"51bc1ea254b4309e94b61385fd99d02924feb135ee690a42b36f1635374b80b9","at":1743662881736},"key":"37 Tips from a Senior Frontend Developer.md#24. Develop your communication skills","lines":[320,334],"size":288,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#25. Take breaks when you're stuck on a problem": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06644462,-0.01846065,0.01370917,-0.08335811,0.01695145,-0.01414381,-0.0206975,-0.0423415,0.0377604,-0.05704269,-0.00256561,0.06496516,0.0332382,0.00960007,-0.00054293,0.0342776,-0.01445534,0.05369901,-0.04592041,0.01122609,-0.05134545,0.00965809,-0.03254299,-0.01667021,0.03701341,0.00939589,-0.02803082,-0.08322281,-0.0642247,-0.18230823,-0.03551248,0.04081269,0.00379032,-0.03801918,0.01955041,0.03040222,-0.03417951,0.06600695,-0.00778641,0.05161142,0.0461277,0.0683172,0.0127373,-0.06809122,-0.03235474,-0.00772955,-0.04500453,-0.05187262,0.07753103,-0.01250008,-0.04355916,-0.0481724,-0.02483079,0.03218687,-0.02772724,0.05699578,0.03127849,0.08045974,-0.01363434,0.04681549,0.05694487,-0.03881491,-0.13702253,0.05194924,0.05637301,0.04052795,-0.0153099,0.00225101,-0.02032586,0.11850764,-0.05474579,-0.00726068,-0.06595096,0.0916865,0.01187012,0.03923721,0.00582049,0.00104833,0.069534,0.05437398,0.01934876,-0.01475813,-0.01836819,0.00957407,-0.04986281,-0.01144976,0.06330661,0.00960489,0.07133634,0.02311272,0.02499465,-0.03582678,0.05693277,-0.00226106,-0.00305317,-0.02277622,0.0392977,-0.00043175,-0.12723166,0.12777072,-0.0775857,0.06905592,0.0589698,0.01348717,0.01507057,0.00535035,0.00417705,0.02988782,-0.01317023,0.02526246,-0.01775138,0.00743569,0.05576837,-0.03470051,0.01582967,0.00541361,0.01144857,-0.0051808,-0.01445799,0.03561479,0.01909254,0.03732596,0.02193895,0.00774373,-0.00505612,-0.02763347,0.034645,0.04946583,-0.01260702,0.00349764,0.01860107,0.04418302,-0.10112721,0.00266625,-0.01585622,0.00893315,0.03305392,-0.02879252,0.05714851,-0.01083615,-0.02051647,-0.05916589,0.05905489,-0.05288175,0.00759052,0.10170363,-0.0298187,0.04550079,0.01627918,-0.0846182,-0.00029642,0.02623949,-0.02706821,-0.08462075,0.06159285,0.03920476,0.07468819,0.00734831,-0.04045655,0.01794915,0.01934638,-0.0611024,-0.07759003,-0.0007506,0.02335461,-0.05129892,-0.03299147,0.01771531,-0.02260593,-0.02045956,-0.01543166,0.00248845,0.01771631,-0.0473283,0.10639492,-0.03173141,-0.01862243,-0.01253545,0.0671941,0.07625601,0.01267223,0.01668844,-0.01402185,0.03092392,0.02442068,-0.09997661,0.0370032,-0.03610349,0.06034464,-0.00279552,-0.07465619,-0.02297733,-0.05283853,0.04642478,-0.04321389,-0.0186718,-0.01713729,-0.00949373,0.07185306,-0.06019321,-0.02094838,0.00494423,-0.03651229,-0.00151161,-0.06908257,0.03143672,0.0229884,-0.07260248,0.12633456,0.01581345,-0.0522662,0.00199239,0.11601062,0.02324442,0.00700358,0.01217837,0.01521322,0.06280234,-0.02901267,0.05855835,0.09569425,0.03760263,-0.03309946,-0.21522625,0.01066729,-0.00203646,-0.03238332,0.02585021,-0.02607329,0.0551646,-0.06755283,0.03103061,0.03801462,0.06632712,-0.0465519,-0.06829338,-0.06449524,-0.01746806,-0.00724102,0.03057272,0.02143151,-0.05196427,-0.02550693,0.00739306,-0.05538949,-0.0482989,-0.12492625,0.02122764,0.02538506,0.15554792,0.00620175,0.01622983,-0.07814892,0.03226072,-0.01696225,0.03580566,-0.12916046,0.05023213,0.0004615,-0.01052844,-0.10738721,-0.02783003,-0.03337874,0.01016419,0.00173347,-0.00978733,-0.04874132,-0.08746935,-0.01993091,-0.061842,-0.04986256,0.01988595,-0.0108468,0.04053237,-0.00375792,0.01020054,0.03965714,-0.02070764,-0.02695827,-0.0787237,0.03388942,-0.01524509,0.03981121,-0.04873842,-0.06010554,0.02021537,-0.00533241,0.03755761,0.04484911,-0.03614462,0.01910342,0.06073368,-0.05201013,0.00747959,0.13234344,-0.00878992,-0.06108079,0.00727567,-0.01331345,-0.05256202,-0.03899299,0.03557575,-0.00210008,-0.0271374,-0.14049108,0.06386179,0.04964437,0.00750369,-0.00190518,0.02250774,-0.00051338,0.04961089,-0.02677206,-0.01534592,0.07360202,-0.09105536,-0.02639966,0.058982,-0.03442971,-0.20210995,0.04963922,0.02453354,-0.03475467,0.0277645,0.11231574,0.06547004,-0.01296737,-0.04503157,0.01192384,-0.02365088,0.03596178,0.04455122,-0.06427265,0.04428864,-0.00680081,0.0304235,-0.02384915,0.02718432,-0.08068135,0.03247257,0.04104701,0.17014542,-0.01160681,0.04274585,0.04503866,0.01987223,0.02923209,0.05662559,-0.02274585,-0.04392961,0.00120876,0.02172311,-0.03608634,0.00410241,0.02753408,-0.0813591,0.07406757,0.00506657,-0.00105959,0.07387587,-0.00805498,0.04016642,-0.01597658,0.10170414,-0.09100804,0.0032751,-0.03329695,-0.04874483,-0.02987011,-0.07802188,0.00555511,-0.0058003,0.02235047,0.00964173,0.0401358,0.00974765,0.02862071,-0.0154928,-0.02979052,0.00939812,0.00675453,0.04625415,0.04383143,0.08508033],"last_embed":{"hash":"25d416ceac5b8cf9b4f2619cf3f0ce60fa964bb21696e8f3623930ca86d729cb","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"25d416ceac5b8cf9b4f2619cf3f0ce60fa964bb21696e8f3623930ca86d729cb","at":1743662881747},"key":"37 Tips from a Senior Frontend Developer.md#25. Take breaks when you're stuck on a problem","lines":[335,346],"size":269,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#25. Take breaks when you're stuck on a problem#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0659801,-0.02443622,0.01832327,-0.07958848,0.01080029,-0.01030475,-0.02276477,-0.04959489,0.04179611,-0.06004777,0.00194968,0.06739008,0.03182498,0.01520139,-0.00129376,0.03139108,-0.01742135,0.05003101,-0.04752918,0.01554093,-0.05015669,0.00974703,-0.02798795,-0.01231173,0.03233389,0.01185954,-0.02378462,-0.08656813,-0.06145679,-0.18355301,-0.03783466,0.04075772,0.00447054,-0.03622685,0.01677336,0.02984605,-0.03791445,0.06808069,-0.00234876,0.05867516,0.0494237,0.07014194,0.0165608,-0.07274198,-0.03646667,-0.00365512,-0.04769452,-0.05264777,0.08487462,-0.01103036,-0.04768949,-0.04491504,-0.02125156,0.03922997,-0.03019069,0.05655592,0.02961408,0.08553058,-0.01349013,0.04243714,0.0551386,-0.03870543,-0.13788091,0.04607248,0.05339325,0.04221689,-0.01562311,0.00628327,-0.02398941,0.12556563,-0.0601039,-0.00359437,-0.0688162,0.09430489,0.01245543,0.03879133,0.00555554,-0.00190121,0.06739081,0.05884662,0.01767957,-0.01988869,-0.01517618,0.0052565,-0.05254078,-0.01171212,0.06345016,0.00238159,0.06950099,0.02633349,0.0215903,-0.02951902,0.05374106,0.0003006,-0.00637872,-0.02748743,0.03328114,0.00212562,-0.12349652,0.1237656,-0.07931284,0.07455337,0.0589696,0.01456085,0.01166554,0.0076758,-0.00044464,0.0322293,-0.01500112,0.02354445,-0.01255306,0.00605126,0.05525084,-0.0346829,0.01321709,0.00980211,0.0160007,-0.01106502,-0.01587657,0.03643488,0.0184974,0.03795683,0.02331229,0.00762037,-0.00612703,-0.02753605,0.03579692,0.04814291,-0.00964594,-0.00360824,0.0180633,0.0426443,-0.10388274,0.00332471,-0.01715414,0.00865678,0.0354445,-0.02952736,0.06049939,-0.01159843,-0.01695848,-0.06469153,0.06674372,-0.05542737,0.00603554,0.10430544,-0.03277469,0.04396294,0.01358658,-0.09248894,-0.00293976,0.02600593,-0.0313249,-0.09152215,0.05713699,0.04138048,0.07844634,0.00662652,-0.04214075,0.01839506,0.01408208,-0.06027339,-0.07678962,-0.00343623,0.02585938,-0.03750911,-0.03062616,0.01889057,-0.02931678,-0.02305512,-0.01600537,0.00301438,0.01999082,-0.04063462,0.10227524,-0.03075833,-0.02082347,-0.01191135,0.06826827,0.07944404,0.00828036,0.014881,-0.01735166,0.03567132,0.02922486,-0.09404863,0.03240762,-0.03328262,0.06425183,-0.00723094,-0.0735498,-0.02543038,-0.04799436,0.04968021,-0.04448038,-0.02247497,-0.01967892,-0.00943938,0.06874605,-0.05855548,-0.01880873,0.00198668,-0.03594263,-0.00530245,-0.06254795,0.02824515,0.01420821,-0.06888773,0.12354209,0.01377639,-0.05229775,0.00640106,0.1154554,0.01924533,0.00696162,0.01026244,0.01406823,0.05607808,-0.02760926,0.06084133,0.09647407,0.03199508,-0.02539629,-0.20937009,0.01146293,-0.00420136,-0.02772901,0.02674418,-0.02276903,0.05257532,-0.06731699,0.03236064,0.04045229,0.06099402,-0.04575909,-0.07061006,-0.06788809,-0.02052613,-0.01069416,0.0307548,0.02165847,-0.05414929,-0.02357088,0.00514019,-0.05454812,-0.05018608,-0.12128548,0.01752036,0.02814058,0.16041589,0.00196818,0.01256592,-0.0700032,0.03546112,-0.01533564,0.03571652,-0.12535256,0.04763393,0.00352753,-0.01349858,-0.10864008,-0.02596184,-0.03491737,0.00973851,-0.00206108,-0.0097906,-0.04369425,-0.08182386,-0.02161022,-0.05844853,-0.04332992,0.020344,-0.01303417,0.04518735,0.00187948,0.00499947,0.04028315,-0.01900423,-0.02136921,-0.0771768,0.03413065,-0.01599817,0.03127444,-0.04837858,-0.06075817,0.0228383,-0.00449202,0.03996776,0.04745642,-0.03303949,0.01753578,0.04989713,-0.04280736,0.00929822,0.1337138,-0.00970191,-0.06347528,0.00752569,-0.01375595,-0.05374061,-0.040709,0.03314914,-0.00480418,-0.02564104,-0.14610511,0.05948639,0.05361666,0.00552625,-0.00585154,0.01974721,0.00257373,0.05657436,-0.02814427,-0.00295406,0.07493582,-0.09582129,-0.01685969,0.05983394,-0.033771,-0.20364,0.05123745,0.01828602,-0.03031949,0.03219548,0.11500128,0.06161582,-0.00479261,-0.04386288,0.0091806,-0.02312843,0.03235862,0.04436855,-0.0630369,0.04094075,-0.00921927,0.02844057,-0.02655856,0.02844103,-0.07717018,0.03614104,0.04073695,0.16106528,-0.01760772,0.04360417,0.0442564,0.0239173,0.02721621,0.05065631,-0.02363272,-0.04913613,-0.00157385,0.02503962,-0.03710705,0.00429971,0.03019089,-0.08720239,0.07770687,0.00990842,-0.00441706,0.07622393,-0.00873522,0.04103497,-0.02034574,0.11010197,-0.09416531,-0.00034252,-0.03889084,-0.04439975,-0.02575417,-0.07185553,0.00477522,-0.00406916,0.0258882,0.00867142,0.03635585,0.00891381,0.0355464,-0.01146203,-0.03274417,0.00839419,0.0065566,0.04123398,0.03743101,0.08237278],"last_embed":{"hash":"47eb34c867d2d8291b3514395f52c4fc03a7b6b297955b639cd9b104fee5d307","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"47eb34c867d2d8291b3514395f52c4fc03a7b6b297955b639cd9b104fee5d307","at":1743662881759},"key":"37 Tips from a Senior Frontend Developer.md#25. Take breaks when you're stuck on a problem#{1}","lines":[337,346],"size":219,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#26. Work from your strengths, not your weaknesses": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00836473,-0.00687684,0.01290209,-0.09296091,-0.02083621,0.01996989,0.03830185,-0.04451078,0.02023392,-0.03836405,0.01085664,0.03027781,0.04433426,0.03922796,0.01998846,0.04120143,0.00640526,0.060973,-0.10549909,0.01576226,-0.0476963,-0.05223583,-0.05864679,-0.01024734,0.08304349,-0.01856459,-0.02175033,-0.05721319,-0.06105424,-0.17858678,-0.0345649,-0.04107185,0.03417859,-0.01003047,-0.01536638,0.03753513,-0.04151478,-0.00124637,-0.02972116,-0.00951182,0.04737085,0.04075624,0.00290109,-0.06513955,-0.03168838,-0.03899115,-0.04541081,-0.06583463,0.0673749,-0.00125536,-0.00757576,-0.03184817,0.00466958,0.01989481,0.02893591,0.07914779,0.04194291,0.05115994,0.00401088,0.02480811,0.00850017,0.00012689,-0.17408988,0.03200231,0.09126487,0.03296517,-0.01144532,-0.00804527,-0.03270255,0.07261125,-0.02326657,0.00787702,0.00600272,0.0578815,0.0454619,0.0298178,-0.04221087,-0.03058042,0.06164208,0.04014812,0.02265234,-0.00186268,-0.00987685,0.06177795,0.00323479,0.03418152,0.05073036,0.0217812,0.07989991,0.03367316,0.03830316,-0.08261721,-0.03090638,0.01685261,0.02770293,-0.02403403,0.05544432,-0.0006564,-0.1405488,0.14607701,-0.03569343,0.04639447,0.04093155,-0.00758883,0.03686374,-0.01599432,0.04431515,0.05404701,-0.00480214,0.03410015,0.0060606,0.00983969,-0.03659436,-0.0374087,0.03574635,0.04551847,-0.00304691,-0.00142664,-0.04209475,-0.00065613,-0.01495377,-0.00623408,0.04424485,-0.000239,-0.00704715,-0.06838205,0.04966432,0.042204,-0.01611498,0.03095882,0.01286373,-0.0113166,-0.09228082,0.00181827,-0.01187886,0.02673023,0.01597103,-0.02342789,0.01990451,0.04409663,0.0181574,-0.01086742,-0.01476927,-0.08201348,0.02884121,0.14258464,0.02746696,0.06626406,-0.01023718,-0.07639819,-0.05117846,-0.03137375,-0.01170367,-0.04918532,0.03973455,-0.01739319,0.04184793,-0.02816244,-0.03848636,-0.01972384,-0.01833148,-0.02121775,-0.08212268,0.03460107,0.04477715,-0.03270893,-0.03651002,-0.00383924,-0.00452001,-0.0126128,0.03749938,-0.02356118,-0.01107461,-0.03049216,0.14630717,-0.04205162,-0.02524193,0.00518731,0.03179387,0.04986247,0.09359507,0.01077677,-0.0033276,-0.01961586,-0.01822807,-0.07996155,0.03205702,-0.03301358,0.04625798,0.03350617,-0.13720608,0.03126234,-0.04410209,0.00398488,-0.04892903,-0.01561206,-0.02039715,0.00403991,0.02806531,-0.0052377,-0.01259949,0.0003796,0.0256196,0.04290006,-0.02294663,0.06578469,-0.00689575,-0.05425169,0.14964473,0.0385488,-0.05552464,-0.02758346,0.09998737,0.04562844,-0.012666,0.06081045,0.03022462,0.09989239,-0.005027,0.05861656,0.07290543,0.05067278,-0.06391255,-0.21088655,-0.01802592,-0.00334629,-0.04905736,0.0505334,-0.00364813,0.08908682,-0.04138558,-0.03574698,-0.00971559,0.06436393,-0.07745565,-0.06711424,-0.02563879,-0.01044768,0.04975203,-0.0006215,0.00349218,-0.01896102,-0.01796626,0.01578485,-0.00601724,-0.01975089,-0.09051169,0.06320206,0.00104073,0.11858427,-0.03785844,0.01198083,-0.06363266,0.00298385,-0.04939475,-0.02979149,-0.10504171,0.06390417,-0.02921962,0.00265158,-0.05263732,-0.04361875,0.00225058,-0.0187338,0.02097872,0.0137756,-0.0463427,-0.10728917,0.00264999,-0.05128914,-0.04237932,-0.00735035,-0.02265881,0.03919334,0.02429304,0.0073041,0.03905779,0.03619115,-0.05432429,-0.11115085,0.07840121,-0.02612474,0.07764725,-0.04240625,-0.07720736,0.00272974,0.00491372,0.06052073,-0.01774792,-0.0298646,-0.00938579,0.03448939,-0.02595,-0.00390026,0.03252165,-0.00182891,-0.01423759,-0.01810099,0.00302961,-0.03130954,-0.00273129,0.06569867,0.01473309,-0.04669289,-0.10119668,0.03773415,0.04089328,0.01514088,-0.00646316,0.03822448,-0.00351735,0.04043287,-0.014323,-0.0701025,0.03652471,-0.06191825,-0.04193699,0.03261425,0.00469092,-0.22073723,0.01618703,0.04776907,-0.03831878,-0.00739209,0.01186498,0.0950705,-0.05179148,-0.08003052,0.01574918,-0.01024198,0.00101321,0.01103504,-0.05850091,0.00895332,0.00141598,0.07818818,0.01291786,0.02767298,-0.03885402,-0.01164194,0.0689532,0.19646288,-0.00786069,0.02201151,0.07182959,-0.0246253,-0.0178137,0.06595901,-0.01475529,0.01976663,0.00325379,0.06630944,-0.00601199,0.00014664,0.02066754,-0.05165846,0.01203387,0.00839756,-0.01463315,0.08368173,-0.04319192,0.01788294,0.00441759,0.1115139,-0.07819071,-0.01691554,-0.07935943,-0.0715284,-0.05184396,-0.0702024,0.0387442,0.01523908,-0.00968657,0.02462192,0.03869929,0.03029307,0.03051133,0.0278304,-0.04018281,-0.00908214,0.08351456,0.0425688,0.05754552,0.06833493],"last_embed":{"hash":"ff4368e84d3f0c3a03c24ac9234c8d88f42093ada0ae3ea2991a8014c7fb5658","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"ff4368e84d3f0c3a03c24ac9234c8d88f42093ada0ae3ea2991a8014c7fb5658","at":1743662881774},"key":"37 Tips from a Senior Frontend Developer.md#26. Work from your strengths, not your weaknesses","lines":[347,358],"size":447,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#26. Work from your strengths, not your weaknesses#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00484671,-0.00707581,0.01511063,-0.08788262,-0.02630174,0.02346531,0.03941317,-0.04831893,0.02107445,-0.04007501,0.01819076,0.02948062,0.04382093,0.04452484,0.01912001,0.04084631,0.00437422,0.059005,-0.11201326,0.01666414,-0.0481076,-0.05378572,-0.055775,-0.00409102,0.08333126,-0.01468464,-0.01705096,-0.05622211,-0.05759877,-0.18094251,-0.03740438,-0.04291095,0.03300964,-0.00411177,-0.01810569,0.03943189,-0.04853471,-0.00220074,-0.03053001,-0.00551336,0.05379301,0.03785054,0.00268825,-0.06712447,-0.0344703,-0.03646071,-0.04641757,-0.0650572,0.07683191,0.00146268,-0.00818086,-0.02859439,0.00983505,0.02569856,0.0324451,0.07979922,0.04602221,0.05210097,0.00790592,0.0162594,0.00419861,0.00387594,-0.17586946,0.02883922,0.0942035,0.03264294,-0.00855194,-0.00104441,-0.0385021,0.07587916,-0.02156867,0.01271567,0.0044384,0.05669737,0.05035983,0.03070787,-0.04134145,-0.03447782,0.05982387,0.03956236,0.02378018,-0.00319751,-0.00272008,0.06243415,0.00032419,0.03885517,0.04627367,0.01835285,0.07773171,0.03516633,0.03584153,-0.0779831,-0.03847558,0.02492951,0.02704971,-0.03108554,0.04797425,0.00187303,-0.13913853,0.1429328,-0.03329216,0.04767535,0.04580696,-0.00822027,0.03281247,-0.01536003,0.04165009,0.05493087,-0.00541645,0.03299737,0.01607631,0.01437281,-0.04380599,-0.03971821,0.03593466,0.04853879,-0.00153346,-0.00600433,-0.04326718,-0.00228343,-0.02128987,-0.00457228,0.04811861,0.00097361,-0.00846664,-0.07464513,0.05164606,0.04042218,-0.0079556,0.02981513,0.01413744,-0.01576712,-0.09356121,0.001843,-0.01855539,0.02956501,0.01914035,-0.02093981,0.0241163,0.04444138,0.0277848,-0.01501793,-0.01107622,-0.08749101,0.03216323,0.14402026,0.02793648,0.06562244,-0.01681419,-0.07805164,-0.05862993,-0.03632359,-0.01387101,-0.05123589,0.03587911,-0.02155235,0.0457176,-0.02925178,-0.039465,-0.02030995,-0.02614322,-0.01676756,-0.0821673,0.0294507,0.05057798,-0.02064292,-0.03411387,-0.00359214,-0.00705222,-0.01535106,0.04127078,-0.02548713,-0.01032393,-0.02182935,0.14468902,-0.04340185,-0.02901364,0.00186449,0.02542376,0.05547569,0.09669869,0.00736329,-0.00373038,-0.01702561,-0.01541408,-0.0690971,0.02515878,-0.03083428,0.04820198,0.03361058,-0.14007352,0.03224672,-0.04142855,0.00454283,-0.05155287,-0.02122007,-0.02462346,0.00172867,0.01900094,-0.00151753,-0.01214725,-0.00764616,0.02941404,0.04409949,-0.01836007,0.06348059,-0.01505023,-0.05072568,0.152476,0.03946406,-0.05567665,-0.02658451,0.09633794,0.04364782,-0.01268584,0.05887584,0.03349499,0.09772488,0.00103303,0.05793883,0.068836,0.04344803,-0.06184737,-0.20815492,-0.02322547,-0.00986268,-0.04417392,0.05308122,-0.00073311,0.08427344,-0.03840054,-0.04012375,-0.01143146,0.05900288,-0.07807055,-0.06682039,-0.02777404,-0.01413159,0.04764035,-0.00671954,0.0057307,-0.0198072,-0.0159877,0.01533565,-0.00147598,-0.02260029,-0.08742955,0.06141087,-0.00254215,0.12205407,-0.04308151,0.00933396,-0.05314372,0.00315602,-0.04939638,-0.03571671,-0.1031021,0.06142946,-0.02861678,-0.00113862,-0.05283542,-0.04197068,0.00117862,-0.02404353,0.01943868,0.0094162,-0.04060827,-0.10908435,0.00183217,-0.04795752,-0.03479302,-0.01273466,-0.02378427,0.0425998,0.03321319,0.00076714,0.03687401,0.04092317,-0.04984195,-0.11067563,0.08083776,-0.02550676,0.07110359,-0.04057769,-0.07566391,0.0000304,0.00926345,0.06272937,-0.01766867,-0.02339161,-0.00996693,0.02090477,-0.01546532,0.00247973,0.03053465,-0.00526583,-0.01705961,-0.01902556,0.00570128,-0.03140305,-0.00174982,0.06397811,0.01006776,-0.04302999,-0.10390057,0.03558068,0.04297868,0.01620298,-0.00794517,0.03583836,-0.00293888,0.04670937,-0.01285902,-0.06536588,0.03554072,-0.06184492,-0.03432321,0.03670555,0.00909763,-0.21884446,0.01594209,0.04657883,-0.03437,-0.00582634,0.00674797,0.09394579,-0.04848101,-0.08274048,0.0182012,-0.00588868,-0.00346451,0.00289619,-0.05568257,0.00151114,-0.00081662,0.07807154,0.01215157,0.03014383,-0.02816106,-0.00914869,0.07183167,0.19189531,-0.01473989,0.0242421,0.06789814,-0.02118857,-0.01931232,0.05901934,-0.0152289,0.01808328,-0.00175277,0.07254286,-0.00442913,-0.00197159,0.015996,-0.05546929,0.01302657,0.01425297,-0.01953383,0.08393312,-0.0432,0.01693119,0.00052057,0.12000442,-0.08372281,-0.02337601,-0.08461934,-0.06731664,-0.05068834,-0.06408755,0.03833706,0.01841652,-0.00744309,0.02308397,0.03581948,0.02885643,0.03209436,0.03390794,-0.04280237,-0.01048407,0.08549277,0.03502041,0.05324502,0.06165614],"last_embed":{"hash":"3ffb76e7036057a17e97a7f245a5470056c6dc38ec6f1e90bf91e6934c03e512","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"3ffb76e7036057a17e97a7f245a5470056c6dc38ec6f1e90bf91e6934c03e512","at":1743662881786},"key":"37 Tips from a Senior Frontend Developer.md#26. Work from your strengths, not your weaknesses#{1}","lines":[349,358],"size":394,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#27. Take ownership of your career path": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00772693,-0.00520796,-0.00978769,-0.10618304,-0.03037896,0.02318227,0.0408714,-0.03596335,-0.02028282,-0.01532414,0.00290331,0.03448571,0.03546318,-0.01943715,-0.02462834,0.02785259,-0.03534067,0.03695013,-0.04924694,0.01437328,-0.02935832,-0.03120663,-0.05270059,-0.01518056,0.08812535,0.00520625,-0.03159574,-0.05694165,-0.06741848,-0.11385541,0.00344611,-0.04993605,0.00958692,-0.03034342,-0.00858507,-0.00608402,-0.01020348,0.06015687,-0.02188814,0.00396505,0.02913906,0.03102817,0.01751652,-0.05048478,-0.01892136,-0.02608566,-0.04671315,-0.05515818,0.05894699,-0.01374743,-0.03237636,-0.05355437,-0.06673433,0.0293865,-0.00161728,0.07784351,0.01955628,0.07444953,-0.00834287,0.03674016,0.02091072,-0.02335366,-0.17067198,0.0343753,0.00649458,0.0669024,-0.02327998,-0.01823562,-0.02007236,0.0709025,-0.03179058,-0.00855948,-0.04227337,0.06126989,0.04492065,0.00000573,-0.00398017,-0.00565451,0.03703995,0.03568251,-0.03798046,0.06699132,-0.06275291,0.0189862,-0.05128656,0.01320927,0.05316075,-0.00281269,0.03834243,0.03846261,0.05524048,-0.09442111,0.0089391,-0.00683308,0.00118234,-0.00220665,0.04827922,0.05235728,-0.08252355,0.15571582,-0.06326248,0.04172413,0.03943355,0.00612323,0.02308309,0.01124697,-0.01683345,0.04914709,0.0309667,0.02993843,-0.03145111,-0.00357068,0.00039378,-0.0353913,0.08406269,0.04177109,-0.02111674,0.05342092,-0.0054096,0.00709844,0.02790406,0.01460811,0.00967609,0.01670951,-0.051348,-0.05235011,0.05278787,0.05777838,-0.02906556,0.05861379,0.03144131,0.00957024,-0.09478103,0.01723066,-0.01891523,0.01878962,0.00841099,0.02443016,-0.00931517,-0.0142746,-0.05082824,-0.02916342,0.07063747,-0.04802125,0.04630782,0.10764063,0.05276502,0.04640225,-0.00355875,0.01631726,-0.03162697,0.03025297,0.00569787,-0.08512274,0.0238612,-0.00025337,0.0635346,-0.04047225,-0.05038365,0.00875091,0.00586472,-0.06755786,-0.06189218,0.05798901,0.02749415,-0.09827238,-0.03986557,0.03033142,0.00692487,0.01094079,-0.01681949,-0.03758297,-0.01030904,-0.01718519,0.13843423,-0.01658755,-0.02840858,0.01157496,0.01065,0.04873099,0.01379305,0.00934003,0.04222115,-0.00421966,-0.02384452,-0.07645038,0.04468765,-0.03734155,0.03342472,0.00622086,-0.05597339,0.09759535,-0.08748111,0.00427907,-0.03771392,-0.00915209,-0.03345213,-0.0164075,0.08244833,-0.07232283,-0.01956847,0.02184635,-0.03564299,0.01128482,-0.0892451,-0.00140555,0.06602372,-0.03645138,0.12163285,0.00127794,-0.08378541,-0.01300029,0.09969764,0.03900193,0.02391732,0.0237735,-0.02010338,0.06931129,-0.00348518,0.04027342,0.08307482,0.06414361,-0.03054455,-0.19757761,0.00573423,-0.00986121,-0.05270163,-0.02244072,-0.01145312,0.04455269,0.00151117,-0.01403213,0.05794989,0.09803542,-0.08220284,-0.02773999,-0.00769192,-0.02350018,0.02802563,0.00730608,0.02775959,-0.02763285,-0.03368776,0.01752589,-0.0166802,-0.0336454,-0.06025072,0.10811461,0.01826049,0.12111823,-0.03286845,0.01549323,-0.0672536,0.03629738,-0.0676294,-0.01255369,-0.11376781,0.00567966,-0.01501861,-0.04769646,-0.12222946,-0.01834444,-0.01233619,0.00148454,-0.01014136,-0.03917756,-0.04414187,-0.11033212,-0.0157441,-0.07709146,0.00496673,0.01156524,0.00793037,0.02050531,-0.03807113,0.03300459,0.05794673,-0.01757248,-0.06593044,-0.08413505,0.03252083,-0.01191945,0.14875282,-0.04280587,0.00296432,-0.02143385,-0.01983408,0.07375345,-0.01247304,-0.0751287,0.00603571,0.08680557,-0.0820896,0.00991372,0.05757082,0.03532936,-0.0610523,0.03938727,-0.0450139,-0.06385809,0.0131871,0.06011403,0.03147034,-0.04882027,-0.14027524,0.04066418,0.06551935,0.04655572,0.04163416,0.05059328,-0.00507848,-0.02331462,-0.01159689,-0.02065141,0.00550832,-0.05687957,-0.03017205,0.01943736,0.00083732,-0.22332734,0.00734095,0.0376857,0.01241401,0.01184196,-0.00574854,0.10697985,0.0161831,-0.06420927,0.03622028,0.01562742,0.03775903,0.05970427,-0.03220149,0.05908338,-0.0282801,0.03635395,0.01734914,0.03882505,-0.05020104,0.03751139,0.03237491,0.15335493,0.04533596,0.05276965,0.02699867,-0.00127128,0.01812203,0.0596033,-0.01976759,0.01755528,0.01833911,0.05425756,-0.04970422,0.00659841,0.08993965,-0.04108865,0.07607228,-0.0406031,0.02099693,0.01703035,-0.06869566,0.03614867,0.02094979,0.0773325,-0.07558151,0.01437942,-0.02687868,-0.07802237,-0.03216389,-0.11108275,0.03920151,-0.00700283,-0.00709163,0.01061124,0.00681181,0.06707367,-0.00849983,-0.00768524,-0.04486379,-0.01862667,0.04837933,0.03522513,0.07555911,0.06496119],"last_embed":{"hash":"f9788f293ca097ee802d9669dc7576e1ed1a891754c5db4d8560b5c588a38c19","tokens":70}}},"text":null,"length":0,"last_read":{"hash":"f9788f293ca097ee802d9669dc7576e1ed1a891754c5db4d8560b5c588a38c19","at":1743662881799},"key":"37 Tips from a Senior Frontend Developer.md#27. Take ownership of your career path","lines":[359,368],"size":230,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#28. Hang with other devs": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02603393,-0.00629359,-0.00161511,-0.0479024,-0.00840109,-0.03422381,0.0555934,-0.01947049,0.00502481,-0.01932832,-0.00108904,-0.01073252,0.00338309,-0.02977611,0.00020644,0.03655145,-0.00822818,0.05529335,-0.01800674,0.01179587,-0.08267508,-0.03445053,-0.03279415,-0.04348375,0.01425929,0.025542,-0.05983217,-0.08582281,-0.05639239,-0.15778229,-0.00339288,-0.03944474,0.01602592,-0.01285681,0.00380225,-0.01020738,-0.0229268,0.06264419,-0.03296154,-0.02638311,0.00889416,0.027546,-0.00897174,-0.02463554,-0.04742884,-0.02489915,-0.03457898,-0.03509355,0.0088866,-0.05866085,-0.02153361,-0.04307231,-0.02551618,0.03520652,-0.0306575,0.05784003,0.0110726,0.05124185,-0.0534615,0.04224003,0.05739025,-0.03221257,-0.1425083,0.05543811,0.07799435,0.03181621,-0.03881101,-0.03194945,0.02110769,0.01703421,-0.02570917,0.00540695,-0.01120653,0.05443427,0.01978148,0.00244306,0.00624406,-0.02508812,0.01443658,0.03540867,0.02884481,0.00030788,-0.0143736,0.00366185,0.02717267,-0.01018948,0.04658461,0.0111475,0.04440257,0.01376942,-0.00546097,-0.10999957,0.05981483,0.02082537,0.01194038,-0.01411103,0.054011,0.00894612,-0.10169765,0.14885755,-0.04207589,0.02306883,0.06752318,0.02243437,0.05978815,-0.02806781,0.03212771,0.00089203,-0.01714199,0.02614798,-0.01409932,-0.06854879,0.0393122,-0.01731491,0.0465727,-0.01137301,0.05005438,0.06784017,0.02283541,0.01889792,0.04092515,0.02627746,0.05394844,-0.00255262,0.02703895,-0.00035125,-0.00554183,0.07448395,-0.01856902,0.03658556,-0.01044341,0.00481065,-0.0592017,0.02873884,0.01478117,0.0036288,0.04785191,0.01885625,0.05590796,0.02326968,-0.00749459,-0.00427173,0.02528382,-0.0332203,0.01072062,0.08644874,-0.0101283,0.04536485,-0.01179839,0.00371067,-0.00805891,0.03934706,-0.054945,-0.06079996,0.04187442,0.01605185,0.05303444,-0.05834741,-0.05730885,0.0250977,0.03061271,-0.02988167,-0.03687216,0.07104515,0.06762297,-0.12607981,-0.02541925,0.02046329,0.03927142,-0.02781042,-0.02457853,-0.0331821,-0.01816938,-0.04782291,0.12245802,-0.02923964,-0.05078393,0.00554996,0.00117443,0.04804403,0.03549822,-0.01188449,0.00320011,0.03119055,-0.01393827,-0.11833753,0.03411648,-0.06506041,-0.00035072,-0.01255406,-0.08283318,-0.00946061,-0.12919886,-0.0074336,-0.02753091,-0.00567936,-0.02296116,-0.00397516,0.00993307,-0.03066752,-0.00376005,0.03959426,0.0049262,-0.00563594,-0.05025463,0.07388201,0.03004891,-0.07522558,0.0991812,-0.02971712,-0.07281452,0.02032532,0.12884358,0.03891474,0.01040696,0.03538086,-0.01601068,0.09233468,-0.02752561,0.00600172,0.07517461,0.05552499,-0.05890816,-0.23910463,-0.01830488,0.01991668,-0.06933371,0.007062,-0.01623181,0.0636536,-0.00098295,0.03137247,0.06206931,0.08632269,-0.03457461,-0.04892303,0.00125914,0.02164468,0.05634251,0.03616019,-0.00638023,-0.00053202,-0.03956047,-0.01236023,-0.01511291,0.07144003,-0.07637848,0.03748925,0.02037985,0.11640976,0.04463961,0.00438159,-0.05754029,-0.04046626,-0.0160925,0.00929187,-0.11473305,0.06522138,0.01625177,-0.0259687,-0.10912247,0.00531306,-0.01865218,-0.03815227,0.01027871,0.00231037,-0.04631007,-0.08405472,0.01875023,-0.04434389,-0.04898958,-0.0289441,0.04248809,0.01145335,-0.04051277,0.03114194,0.05157234,-0.01853839,-0.05850401,-0.05510588,-0.00364355,-0.05423176,0.09815116,-0.0046208,0.01176682,-0.03123666,-0.04489914,0.07003877,0.03404406,-0.09928389,0.05919928,0.09159183,-0.05558906,-0.04264721,0.11056421,0.00262456,-0.02956117,-0.00030586,0.01557853,-0.02184862,-0.0429331,0.03136627,0.03357536,0.00448749,-0.08507527,0.01821522,0.07386036,0.01952341,-0.00701854,-0.00656788,0.02832257,0.02313764,-0.01264275,-0.06523855,0.07185882,-0.05124503,-0.03716069,0.03534675,-0.00573596,-0.25387335,-0.02296697,0.03453236,-0.07725855,-0.03568522,0.02576863,0.10110384,-0.0340237,-0.03659764,0.00370004,0.03994028,0.03049399,0.04659406,-0.04512095,0.03765601,0.07033961,0.05607444,-0.05220601,0.00718446,-0.111002,0.00089412,0.06891312,0.21112792,0.08314236,0.00452815,0.06840679,-0.00732376,0.06766144,0.03314515,0.021895,0.02478231,0.00179641,-0.01942818,-0.02979566,-0.00750337,0.08649851,-0.06275135,0.02431767,-0.01108907,0.0123258,0.04578326,-0.00422767,-0.02319102,0.03227306,0.09657752,-0.05107173,0.00146258,-0.02776756,-0.02970304,-0.04672755,-0.07250167,0.0048453,-0.00758192,-0.02385446,0.08597519,0.03769858,0.00455362,-0.04245812,-0.03615157,0.01476304,-0.01494332,0.06417058,0.02937098,0.05552029,0.07409369],"last_embed":{"hash":"98a6f7ffa85e9eaa46a21595e3301736fc5014b7807ca01d3385683d4a21118d","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"98a6f7ffa85e9eaa46a21595e3301736fc5014b7807ca01d3385683d4a21118d","at":1743662881811},"key":"37 Tips from a Senior Frontend Developer.md#28. Hang with other devs","lines":[369,385],"size":338,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#29. Mentor younger devs": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02711842,-0.03147954,0.01636427,-0.08896951,0.00689288,-0.02659511,-0.00559494,0.01781136,-0.02478603,-0.03414822,0.02233909,-0.04305077,0.03045681,-0.01188848,-0.01928623,0.04516928,0.02392673,0.09477267,-0.00646781,0.04927152,-0.05342464,-0.00380518,-0.02695893,-0.06723589,0.05683399,0.02754888,-0.04253404,-0.07151498,-0.06559932,-0.12995501,0.01878011,-0.01972924,0.01196837,-0.0076514,-0.02081383,-0.00734449,0.00037084,0.03955885,-0.02310232,-0.04001532,-0.01358925,-0.00140669,-0.01785439,-0.0056083,-0.03525831,-0.02017586,-0.02532718,-0.03883794,0.02552655,-0.03187257,-0.06349611,-0.05395152,-0.03235772,0.05666652,-0.03569025,0.03334802,0.03171253,0.03964626,-0.03058428,0.05290193,0.06196421,-0.02496777,-0.16511448,0.04807232,0.05939948,0.04631273,-0.00385496,-0.03747018,-0.01482903,0.01167733,-0.02418656,-0.00768162,-0.00443746,0.054613,-0.01125368,0.01138118,-0.03773374,-0.0298828,0.05588826,0.00465571,0.0286435,0.01129123,-0.00043444,0.0175147,-0.01013164,0.01075874,0.05134877,-0.00949923,0.02931531,0.01291192,-0.00566421,-0.08994601,0.02591016,0.00137602,-0.01828901,-0.05063554,0.05382233,0.02705193,-0.10170972,0.15321627,-0.03937858,0.01369269,0.04707074,0.00515678,0.04575909,-0.08025149,0.06184934,0.02662617,0.00251737,0.05325078,-0.03223164,-0.05336119,0.04330876,-0.02617445,0.02334411,-0.00692509,0.02583468,0.06339816,0.0024347,-0.00495778,0.06880946,0.01105282,0.07084648,0.01376423,-0.02368701,-0.01024318,-0.03304864,0.05702152,-0.0041464,0.03546523,-0.02932884,0.03539352,-0.06850754,0.04861762,0.03539604,-0.00747845,0.0077997,-0.02083091,-0.00585064,-0.04352268,-0.03766498,-0.03382489,0.00756893,-0.04677744,0.01581361,0.07053663,-0.03170132,0.04388733,-0.01692598,-0.00786283,-0.01100028,0.02038944,-0.0340364,-0.04855759,0.02533012,0.01500207,0.06905466,-0.05650013,-0.02200119,0.04386344,0.01465519,-0.05522533,-0.06035037,0.05298654,0.04940173,-0.11349602,-0.01241469,0.01715489,0.02655293,0.0220138,0.01424256,-0.04022048,-0.01956877,-0.020903,0.11425991,-0.03282251,-0.03251549,0.01359212,-0.01382977,0.10361703,0.07096817,-0.03121852,0.01469395,0.04213091,-0.0159677,-0.07433891,0.03024405,-0.08779386,0.02328753,-0.0389032,-0.07038981,0.01861817,-0.09766588,-0.03193323,-0.02710103,0.00834032,-0.0227823,0.00596798,0.02396339,-0.01076158,-0.04570686,0.04236405,-0.0032264,0.05120391,-0.06125275,0.07111281,0.0310304,-0.05301842,0.10963885,0.00830546,-0.03963438,0.01848238,0.12212093,0.02624889,-0.01101458,0.06118588,-0.02617303,0.05322326,-0.06352968,0.03313809,0.10249166,0.03681923,-0.07633611,-0.24131596,-0.01239854,0.02239341,-0.03947772,0.03957458,-0.00103605,0.05186742,-0.00157835,0.03081767,0.03228617,0.05093837,-0.04739005,-0.01864007,0.0263112,-0.02572566,0.05799855,0.04063619,-0.0000486,0.00969244,-0.04009598,-0.00497539,0.00802921,0.04486311,-0.06460722,0.02777195,0.04671655,0.11669505,0.0663858,0.00988941,-0.07307009,0.02213462,-0.02830441,0.01595305,-0.09751713,0.06415789,-0.00927045,-0.01257111,-0.13089998,-0.01612977,-0.01112477,-0.02247399,0.0243807,-0.00080344,-0.04479244,-0.10611735,0.00116629,-0.05960161,-0.0073157,0.0066033,0.04724278,0.01479245,-0.03318769,0.04756831,0.07070936,-0.00804558,-0.08160175,-0.09947415,0.01110771,-0.01401486,0.04791524,-0.00161559,-0.0133895,-0.03716576,-0.02974859,0.03355027,0.0053854,-0.06606189,0.03315097,0.08465926,-0.10914256,-0.04070669,0.099412,-0.01733435,0.00068839,0.00182712,0.02366313,-0.0485586,-0.04609377,0.01927102,0.04435679,-0.03069081,-0.0726474,0.01165086,0.07657794,0.00384349,-0.03003179,0.01200784,0.04792558,0.02648562,0.00966465,-0.04547731,0.06595264,-0.0723014,-0.0297404,0.03026799,-0.02644592,-0.23086821,-0.01352477,0.042035,-0.06611811,-0.01472293,0.02749438,0.11717997,-0.06568403,-0.00614987,0.03272706,0.02390519,-0.01706264,0.07708661,0.00907248,0.02186106,0.04990318,0.03397803,-0.05052438,-0.02239103,-0.12808676,0.00742515,0.05938337,0.19369036,0.04813977,0.02261232,0.08762843,-0.03494491,0.0359994,0.09034594,-0.00372008,0.04838425,0.02207882,0.02178163,-0.01245428,0.0373585,0.07686586,-0.06807604,0.03132363,-0.03816075,0.00041266,0.05427168,0.00699337,-0.02586567,0.00934638,0.0944182,-0.06188958,0.00591583,-0.03170403,-0.00465013,-0.03339484,-0.07070681,0.03695078,0.01433435,-0.04772737,0.05554477,0.06874947,0.02677472,-0.07567389,-0.04618444,-0.00804876,0.00009376,0.07473207,0.06088362,0.0948131,0.04831442],"last_embed":{"hash":"abc636446696f37314df98761ae32aecdeeff2fc4453ac3ae351caed0318218d","tokens":65}}},"text":null,"length":0,"last_read":{"hash":"abc636446696f37314df98761ae32aecdeeff2fc4453ac3ae351caed0318218d","at":1743662881823},"key":"37 Tips from a Senior Frontend Developer.md#29. Mentor younger devs","lines":[386,397],"size":213,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#30. Diversify the problems you solve": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0555554,-0.03717836,0.01614276,-0.08255453,0.0031757,-0.01362616,-0.00792409,-0.01311902,-0.00735859,-0.01768543,0.01272475,-0.01170848,0.04154057,-0.01466456,0.05427174,0.06205547,-0.00915941,0.05920481,-0.01404534,-0.00093217,-0.07334983,0.01182125,-0.04924485,-0.03905019,0.030114,0.00852448,-0.017024,-0.05674788,-0.03641868,-0.18465662,-0.04479583,0.01971796,0.03137937,-0.05986377,0.04251537,0.01541812,0.00826376,0.02981996,-0.04679303,0.01606173,0.03134162,0.03674631,0.00589558,-0.06889439,-0.01727834,-0.00944912,-0.05732271,-0.03264728,0.02962727,-0.03767454,-0.02399355,-0.08548322,-0.05046818,-0.01550723,-0.0130387,0.05449213,0.03286318,0.07284404,-0.01340853,0.06436063,0.05423442,-0.01678982,-0.15505381,0.07970773,0.07344142,0.04385243,-0.01849661,-0.0332966,0.00802406,0.07840678,-0.03607403,-0.01866435,0.03212537,0.08655662,0.03153516,0.04070967,-0.02641524,-0.02185663,0.04854327,0.0303445,0.02348633,0.00118027,-0.02711621,0.0040755,-0.00308159,-0.04634581,0.0717556,0.02995512,0.06512631,-0.02115127,0.05209249,-0.02753465,0.05695199,-0.04094133,0.02162158,0.02121539,0.05518176,-0.03629485,-0.06843635,0.17235212,-0.02691346,0.04953735,0.08491915,0.0342647,0.01890165,-0.02353642,0.00072488,-0.00041201,0.0080422,0.0042751,-0.0367509,-0.01678193,0.03127282,-0.01075566,-0.00219591,-0.03261573,-0.0506904,-0.01282982,-0.01598943,0.02552744,0.05260507,-0.01093785,0.05586413,0.00280609,-0.00331919,0.00568565,0.00689049,0.06427141,-0.04792007,0.02442022,0.03825253,0.03204114,-0.09970174,-0.03653607,-0.00489713,0.00887259,0.01031351,-0.00425948,0.02271151,0.03671872,-0.0332234,-0.02780337,0.02845702,-0.04554857,-0.03555853,0.14257604,-0.04571541,0.05272629,0.01793174,-0.06013467,0.0058657,0.00662804,-0.03104462,-0.05315599,0.0296168,0.04672269,0.08466579,-0.0454229,-0.05470703,0.05774906,0.02608154,-0.06244421,-0.07136735,0.07946791,-0.00532987,-0.07365301,-0.04477869,0.00235933,-0.01829096,-0.03863914,-0.01862646,-0.01491398,0.03847758,-0.02804398,0.11470629,-0.03677199,-0.03653231,0.01323342,0.03899035,0.01836055,0.06296749,0.03212432,-0.00467969,0.02646119,0.01939,-0.11902947,0.08894956,-0.03899616,0.03945428,-0.01330609,-0.07671133,0.00442059,-0.0216173,0.02429202,-0.00177371,-0.00668549,-0.03334861,-0.02744094,0.04713918,-0.00325031,-0.00779108,0.02816449,-0.00716,0.05041444,-0.05550518,0.06494516,0.01576948,-0.11852147,0.10615137,0.05510741,-0.097596,-0.01155667,0.11328315,0.02354504,0.01112787,0.01855369,-0.00489791,0.10919851,-0.00006838,0.07278482,0.06185433,0.06123295,-0.04959623,-0.22142282,-0.02906416,0.01642447,-0.02805605,0.02363657,-0.01810683,0.06902693,-0.03128651,-0.01681454,0.06450953,0.08096903,-0.01364964,-0.02336317,-0.00658114,-0.00092728,0.0242577,-0.00938013,0.01121003,-0.03278942,-0.04046033,0.00198604,-0.06258307,-0.00262477,-0.12391199,0.00961788,0.02657209,0.1142901,-0.00557383,0.00810322,-0.09369029,0.00567521,-0.03790672,0.01091196,-0.07165666,0.08050361,0.00006453,-0.00793463,-0.08794761,-0.03518086,-0.0380744,0.0249886,0.03967068,0.03812985,-0.05870941,-0.08520232,-0.00723144,-0.05598067,-0.03984731,0.01061276,-0.035654,0.02749029,-0.01698143,0.02619317,0.06120894,-0.01588969,-0.03666232,-0.0893056,0.0145476,-0.00626644,0.02022804,-0.02837523,-0.05383121,-0.00842353,-0.01436125,0.08794196,0.0471871,0.00116395,0.00809696,0.05923351,-0.07342825,0.00584243,0.09068596,0.00442532,-0.02992798,-0.06862225,0.03128595,-0.01139233,0.00231279,0.05884366,0.00792935,-0.01559322,-0.08348253,0.05928047,0.0462307,0.02895871,0.0045328,0.02400391,-0.02588576,0.03108192,-0.05908088,-0.04787957,-0.01618569,-0.08447054,-0.05170621,-0.02690773,0.01410582,-0.1968936,0.01218068,0.00453513,-0.07157748,0.00857486,0.04287428,0.05167869,-0.04603258,-0.02035872,0.02517315,-0.01458278,0.02431241,0.03695126,-0.06551223,0.04510837,0.00280964,0.09541281,-0.01495684,0.05680859,-0.11277907,0.01895573,0.07394979,0.20428376,-0.01093297,0.00827454,0.04943045,0.03643127,-0.00464862,0.07532997,-0.01322576,0.0399738,-0.00738761,0.03160588,-0.01115974,0.00350576,0.08346194,-0.02249242,0.01712753,0.00226321,0.01224909,0.04690778,-0.05305971,-0.00597669,0.00786618,0.07539902,-0.05633674,0.00547945,-0.06001609,-0.09255763,-0.08936346,-0.08379287,0.01700484,-0.01214323,0.00819859,0.01845467,0.03033591,0.02213098,0.00111609,-0.03721163,-0.03137725,0.02227267,0.04390799,0.05579869,0.0627189,0.05524936],"last_embed":{"hash":"d03c00627ab141d43bf221fabba88cbc5cd06edfa975a90b9877ff04149cf97f","tokens":65}}},"text":null,"length":0,"last_read":{"hash":"d03c00627ab141d43bf221fabba88cbc5cd06edfa975a90b9877ff04149cf97f","at":1743662881835},"key":"37 Tips from a Senior Frontend Developer.md#30. Diversify the problems you solve","lines":[398,409],"size":249,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#31. Find mentors": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03453662,-0.02779836,0.00362106,-0.0975107,0.01933471,-0.05609154,0.06095429,-0.03499002,-0.01844402,-0.0180411,0.03081679,0.01447398,0.05216542,-0.00418527,0.00446991,0.03417297,-0.0211251,0.06659076,-0.05059703,0.01046834,-0.07021049,-0.02551805,-0.05973787,-0.03120396,0.07689942,0.02511998,-0.02217134,-0.09322242,-0.0560498,-0.13455863,-0.03442306,0.01923605,0.05905103,0.01160401,-0.03908054,0.04825468,0.03990927,0.02259818,0.01514078,-0.00237807,0.04326804,0.00632252,0.04717569,-0.05426895,-0.01608334,-0.03226116,-0.01688016,-0.05837549,0.01062684,-0.04689204,-0.05653002,-0.06788813,-0.02998693,0.0265157,-0.03586245,0.09694361,0.03175713,0.04115786,-0.01862969,0.04895173,0.07055639,-0.02578494,-0.17685679,0.0586715,0.03125527,0.0422244,-0.03936773,-0.01160707,0.02766086,0.01545365,0.00931932,-0.01521908,-0.00570744,0.04733341,0.01776215,0.00492998,-0.01090169,-0.0386894,0.07519445,0.02915683,0.00865765,0.08023024,-0.01053957,0.01714207,-0.00518418,0.0011596,0.07156835,0.00026085,0.03392239,0.00677515,-0.00285149,-0.05081553,-0.00349939,-0.02695889,-0.01324205,-0.04177216,0.06089969,0.03156829,-0.10118853,0.15892041,-0.0498744,0.04140002,0.03576143,0.04558115,0.03830197,-0.06312386,0.00708535,0.04314807,0.03802545,0.04856612,-0.01534296,-0.02790537,0.01919908,-0.03796021,0.02878556,0.02975015,-0.04452513,0.04239738,-0.00701476,-0.00944304,0.02015636,0.00471353,0.08716508,0.0295362,0.02364,-0.02765902,-0.01986696,0.05582499,-0.03133272,0.0459272,0.04164036,0.02703279,-0.12234183,0.0158295,0.01257451,0.02516856,0.01655294,-0.05464074,-0.00579573,0.000435,-0.00257866,-0.02926224,0.04060327,-0.02968968,-0.01062871,0.10593507,0.01208728,0.04975265,-0.00066268,-0.00433579,-0.00950088,0.0089107,-0.01932443,-0.03640904,0.05366493,0.01021463,0.10072538,-0.01259894,-0.0476374,0.03468443,-0.00911862,-0.06080647,-0.07322363,0.05787637,0.01884533,-0.12175729,-0.01692139,0.01679989,0.0111764,-0.00161572,0.03690369,-0.01005899,-0.01925711,-0.02777584,0.11896843,-0.06465672,-0.02970058,-0.01410955,0.01892122,0.04448346,0.0815531,0.02165464,0.03056684,0.0323585,-0.00113153,-0.08621874,0.03892982,-0.08208407,0.02915659,-0.04914074,-0.07030163,0.00707764,-0.06646996,0.02422729,-0.03605122,0.03142642,0.00146237,-0.04064829,0.02220833,-0.01994769,-0.02773684,0.02062667,-0.01072077,0.03045765,-0.12227111,0.05706113,0.01851322,-0.07380394,0.07770143,0.03487639,-0.09919555,0.00426328,0.11399285,0.04053002,0.00528982,-0.01030641,0.00627637,0.08835547,-0.09213328,0.06702915,0.09293808,0.0307587,-0.05989812,-0.22958475,-0.027624,-0.01547026,-0.01666923,-0.00907562,-0.02867957,0.0865797,0.00709587,0.00417409,0.09027185,0.08679416,-0.06014087,-0.02296107,-0.01368501,0.01722271,0.08222915,0.02491082,0.02739887,0.01278367,-0.03552987,-0.00438628,-0.01854188,-0.04905022,-0.09336641,0.02419946,0.03042116,0.10257366,0.03345218,-0.04821469,-0.09773558,0.0355372,-0.04732314,0.02599897,-0.11072264,0.04214399,-0.02051696,-0.00271022,-0.08180416,0.01944862,-0.01582441,-0.00657224,0.00402346,-0.01670158,-0.04990533,-0.0869488,-0.01879457,-0.02666612,-0.04472874,0.00563554,0.02999849,-0.00637961,-0.01018953,0.05006105,0.03357138,-0.0705841,-0.03492784,-0.07024129,0.03670183,-0.01879211,0.09142336,-0.03211348,0.00616373,-0.02404942,-0.01627778,0.06739456,-0.01140242,-0.06010007,0.03607585,0.10721657,-0.08240012,0.00336237,0.06712266,0.01964032,0.02224873,0.01303162,-0.0150044,-0.0330642,-0.03239018,0.04281257,-0.00213322,-0.04152442,-0.09288344,0.0494251,0.04426049,0.0029463,0.02332322,0.03206896,-0.00841578,0.02572361,-0.02500769,-0.07987688,0.01426924,-0.05081241,-0.09810385,0.02733896,-0.02153218,-0.18515807,0.02134327,0.07316897,-0.06184997,-0.02218409,0.03474176,0.11902985,-0.08876124,-0.01634559,0.02639388,0.03193064,0.02575223,0.05221894,-0.04184316,0.03460653,0.03777471,0.04830063,-0.01328755,0.01325815,-0.09144157,0.00024509,0.02183437,0.16927575,0.0257141,0.02188486,0.03673097,-0.0614146,-0.02314308,0.0929964,-0.03217507,0.02294024,0.00668044,0.04523421,-0.04188543,0.0102324,0.02773028,-0.02018143,0.04067098,-0.02976978,-0.00610661,0.05590675,0.00233274,-0.01865993,-0.00624555,0.04139674,-0.03477777,0.00228217,-0.03056323,-0.0475981,-0.05084838,-0.09111045,0.05063672,0.01328347,-0.0176181,0.02911592,0.07102188,0.02815017,-0.05069086,-0.00609299,-0.03395962,-0.00199588,0.04083117,0.06243565,0.05989771,0.07992034],"last_embed":{"hash":"5c14974f121b8797c4cd55360da2d247b23684532eb386f059fe49f1bb91f2eb","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"5c14974f121b8797c4cd55360da2d247b23684532eb386f059fe49f1bb91f2eb","at":1743662881847},"key":"37 Tips from a Senior Frontend Developer.md#31. Find mentors","lines":[410,425],"size":487,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#31. Find mentors#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03402327,-0.02724821,0.00972426,-0.08521668,-0.00370323,-0.04820443,0.04554125,-0.02859443,-0.03011777,-0.02080446,0.03032577,0.01103818,0.0477604,-0.0089544,0.02904758,0.04617078,-0.02255197,0.07032628,-0.04990141,0.01119078,-0.06770923,-0.02485217,-0.06987483,-0.03050358,0.06275243,0.02872972,-0.02328423,-0.1024481,-0.05412721,-0.12286879,-0.03108052,0.02796475,0.06571967,0.00690858,-0.03485946,0.04502323,0.04346148,0.0027732,-0.00243262,0.00517683,0.05460906,0.01195249,0.04115303,-0.04892436,-0.02601129,-0.04387409,-0.02425312,-0.05547281,0.01661214,-0.05761714,-0.04272341,-0.05519513,-0.00916698,0.01284378,-0.03105288,0.10961008,0.00054573,0.02330313,-0.02277583,0.04564019,0.07003703,-0.02962086,-0.1619069,0.08014202,0.03315054,0.04133535,-0.03615326,-0.02086316,0.03112234,0.00584722,0.02866475,0.00128643,0.00176358,0.03015259,0.02394912,0.00296583,-0.00897673,-0.04535404,0.08751313,0.01635138,-0.00194608,0.08465945,-0.02770005,0.02885364,0.00131751,-0.00141765,0.07739555,0.00428933,0.02402437,0.02357397,-0.02222742,-0.01639816,0.00058338,-0.0209638,-0.01739912,-0.02444818,0.06323627,0.02581636,-0.09092029,0.16382782,-0.05538666,0.06604944,0.03265088,0.04736225,0.03536487,-0.06635296,-0.00451953,0.03091435,0.03221359,0.0467648,-0.02250375,-0.04304443,0.00886477,-0.04324869,0.03266839,0.02075876,-0.05569906,0.03875746,0.00129353,-0.00979988,0.02191443,-0.01000632,0.0717407,0.03009557,0.03285605,-0.03131148,-0.02137963,0.06840371,-0.03403709,0.03204476,0.05031398,0.02451212,-0.1327074,0.01450034,0.01903274,0.03166181,0.01965217,-0.06121084,-0.01705866,0.01327499,-0.01466537,-0.0355,0.06417646,-0.03998477,-0.00762716,0.10812788,0.01397428,0.05471619,-0.03208651,-0.01324312,-0.00517542,0.00561514,-0.02447988,-0.02298235,0.0492421,0.01322532,0.11682804,-0.02684921,-0.05293532,0.03704232,-0.00773498,-0.05410875,-0.06485426,0.06334829,0.02010027,-0.12044551,-0.01783895,0.0253083,0.00772017,0.00091664,0.03831519,0.00168948,-0.01943292,-0.0105159,0.12990053,-0.0435154,-0.0450208,-0.00927601,0.03050928,0.05250295,0.06336509,0.02333247,0.02523096,0.02440243,-0.00253449,-0.08633011,0.05206703,-0.0747384,0.03823003,-0.04427134,-0.06273483,0.02005611,-0.05380947,0.02351543,-0.04004256,0.03157974,0.00755733,-0.0296057,0.02023263,-0.0176862,-0.02207322,0.02083069,-0.02639311,0.02228771,-0.12464216,0.05108866,0.01739052,-0.06521372,0.07780638,0.008688,-0.09304287,0.00587306,0.11466673,0.03861805,0.00678158,-0.01232975,0.00420214,0.07247153,-0.09566795,0.08110539,0.08935897,0.02024494,-0.05322376,-0.22586656,-0.01699834,-0.00906357,-0.02000457,-0.0122582,-0.0321837,0.0984418,-0.0038326,-0.00088864,0.11080581,0.10242837,-0.05201105,-0.02908082,-0.00342442,0.0207892,0.08709285,0.03802456,0.01895252,0.01417073,-0.04229799,-0.01644638,-0.03272957,-0.04220919,-0.09195676,0.0282488,0.02673223,0.103339,0.03582348,-0.0489259,-0.09357452,0.03172176,-0.04941554,0.00854053,-0.09852445,0.03276051,-0.01777665,0.00144951,-0.06382997,0.03109764,-0.00858464,-0.02431867,-0.00087649,-0.01722455,-0.03565244,-0.07068148,-0.01014645,-0.01678108,-0.04570828,0.00374113,0.03009021,-0.0080294,-0.00677372,0.03776861,0.03782398,-0.09009711,-0.03337362,-0.05552512,0.03132246,-0.02775403,0.07771985,-0.03274077,0.02014455,-0.02824413,-0.02834668,0.06380972,-0.02049279,-0.0610051,0.02813122,0.10347764,-0.08004218,-0.00257692,0.07273374,0.01364464,0.03657747,0.01111086,0.008053,-0.02219505,-0.04625114,0.03545032,0.00151328,-0.03100513,-0.10245079,0.0588783,0.02907265,-0.00068005,0.023592,0.01685091,-0.01618338,0.02168259,-0.02501225,-0.07555,0.01135995,-0.05057888,-0.10205171,0.00916813,-0.04076093,-0.19229309,0.01138313,0.08682234,-0.0587519,-0.0312312,0.03242981,0.13125142,-0.09181402,-0.0276018,0.01493119,0.04111419,0.02316013,0.04748908,-0.0470914,0.03315866,0.03657554,0.05388726,-0.01058485,0.00687351,-0.09297631,-0.00408308,0.01223134,0.17032552,0.01034076,0.03644535,0.04594873,-0.05749954,-0.02842315,0.08580171,-0.03775327,0.01864227,-0.00373916,0.03280616,-0.05752305,0.007203,0.04724739,-0.0122294,0.03559732,-0.02039496,-0.01454404,0.0376662,-0.00690679,-0.02604268,-0.01876504,0.03106702,-0.01135708,0.00493451,-0.02512468,-0.05960489,-0.02717408,-0.07474913,0.05540505,0.03085099,-0.00713922,0.04360884,0.05645852,0.01462179,-0.05061641,0.00394214,-0.02318217,-0.00697686,0.03364823,0.07363107,0.04559493,0.08627129],"last_embed":{"hash":"f4d1d3b6468db6085bf1014cabe40b12768077ac7439a55a4f76f53d3992f32c","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"f4d1d3b6468db6085bf1014cabe40b12768077ac7439a55a4f76f53d3992f32c","at":1743662881860},"key":"37 Tips from a Senior Frontend Developer.md#31. Find mentors#{5}","lines":[418,425],"size":283,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#32. Commit to a JavaScript framework & master it": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05534321,-0.00771697,-0.03893721,-0.07033499,0.0351128,0.00259332,-0.00785975,0.00707415,0.01395889,0.02617673,-0.05738761,0.0164917,-0.01518566,0.0394104,0.02352488,0.0538171,-0.04203601,0.04495864,-0.02373627,0.02404849,0.04120508,-0.00560808,-0.06098091,-0.05555946,0.03468444,0.03451816,-0.06408458,-0.06482632,-0.03752102,-0.12168732,-0.05761252,-0.08102379,0.00756587,-0.04825776,-0.02014086,0.00625244,0.02494212,0.02298973,-0.05057523,0.01364573,0.00911795,0.06571074,-0.01121119,-0.01663517,-0.00951983,-0.06002383,-0.01647626,-0.03328031,-0.0248105,-0.0403152,-0.02098854,-0.0527039,-0.07371394,0.00362868,0.05626388,0.06668877,-0.0037288,0.070049,-0.02889429,0.07050323,0.02755164,-0.00920385,-0.17031847,0.08683553,0.06502225,0.04027966,0.01605354,-0.0254983,-0.00627867,0.06576264,-0.03042779,0.04557009,-0.01084316,0.04860323,0.00764393,-0.0451943,0.04048545,-0.02407075,0.05689596,0.04881709,-0.00604133,0.00348569,-0.00223277,-0.02333415,-0.02507036,-0.01944733,0.00889074,-0.03446761,0.02738436,0.02234742,0.02477044,-0.03325552,0.03819116,-0.01326813,0.0002812,0.0409494,0.05392577,-0.00077166,-0.04384972,0.1493924,-0.05359797,0.04240635,0.05082728,-0.00570073,0.02431106,-0.01585019,0.01168337,0.01235823,0.04308195,0.02406253,-0.03611623,-0.04261994,0.01307489,-0.02224514,0.02876049,-0.01941496,-0.0181566,0.02805141,0.00978564,-0.00733196,0.04392911,0.04958272,0.05759854,0.00474382,0.04822951,-0.06412166,-0.04180594,0.01180821,0.0182704,0.03794375,0.02361156,0.00138267,-0.06401573,-0.0195154,0.03290956,-0.01331705,0.01182164,0.03211549,0.05428838,0.01041162,-0.03904416,0.00832753,0.02142079,-0.0499956,-0.00748337,0.13034558,0.00581947,0.05477946,-0.07739545,0.00013372,0.01451198,0.05749645,-0.03151933,-0.03764371,-0.03000062,0.03050781,0.02202691,-0.01267247,-0.02972455,-0.02479481,-0.0759814,0.00293416,-0.03836593,0.03531451,0.01828439,-0.13596444,-0.05022966,-0.00176231,0.00792589,-0.03649146,-0.02144904,-0.00254812,-0.03926034,-0.03838005,0.07992651,-0.05358994,-0.00602395,0.01642168,0.05695949,0.02669159,0.0917445,-0.02580928,-0.06386208,0.02941778,0.01993037,-0.04876035,0.03850613,-0.07071815,0.06126776,-0.03035756,-0.07766505,0.08158457,-0.01051742,0.00138574,-0.02514142,-0.0063123,-0.02441582,0.01485273,0.0581868,0.01283344,0.0246118,0.05620231,0.0128464,0.03998061,-0.04113569,0.06865347,-0.01189316,-0.10050625,0.10571699,0.04713761,-0.09045064,-0.03713518,0.11598034,0.0386696,-0.01784733,-0.0567528,-0.01313167,0.1259352,-0.03122872,0.00265694,-0.00982597,0.0500458,-0.03611837,-0.20483521,0.0350868,-0.00032593,-0.02043857,0.07044008,-0.05736583,0.09153641,-0.04406603,-0.05714088,0.06948086,0.05851996,-0.05433316,-0.03648387,-0.00624224,-0.02981652,0.02690203,0.00296914,-0.01281584,-0.02835532,-0.04915174,-0.0247731,-0.02299728,-0.09729905,-0.10781971,0.03129647,0.00603544,0.15316355,-0.0180332,0.0022411,-0.08353038,0.01369335,-0.01278208,0.05563166,-0.11657073,0.02268872,-0.07387119,-0.01202519,-0.08311969,-0.02993029,-0.01203561,0.07510983,-0.00231506,0.0380302,-0.06765372,-0.08619919,-0.03849514,-0.03921592,-0.09302731,0.02182225,0.00424649,0.0369877,-0.00360583,0.08177021,0.058422,-0.0438427,0.05079667,-0.04749894,0.04352648,-0.03838328,0.06186045,-0.02886368,-0.0210996,-0.01911156,-0.05646179,0.06815767,-0.0123887,-0.00064805,-0.0211083,0.09099112,-0.04682901,-0.01924626,0.08045222,0.02811774,-0.04065524,0.08419795,0.03635129,0.01518552,0.01559283,0.01785857,-0.02141407,0.02767851,-0.0485847,0.0552735,0.00182935,0.01896834,0.01825956,0.06163454,-0.01740755,-0.0662952,-0.04570037,-0.07611715,0.04167665,-0.03190624,-0.037877,0.01943007,-0.00669847,-0.22980505,0.03300698,0.02108588,-0.03317005,-0.01779883,0.0070523,0.07861245,-0.04025901,-0.03322507,0.04490307,-0.00587245,0.04537967,0.07055786,-0.03487392,0.0520528,0.03911043,0.05784723,0.01930632,0.03282987,-0.08134849,0.00311312,0.02131694,0.17955764,-0.01642495,0.02225449,0.08884259,-0.02170358,0.04487671,0.1464773,-0.0280848,0.01220281,-0.00414422,0.08812629,-0.03472188,-0.0211808,0.00834036,-0.02768592,0.01666286,-0.00752402,0.00838688,0.0343813,-0.02409241,0.00323097,0.00181858,0.06627353,-0.05724772,0.02924113,-0.06251076,-0.07474437,-0.02778838,-0.08629676,0.04236774,0.02197116,-0.006497,-0.00907785,0.02064969,0.08950651,-0.0124516,-0.06479732,0.0256987,0.0120894,0.05990613,0.04398231,0.09313212,0.02094047],"last_embed":{"hash":"48e54745740c107a5b52ebc264aede30a1fcfbe1d0e26fde7402b33a38044bad","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"48e54745740c107a5b52ebc264aede30a1fcfbe1d0e26fde7402b33a38044bad","at":1743662881872},"key":"37 Tips from a Senior Frontend Developer.md#32. Commit to a JavaScript framework & master it","lines":[426,439],"size":363,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#32. Commit to a JavaScript framework & master it#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05417724,-0.01127649,-0.03974506,-0.06418096,0.03219592,0.00504405,-0.01002596,0.00626648,0.01796422,0.02992648,-0.05267496,0.01253582,-0.01906245,0.04257232,0.02130612,0.05287683,-0.05004654,0.04022837,-0.02379046,0.02888134,0.0484942,-0.00920423,-0.05722744,-0.05382921,0.03074794,0.03981191,-0.06638192,-0.06796458,-0.03576868,-0.12186967,-0.06004313,-0.08179054,0.00721765,-0.04606906,-0.02550122,0.00476212,0.02211288,0.02417229,-0.0525702,0.01850974,0.00871532,0.06819176,-0.01071996,-0.01772794,-0.00881843,-0.06046945,-0.01659877,-0.03144407,-0.02182677,-0.04092952,-0.01877913,-0.04921251,-0.07400594,0.01123871,0.058896,0.06772123,-0.0061372,0.07361164,-0.028727,0.06671508,0.02560648,-0.00753172,-0.17020527,0.08626071,0.06121187,0.03982925,0.0178291,-0.02382943,-0.00907047,0.06834326,-0.02875861,0.05474484,-0.01546797,0.04560878,0.0074489,-0.05251946,0.04990507,-0.02378301,0.05457659,0.04933058,-0.01117078,0.00305604,0.00083038,-0.02899822,-0.02924421,-0.02030528,0.0042987,-0.04236332,0.02084043,0.02482315,0.02170688,-0.02798284,0.03512872,-0.01107461,-0.00454959,0.04029271,0.05045918,0.00279686,-0.03729929,0.14640245,-0.05238275,0.0411847,0.05126752,-0.00410164,0.02343216,-0.01362919,0.01045512,0.01233632,0.04215243,0.0226717,-0.03210307,-0.04001009,0.01553103,-0.02152385,0.02874131,-0.0175661,-0.0163037,0.02364262,0.00796086,-0.00980872,0.04286606,0.05805608,0.06284842,0.00748382,0.04866172,-0.06653828,-0.04331769,0.00629305,0.02744711,0.03857117,0.02358745,-0.00003766,-0.06322001,-0.01820343,0.03459648,-0.01614802,0.01090057,0.0347428,0.0561056,0.00929436,-0.03521939,0.00955454,0.02557747,-0.0529184,-0.0093645,0.13367909,0.00502519,0.05315629,-0.08854496,0.00109341,0.01341485,0.06044142,-0.0339802,-0.04218774,-0.03872977,0.03085414,0.02282381,-0.01502316,-0.03059266,-0.02701489,-0.0824002,0.00510859,-0.0323628,0.0329627,0.02386392,-0.13331077,-0.04873378,-0.00424706,0.00574477,-0.04160904,-0.02077962,-0.00004835,-0.0409479,-0.03038003,0.07485376,-0.05848119,-0.00543003,0.01699647,0.05500339,0.02827369,0.09349764,-0.02818517,-0.06802891,0.03126355,0.0261868,-0.04198906,0.03200841,-0.07060219,0.0636522,-0.03256068,-0.07715304,0.08729991,-0.00333348,0.00371466,-0.02457097,-0.01046284,-0.02736988,0.01451372,0.05363198,0.01621301,0.02647616,0.05136702,0.01188793,0.03854024,-0.03413265,0.06830521,-0.02093233,-0.09852438,0.1034072,0.05203285,-0.09159376,-0.03617791,0.1138797,0.03547152,-0.01891073,-0.05846607,-0.01358977,0.12426728,-0.02585835,-0.00042637,-0.0176015,0.04272747,-0.02831637,-0.20174313,0.03427917,-0.00352047,-0.01568802,0.07380982,-0.05842654,0.08985995,-0.04471268,-0.060711,0.07190976,0.05159389,-0.05473423,-0.03399583,-0.00458726,-0.03553599,0.02483867,0.00008225,-0.01329296,-0.02473179,-0.04905853,-0.0301624,-0.0198117,-0.10734694,-0.10257629,0.02813106,0.00329004,0.15930744,-0.0194627,-0.00050036,-0.07617242,0.01802612,-0.00893271,0.05646366,-0.11678738,0.01637496,-0.07405575,-0.01767662,-0.08194096,-0.02629618,-0.01384635,0.07340673,-0.00356632,0.03387447,-0.06303807,-0.08447846,-0.04036856,-0.03595513,-0.08945829,0.02123627,0.00633393,0.03955906,0.00141882,0.0825657,0.057056,-0.04310407,0.05955818,-0.04466609,0.04171752,-0.04176622,0.05701211,-0.02994144,-0.02058333,-0.02152075,-0.05798814,0.07227984,-0.01421081,0.0076368,-0.0203871,0.08307354,-0.0417702,-0.0145633,0.07923053,0.02755536,-0.04435589,0.09187544,0.03718087,0.014469,0.01780852,0.01107021,-0.02704114,0.03374586,-0.04686333,0.05162355,-0.00090056,0.01570785,0.02146441,0.06236362,-0.01719929,-0.06967627,-0.04338947,-0.06625941,0.04122008,-0.02820458,-0.03082472,0.01923776,-0.00739078,-0.23193274,0.03433072,0.01953468,-0.02867747,-0.01701815,0.00438,0.07514727,-0.0368676,-0.03406975,0.04703967,-0.00561143,0.04441647,0.07046335,-0.03352051,0.05263413,0.03771877,0.05445985,0.01909852,0.03254413,-0.07722383,0.00353169,0.0184997,0.17243402,-0.02124328,0.01901781,0.08855675,-0.02084786,0.04873291,0.14808689,-0.02893899,0.00913926,-0.00617915,0.09425958,-0.03327837,-0.0236173,0.0075448,-0.03051781,0.01760053,-0.00240006,0.00798666,0.03284119,-0.0242474,0.00232474,0.00184161,0.07163856,-0.05742922,0.02814089,-0.06293385,-0.07235221,-0.02249693,-0.08123178,0.04487656,0.02256477,-0.00607838,-0.01043031,0.01874991,0.09219124,-0.01336357,-0.06984387,0.02769635,0.01146992,0.06145801,0.03969642,0.09100819,0.01067771],"last_embed":{"hash":"cf3745fc45e305467243ced4e744d0a6b073cf14897d713cefb32a204b8fbbaa","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"cf3745fc45e305467243ced4e744d0a6b073cf14897d713cefb32a204b8fbbaa","at":1743662881885},"key":"37 Tips from a Senior Frontend Developer.md#32. Commit to a JavaScript framework & master it#{1}","lines":[428,439],"size":311,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#33. Constantly think of the user experience": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06393294,-0.0099715,0.01446312,-0.0958074,-0.01368838,0.03606868,0.04072215,0.00563711,-0.00474472,-0.01871751,-0.05969052,0.02818944,0.01219943,0.0235812,0.0165886,0.00644924,0.01804265,0.00012634,-0.00152387,-0.00728438,-0.00569758,0.00517781,-0.02337077,-0.02948477,0.01383059,0.0017234,-0.05529669,-0.0683689,-0.07268946,-0.15704359,0.0066582,-0.02124752,0.05640304,-0.02469467,-0.02873833,0.0062037,0.00542776,0.01976688,0.01068214,0.01364235,0.03342165,0.01659483,0.01663827,-0.03526946,0.00707156,-0.02108319,-0.00964948,-0.06961086,-0.01608434,-0.0079557,0.03773882,-0.04965373,-0.019005,-0.01133346,0.00355611,0.06826975,0.00370221,0.06958788,-0.03064442,0.04651239,0.04811392,-0.02115622,-0.18147168,0.07347699,0.05791114,0.03361255,-0.04073735,-0.03336614,-0.03023403,0.023684,-0.0634644,0.00725931,-0.00968606,0.06196757,0.01258443,-0.02130934,0.01983123,-0.02847073,0.03570982,0.0315665,-0.00648191,0.0150276,-0.02115858,0.0105675,0.00410486,-0.01468288,0.06801102,0.02947673,0.02332032,-0.00025313,0.03787521,-0.06256209,0.00681688,0.00100485,0.04610795,0.02442157,0.06854451,0.05266655,-0.11760813,0.16059466,-0.06039492,0.04381836,0.03312676,-0.01049546,0.03899366,0.01044453,0.06625967,0.02979257,-0.00840412,0.04679615,-0.06055288,0.01460303,-0.00037038,-0.02767946,0.0468171,-0.03302971,-0.00923654,0.01784179,0.00850513,-0.05339758,0.04767058,0.01902398,0.00670324,-0.04540411,0.04421888,-0.03308273,0.0649644,0.05634795,-0.07178761,0.04869306,0.02536883,0.00995969,-0.11552218,0.03648233,-0.00225708,0.01094722,0.0323113,-0.02665146,0.02311581,0.05375616,-0.00082435,-0.01204692,0.01820042,-0.07812535,0.00318889,0.11759355,-0.00018557,0.04311481,0.02749333,-0.01821229,0.01645432,0.04142937,-0.03939072,-0.05042251,0.00226866,0.008816,0.03260724,-0.02550965,-0.0414872,0.04344714,0.03338913,-0.04712689,-0.05677696,0.02658335,0.00151903,-0.1135905,-0.03236846,-0.01498764,0.0055824,-0.00156955,-0.05618178,-0.00942558,-0.02181511,-0.01163955,0.12039088,-0.00904309,-0.0192886,-0.01796597,0.02955393,0.05040798,0.04655261,0.02823141,-0.00656426,0.00158998,-0.02213379,-0.06972225,0.07658499,-0.05258628,0.0544918,-0.02638748,-0.06282049,-0.00600301,-0.05617733,0.03855257,-0.0438312,-0.01348966,-0.01829187,-0.02110684,0.03647038,-0.03536624,-0.00704658,0.03970809,-0.02037287,0.01458474,-0.06805729,0.09689597,0.01068574,-0.06823725,0.09096053,0.00631929,-0.08119884,-0.00757626,0.12088918,0.03248874,0.00300847,-0.00562312,0.0134448,0.08500407,-0.00757812,0.06298121,0.06617781,0.11163191,-0.08160891,-0.22641042,0.00906787,0.05067864,-0.06135547,-0.00269092,-0.0524818,0.08571846,-0.01809632,0.00923271,0.03817658,0.11110658,-0.0511728,-0.03886757,-0.00299935,0.01918437,0.04030427,-0.00870831,0.00573456,-0.07827324,-0.01257712,-0.03636029,0.00415329,0.00072368,-0.11753129,0.06518951,0.01372938,0.11883601,-0.03152854,-0.01414072,-0.06971201,-0.00449262,-0.00659358,-0.0128191,-0.15268433,0.06247814,0.03710391,-0.01059801,-0.04889364,0.00506265,-0.02581879,0.01687378,0.0115916,0.01770467,-0.06942698,-0.07271395,-0.03590375,-0.0164055,-0.07980101,-0.05709872,-0.00738589,0.00926131,-0.06345635,0.033774,0.07229366,-0.02758303,-0.06408564,-0.0925605,0.02488605,-0.05250204,0.12455865,-0.03640492,-0.0426353,0.01572656,-0.03900555,0.00582178,0.01789774,-0.04153757,0.01771515,0.12269107,-0.06931887,-0.02342709,0.09330444,-0.05228967,-0.00506519,0.01468044,0.00150206,-0.02268196,0.01453887,0.03420527,0.01875401,-0.03763771,-0.03756352,0.06178935,0.0383504,0.0284541,0.00811271,0.0152876,0.01145284,-0.0065754,-0.06435166,-0.05896136,0.04174684,-0.0629959,-0.0850483,0.016314,-0.02727623,-0.23172493,0.01862798,0.06505106,0.01021533,0.00204399,0.03204443,0.10137329,-0.08296235,-0.03862174,0.04019566,0.028616,0.01473218,0.03934169,-0.05823622,0.03881021,0.03566238,0.03189191,0.0059228,0.05434333,-0.11335216,0.00490282,0.03503339,0.19238126,0.01729807,0.04905764,0.04674718,-0.04112856,0.01303546,0.05248603,0.02386471,0.01993195,-0.0062507,0.066332,-0.00745662,-0.02694012,0.0364168,-0.02637231,0.00808937,0.00046349,0.03783048,0.02691607,-0.01615193,0.02025897,0.00445768,0.05320762,-0.02406652,0.0348263,-0.02342715,-0.0459452,-0.0548831,-0.07093086,0.02867885,-0.01108991,-0.00477463,0.05411467,0.04386253,0.00074716,-0.0132402,-0.01750759,0.00209976,0.0076526,0.08287345,0.09691194,0.02801787,0.06597861],"last_embed":{"hash":"f26b2d8a648b0bb9b1d17bcc97cca4cdd727c7fea8511c2c562da0b745ac928c","tokens":73}}},"text":null,"length":0,"last_read":{"hash":"f26b2d8a648b0bb9b1d17bcc97cca4cdd727c7fea8511c2c562da0b745ac928c","at":1743662881898},"key":"37 Tips from a Senior Frontend Developer.md#33. Constantly think of the user experience","lines":[440,452],"size":287,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#34. Be comfortable saying no": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03128133,-0.01500658,-0.00757786,-0.09718417,0.01145037,-0.01675832,0.04075871,-0.00848018,0.00190902,-0.017341,-0.00907166,0.0099838,0.005731,-0.00608238,0.0028377,0.03443154,0.01631876,0.05074432,-0.04472227,0.01395129,-0.02470052,0.01477241,-0.02063277,-0.026487,0.0580649,-0.01789068,-0.01868141,-0.09439918,-0.06174099,-0.16782658,-0.0301061,-0.00978115,0.03850259,-0.06558934,0.0133199,0.03688603,-0.01369604,0.0324411,-0.04920417,-0.00271454,0.03665972,0.05272715,0.0079577,-0.06357802,-0.00780558,-0.0366699,-0.02954325,-0.04527485,0.00758251,-0.01595495,0.00781081,-0.06288131,-0.04231349,0.00570765,0.02292094,0.06166653,0.01671077,0.05689888,-0.01456184,0.0552155,0.05331346,-0.045771,-0.14191176,0.05721999,0.05650667,0.02264585,-0.03751554,-0.02605197,-0.02678341,0.11253031,-0.01274353,-0.02865133,-0.0137146,0.08285839,0.01808346,-0.00970074,0.00158789,-0.00065796,0.05035432,0.00582077,0.02786209,0.01462187,-0.02119811,0.01493757,-0.01906653,-0.03603527,0.07407632,0.04209438,0.06280134,0.01667104,0.02794436,-0.07684804,0.02209096,-0.03278825,0.01091561,0.00378683,0.06383246,0.00087244,-0.12144128,0.17537034,-0.06757607,0.02795499,0.03061047,0.00327269,0.04900438,-0.03069557,0.03300894,0.00362321,0.03027653,0.04081805,-0.04347807,-0.0211514,0.01065265,-0.05328132,0.03777869,0.01178323,-0.02257878,0.03545251,-0.00676033,-0.01094468,0.03175045,-0.00388048,0.01590441,0.01665982,-0.01216904,-0.05144979,0.0206751,0.05854617,-0.01959569,0.05447671,0.0184949,0.01735634,-0.10058707,-0.00785263,0.00682175,0.00947608,0.02460778,-0.00558613,0.02264062,0.01771738,-0.03448692,-0.04841422,0.02007184,-0.06508394,0.00305575,0.13257807,-0.00585425,0.06906135,0.00597862,-0.02464176,0.0020982,0.02095976,0.00061769,-0.04455698,0.05046449,0.0037055,0.02358496,-0.00883367,-0.05475261,0.01876557,0.05039497,-0.05154872,-0.0636431,0.02174085,-0.0065737,-0.10317694,-0.03103697,-0.00670866,0.02284032,0.00785728,-0.01407202,-0.0095954,-0.00014926,-0.04357298,0.12312879,-0.00812155,-0.02838275,-0.01271738,0.03133112,0.0428917,0.05042055,0.03773853,0.01819329,-0.00440496,0.00271766,-0.09375827,0.05324647,-0.02609283,0.0323158,-0.02123857,-0.08568361,0.01253346,-0.04263384,0.02111098,-0.02729191,0.03143999,-0.00254394,-0.01456823,0.05921225,-0.03990566,-0.0324771,0.03919718,-0.00489416,0.0704148,-0.05564789,0.08521628,0.05543821,-0.09277004,0.10438588,0.02033361,-0.10479622,-0.01842695,0.11350559,0.03973043,-0.0214526,0.0349996,0.03240019,0.12371203,-0.05923244,0.03916721,0.07981352,0.07968218,-0.0783195,-0.2359491,0.02683588,0.00243258,-0.0746251,0.01087814,-0.02528685,0.08455636,-0.02659267,-0.02239722,0.08095246,0.08587009,-0.05733302,-0.04813272,-0.03256717,0.01312588,0.03969198,0.01810942,-0.00006583,-0.04305134,-0.03847312,0.03164851,-0.04701183,-0.02490965,-0.1153622,0.03980844,0.03605936,0.11418167,0.02177801,0.00249467,-0.06964114,0.00359619,-0.04599756,0.02640588,-0.14032252,0.06233789,-0.00299792,-0.01722611,-0.10315523,-0.00252664,-0.02777655,0.01759681,0.02514312,-0.00292475,-0.06582038,-0.09995771,-0.01313981,-0.06075549,-0.04730649,-0.005193,0.02296046,0.00585058,-0.04288066,0.0470533,0.03683349,-0.00286874,-0.04183082,-0.1030237,0.01028246,-0.00199303,0.08836723,-0.0447063,-0.04771308,-0.00257554,-0.03445485,0.04724179,0.02730485,-0.05979977,0.00946799,0.09632385,-0.06339577,0.0032806,0.06498878,-0.00200895,-0.04535186,0.00683857,0.00081935,-0.05948573,0.00676197,0.07012599,-0.01712658,-0.02796447,-0.09746223,0.0437786,0.02629078,0.03000703,0.01848418,0.03268366,0.00305533,0.00740585,-0.03133135,-0.04937057,0.02521343,-0.06401043,-0.08637082,0.05219657,-0.01429869,-0.19173841,0.02239125,0.05926043,-0.04349663,-0.02923069,0.06018914,0.09558643,-0.03901394,-0.06234695,0.04862377,-0.01638206,0.06293911,0.03482777,-0.05948928,0.04396332,0.00291713,0.03525462,0.00406459,0.02885506,-0.09432233,0.01508638,0.02373975,0.2027044,0.02007292,0.03648152,0.04849736,-0.03333616,0.02990893,0.06550253,-0.00211363,0.01188792,-0.00267063,0.0361543,-0.0160045,0.01694483,0.05275596,-0.0198519,0.04473435,-0.01605138,0.02481895,0.04138512,-0.00597316,0.02687328,0.00130162,0.08531336,-0.04419719,-0.00949224,-0.00089743,-0.069022,-0.05243302,-0.07693939,0.03017915,0.00059914,0.00660443,0.0112305,0.06322443,0.00828907,-0.01530384,-0.04365434,-0.00629199,0.00667129,0.06055661,0.04220452,0.05890936,0.06223835],"last_embed":{"hash":"c8b344935a3fb4d0a364b7f98d8f4946b4f4536043e5745fc2c388f75d537941","tokens":68}}},"text":null,"length":0,"last_read":{"hash":"c8b344935a3fb4d0a364b7f98d8f4946b4f4536043e5745fc2c388f75d537941","at":1743662881908},"key":"37 Tips from a Senior Frontend Developer.md#34. Be comfortable saying no","lines":[453,462],"size":255,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#34. Be comfortable saying no#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02575077,-0.0165124,-0.01010708,-0.09179237,0.00567812,-0.01286944,0.04363847,-0.00871043,0.00545184,-0.01563977,-0.00923658,0.01149248,0.00441061,-0.00407745,0.00114738,0.03624507,0.01835734,0.0501521,-0.04456377,0.01729111,-0.02054087,0.01809721,-0.01477459,-0.02449569,0.05858007,-0.01436219,-0.01519836,-0.0982191,-0.05754544,-0.16666478,-0.0360611,-0.01133094,0.04014987,-0.06465071,0.01356766,0.03572746,-0.01497359,0.03152478,-0.05113376,-0.0024968,0.03718324,0.05155761,0.01298222,-0.06789976,-0.00681583,-0.03598344,-0.02902922,-0.04840436,0.01163487,-0.01428909,0.00990692,-0.06206512,-0.04176569,0.00662932,0.02056617,0.06288479,0.01301998,0.0630707,-0.01476771,0.05121126,0.05440834,-0.04768428,-0.14423403,0.05400583,0.05674135,0.02265032,-0.04101018,-0.02672512,-0.03241911,0.11522277,-0.00928611,-0.02771437,-0.01536898,0.08742383,0.02239501,-0.01225837,0.00327,-0.00122757,0.05186424,0.00633494,0.02598967,0.01583161,-0.02484559,0.00904102,-0.02181498,-0.03917537,0.07488815,0.03923362,0.06225592,0.01873594,0.02653545,-0.07499143,0.01886622,-0.03085263,0.01059981,0.00215676,0.06030281,0.00280502,-0.1215321,0.17343956,-0.06901845,0.03138973,0.02658779,0.00132928,0.04646062,-0.03196035,0.03225049,0.00268512,0.03236363,0.04033444,-0.04350475,-0.02138424,0.00895201,-0.05369912,0.03704512,0.01644637,-0.02390859,0.03235459,-0.00779883,-0.01070513,0.03127375,-0.00586201,0.01620993,0.01759063,-0.01410223,-0.05413737,0.02127905,0.06157425,-0.01918862,0.05249798,0.01921511,0.01316407,-0.10336408,-0.00612021,0.00790581,0.00905256,0.02700357,-0.00207721,0.01824646,0.0181334,-0.0316136,-0.0523852,0.02403952,-0.06480121,0.00469399,0.13444352,-0.00922053,0.07076351,0.00236232,-0.02468024,0.00215926,0.01937457,-0.00009241,-0.04742382,0.04779255,0.00177217,0.02065562,-0.01159352,-0.05964891,0.02016595,0.0510359,-0.05093684,-0.06061204,0.02397302,-0.01002065,-0.09148915,-0.03107893,-0.00633372,0.02292293,0.00836113,-0.01391549,-0.00804064,-0.00046592,-0.0360939,0.11909898,-0.00851424,-0.03136161,-0.0159595,0.0272225,0.04308543,0.04827758,0.0381358,0.01835773,-0.00119455,0.00222252,-0.08866969,0.05178951,-0.02208198,0.03529089,-0.02390108,-0.08860683,0.01642296,-0.03995456,0.02403181,-0.02822072,0.03186334,-0.00377907,-0.01383655,0.05826448,-0.04199059,-0.03399783,0.0391239,-0.00045368,0.07331398,-0.05223997,0.08813353,0.05065808,-0.09400059,0.10598806,0.0218196,-0.11204503,-0.01941308,0.11082724,0.03909456,-0.01845657,0.04010545,0.03645278,0.122935,-0.05837253,0.04081841,0.07839613,0.08009639,-0.07690023,-0.23431069,0.03000935,0.00352339,-0.07435538,0.01140016,-0.0254969,0.08680137,-0.02333243,-0.02480849,0.08730331,0.07705093,-0.05869236,-0.04897084,-0.03051435,0.00933611,0.04321566,0.01717686,-0.00075457,-0.04587601,-0.03570142,0.03166154,-0.04788258,-0.02430727,-0.11478801,0.03779938,0.03915306,0.11786078,0.01460154,-0.00235112,-0.06624389,0.00637862,-0.04586024,0.02230645,-0.14231773,0.06496921,-0.00169651,-0.02349697,-0.1048767,0.00121827,-0.02867564,0.01879567,0.02525881,-0.00423951,-0.06339215,-0.09909815,-0.01688499,-0.057867,-0.04465002,-0.00795677,0.02343371,0.00795338,-0.03956121,0.04632429,0.03648081,-0.00207751,-0.03909638,-0.10455607,0.00751571,-0.00058003,0.08541456,-0.04834002,-0.04765004,-0.00189332,-0.03718639,0.0498051,0.02743747,-0.05711326,0.00824365,0.09309536,-0.05910303,0.0070192,0.06410865,-0.00207347,-0.0480517,0.00752755,0.00063037,-0.0627763,0.00636077,0.07084129,-0.01976788,-0.02656978,-0.09991442,0.0418326,0.02941909,0.03289549,0.01427271,0.0298082,0.00246325,0.00789859,-0.03477117,-0.04104591,0.02365432,-0.06642923,-0.08305667,0.05312585,-0.01715109,-0.19492421,0.02350447,0.05468766,-0.04291148,-0.02955352,0.05837307,0.09402537,-0.03175471,-0.06685641,0.05062647,-0.0173787,0.06029688,0.03516854,-0.06072783,0.04269487,-0.00009204,0.03630413,-0.0013973,0.03127634,-0.09380541,0.02033855,0.02054917,0.1953883,0.01841161,0.03659092,0.04801143,-0.03553746,0.02754156,0.05924053,-0.00212346,0.00833965,-0.00908204,0.0414355,-0.01750135,0.01706663,0.05817504,-0.02105232,0.04183793,-0.01282987,0.02569757,0.04355132,-0.01044536,0.03073428,-0.00211845,0.09274016,-0.04088402,-0.00911386,-0.00106093,-0.07173499,-0.05015467,-0.07452234,0.03643612,0.00375435,0.00719159,0.01357705,0.0660351,0.01183635,-0.01431613,-0.04637286,-0.00914269,0.00195535,0.06579418,0.04568039,0.05536968,0.0568912],"last_embed":{"hash":"e8291e1a2709f3b8901bb70c96da704388641caa4291a7a0a4cc91dfce6a5460","tokens":68}}},"text":null,"length":0,"last_read":{"hash":"e8291e1a2709f3b8901bb70c96da704388641caa4291a7a0a4cc91dfce6a5460","at":1743662881922},"key":"37 Tips from a Senior Frontend Developer.md#34. Be comfortable saying no#{1}","lines":[455,462],"size":223,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#35. Continuously invest in your skills": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00871815,-0.00473748,-0.04264199,-0.08978999,-0.01256852,0.01930281,0.04043884,-0.03711245,-0.02073998,-0.0028366,-0.02243578,-0.01779427,0.0343914,-0.03343378,0.01092426,0.04415132,-0.03635712,0.03001609,-0.06425641,-0.00239552,-0.0326831,-0.02513808,-0.02931322,-0.04802062,0.06405781,-0.01891314,-0.02213228,-0.0732348,-0.04673375,-0.10738239,-0.02929658,-0.0327866,0.00713997,-0.04997562,-0.0271206,0.01058496,-0.00499059,0.01801286,-0.00138648,-0.01013397,0.01801816,0.01926918,0.00840031,-0.0693022,0.01636472,-0.04453002,-0.02657945,-0.05965666,0.04861274,-0.03416228,-0.01799423,-0.07402942,-0.02884243,-0.01061713,-0.0200424,0.04494093,-0.00801028,0.05645285,-0.03135554,0.05855167,0.05655844,-0.01190768,-0.16441126,0.03968887,-0.00787662,0.02998298,-0.00273078,-0.00203693,-0.0131429,0.03172842,0.00665408,-0.0053618,-0.04620687,0.04431178,0.02286363,-0.01719158,-0.03412282,-0.02574511,0.07217359,0.07329957,0.04159857,0.02535214,-0.01287722,0.02430432,-0.03254661,0.01714911,0.09842927,-0.00370262,0.03655384,0.00721293,0.05134473,-0.08920194,-0.00744844,-0.03370396,0.02928439,-0.00472313,0.08170794,0.01179075,-0.12011396,0.16148697,-0.03736065,-0.00341177,0.03968096,0.00776796,0.04476114,0.00067231,0.01878735,0.05757616,0.0068292,0.04065995,-0.07319297,-0.01747343,0.01451276,-0.02259887,0.00088291,0.02979506,-0.01268534,0.02163891,0.00301134,0.03880025,0.04587764,0.01868723,0.02800714,0.00658784,0.02571146,-0.08340497,-0.00557905,0.06597382,-0.02758186,0.06630206,0.00671611,0.03030669,-0.13497636,0.02570895,0.03472465,0.04266921,0.01551568,-0.02273686,-0.00424392,0.01798067,-0.05101965,0.00397622,0.03866159,-0.05006539,0.03090323,0.10207442,-0.00311009,0.05187074,0.02091048,-0.00708942,-0.01236416,0.02651953,0.02041425,-0.05184587,0.02426824,0.04794825,0.07212961,-0.02754784,-0.0426657,-0.00969007,-0.00248886,-0.06728462,-0.05010843,0.07575804,0.00006481,-0.09044912,-0.05872103,-0.00891749,0.02462771,-0.00439037,-0.01398017,0.03484991,-0.05359778,-0.04203143,0.12367431,-0.01642998,-0.03913055,0.0069483,0.02751946,0.01841179,0.06230079,0.01095466,0.0330273,0.00282035,-0.02707021,-0.0645116,0.03199086,-0.05396775,0.05402713,0.00565785,-0.07786069,0.11499938,-0.06929625,0.01826713,0.01012479,0.02004115,-0.03003643,-0.02326253,0.06726664,-0.03649813,-0.00973638,0.02483839,-0.02305528,0.03247469,-0.09826306,0.0483024,0.0471468,-0.08052897,0.1435757,0.00340425,-0.06984875,0.01155186,0.12964135,0.06211724,0.01323745,0.01930848,-0.00459526,0.10331374,-0.0046492,0.00806642,0.07326367,0.03235531,-0.04132732,-0.21752344,-0.00471534,0.01633096,-0.07410593,0.04493838,0.00214492,0.07611068,0.00519014,0.00384096,0.03647191,0.06407886,-0.05632783,-0.06160936,0.00582751,0.00248559,0.02666573,0.00856621,0.00743909,-0.03234341,-0.00528302,-0.01764257,-0.01575824,-0.04964393,-0.10471737,0.08913317,0.02730886,0.11131436,-0.02784748,0.01168344,-0.09253041,0.0118577,-0.04288281,0.00377434,-0.10500919,0.05619701,-0.04375152,-0.0048637,-0.05482597,-0.03514362,-0.02087897,0.05413454,0.01042881,-0.00331701,-0.0466997,-0.07258214,-0.00383349,-0.07186419,-0.06868765,-0.00422797,-0.01942172,0.01810696,0.00749139,0.0359986,0.05266774,-0.0408238,-0.07103405,-0.07060844,0.02571515,-0.02706565,0.09536541,-0.00792028,-0.02644417,-0.01957905,-0.00724128,0.06416018,-0.0239072,-0.05888079,0.00156386,0.10836288,-0.07906836,-0.02647148,0.06475923,0.03288675,-0.04997678,-0.02944321,0.00718262,-0.03911769,0.0075021,0.07997556,0.04995368,-0.02990346,-0.1062395,0.03028414,0.07010398,0.00585177,0.01884815,0.08124406,-0.03689271,-0.00735178,-0.00701164,-0.07876561,0.0271384,-0.08713245,-0.05898025,-0.00850454,-0.04203657,-0.20185664,0.01142598,0.04823205,-0.01756118,0.02105581,0.00700187,0.09764674,-0.04069908,-0.04712648,0.03700775,0.01331514,0.03992895,0.08328105,-0.03161064,0.03638224,0.01465056,0.08555744,0.02606671,0.03145226,-0.04717736,0.01181983,0.05502737,0.18010242,0.02404534,0.05273714,-0.00321504,-0.06176812,0.03368375,0.0844547,-0.00076301,0.0602705,0.02453233,0.01860927,-0.00647305,-0.00357339,0.04800823,-0.04177364,0.06458262,-0.00553974,0.00590934,0.02965741,-0.03482449,-0.02021218,0.00843244,0.02655681,-0.04393101,0.06021968,-0.06147659,-0.10609215,-0.06629007,-0.09533979,0.04187927,0.01572149,-0.02955741,0.0008149,0.02349575,0.01844573,-0.0064314,-0.00622875,-0.02861563,0.02080143,0.01303668,0.05208136,0.09246082,0.10083716],"last_embed":{"hash":"fd0e3ccc658705872fda378d9980085f3607a5b35c16371696c73b12109c31dc","tokens":60}}},"text":null,"length":0,"last_read":{"hash":"fd0e3ccc658705872fda378d9980085f3607a5b35c16371696c73b12109c31dc","at":1743662881933},"key":"37 Tips from a Senior Frontend Developer.md#35. Continuously invest in your skills","lines":[463,470],"size":256,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#35. Continuously invest in your skills#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01604081,-0.01031032,-0.04415864,-0.08398597,-0.02050343,0.02631381,0.03864619,-0.03655012,-0.01929907,0.00032401,-0.02157491,-0.0188424,0.03478597,-0.03220123,0.00820996,0.04732031,-0.039234,0.02212371,-0.06789236,0.00060807,-0.02784827,-0.02740294,-0.02555348,-0.05138938,0.06084653,-0.01645451,-0.01950692,-0.07529546,-0.04274455,-0.10476952,-0.03211695,-0.03482687,0.0080719,-0.04825382,-0.03231642,0.00626766,-0.00705763,0.01886757,0.00191649,-0.0077906,0.01959526,0.02013707,0.01151609,-0.07070369,0.01829411,-0.04322961,-0.02892599,-0.06003555,0.0553493,-0.03323328,-0.01876134,-0.07504755,-0.02570114,-0.00562804,-0.02575094,0.04314052,-0.01257378,0.06288545,-0.02971776,0.05589191,0.05926504,-0.00921885,-0.164203,0.0337418,-0.01556444,0.03037858,-0.00407366,-0.00143661,-0.01732039,0.03111779,0.01132851,0.00133287,-0.05020984,0.04199639,0.02482226,-0.01988018,-0.03360367,-0.02870553,0.07304179,0.07818254,0.04355641,0.02688003,-0.01239931,0.0207395,-0.03897674,0.02065011,0.09928802,-0.00843631,0.03215478,0.01161322,0.04945025,-0.083744,-0.01546077,-0.03273642,0.02789349,-0.00962602,0.07914339,0.01396568,-0.11925909,0.15852739,-0.03506468,-0.00630003,0.03583642,0.00446693,0.04261495,0.00716035,0.01254056,0.06083303,0.00632387,0.04240691,-0.07233641,-0.01534682,0.01406784,-0.02389305,-0.00464892,0.03674319,-0.01504108,0.01781043,0.00296745,0.04420774,0.04887196,0.02149555,0.03051248,0.00842611,0.02640899,-0.08515739,-0.00618334,0.06658178,-0.02562017,0.06432563,0.00691979,0.03143127,-0.13724539,0.02918278,0.03728447,0.0436926,0.01830236,-0.02252476,-0.00721221,0.01778906,-0.04903871,0.00783725,0.04715503,-0.05390599,0.03438765,0.10512954,-0.00620472,0.05198757,0.018488,-0.00981215,-0.01650137,0.02792207,0.0205749,-0.05425273,0.01974033,0.05157125,0.07698213,-0.03222099,-0.04047762,-0.01225666,-0.00469824,-0.06759022,-0.04657274,0.07727151,-0.00102302,-0.0803662,-0.0598948,-0.00634582,0.0204667,-0.00733428,-0.01516056,0.04210537,-0.05695189,-0.03394779,0.12202198,-0.01716718,-0.04400001,0.00954184,0.02614523,0.01656269,0.06342331,0.01105474,0.03373617,0.00508631,-0.03165323,-0.05942113,0.02702844,-0.053011,0.05415457,0.00451617,-0.07690535,0.12459558,-0.06675711,0.02253165,0.01099054,0.0164319,-0.03352403,-0.02589081,0.06637777,-0.03600738,-0.00833778,0.01797039,-0.02072465,0.02879531,-0.09697274,0.04606674,0.04433247,-0.07672576,0.1475412,0.00380637,-0.07364877,0.01557435,0.12919685,0.06038748,0.01364126,0.02248234,-0.00529873,0.10262958,0.00264209,0.00803329,0.07106853,0.02639301,-0.03644866,-0.21328475,-0.00684019,0.01439022,-0.07427066,0.04860757,0.00124467,0.0705061,0.00931254,0.00067249,0.03749637,0.05655593,-0.05674237,-0.06421427,0.00888932,-0.0026091,0.02255604,0.00687857,0.00671478,-0.03394628,-0.00057408,-0.02213708,-0.01311841,-0.05220967,-0.10090321,0.08694541,0.02924299,0.11487899,-0.03485145,0.00719073,-0.08889318,0.01716148,-0.04128368,0.00173232,-0.10743168,0.05454022,-0.04379395,-0.0048304,-0.05077703,-0.03493725,-0.02198948,0.05599755,0.01000849,-0.00599757,-0.04411208,-0.06757731,-0.00516513,-0.07148515,-0.06667243,-0.00650143,-0.0220814,0.01789595,0.012671,0.03470604,0.04994641,-0.04133107,-0.06850917,-0.07103773,0.02678714,-0.02810322,0.09388586,-0.00784958,-0.02499052,-0.02216165,-0.00636164,0.06808819,-0.02482547,-0.054856,0.00168946,0.10504242,-0.07864169,-0.02472816,0.0591449,0.03352945,-0.05629221,-0.02914847,0.00518454,-0.04065064,0.01255297,0.07929756,0.04806726,-0.02776388,-0.11323071,0.02479858,0.07354539,0.00566307,0.01922761,0.08325253,-0.0375545,-0.00533579,-0.00542502,-0.07368457,0.02507035,-0.09325337,-0.05021194,-0.00929476,-0.04364047,-0.20330302,0.00906918,0.04518354,-0.01170141,0.02475685,0.00207756,0.0966986,-0.03892966,-0.04495057,0.03649859,0.01551059,0.0389808,0.08739845,-0.03050367,0.03607074,0.01237881,0.08568265,0.02373712,0.03227825,-0.04430353,0.01593325,0.05679256,0.17380896,0.01939104,0.05400062,-0.01005847,-0.06527215,0.0331038,0.0796944,-0.00261654,0.06136826,0.02160662,0.01999257,-0.00565099,-0.00483548,0.05251434,-0.04236104,0.06424174,-0.00340245,0.00210841,0.03022829,-0.03906314,-0.02216386,0.0091798,0.03009208,-0.04057416,0.06311381,-0.06672627,-0.11007347,-0.06518853,-0.09171034,0.04506106,0.01617966,-0.03049324,0.00027203,0.02394091,0.01862039,-0.00449114,-0.00677953,-0.03300298,0.02030247,0.015979,0.05335502,0.09011278,0.09878648],"last_embed":{"hash":"fbd5a319eb6bd0fcdb43521e49592e3a36d933d11ae8444431b2fe795d07fc7f","tokens":60}}},"text":null,"length":0,"last_read":{"hash":"fbd5a319eb6bd0fcdb43521e49592e3a36d933d11ae8444431b2fe795d07fc7f","at":1743662881947},"key":"37 Tips from a Senior Frontend Developer.md#35. Continuously invest in your skills#{1}","lines":[465,470],"size":214,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#36. When faced with too much work, reduce the features vs. the quality of the code.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06274037,-0.03463548,0.0016889,-0.09312788,0.01436504,0.01148537,-0.00792424,0.02540639,0.00275125,-0.01387529,-0.04103432,0.08436518,0.05496542,0.01504167,-0.01776106,0.00331883,0.05933748,-0.00272682,-0.07219906,-0.02232268,0.00438356,-0.00881242,0.0034079,0.00638876,0.00053088,0.01888831,-0.0676216,-0.06591799,-0.03876944,-0.23107639,-0.04075053,0.0103947,0.08778179,-0.04748717,-0.0174057,-0.00905686,-0.02300439,0.04291485,-0.07777527,0.03256761,0.00323411,0.0413761,-0.01855011,-0.07416838,-0.07719418,-0.02153357,-0.00155843,-0.06039527,-0.03494675,0.00428408,0.01492512,-0.04259957,-0.04309072,0.0359766,0.02653085,0.07787598,-0.00063678,0.08589045,-0.01344542,0.08484334,0.06673524,-0.01161218,-0.15195265,0.07676835,0.07126323,0.02038501,-0.0514326,-0.06362292,-0.03130775,0.09466485,-0.05065059,0.00551019,-0.01354857,0.09083083,0.0148685,-0.02324058,0.00177357,-0.00140312,0.00539834,0.02458697,0.0218227,-0.01972567,-0.01645461,0.03827678,-0.00876566,-0.03627196,0.04044602,0.0193501,0.04574988,-0.03660544,0.07789957,-0.04912013,0.03406318,-0.00300907,0.00986802,0.01970767,-0.01566466,-0.01702712,-0.07435403,0.15711199,-0.05915084,-0.00666261,-0.00532536,-0.02882707,0.0805988,-0.02267208,0.07538678,0.02260628,0.00927276,0.08159485,-0.03034483,0.02011194,-0.0063613,-0.00711913,0.00246285,-0.001928,-0.00123563,-0.00975397,0.04317759,0.00525129,0.03357044,-0.02339619,0.04366685,-0.03255259,0.02020812,0.01479533,0.02742434,0.0307184,-0.06393541,0.04025031,0.05667968,-0.01486556,-0.08481191,0.01888322,0.00938356,-0.00024603,0.01785553,0.03552951,0.01479807,0.0026695,-0.05917883,-0.01332342,0.03076515,-0.04545795,-0.03755048,0.09368294,-0.06417983,0.05287612,-0.01474847,-0.03802129,-0.03750895,0.06012527,-0.00024562,-0.05252014,0.03777341,0.00398628,-0.01748232,-0.0417355,-0.03688917,-0.00768407,0.06287066,-0.02078962,-0.07900425,0.06644399,-0.02500616,-0.05532942,-0.03320009,0.01568235,0.01280789,0.02673898,0.00776355,-0.01592592,-0.02205007,-0.0458221,0.09330246,0.01444643,-0.04138177,0.00729922,0.01390885,0.03524674,0.0851304,0.03968981,-0.03528652,0.01511348,0.04774519,-0.0577455,0.04870922,-0.01768176,0.06072592,-0.00036687,-0.07956059,-0.04241243,-0.00998897,0.02479207,-0.01792712,0.00332372,-0.02659302,0.0142837,-0.00185847,-0.05748574,-0.02019774,0.03272486,0.03224508,0.00868123,-0.04398342,0.07687528,-0.01380623,-0.08461139,0.0865851,0.02282307,-0.10233269,-0.00576822,0.11101304,0.00926508,0.01458874,-0.00450091,-0.01209453,0.07550722,-0.02067291,0.03840338,0.01210483,0.05624845,-0.03634053,-0.23669685,0.02278034,0.00063117,-0.04373541,0.04002091,-0.06634768,0.01378195,0.03352387,0.0196602,0.01857305,0.06968021,-0.04272219,-0.0585316,-0.01441845,-0.00064447,0.0140636,0.04047573,0.02138723,-0.09182715,0.0044878,-0.02149615,-0.00496214,-0.00271793,-0.11202184,0.01835906,-0.0094127,0.11743577,-0.07783089,0.06015968,-0.03452025,-0.01303499,0.01289266,0.01339066,-0.08670253,0.08937174,0.02587436,-0.01423752,-0.07297502,-0.02210524,-0.02896144,0.04096927,0.03212241,0.00829798,-0.09113322,-0.04923899,-0.01766116,-0.0574502,-0.03497751,-0.0425617,0.01740213,0.0009425,-0.01453288,0.02827869,0.07785691,0.06011245,-0.0168816,-0.1003416,0.06356766,-0.0143886,0.03757723,-0.01593764,-0.0457068,0.01972506,-0.05497773,0.05085279,0.03900785,-0.05426769,-0.02307803,0.02896966,-0.03164629,-0.01082328,0.1229435,-0.03772365,-0.04047132,-0.05637156,0.01007196,-0.02418361,0.04368154,0.04909748,-0.0228958,0.0253652,-0.02415772,0.0267707,0.04307562,0.0120887,-0.0104631,0.01852888,-0.02033287,0.01047752,-0.04633699,-0.05158037,-0.00600007,-0.020813,-0.11605635,0.02841392,-0.00094347,-0.20782577,0.03038575,0.05977568,-0.04743978,-0.03725001,0.04018629,0.05334549,-0.0261621,-0.01811776,0.0110872,-0.00335408,0.05598848,0.04796783,-0.07791776,0.04818746,0.06049654,0.06261273,-0.04878788,0.0672166,-0.07876812,0.02325828,0.02099226,0.19071554,-0.03601305,0.00290133,0.0364436,-0.02258235,0.04223336,0.06148911,0.00767017,0.05696678,0.04437451,0.16131248,0.0165064,-0.05742934,0.07964482,-0.02566649,0.00798539,-0.03922088,0.07255171,0.02911775,0.00454449,-0.02097868,0.01038747,0.0718043,-0.04186721,-0.02880084,-0.0170849,-0.05026989,-0.0255122,-0.04509285,0.01853901,-0.04305222,-0.01007452,0.00460764,0.02752771,-0.03397202,0.03530909,-0.00026387,0.03719746,0.05573534,0.0498626,0.03310771,0.09186143,0.05452195],"last_embed":{"hash":"c88d3e47490a77d4df76000ab6fd2951cf58bbced5da5c8e4b0905e98ea237ad","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"c88d3e47490a77d4df76000ab6fd2951cf58bbced5da5c8e4b0905e98ea237ad","at":1743662881954},"key":"37 Tips from a Senior Frontend Developer.md#36. When faced with too much work, reduce the features vs. the quality of the code.","lines":[471,480],"size":360,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#36. When faced with too much work, reduce the features vs. the quality of the code.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0590421,-0.03683013,0.00252177,-0.09132327,0.00930948,0.01218065,-0.00986912,0.02599543,0.0071667,-0.01155154,-0.0415291,0.0829797,0.0540191,0.01980524,-0.01594725,0.00234312,0.06146615,-0.00926238,-0.07388034,-0.0211654,0.00589818,-0.00955813,0.00675962,0.01060611,-0.00095158,0.02064252,-0.06789183,-0.07092862,-0.03522198,-0.2334803,-0.04241204,0.01137714,0.08978603,-0.04396909,-0.02038216,-0.0120553,-0.02371069,0.04195622,-0.07784431,0.03512767,0.00587323,0.0421135,-0.01743509,-0.07602748,-0.08065751,-0.01988592,-0.00225079,-0.06195778,-0.03135294,0.00619075,0.01446117,-0.04204015,-0.04082696,0.04258666,0.02691719,0.07622912,-0.00255569,0.0904457,-0.01293627,0.082155,0.06806386,-0.01114151,-0.15106182,0.07416036,0.07130814,0.0185874,-0.05405205,-0.06364545,-0.03481776,0.09742357,-0.05325863,0.00740172,-0.01615573,0.09112847,0.01717602,-0.02445313,0.00086793,-0.0018137,0.0054822,0.02348615,0.02264422,-0.02334918,-0.01440907,0.03733059,-0.0117535,-0.04116799,0.03700558,0.02067659,0.04402329,-0.04029515,0.08035198,-0.04025836,0.03159036,0.00001315,0.01131091,0.01996609,-0.02001274,-0.01535535,-0.07289824,0.15426914,-0.05789763,-0.0074183,-0.00696612,-0.02615437,0.07872353,-0.01972958,0.07558423,0.02596516,0.01003086,0.08432353,-0.02332362,0.01820159,-0.00530191,-0.00815769,0.000838,0.00153041,-0.00107035,-0.01232098,0.04509141,0.00521335,0.0326288,-0.02379933,0.0460522,-0.03161922,0.02271011,0.0133306,0.03035241,0.03067868,-0.06174152,0.03949175,0.05969385,-0.01524869,-0.08561084,0.02142772,0.01126967,-0.00244056,0.0211613,0.03642226,0.01536919,0.0019361,-0.05445048,-0.0122847,0.03755986,-0.05061223,-0.03758679,0.09881099,-0.06630348,0.05082545,-0.01880856,-0.04028213,-0.04075345,0.06115356,-0.00192008,-0.05816789,0.03164133,0.00401896,-0.01655994,-0.04283005,-0.03909707,-0.00856289,0.06153963,-0.01624709,-0.07703244,0.06369347,-0.02576592,-0.04512884,-0.0300524,0.01503778,0.00905309,0.0269927,0.00667585,-0.01698168,-0.02211299,-0.03981922,0.09099834,0.01664509,-0.0453223,0.00753911,0.00984451,0.03912067,0.08643693,0.04352841,-0.03877015,0.01813261,0.04932568,-0.05178116,0.05068744,-0.01006104,0.06355959,-0.00089424,-0.08207811,-0.04234076,-0.00784723,0.02611374,-0.01847289,0.00010244,-0.02696593,0.01477607,-0.00409768,-0.05668617,-0.02076837,0.02885741,0.03422575,0.01076619,-0.03972363,0.07802731,-0.01788439,-0.08533785,0.08629498,0.02322565,-0.10558905,-0.00296743,0.11051067,0.00767769,0.01938619,-0.00700185,-0.01135996,0.07555795,-0.0164911,0.03939638,0.01271637,0.05125906,-0.0342174,-0.23504294,0.02209117,-0.00225104,-0.04300026,0.04288998,-0.06909367,0.00876668,0.0366226,0.02025928,0.02095446,0.0619935,-0.042905,-0.05835334,-0.01679885,-0.00314321,0.01136304,0.03955225,0.02601747,-0.09221869,0.00785777,-0.02316961,-0.00412914,-0.00144023,-0.10898548,0.01566497,-0.01186975,0.12056281,-0.0844617,0.05369958,-0.03009904,-0.00982108,0.01351667,0.01039875,-0.08319973,0.09262317,0.02930442,-0.01799715,-0.07393919,-0.02016837,-0.02964416,0.03767197,0.03083999,0.00494933,-0.08967891,-0.05047088,-0.01692806,-0.05660544,-0.02885662,-0.04893808,0.01460832,0.00166212,-0.01259725,0.02608258,0.07674166,0.06303203,-0.01397872,-0.10250425,0.0641685,-0.01687286,0.03190237,-0.01622857,-0.04616134,0.01882229,-0.05623274,0.0531014,0.04114405,-0.05385168,-0.0202081,0.0201128,-0.02758929,-0.00791745,0.12426917,-0.04206412,-0.04475006,-0.05834559,0.00815805,-0.02293705,0.04504579,0.0459769,-0.025465,0.02798466,-0.02456402,0.02124379,0.04513237,0.00952957,-0.0109859,0.01722301,-0.01945228,0.01246498,-0.0486276,-0.04734742,-0.005903,-0.0213023,-0.10918444,0.02691231,0.00018792,-0.20859127,0.0314865,0.05655437,-0.0446548,-0.03625247,0.04006014,0.05110951,-0.02116142,-0.01633264,0.01215962,-0.00526574,0.05772598,0.04878997,-0.07939378,0.04596448,0.05937437,0.05650576,-0.05325615,0.07009872,-0.0730516,0.02648073,0.01955607,0.18397015,-0.04159261,0.00038358,0.0357865,-0.02310151,0.03976393,0.05599631,0.00631288,0.05625296,0.03978316,0.16610418,0.01517484,-0.05668928,0.08438628,-0.03048431,0.00329118,-0.03549246,0.07351086,0.03195012,0.00290668,-0.0215531,0.0053498,0.07861241,-0.03966791,-0.03184937,-0.01717952,-0.0484223,-0.02478867,-0.0430635,0.01895733,-0.04383849,-0.00804101,0.00607121,0.02798729,-0.03498142,0.03703186,0.00103351,0.03469335,0.05407259,0.05000727,0.02952304,0.0897018,0.0500452],"last_embed":{"hash":"6c901e15f57f08efee59db48c677a8d84f52fd009e65988e8d0b32d0d3414680","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"6c901e15f57f08efee59db48c677a8d84f52fd009e65988e8d0b32d0d3414680","at":1743662881966},"key":"37 Tips from a Senior Frontend Developer.md#36. When faced with too much work, reduce the features vs. the quality of the code.#{1}","lines":[473,480],"size":273,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#37. Strive to understand your collaborators (designers, Backend developers, etc.)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03387738,-0.01047032,-0.01338185,-0.09393898,-0.02762877,-0.01896013,0.06092284,-0.00808546,0.00248622,-0.03216109,-0.02381902,0.00064099,0.06351636,-0.01971171,-0.00762058,0.00320378,0.01307479,0.03526703,-0.04351529,-0.03160002,-0.04832894,-0.00552725,0.00157174,-0.0482086,0.05534171,0.03172867,-0.07544933,-0.0752581,-0.04396847,-0.09747057,-0.02779856,0.00683186,0.03460596,-0.01665929,-0.01120681,0.07104341,0.00977281,0.00884004,-0.05286317,-0.00464273,0.03298151,0.03873729,0.0023135,-0.05631772,-0.00111918,-0.04444162,-0.02242006,-0.0445514,-0.04895562,-0.00353905,0.08252887,-0.04736365,-0.06214171,0.03475168,-0.02308906,0.11646543,0.01819914,0.0457777,-0.01192399,0.07358431,0.06234084,-0.02489169,-0.1714934,0.08703116,0.06240313,0.02059822,-0.07899054,-0.01833822,-0.00499023,0.00489292,-0.01113074,-0.00354597,0.03503757,0.04083996,0.05842739,0.02898936,-0.01848012,-0.02888742,0.04224992,-0.04634506,0.0458246,-0.0086166,-0.01440897,0.06371013,0.01564241,-0.00072514,0.07709257,0.01260433,0.06769311,0.00282756,0.03147417,-0.05734196,0.03871318,-0.03286397,0.00574517,0.01990697,0.04866828,-0.02176521,-0.0995511,0.19325319,-0.08663777,0.0374708,0.02506066,0.01246039,0.0407058,-0.01026774,0.03600808,0.01114043,0.01031239,0.11226255,-0.07059705,0.01636839,-0.02382676,-0.03792422,0.04567279,-0.04224715,-0.02977544,0.03520349,-0.00502461,-0.02298256,0.05162283,-0.00207421,0.05970661,-0.00873386,0.01312559,-0.02841887,-0.0078504,0.04415567,0.0011555,0.05273779,0.03955271,0.06550696,-0.09912331,-0.02340689,0.02707479,-0.00207023,0.01464543,-0.04436545,-0.01290417,0.03914233,-0.03628525,-0.01249337,0.05170489,-0.05713775,0.00632263,0.10061421,-0.00794561,0.06772301,-0.02840608,0.01854604,-0.02619144,0.01811823,-0.04991741,0.0121164,0.03583404,0.01991392,0.04824264,-0.05811103,-0.01535554,0.04564118,0.05919883,-0.02197209,-0.0753855,0.0501508,0.00931073,-0.12128476,-0.00836712,0.00907132,0.02725317,0.03208917,-0.01967379,-0.00768514,-0.00512249,-0.03430792,0.06516652,-0.00870073,-0.0147549,0.00621536,0.04418244,0.00843703,0.04296669,0.05432439,0.00640641,-0.00585267,-0.00213218,-0.0702128,0.07411261,-0.0622095,0.05714899,0.02192778,-0.06868247,0.03715216,-0.05029076,0.03455808,-0.01111689,0.00818648,0.01306209,-0.05341735,0.0472628,-0.02217616,-0.03850424,0.07265985,-0.01280682,0.0360576,-0.0591481,0.04397846,0.00191748,-0.0705242,0.07946869,0.00404331,-0.08451568,-0.01316611,0.08209047,0.04588214,-0.02668883,0.0279973,-0.01793676,0.11781748,0.00396741,0.03006617,0.0327209,0.09737094,-0.05111266,-0.19371448,0.02396078,0.0325379,-0.06726822,0.00653725,-0.00631656,0.05536377,0.00042908,-0.01857794,0.02411659,0.14758539,-0.06411989,-0.03508191,0.02081949,-0.00128017,0.02568506,0.00034049,0.06184176,-0.04568399,-0.01495247,0.02895197,-0.01552068,-0.0189149,-0.07987382,0.03466529,0.04914493,0.11990066,0.02978169,-0.04315995,-0.05696529,0.01172246,0.00113014,-0.02669071,-0.15257357,0.01187151,0.01143579,-0.02178426,-0.10088098,-0.01501903,-0.02854953,0.00812977,0.02645339,0.00796124,-0.10931579,-0.05625566,-0.00371617,-0.05795378,-0.08721108,-0.01707911,-0.01558194,-0.01565611,-0.04604146,0.0525098,0.07804324,-0.02173085,-0.03193908,-0.06803412,0.06302506,-0.03291359,0.06773262,-0.0369692,-0.04433292,-0.01563063,-0.01106656,0.05211148,-0.01207592,-0.06432034,0.0013954,0.0809736,-0.03337013,0.01806061,0.08429305,-0.02525771,-0.03265605,-0.01075141,-0.01204549,-0.01583092,-0.0422912,0.02497974,0.01170791,-0.04349234,-0.10259753,0.07006602,0.00902164,0.04199329,0.00170496,0.04376489,-0.02897266,-0.00524083,-0.07575085,-0.04807168,0.01247982,-0.03847971,-0.07308026,0.00182833,-0.01173861,-0.23373045,0.0001035,0.019021,-0.03035844,-0.0422141,0.04245934,0.07354099,-0.04676185,-0.04476087,0.0088738,0.02182425,-0.01535208,0.05541182,-0.02653491,0.05191081,0.0700582,0.09511068,-0.02939704,0.04884311,-0.1040462,0.00004401,0.00747287,0.21155374,-0.02674771,0.04616503,0.06064533,-0.030618,-0.00003495,0.0517271,0.00527681,0.00811975,0.00738458,0.04664161,0.00007749,-0.0377324,0.06632886,-0.01450287,0.01143872,-0.04600859,0.03911157,0.04103576,-0.03046471,0.02550657,-0.0146371,0.0638525,-0.02077007,-0.05196876,-0.02744775,-0.0573507,-0.03458847,-0.07159612,0.00836901,-0.03891018,-0.02775519,-0.00376646,-0.00094738,0.02695183,0.02140708,-0.01678721,0.0152458,-0.03336375,0.05406873,0.05476057,0.08487757,0.07225826],"last_embed":{"hash":"de78897295588f6e5b5e8373f2276a38115d9c233ec2c2d7c636127c7e3fba98","tokens":75}}},"text":null,"length":0,"last_read":{"hash":"de78897295588f6e5b5e8373f2276a38115d9c233ec2c2d7c636127c7e3fba98","at":1743662881978},"key":"37 Tips from a Senior Frontend Developer.md#37. Strive to understand your collaborators (designers, Backend developers, etc.)","lines":[481,487],"size":296,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:37 Tips from a Senior Frontend Developer.md#37. Strive to understand your collaborators (designers, Backend developers, etc.)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02922854,-0.01614328,-0.0108506,-0.08549234,-0.03709649,-0.0146319,0.0622142,-0.00578839,0.00534516,-0.03559666,-0.02109544,-0.00280733,0.06517912,-0.01640842,-0.00843561,0.0023049,0.01241579,0.03341712,-0.04476193,-0.0303083,-0.04637395,-0.00813984,0.00965603,-0.04688058,0.05363836,0.03989342,-0.07571152,-0.07462109,-0.03812368,-0.09509856,-0.02894795,0.00701174,0.03641555,-0.01084316,-0.01173751,0.07121544,0.0116228,0.0065563,-0.05299465,-0.00520457,0.03608996,0.03674814,0.00394561,-0.06066334,-0.00056999,-0.0472129,-0.02344043,-0.04510842,-0.04939576,0.00031882,0.08510948,-0.04716498,-0.06091775,0.04058434,-0.02557195,0.12068947,0.01960228,0.054159,-0.00873905,0.06978915,0.06220894,-0.02764715,-0.17275707,0.08485617,0.06023701,0.0203288,-0.08708779,-0.01982649,-0.00999636,0.00581904,-0.00608674,-0.00021719,0.03232337,0.03901982,0.06008558,0.02555967,-0.01852487,-0.03182037,0.03913732,-0.04954731,0.04836907,-0.00879448,-0.00888142,0.06100846,0.01115431,0.00347688,0.08023463,0.01044603,0.06667411,0.00675561,0.02945044,-0.05641789,0.03143787,-0.03450561,0.00386994,0.02052808,0.04591044,-0.019657,-0.09688844,0.19145624,-0.0867753,0.03494028,0.02096014,0.01113058,0.03730709,-0.00514685,0.03354032,0.01200153,0.01186302,0.11302507,-0.07461902,0.01832439,-0.03005388,-0.04113404,0.04623061,-0.03715912,-0.03178527,0.03001094,-0.00561772,-0.02357031,0.04922545,-0.00184295,0.06624398,-0.01031645,0.01354539,-0.02719236,-0.00869529,0.0427231,0.00432742,0.05510167,0.03998147,0.06955935,-0.09727659,-0.02216659,0.0291827,-0.00150763,0.0151539,-0.04376515,-0.01513411,0.04474475,-0.02801174,-0.01123483,0.05792566,-0.06004341,0.00821703,0.1085596,-0.01381481,0.06855532,-0.03256364,0.01747912,-0.02739552,0.01802448,-0.05265311,0.01088409,0.03083118,0.02177811,0.05142231,-0.06486541,-0.01703686,0.04503953,0.05904825,-0.01798839,-0.07551868,0.05128464,0.01229351,-0.11325663,-0.00690592,0.00882843,0.02690073,0.03310781,-0.02342862,-0.00579089,-0.00558058,-0.03033391,0.06117771,-0.00862009,-0.01869921,0.0094022,0.04089027,0.00406933,0.04046519,0.05342778,0.00521971,-0.00370739,-0.0035263,-0.0656661,0.07397106,-0.06165281,0.05825013,0.02174104,-0.06655461,0.03977415,-0.04731383,0.03856818,-0.00915331,0.00335485,0.01237075,-0.05726,0.03995191,-0.02308372,-0.04229347,0.07095376,-0.01013042,0.03593189,-0.05853811,0.044246,-0.00024307,-0.0658981,0.08396753,0.00324267,-0.08824752,-0.00783215,0.07399251,0.04030253,-0.02898343,0.02965633,-0.01295767,0.11666645,0.01127733,0.03063821,0.02911107,0.09778156,-0.04573426,-0.18879157,0.0239129,0.03078245,-0.06551224,0.00993164,-0.00636159,0.04905523,0.00554278,-0.02302026,0.02243857,0.14529382,-0.06473351,-0.03091586,0.02312415,-0.00807726,0.02218041,-0.0004422,0.06681807,-0.04815255,-0.01304663,0.03003758,-0.01146238,-0.02143051,-0.07411985,0.03048618,0.04786139,0.12136915,0.02647785,-0.04502746,-0.05093383,0.01466506,0.0055026,-0.03164225,-0.15983041,0.00653326,0.01547683,-0.02478837,-0.10131165,-0.00984031,-0.02882531,0.00670739,0.0269148,0.0084356,-0.10884522,-0.05521663,-0.00513926,-0.05456067,-0.08501942,-0.02042341,-0.01266191,-0.01460602,-0.0425824,0.05123226,0.07918261,-0.02374897,-0.02804691,-0.0700378,0.0644015,-0.03387013,0.06320898,-0.03855691,-0.04093426,-0.01974435,-0.01507556,0.05504565,-0.01274751,-0.06029334,0.00340566,0.0767438,-0.02580683,0.02411703,0.0795241,-0.02969303,-0.03854847,-0.01327015,-0.01363542,-0.01732548,-0.04466897,0.01981898,0.01088932,-0.0379808,-0.10370542,0.06586751,0.00128888,0.04533929,0.00226577,0.04287438,-0.02945566,-0.00428128,-0.08001361,-0.04216379,0.00888708,-0.03756503,-0.06962273,-0.00067711,-0.00912708,-0.23810598,-0.00428137,0.01262355,-0.02509522,-0.04426433,0.04042019,0.07240368,-0.04822495,-0.04492244,0.0030983,0.0259419,-0.02224151,0.05327743,-0.02335607,0.05220331,0.07584762,0.09562266,-0.03150969,0.05296158,-0.10266174,0.00327551,0.00687377,0.21117657,-0.03514807,0.04564377,0.05699607,-0.03128407,-0.0053857,0.04572547,0.00333448,0.00773091,0.00341602,0.05041842,0.00047862,-0.04034772,0.07486402,-0.01450786,0.01185957,-0.04500661,0.03795644,0.04422696,-0.03326843,0.024299,-0.01348053,0.07084142,-0.0152932,-0.05640738,-0.03418801,-0.05679685,-0.03335769,-0.07041667,0.01142894,-0.03703439,-0.02568063,-0.00599825,-0.00139481,0.02486601,0.02777548,-0.01723879,0.01503339,-0.03831531,0.05847421,0.05323048,0.08342583,0.06647854],"last_embed":{"hash":"ab7beec85be85626f0073c1a1a8367de4b26d6d14325e61c349a609a4f99b21f","tokens":75}}},"text":null,"length":0,"last_read":{"hash":"ab7beec85be85626f0073c1a1a8367de4b26d6d14325e61c349a609a4f99b21f","at":1743662881990},"key":"37 Tips from a Senior Frontend Developer.md#37. Strive to understand your collaborators (designers, Backend developers, etc.)#{1}","lines":[483,487],"size":211,"outlinks":[],"class_name":"SmartBlock"},
