
"smart_sources:Clean Code notes.md": {"path":"Clean Code notes.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07461294,-0.04848973,0.01683489,-0.0703034,0.00700432,-0.04510064,0.01715925,0.03310763,0.01247499,-0.00177061,-0.06224453,-0.01011002,0.0497539,0.02105091,-0.05874971,0.00560675,-0.01239877,0.06877293,-0.05898554,0.01126359,0.12502961,-0.01984967,0.02502648,-0.00788752,0.06888151,0.05009786,-0.00671081,-0.0359569,-0.06628603,-0.2171618,-0.00384392,0.04205236,0.00668093,0.00922823,-0.02922183,-0.00066876,-0.0228996,0.06911325,-0.05408688,0.0451564,-0.01731337,0.03367427,0.04542826,-0.02477087,-0.0494214,-0.05131673,-0.01591244,-0.04418895,0.01536752,-0.05617759,-0.03095596,-0.03115275,-0.05561769,0.01567968,0.02002001,0.06059638,0.04663055,0.03262408,-0.03176785,0.02105238,0.02475353,0.01932943,-0.16621302,0.11150321,0.05768926,0.06546267,-0.0372817,-0.00878164,0.01399831,0.04034637,0.00681135,0.03787592,-0.02550425,0.04170167,0.01522894,-0.0307552,0.00706245,0.0093263,-0.02602494,-0.04429885,-0.09429118,-0.03869961,0.01152611,0.00304398,-0.02845663,0.01417316,-0.04188898,-0.01025268,0.07084755,0.02374213,0.02952854,-0.05631391,0.01211061,-0.01013154,0.03352496,0.04714738,0.0496584,0.03460337,-0.10509021,0.08527252,-0.06004954,-0.01647366,-0.05174818,-0.00633173,0.0300925,-0.03147809,0.0456428,-0.0462981,0.02063571,-0.02030282,-0.02167767,-0.00535333,0.02188164,-0.03990429,-0.00442674,0.07385994,0.0355258,-0.00621507,-0.01602778,-0.02807204,-0.01112932,-0.01291303,0.00519614,0.01007943,0.03875405,0.00414712,-0.0267172,0.07623011,-0.01287515,0.0689456,0.03490174,0.03353883,-0.04931424,0.01419776,-0.02434728,-0.0319287,-0.0055456,0.05000141,0.03954535,-0.04834925,-0.04678517,-0.01859871,0.03316746,-0.07958973,-0.05166822,0.05106862,-0.09044949,0.00742278,-0.06348145,-0.0231879,0.00005052,0.07386357,-0.02323921,-0.06310805,0.01495377,0.05992332,0.08189468,-0.02803023,-0.03993041,0.01764835,0.03647583,-0.01451613,-0.01917973,0.09130935,-0.03871996,-0.01109564,-0.01769467,0.02011261,-0.00242636,-0.03906972,-0.00041609,-0.02275539,0.01845768,0.00982111,0.04672059,-0.0323978,-0.01439294,0.0148634,0.044482,0.03437967,0.01619476,-0.03375488,-0.08539286,0.02455348,0.08284485,-0.0470414,-0.01444814,-0.03262339,0.05879249,0.03588383,-0.10938697,-0.06359229,-0.01368368,0.01060844,-0.07260431,0.02642228,-0.0505014,-0.0499414,-0.01852823,-0.02284601,-0.01426062,0.05928202,0.01641267,0.03160039,-0.02407127,0.07833645,0.04141648,-0.07259299,0.0497091,0.04528514,-0.07426702,0.04128031,-0.01318125,-0.00081383,0.02713955,-0.02139207,0.06482647,0.07278118,0.00245672,0.02896034,-0.01027291,-0.02737163,-0.05244207,-0.20878696,-0.03227486,0.04774735,0.03936369,0.0327115,-0.10847232,0.01823309,-0.01196168,-0.0379385,0.03238047,0.07759836,-0.00185064,-0.04235495,0.003784,-0.04835839,0.01880121,0.02778191,-0.00216834,-0.05434186,0.00994124,0.04216704,0.01071313,0.07209624,-0.03850568,0.04914304,-0.00178026,0.17125514,-0.05894744,0.07838262,0.07260957,0.04593673,-0.00253684,0.05345503,-0.16550581,0.04515244,0.04074438,-0.01288319,-0.0136508,0.02424247,-0.04887002,0.05072297,0.04201642,-0.06041766,-0.0772,-0.06342746,-0.03139435,-0.12204079,-0.07988449,-0.07364546,0.02277212,0.04853485,0.01633972,-0.00746685,0.03885234,0.012249,-0.00480017,-0.05046766,0.01114021,0.00438711,0.03956746,-0.01500495,0.00684998,0.02525432,-0.06519548,0.02847194,0.04509678,0.03518432,-0.00531516,0.00262583,-0.00005942,-0.0306377,0.08503205,-0.01157992,-0.08925433,-0.04903593,0.05478534,-0.08793246,0.03804017,-0.00456201,-0.04536631,0.05295484,0.01342742,0.04318139,-0.00030037,0.01786,0.06252848,-0.01815129,-0.01891884,0.03636612,0.00773664,0.02417507,0.02296599,-0.00859945,-0.02596412,0.09049631,-0.00277442,-0.24656683,-0.05024593,0.01044003,0.0318999,-0.02239924,0.07208915,0.06762784,-0.03502398,-0.016448,0.01134728,0.00498372,0.04310897,-0.01644054,-0.03508086,0.00955722,0.02476743,0.05649259,-0.05864791,0.04220473,-0.07252903,0.08106393,-0.00758289,0.21270765,-0.03109001,0.04856535,0.05664096,0.04326648,0.03408655,0.0440483,0.06355097,0.04177733,-0.01371184,0.14950542,-0.00671106,-0.02683888,-0.00861291,0.01432591,-0.04373015,0.03537892,-0.02903352,-0.00779034,0.02237734,-0.009843,-0.0221127,0.07478601,-0.03227406,-0.04128105,-0.08844902,-0.00539295,-0.01694072,-0.02976196,0.02388578,-0.02943553,0.01099923,0.01363064,0.01339865,-0.00890269,-0.01494196,-0.0077052,0.03131468,0.05234063,-0.01971619,0.06392349,0.06258798,-0.01603574],"last_embed":{"hash":"6a23aa63734a8dd3c8442b2dbb3c7980c3bbcc86280cd29a6f58130ae08dccfb","tokens":502}}},"last_read":{"hash":"6a23aa63734a8dd3c8442b2dbb3c7980c3bbcc86280cd29a6f58130ae08dccfb","at":1743662838043},"class_name":"SmartSource","outlinks":[{"title":"Design Patterns and Coding Styles","target":"Design Patterns and Coding Styles","line":102}],"blocks":{"#":[1,4],"#General rules":[5,11],"#General rules#{1}":[7,7],"#General rules#{2}":[8,8],"#General rules#{3}":[9,9],"#General rules#{4}":[10,11],"#Design rules":[12,20],"#Design rules#{1}":[14,14],"#Design rules#{2}":[15,15],"#Design rules#{3}":[16,16],"#Design rules#{4}":[17,17],"#Design rules#{5}":[18,18],"#Design rules#{6}":[19,20],"#Understandability tips":[21,29],"#Understandability tips#{1}":[23,23],"#Understandability tips#{2}":[24,24],"#Understandability tips#{3}":[25,25],"#Understandability tips#{4}":[26,26],"#Understandability tips#{5}":[27,27],"#Understandability tips#{6}":[28,29],"#Names rules":[30,38],"#Names rules#{1}":[32,32],"#Names rules#{2}":[33,33],"#Names rules#{3}":[34,34],"#Names rules#{4}":[35,35],"#Names rules#{5}":[36,36],"#Names rules#{6}":[37,38],"#Functions rules":[39,47],"#Functions rules#{1}":[41,41],"#Functions rules#{2}":[42,42],"#Functions rules#{3}":[43,43],"#Functions rules#{4}":[44,44],"#Functions rules#{5}":[45,45],"#Functions rules#{6}":[46,47],"#Comments rules":[48,58],"#Comments rules#{1}":[50,50],"#Comments rules#{2}":[51,51],"#Comments rules#{3}":[52,52],"#Comments rules#{4}":[53,53],"#Comments rules#{5}":[54,54],"#Comments rules#{6}":[55,55],"#Comments rules#{7}":[56,56],"#Comments rules#{8}":[57,58],"#Source code structure":[59,71],"#Source code structure#{1}":[61,61],"#Source code structure#{2}":[62,62],"#Source code structure#{3}":[63,63],"#Source code structure#{4}":[64,64],"#Source code structure#{5}":[65,65],"#Source code structure#{6}":[66,66],"#Source code structure#{7}":[67,67],"#Source code structure#{8}":[68,68],"#Source code structure#{9}":[69,69],"#Source code structure#{10}":[70,71],"#Objects and data structures":[72,83],"#Objects and data structures#{1}":[74,74],"#Objects and data structures#{2}":[75,75],"#Objects and data structures#{3}":[76,76],"#Objects and data structures#{4}":[77,77],"#Objects and data structures#{5}":[78,78],"#Objects and data structures#{6}":[79,79],"#Objects and data structures#{7}":[80,80],"#Objects and data structures#{8}":[81,81],"#Objects and data structures#{9}":[82,83],"#Tests":[84,91],"#Tests#{1}":[86,86],"#Tests#{2}":[87,87],"#Tests#{3}":[88,88],"#Tests#{4}":[89,89],"#Tests#{5}":[90,91],"#Code smells":[92,102],"#Code smells#{1}":[94,94],"#Code smells#{2}":[95,95],"#Code smells#{3}":[96,96],"#Code smells#{4}":[97,97],"#Code smells#{5}":[98,98],"#Code smells#{6}":[99,102],"#---frontmatter---":[101,null]},"last_import":{"mtime":1726813050000,"size":3523,"at":1743662830176,"hash":"6a23aa63734a8dd3c8442b2dbb3c7980c3bbcc86280cd29a6f58130ae08dccfb"}},"smart_blocks:Clean Code notes.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09591641,-0.06549285,0.01463231,-0.02307227,0.01834426,-0.02684722,0.03266316,0.02753877,-0.01042538,0.00551989,-0.05189629,0.00402255,0.02244109,0.00649753,-0.04816488,0.01515398,-0.01316034,0.01198103,-0.06674678,0.00441294,0.07847427,0.02891438,0.04613952,0.00253482,0.04553806,0.05540339,-0.0148724,-0.02637465,-0.07927249,-0.19486904,-0.03398057,0.02770979,0.06444702,0.00973443,0.01965328,-0.00135681,-0.02206808,0.05463219,-0.04302146,0.05559779,-0.02667022,0.00456632,0.01379932,-0.02949007,-0.02725542,-0.04176321,0.02704156,-0.04973454,0.02115401,-0.0385687,-0.01331926,-0.01299544,-0.05540035,0.01726999,0.02783579,0.0247362,0.01935049,0.01746762,-0.01296368,0.04241981,0.02452958,0.00742202,-0.17624927,0.10224193,0.02958185,0.08750236,-0.0482564,-0.00323673,0.01680579,-0.03099211,-0.00333554,-0.00121823,-0.03455845,0.02860217,0.0357717,-0.0177679,0.01056476,-0.01233447,-0.05341762,-0.06160006,-0.06232572,-0.04140386,-0.00573594,-0.00718644,-0.03696217,0.01510385,-0.03304006,0.03185907,0.06227002,-0.01117761,0.02868936,-0.05019446,0.0410299,0.01977111,0.03789331,0.0027902,0.06061484,0.04940936,-0.06835464,0.13760695,-0.08785436,-0.0247898,-0.05641595,-0.01660465,0.05603297,-0.02947491,0.10086574,-0.02144012,0.03157826,-0.04181135,-0.03257323,-0.03988859,0.00258165,-0.03347784,0.06317597,0.09608492,0.01773793,-0.0232762,-0.00672581,-0.01195803,0.00641659,0.032557,0.00774228,0.00136437,0.0334034,0.01317381,-0.01065704,0.08389191,-0.00252045,0.05502843,0.04581465,0.02626843,-0.04982612,0.04367923,-0.03060156,-0.02098624,-0.05430248,0.0060058,0.01269883,-0.03130861,-0.03321114,-0.00752785,0.00930933,-0.07369638,-0.06749909,0.04454696,-0.07930398,0.02590548,-0.07494666,-0.01104405,0.0301597,0.05646495,0.01052698,-0.00982849,0.01368737,0.03118835,0.09582279,-0.04478466,-0.03800245,-0.00260114,0.03869806,0.0068444,-0.05284359,0.08513005,-0.01185776,0.0019944,-0.00303252,-0.02240884,0.02047501,-0.02004386,0.01988601,-0.05125688,-0.02569437,0.03319768,0.07232422,0.00570295,0.01446144,0.01140667,0.0419028,-0.00560728,-0.00821174,-0.03885778,-0.08650233,0.05269608,0.06620875,-0.02375466,0.01364149,-0.05549696,0.0620675,0.07670619,-0.04456666,-0.03200954,-0.00051966,0.06226359,-0.07990335,0.00962248,-0.07336438,-0.04741556,-0.06562208,-0.02709426,-0.08849563,0.07456981,-0.00363547,0.0506283,0.00076566,0.0011389,0.02336133,-0.06816819,0.06491165,0.0238978,-0.08502021,0.0128313,0.00961127,0.01167157,0.02982113,-0.01344444,0.00530505,0.04543942,-0.01533681,-0.01236301,0.01973053,-0.02316909,-0.05791879,-0.20756975,-0.01367833,0.01284219,0.00559469,0.04983826,-0.07800588,0.02882204,-0.02326775,-0.03407859,0.04214266,0.11447429,0.02306552,-0.04818258,0.02433153,-0.01986775,0.06433913,0.02148343,-0.04002549,-0.0499116,-0.01631917,0.05673185,0.01872315,0.0108193,0.00595258,0.07763319,0.0165884,0.14973235,-0.05053059,0.09220948,0.07514507,0.0012167,0.00847586,0.06086214,-0.20117798,0.03929884,0.08072501,-0.03280483,-0.0092184,-0.00081607,-0.05443669,0.04269748,-0.00922825,-0.0459413,-0.0605626,-0.05107525,-0.03724359,-0.09621628,-0.08594396,-0.06889015,0.00822212,0.03408911,0.04367175,-0.00031461,0.09041084,0.04352205,0.04002505,-0.03484906,0.03838681,0.0011597,0.03860878,-0.00755575,0.00691397,0.00224445,-0.05618591,0.04425491,0.008836,0.00771886,-0.00931136,0.0055654,0.0180782,-0.02000724,0.08032396,-0.02515957,-0.07560056,-0.04328721,0.0442127,-0.08374298,-0.02040336,0.01444956,-0.06116379,0.02782353,-0.01192878,0.03517145,0.02780199,0.03730504,0.0549074,-0.07160483,-0.04650747,-0.02641328,0.0417621,0.00465476,0.02630831,-0.00621327,-0.11030701,0.06129502,-0.00939539,-0.19747257,-0.0737144,0.00533531,0.00992066,-0.02936933,0.06355028,0.03556316,-0.00546939,-0.04095572,0.01616237,0.03652276,0.033792,-0.03076439,-0.05937297,0.01651472,0.01869835,0.09924833,-0.03669427,0.05137677,-0.03709599,0.0505345,-0.03157451,0.22311997,-0.01639185,0.04426427,0.04462822,0.02363722,0.04024286,0.07205898,0.05153992,0.0623361,0.02293355,0.08818489,0.03659144,-0.09672768,0.03493831,-0.00093157,0.00859419,0.00514158,-0.03711007,-0.01171328,0.02709168,-0.04443417,-0.05726183,0.08341399,-0.01473935,-0.08279429,-0.10847136,-0.00979942,-0.01395756,0.00063914,0.02255845,-0.0034499,0.00303528,0.03222554,-0.00945432,-0.03803624,-0.0437553,0.02192085,0.02393082,0.06168202,-0.01109159,0.03908152,0.08006188,0.00409076],"last_embed":{"hash":"f8bb109342636c7870540ea7001c721124d2aaa006695e58f77cade8b2127b6a","tokens":91}}},"text":null,"length":0,"last_read":{"hash":"f8bb109342636c7870540ea7001c721124d2aaa006695e58f77cade8b2127b6a","at":1743662837949},"key":"Clean Code notes.md#","lines":[1,4],"size":286,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#General rules": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03324058,0.00365013,0.06951585,-0.05678456,0.05337932,-0.01528795,-0.00427177,0.04228281,-0.01510047,-0.00509217,-0.03623727,-0.00530036,0.06297052,0.0293511,-0.03350528,-0.00368199,-0.03997969,0.05046324,-0.07054757,0.02557087,0.1486098,0.00494518,-0.00738192,-0.00083388,0.03393863,0.00750453,-0.027141,-0.05762136,-0.0422296,-0.19016743,0.00123631,0.04021649,0.02574022,0.0058911,-0.03421838,0.0064187,-0.00814582,0.03650765,-0.0509367,0.05045367,-0.01931546,0.05545355,0.08087418,-0.03010372,-0.03549735,-0.04106104,-0.01668878,-0.02684385,0.04395849,-0.0644452,0.01591224,-0.01312695,-0.04268226,0.00518767,-0.02097362,0.0235683,0.07525577,0.01358328,-0.02707413,0.00405736,0.03647967,0.00182693,-0.20499255,0.0679314,0.04062962,0.0554501,-0.03124205,-0.00119109,-0.01135303,0.09944623,-0.00273635,0.0318328,-0.01381417,0.02170078,-0.05163739,-0.01926917,-0.01297859,-0.01271567,-0.01977603,-0.00175415,-0.11308337,-0.02899052,-0.0122008,0.017436,-0.0109584,-0.03803618,-0.06380142,-0.02225541,0.06243272,0.00728431,0.04171796,-0.06634643,0.00123052,-0.0007277,-0.0112002,0.0393368,0.03195663,0.05004307,-0.09013992,0.12916334,-0.01613498,0.05172676,-0.0518651,0.03131301,0.04065466,-0.05567959,0.02942102,-0.02230698,0.01714094,0.01926942,-0.0002811,0.00939786,0.06053732,-0.04183442,-0.04339508,0.02096023,0.08724628,0.01168259,-0.01136886,0.02423307,-0.02855356,-0.04649875,0.04856439,-0.00218976,0.06007477,-0.01085461,-0.03681275,0.07113882,0.02959619,0.03319038,0.02978443,-0.01580104,-0.02171835,0.01956754,0.00723367,-0.00222239,0.01326778,0.08763637,-0.00837729,-0.04586408,-0.0459701,-0.04009587,-0.0062683,-0.09551675,-0.03792188,0.0313965,-0.04835038,-0.03333055,-0.02666987,-0.01319429,0.01605864,0.05423395,0.00802617,-0.10977315,-0.00289362,0.05396406,0.08877873,-0.04374681,-0.02156899,0.02075843,-0.00236347,0.00966945,-0.00437446,0.05741185,-0.02162271,-0.02354245,0.00191714,-0.04405173,-0.01256518,-0.01104865,-0.02160256,0.00713477,0.04459172,-0.0222265,0.0569816,-0.04518285,0.01644582,0.02614575,-0.01312624,0.03011447,0.02276066,-0.02953556,-0.05943669,0.04303322,0.08651374,-0.01095191,-0.0253598,-0.00508291,0.04914023,-0.04317983,-0.10160576,-0.07005168,-0.06666019,-0.04119696,-0.03344287,0.00344994,-0.0426124,-0.02540377,0.03802266,-0.04690108,-0.07255234,0.00718418,0.02287168,-0.0066167,-0.003367,0.09023832,0.03458064,-0.07770352,0.02622011,0.07613697,-0.05537416,0.034628,0.00047453,-0.02444204,-0.01174085,-0.03008376,0.05388929,0.04185454,-0.01652925,0.03905291,-0.02406712,-0.00274326,-0.05265168,-0.21408305,-0.06525849,0.03118157,0.03230244,0.06839295,-0.07965119,0.02581708,0.04618481,-0.07219544,0.04871387,0.05924134,-0.02826527,-0.01203639,0.01374565,-0.04533072,0.05113401,0.02043612,-0.02101559,-0.01460967,-0.00068497,0.01285738,-0.02563508,0.08586579,-0.07275565,0.03806185,-0.03104208,0.16257496,0.00980011,0.08819488,0.04027299,0.05700553,-0.02909557,0.05777112,-0.11019479,0.07142028,0.02817809,-0.04573685,-0.03195823,-0.01385498,-0.05795249,0.03623433,0.06047482,-0.07225197,-0.05245614,-0.04794646,-0.04346035,-0.07771223,-0.09002362,-0.03217652,0.01299048,0.0819409,-0.02067943,-0.00290205,0.00277091,0.05542682,0.00136506,-0.0610303,-0.00319506,-0.01710973,0.02159447,0.00787958,0.00217188,0.00730604,-0.05606608,0.05747261,0.01794835,-0.0077724,-0.05514717,0.02294687,0.03440381,-0.01449314,0.07271659,-0.02350356,-0.11286376,-0.03763714,0.03522849,-0.07291317,0.01982239,0.01845028,-0.073086,0.03497913,-0.03208363,0.06646724,-0.00166981,0.015141,0.10785101,-0.06871235,0.00987549,0.03429783,-0.01286841,0.05959431,0.01657175,-0.00669566,-0.04657019,0.06945975,0.00368762,-0.23928455,-0.01761308,-0.00905381,0.03965205,0.00195566,0.07424308,0.08613912,-0.01957942,0.00127673,0.00648267,0.07103249,0.06791514,-0.00753926,-0.00098437,0.00955191,0.04087979,-0.00079133,-0.07958523,0.05883372,-0.0823761,0.06482995,0.06307909,0.20926812,-0.0173282,0.00952522,0.06321276,0.07202262,0.02646457,0.05000366,0.01498162,0.03671031,-0.0269965,0.11125728,-0.04381659,0.07185947,0.00927545,0.00212217,-0.05073674,0.03692564,-0.04096701,-0.0431386,-0.00530276,-0.01153522,0.01569449,0.06356543,-0.08227298,-0.00906212,-0.02258581,-0.00347674,-0.00462422,-0.02184852,-0.0055663,-0.06602357,0.03047901,0.00765181,0.04571692,-0.02342386,0.00388508,0.00878513,0.02191756,0.01294938,-0.03983876,0.05396395,0.03180999,0.01573727],"last_embed":{"hash":"1e6fe5b1c51b3f7ad55ef27ae3a8bcf53f627f09468763aa0bbffebb1cbf2dee","tokens":65}}},"text":null,"length":0,"last_read":{"hash":"1e6fe5b1c51b3f7ad55ef27ae3a8bcf53f627f09468763aa0bbffebb1cbf2dee","at":1743662837958},"key":"Clean Code notes.md#General rules","lines":[5,11],"size":279,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Design rules": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10501541,-0.06919602,0.00441956,-0.10070115,-0.03175033,-0.01436074,0.01417999,0.01805571,-0.00969458,-0.03305977,-0.03958524,0.00055548,0.05131141,0.00775494,-0.03496855,0.02457689,0.00537699,0.04871564,-0.00226693,0.0175911,0.12831883,-0.02360084,-0.0043676,-0.0338605,0.08846211,0.0889657,0.03453751,-0.0161437,-0.05140403,-0.20121771,-0.01670473,0.0282707,-0.00729162,-0.00697912,-0.0783186,-0.04094443,-0.0499532,0.06613107,-0.04005412,0.03250016,-0.0565085,0.0347867,0.02577849,-0.06955977,-0.03164121,-0.03307994,-0.0182193,-0.02567397,-0.00173342,-0.04852545,-0.02746711,-0.03804843,0.00964347,0.02826272,0.02511108,0.04370488,0.05957627,0.03523338,0.04742587,-0.01220876,0.05432553,0.04962638,-0.12802956,0.06500062,0.05146417,0.04716647,-0.01115035,-0.01198209,0.05013607,0.05326897,0.00418589,0.00324426,0.0048196,0.08270139,0.03749799,-0.03378491,0.0245842,-0.01667851,0.00405654,-0.0174022,-0.06666262,-0.04732569,0.02558727,-0.02863064,-0.04431923,0.00001366,-0.00785001,-0.02567868,0.00142513,-0.00220013,0.00893044,-0.04401776,0.0040168,-0.01351153,-0.02588045,0.09426036,0.03723187,-0.02942651,-0.09803342,0.13456114,-0.05542227,-0.02929626,0.01494115,0.06776233,0.00431232,-0.01290318,0.05384992,-0.03533757,-0.02185934,-0.02626618,0.01041474,0.06342108,0.03980456,-0.01004008,0.03102108,0.0536813,-0.02348639,0.03732233,0.01315218,-0.00894309,0.00717321,-0.02239079,0.01979715,0.00996273,0.05131163,-0.01445928,-0.03347838,0.05146347,-0.0226192,0.02850597,-0.00771986,0.05432503,-0.03871369,0.00890855,0.02982245,-0.02754865,0.02769513,0.0308003,0.05311554,-0.0894989,-0.07807896,0.01704573,0.05175294,-0.05729025,-0.00233928,0.07374021,-0.06585328,0.01869592,-0.00012255,-0.02226922,-0.05696025,0.03679883,-0.07236006,-0.03342456,0.01294668,0.08240191,-0.02050187,-0.01165472,-0.04149206,-0.00336325,0.00828439,0.01339686,-0.02830363,0.092033,-0.06555609,-0.03174702,-0.0287172,0.02229642,-0.00758752,-0.03584936,-0.01073942,0.02403211,-0.00504814,-0.00238608,0.0669383,-0.03405721,-0.04104448,0.00897517,0.04310974,0.04335339,0.02562956,0.01392561,-0.07455772,-0.00850833,0.07187745,-0.0404798,-0.03797624,0.00654324,0.05466408,0.07147681,-0.09697293,0.03451081,0.00157651,0.00859418,-0.04856265,-0.01321809,-0.07249746,0.01849031,-0.02246196,-0.04394773,0.01808223,0.08254115,-0.00459609,0.03954125,-0.04895652,0.01321339,0.03067858,-0.06700534,0.03213824,-0.00307107,-0.05622941,0.07717039,0.0672378,-0.01209551,0.00327595,0.00258843,0.01273028,0.11521136,0.0230414,0.02214216,0.03896084,-0.02747347,-0.00514438,-0.22005245,-0.06004316,0.00607445,0.03287379,0.01824876,-0.0183403,0.03866565,0.01742034,-0.08089856,0.04897431,0.0810141,0.0288996,-0.07109609,0.00886144,-0.03617778,-0.02451339,0.02676614,0.00551838,-0.10538182,-0.03836647,0.01323933,0.02249163,0.01700554,-0.05630166,0.06457371,0.01357979,0.15094885,-0.04234436,0.03029217,0.08243654,0.06810544,0.00759748,-0.00563254,-0.1224946,0.04436733,-0.02513702,-0.04978864,-0.00354984,0.03598086,-0.11353628,0.05945514,0.03949903,-0.06112399,-0.09105905,-0.00825572,-0.05827945,-0.0952422,-0.0634047,-0.06140679,0.01932634,0.01007784,-0.00570391,-0.02359614,0.05757776,-0.03347284,-0.03766741,-0.11029851,0.05172314,-0.00615902,0.03820817,-0.01190943,0.02876913,-0.00179115,-0.05995837,-0.01229194,0.06053912,0.03680491,-0.00331952,0.02792518,-0.03470422,-0.01694761,0.07360841,-0.02853989,-0.05246065,-0.02868373,0.05270279,-0.07230913,0.05885225,-0.01920887,-0.02598973,0.07123006,-0.0004106,-0.02174219,0.02649259,0.01187565,0.03100152,0.0488761,0.04749126,0.08420967,0.01103822,0.00183809,-0.00315018,-0.02749117,0.02216695,0.06877578,0.00966105,-0.24915177,-0.01726478,0.01990878,0.06584519,-0.06994586,0.03035845,0.06498469,-0.05770534,-0.01378343,-0.01350938,-0.01696648,0.05038301,0.02059414,0.01685622,0.01454132,0.01693292,0.06118256,-0.0575657,0.03754519,-0.13051285,0.03842729,0.02357757,0.21189566,-0.01869318,0.05328143,0.03121507,0.01216086,0.05800537,-0.05705812,0.08190426,0.0224083,-0.00314489,0.12579027,-0.05632894,0.00195246,0.00820782,-0.00877663,0.00486306,0.04143008,0.03681527,-0.01995924,-0.00707162,-0.05511308,-0.02354878,0.09141986,0.01386982,-0.03782285,-0.0596041,-0.00138449,0.00678969,-0.00393576,-0.00682328,-0.03167048,-0.0153992,-0.0338109,0.007999,0.00046996,0.00906997,-0.01299846,0.02642609,0.06337121,0.00753614,0.04496391,0.02323892,-0.02496284],"last_embed":{"hash":"5492851110a3c8f1c8487d29492ca0502b75198c522bc6c04f09df703e1fc95f","tokens":77}}},"text":null,"length":0,"last_read":{"hash":"5492851110a3c8f1c8487d29492ca0502b75198c522bc6c04f09df703e1fc95f","at":1743662837968},"key":"Clean Code notes.md#Design rules","lines":[12,20],"size":280,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Understandability tips": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0408343,-0.01351502,0.029285,-0.04174973,0.00261138,-0.04426254,0.02821232,0.0333495,0.00778402,0.0008403,-0.03256956,-0.05618152,0.03404397,0.01498227,-0.01771464,0.0200649,-0.04069517,0.09441642,-0.06964695,0.01431831,0.07385045,-0.03609157,-0.02140746,-0.04624237,0.03810833,0.07410777,0.0383688,-0.03155491,0.01396147,-0.23175482,-0.03520514,0.04179197,-0.08028633,0.01000602,0.00729503,0.00280589,-0.03794456,0.01966457,-0.01434449,0.03484836,0.01019145,0.03068618,-0.02271055,-0.03796413,-0.05616459,-0.04527037,-0.02766838,-0.02815385,0.03284644,-0.03126274,0.01613026,-0.0155274,-0.02005799,-0.00434086,-0.00103522,0.10949403,0.04337507,0.04575516,-0.01779782,0.01209536,0.0044229,-0.02044718,-0.12341189,0.11184718,0.0820106,0.0178608,-0.01118053,-0.00004311,-0.01467346,0.07709754,-0.00831564,0.04909728,-0.0163163,0.09713083,0.03121989,-0.00266387,0.02939308,-0.00599041,-0.04215218,0.00455327,-0.04043798,-0.04444911,0.02556248,0.03246144,-0.02163526,0.00224395,0.01332529,-0.03964942,0.05022425,-0.00479248,0.01578441,-0.02213005,-0.00717401,0.01105112,0.04977307,0.01101909,0.06515714,0.03320859,-0.03296898,0.1102127,-0.0515964,-0.03984931,-0.04410533,0.01160768,0.01157533,-0.00084537,-0.04480334,-0.04709302,0.02120614,-0.04285347,-0.03107668,0.00177898,0.00000494,-0.05678022,-0.04669839,0.01329201,0.01744213,-0.03007737,0.03078191,-0.08906136,-0.00906191,-0.02907963,0.02273477,0.0017247,0.01765212,-0.01145655,0.01470675,0.04168526,-0.00688166,0.05437297,0.05117701,-0.00892833,-0.07102875,-0.0088377,-0.01883333,-0.00540899,0.04124266,0.04177079,0.0218984,-0.05920968,-0.02245054,-0.01967748,0.04179759,-0.06092489,-0.03749961,0.12143256,-0.12345681,0.04304081,-0.08239711,-0.03567224,-0.03402023,0.03549225,-0.05857266,-0.06059354,-0.00440027,0.03188039,0.06624929,-0.04425098,-0.10076096,0.00153554,-0.00666105,-0.01239634,-0.06422439,0.15186714,-0.04709226,0.01628987,-0.02734986,0.05045663,0.05092606,-0.03448091,-0.01066103,0.0025094,0.01888129,-0.04101705,0.0575529,-0.02341937,-0.02508652,0.02637234,-0.00089916,0.06804762,0.04997997,-0.0278859,-0.0577764,-0.03386188,0.06471653,-0.0747164,-0.0222608,-0.03278115,0.05969851,0.03512732,-0.0823587,-0.07046816,-0.03454416,0.02139079,-0.04750421,0.01045197,0.01698886,-0.02412391,0.03022464,0.00405133,0.02969426,0.06446802,-0.01746029,0.05372158,-0.01390613,0.05988101,0.03357306,-0.05573203,0.04171162,0.05399778,-0.11160898,0.01569964,-0.0109408,-0.02409024,-0.00693337,0.01859972,0.07076566,0.09619817,0.02286932,0.079879,-0.01947981,-0.01793957,-0.02687297,-0.21088833,0.00685936,0.04191498,0.00089281,0.03542677,-0.0815587,0.02679829,-0.01081208,-0.06476701,0.0106033,0.0391359,-0.04548601,-0.0320731,-0.02100053,-0.04093003,-0.01029427,0.02631652,0.00696082,-0.01876234,0.06430352,-0.03927194,-0.00006517,-0.01897919,-0.03631322,0.06062653,0.00764023,0.1296877,-0.09991502,0.09332146,-0.01819122,0.0139717,-0.04159978,-0.00213473,-0.11872679,0.02733418,0.02882295,-0.05002375,-0.02144308,0.02241847,-0.06389082,0.02632114,0.04299786,-0.02121229,-0.06055503,-0.09307695,0.0081907,-0.02239474,-0.05757981,-0.04720505,0.03935107,0.01300982,-0.02295106,0.03642447,0.05015678,-0.01592902,-0.01683519,-0.0687954,0.00378571,0.00551228,0.06638294,0.00070318,0.01651006,0.03108839,-0.01579879,0.0100968,0.07752936,0.05291346,-0.01800388,0.00755355,-0.0342577,0.01495467,0.10298311,0.00889244,-0.10432614,-0.0317352,0.01125611,-0.0088218,0.0989586,-0.00221818,-0.01728648,0.05779649,-0.01985547,0.08841181,0.01915147,0.02690631,0.03453057,0.05632749,-0.04910672,0.03810192,-0.00326515,0.00306605,0.02003898,-0.04510402,-0.00389403,0.06271911,0.00319245,-0.25103551,0.00405681,0.02072194,-0.04212822,-0.03964546,0.01690909,0.06500029,-0.05043998,-0.06174559,-0.00666591,-0.01119398,0.00092217,0.02065117,-0.01302881,-0.01514238,0.00029645,0.05156412,-0.06250691,0.02520582,-0.07849701,0.07438096,0.03139802,0.24154185,-0.01155441,0.04914589,0.06803924,0.05630885,0.02433025,0.05242478,0.06533158,0.04743813,-0.01127494,0.13461114,0.00674423,-0.03105156,0.02528792,-0.01991824,0.00434538,0.07307214,-0.02595706,0.0736585,0.01819206,-0.01531003,-0.04627233,0.05186808,0.02972934,-0.03843045,-0.03983247,-0.01784415,-0.04197647,-0.04084416,0.03878806,0.02121442,-0.0058615,0.04668886,0.0699857,-0.02888151,-0.0024818,-0.03684559,0.01011497,0.05819116,-0.02952703,0.02825664,0.0372556,-0.04750348],"last_embed":{"hash":"a67dafc3b719a04b482c972b3e6e7fbc8fda0963b160dd6d5881fcb7e2965b14","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"a67dafc3b719a04b482c972b3e6e7fbc8fda0963b160dd6d5881fcb7e2965b14","at":1743662837977},"key":"Clean Code notes.md#Understandability tips","lines":[21,29],"size":476,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Names rules": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07728084,-0.03438723,-0.00530005,0.00853994,-0.03351141,0.03727917,0.02816504,-0.03228237,0.00398611,0.0381692,-0.03305403,0.03029866,0.05314995,-0.01647385,-0.02754352,0.02511346,-0.03203614,0.10083847,-0.12461283,0.02866395,0.18522833,0.00215801,0.01411144,0.02821955,0.04224029,0.04468471,-0.01382059,-0.02119253,-0.03410838,-0.18250917,-0.0247719,0.04382883,0.03174511,-0.00483658,-0.02479339,-0.02581473,-0.05248793,0.08706795,-0.07733822,0.06261716,0.01182533,-0.03386775,0.07906028,0.00278877,-0.02523818,-0.06615341,0.00805713,-0.02276791,0.03028736,0.00290583,-0.02027348,-0.02243461,-0.00702014,0.05551521,0.02893572,0.02315703,0.07914096,0.01186144,-0.02127612,-0.03079758,0.04049355,0.02052045,-0.15476696,0.13071875,-0.03158672,0.0361217,0.00532378,-0.01732769,-0.00574784,0.08069195,-0.00316332,0.01485666,-0.04690011,0.10489746,-0.00101781,0.00708616,-0.04757779,-0.02932659,-0.00312888,-0.00719286,-0.11488525,-0.06680395,-0.03066708,-0.0466492,-0.05423503,0.01423245,-0.01347859,0.00052063,0.02483334,-0.01593935,0.00641809,-0.05615843,0.03348423,0.04268224,-0.02517734,-0.02279083,-0.00278845,0.03422865,-0.11256159,0.12609048,-0.05485153,0.01749062,-0.05733998,-0.01920151,0.05583374,-0.03283408,-0.02407519,-0.03799605,0.00122022,-0.03241596,0.01738835,0.01300943,0.0051059,-0.05377906,0.00615527,0.06201392,0.04847057,-0.03135826,0.01901397,-0.01039092,0.00605802,-0.00806645,0.01921419,0.0408089,0.04559603,0.01532756,0.02557785,0.02018795,0.00462701,0.08154878,0.0232407,0.0230878,-0.07195527,-0.00433228,-0.0276882,0.01824957,0.02610876,0.03111646,0.05186011,-0.04224374,0.01207673,-0.04697749,-0.01521526,-0.0458799,-0.02075198,0.08403787,-0.11103657,0.03737107,-0.04278173,0.02409212,-0.04174665,0.03582405,-0.04753911,-0.06657034,-0.02316418,0.07657778,0.12194397,-0.05323738,-0.0487901,-0.02445287,0.06306812,-0.03264034,0.00175051,0.10217206,-0.01259142,-0.00929764,-0.06943479,-0.00334965,-0.04055894,-0.06578138,-0.02579759,-0.05368206,-0.03559121,0.05787329,0.04579053,-0.02837872,-0.01060757,-0.0269241,0.00989426,0.04462294,-0.00667574,-0.05374883,-0.03857036,-0.03672346,0.03471621,-0.05614392,-0.01197107,-0.02766402,0.0528769,0.03372406,-0.02999146,-0.0192687,0.00519103,-0.02519762,-0.06983247,-0.00083845,-0.04237591,-0.01437027,0.02902599,-0.10987968,0.05720941,0.04095085,0.042651,0.03060183,0.00899853,0.06255242,0.02172791,-0.06506364,0.10128339,0.0417877,-0.09035514,0.04022286,0.03583133,0.00172503,-0.04873821,-0.02787272,0.03677237,0.05417474,0.0298393,0.01541545,0.01644161,-0.04191994,-0.06802279,-0.17562349,0.03975539,0.01907947,-0.01965154,0.02354921,-0.0976545,0.00658312,0.01998504,-0.00064304,0.08770606,-0.04455571,-0.03888103,-0.06013738,0.04944735,-0.03266495,0.05053313,0.03520176,-0.05589833,0.01262642,0.02322536,0.00001428,0.03407562,0.0224178,-0.07847641,0.06174675,0.04311563,0.14610589,0.0083719,0.05525548,0.0249554,0.0412503,-0.02098964,0.00581376,-0.1365094,0.05755495,0.00916408,-0.04204006,0.01146604,0.0089523,-0.05961366,0.01424504,0.00715009,-0.02990834,-0.07756812,-0.00040782,-0.04716527,-0.08586595,-0.07879714,-0.05354712,0.02652989,0.0113517,0.05033615,-0.00420396,0.00890545,0.02963096,-0.02090284,-0.05565275,0.02020603,-0.00145101,0.02272316,-0.01431815,-0.02624911,-0.0154199,-0.03272595,-0.01976772,0.01949269,-0.01314063,0.02512488,0.03938371,-0.0097453,-0.05400243,0.06529147,0.02889526,-0.10267482,-0.030126,0.05308724,-0.07073022,-0.0257365,0.00447671,-0.0163216,0.02034428,0.04720546,0.11884257,0.00598606,0.03973867,0.03404037,0.0359764,0.00619318,0.04499752,-0.039526,0.00220075,0.0492885,0.02920495,0.02733486,0.05852117,-0.01386898,-0.2327031,0.03475221,0.03138731,0.04479915,0.00341449,0.04185617,0.01746799,-0.0760209,-0.05245162,0.02038932,0.0260936,-0.01300364,0.02192803,-0.04184657,-0.0201243,0.0283971,0.04205469,-0.03187175,0.01760926,-0.02628579,0.06993178,0.0029293,0.24970655,-0.03357592,0.03381084,-0.00463351,0.03019656,0.0794354,0.02282578,0.04439927,0.03295789,-0.00252698,0.12332141,-0.00825877,0.02534361,0.04048293,-0.00775874,-0.01822628,0.03597542,-0.02266011,-0.00718277,0.0013061,-0.04937311,0.01417673,0.06221096,-0.00438436,-0.04986282,0.00121679,-0.05060093,-0.002079,-0.09181096,0.0439181,-0.05167254,0.02626454,0.01231916,0.00256767,-0.01054638,-0.00069454,0.02110766,0.00781959,-0.02410559,-0.02574042,0.05036235,0.03122787,-0.02206105],"last_embed":{"hash":"3064efc7b3419c69eec195a91da903f441493fbca51c9cdbd056a87c9400318f","tokens":68}}},"text":null,"length":0,"last_read":{"hash":"3064efc7b3419c69eec195a91da903f441493fbca51c9cdbd056a87c9400318f","at":1743662837993},"key":"Clean Code notes.md#Names rules","lines":[30,38],"size":255,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Functions rules": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03151138,-0.02141645,-0.02181707,-0.07210223,-0.02098722,-0.02924584,0.05517974,0.03309269,0.07091717,0.0177895,-0.03820008,0.03456842,0.00844513,0.02156054,-0.01839202,-0.00814353,-0.00910252,0.07034877,-0.10486276,-0.00493782,0.08085482,-0.00811566,0.0125129,-0.01071112,0.05399501,0.06780621,-0.04750495,-0.03915966,-0.04405291,-0.18182787,0.01437869,0.06189614,-0.02619299,-0.00544904,-0.05265681,-0.02670302,0.0192539,0.11737936,-0.07845942,0.04192757,0.04975411,0.01475494,0.10045954,0.00110674,-0.05885608,-0.04872559,-0.04947003,-0.07220091,0.00053053,-0.01647179,0.00815163,-0.01183597,-0.04218873,-0.00726764,-0.02303724,-0.02285117,0.0149484,0.03534249,-0.00445534,0.06368446,0.05142967,0.01572636,-0.17568754,0.09425779,0.02978589,0.03421191,-0.01140249,0.04112174,0.00585424,0.14406651,-0.0149749,0.0698209,-0.04318038,0.04018028,0.0074274,-0.07034798,-0.04818399,-0.00184729,0.00704193,-0.05592229,-0.10871223,-0.06996867,-0.02042814,-0.0055001,-0.01322067,0.0444419,-0.030186,-0.01184405,0.05195909,0.02398264,0.01165654,-0.06754596,0.040134,0.0241992,-0.02556166,0.01237053,0.02095794,0.00709103,-0.08981661,0.16773129,-0.04345173,0.01257615,-0.01677609,-0.02027971,-0.01781441,-0.044586,0.01533596,-0.04471079,0.00650942,-0.00655809,0.01698052,-0.01193688,-0.00612448,-0.06950264,-0.01226285,0.05264357,0.00445525,-0.02176333,-0.02536258,-0.01444199,-0.00001462,-0.04880445,0.02594556,0.0286569,0.06238567,-0.00788948,0.01686881,0.06229614,0.00189785,0.069177,0.02364053,-0.01506921,-0.01024011,0.0230542,-0.01452862,-0.01734758,0.03152656,0.07475118,0.08594176,-0.02471679,0.02733306,-0.03102606,0.03009842,-0.06330682,0.01284669,0.0838872,-0.06126767,0.02595302,-0.06471258,-0.01621193,-0.01075649,0.12245264,-0.05759748,-0.01511014,-0.03201679,0.00825841,0.04740833,-0.00933704,-0.03066506,-0.00079219,0.01105888,0.02134039,0.01642447,0.11070503,-0.04666636,-0.02613884,-0.05503276,0.07818938,0.00036969,-0.07186839,-0.04230645,-0.01777915,0.01063551,-0.04750876,0.06296547,0.02239186,-0.01711984,-0.01829822,0.00469633,0.02889052,0.01724295,-0.01651693,-0.06536314,0.0262564,0.00451579,-0.04190276,-0.04107933,-0.02240913,0.04511398,0.02883022,-0.12776837,-0.02938808,-0.01507478,-0.05025177,-0.09793145,-0.01039472,-0.06367058,-0.04608205,-0.03975631,-0.0411437,0.062239,0.0665818,0.06704431,0.0549009,-0.01742173,0.07013849,0.05744174,-0.06974699,0.04241465,0.05781714,-0.07343469,0.04034581,0.02438515,0.03109374,-0.023036,0.01356227,0.06622174,0.02536968,-0.00055353,-0.00414667,-0.00646393,-0.05248678,-0.0395727,-0.18952204,-0.00280038,-0.00187946,-0.00459996,0.03049474,-0.08486851,0.03697933,0.02198176,-0.00622976,0.09619437,0.06011102,0.002744,-0.08763658,-0.0160681,-0.02256562,0.0142754,-0.03053456,-0.03002937,-0.02228987,0.01147495,-0.03072188,-0.02040703,0.04332033,-0.0715711,0.05885202,0.01654663,0.14439017,-0.02052122,0.077458,0.0551153,0.03581712,-0.03417923,0.01361853,-0.08923169,0.02216368,0.02203616,-0.01599489,-0.04043631,-0.00308168,-0.03380803,0.02706751,0.00233887,-0.01512782,-0.0579255,-0.02129124,-0.01100823,-0.03351544,-0.06259464,-0.05587418,0.01409464,0.0319795,-0.00938729,-0.03602208,0.00473077,0.02057287,0.00439398,-0.08091212,-0.00922747,-0.0177251,0.02445169,-0.01431954,-0.00529379,0.00464708,-0.05171087,0.07037117,0.0615725,0.03099848,0.00733368,0.05037431,-0.00447713,-0.05773774,0.08733941,-0.03619849,-0.06885407,-0.01500251,0.03663432,-0.0038745,-0.01514718,-0.02871739,-0.07678198,0.04749329,0.02318283,0.00836906,-0.01758751,-0.00095562,-0.00048816,-0.02112491,0.0360617,0.01456639,-0.06224404,0.01299684,0.04112787,0.00910621,0.08748733,0.07320163,-0.02288755,-0.24147588,-0.02145497,-0.00049175,0.01883269,-0.02969512,0.06273305,0.07524086,-0.04419168,-0.08666213,0.06194324,0.03819583,0.00353296,0.01180391,-0.05215237,0.05424834,0.01224993,0.04356162,-0.04919968,0.04977423,-0.03656283,0.06044047,0.01011773,0.20889507,-0.02898553,0.09887747,0.04473931,0.03510675,0.03771742,0.022449,0.02934828,0.04033136,-0.01362268,0.14870848,-0.0464837,0.03290936,-0.01415494,0.00453816,-0.06640309,0.01750239,-0.0495632,-0.01673651,-0.0209317,0.02200863,-0.03787141,0.04946369,-0.0267722,-0.02017431,-0.08730268,-0.00637722,-0.00499093,-0.07273801,0.04903187,-0.00415162,0.01070109,0.04642205,-0.01252067,0.03314995,0.01214187,0.0163855,0.03280023,0.0246882,-0.02328221,0.06473535,0.03366052,0.04573586],"last_embed":{"hash":"914e2c36a8c12e64f6125702b7593869bd2ecc11fd1cc63306369056867dbb87","tokens":63}}},"text":null,"length":0,"last_read":{"hash":"914e2c36a8c12e64f6125702b7593869bd2ecc11fd1cc63306369056867dbb87","at":1743662838003},"key":"Clean Code notes.md#Functions rules","lines":[39,47],"size":252,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Comments rules": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05515672,-0.04942174,0.03609581,-0.05966857,-0.04844879,0.00482891,0.02650565,0.02851191,0.02332673,-0.03758937,-0.02961323,0.0182762,-0.0051222,0.01998263,-0.0526639,-0.0162854,-0.02144634,0.034968,-0.07472528,-0.00344071,0.12193351,0.01288567,0.03288893,-0.00431424,0.01938752,0.05422433,-0.05282107,-0.06204989,-0.04728667,-0.15912177,-0.00767582,0.02849941,0.03902673,-0.04964957,-0.04588606,-0.00812234,-0.03044116,0.05181553,-0.08475656,0.06815434,0.01673411,0.03891233,0.08108645,-0.05191347,0.0023451,-0.0389086,0.03386155,-0.03785166,0.02223078,0.00842935,0.00592467,-0.0243671,-0.06247891,-0.00257634,0.0023257,-0.00850323,0.06292075,0.06852157,0.00409369,0.04290775,0.06343395,0.01013329,-0.17929386,0.11621103,0.00915538,0.03882882,-0.00479352,-0.0007668,-0.01052698,0.09948568,0.000292,0.00021927,-0.02386889,0.05975218,-0.02233772,-0.01811352,0.02465035,-0.02608541,0.00375126,-0.02101911,-0.08351974,-0.06445134,-0.02704897,-0.00288454,0.01146324,-0.0386842,-0.00811697,0.00689608,0.03235457,-0.00584635,0.07295673,-0.0709831,0.05177408,-0.00329892,-0.00948082,0.01046068,0.02504564,0.05426078,-0.12140882,0.13206302,-0.05806225,-0.00050376,-0.04720389,0.03528264,0.07659601,-0.0292557,0.06327303,-0.02759075,-0.01525519,-0.04235321,-0.00922626,-0.01751078,0.0354215,-0.05762846,0.02242879,0.04397987,0.07234568,-0.01959543,0.04259092,-0.03785049,-0.00154409,-0.04014834,0.04061366,-0.01294134,0.0242286,-0.03057809,-0.03173159,0.08169188,0.01685523,0.02490919,0.04840579,0.0130637,-0.02255743,0.02145386,-0.01927968,-0.00801893,0.01531358,0.0578605,0.09572952,-0.06540467,0.01814554,-0.07201723,0.01113128,-0.06857764,-0.00485023,0.09150474,-0.07129323,-0.0099724,-0.03922486,-0.00146706,-0.02911313,0.03830746,-0.04223461,-0.04002394,0.0040277,0.05190822,0.06315803,-0.02248982,-0.07569532,0.03947822,0.00009544,-0.00077392,-0.04613365,0.01323817,-0.02655181,-0.0179654,-0.01518807,-0.00942187,0.0246487,-0.04653931,-0.0201448,-0.04214126,-0.04200361,-0.02385174,0.03745697,0.00751533,0.01164775,0.01890424,0.04217311,0.07016224,-0.02083084,-0.03253426,-0.07784085,0.01373833,0.02902088,-0.04195206,-0.02614952,-0.06640723,0.06490242,0.04607131,-0.0936108,-0.04730268,-0.03516256,0.03450958,-0.10080989,0.00934272,0.01291226,-0.03009613,-0.02188491,-0.0411543,0.01152392,0.02362776,-0.03706014,0.03911646,0.00518676,0.08525859,0.02920296,-0.08731254,0.04020296,0.02373528,-0.0792984,0.05948128,0.00895023,0.01099143,0.01335788,-0.03791457,0.0287507,0.06924388,0.01884221,-0.02166044,0.00709738,-0.00487384,-0.0197429,-0.22436851,-0.01400688,0.0572165,-0.00844258,0.03789934,-0.10510991,0.06335203,-0.02035083,-0.00611038,0.02820457,0.02610007,0.00425654,-0.06000726,-0.06907767,0.00519482,0.00163471,-0.02016939,0.02670424,-0.0360024,0.01803185,0.03252573,0.00498619,0.00830992,-0.11859696,0.06637838,-0.00896146,0.13764699,0.01840165,0.09976168,0.06763846,0.06027218,-0.01370229,-0.02060649,-0.173705,0.05619863,0.01430193,-0.02597195,-0.0101719,-0.00534341,-0.05009753,0.00619894,0.05678354,-0.05842493,-0.01309,-0.04661394,-0.04863248,-0.0712845,-0.05348407,-0.05646691,0.01995308,0.02948065,-0.01088723,-0.00133856,0.08863495,0.06549101,-0.02855475,-0.05264763,0.0147729,-0.01708262,0.02225092,-0.02910631,0.02084808,0.04460662,-0.05876452,-0.04673489,0.06099465,0.04142084,0.03166393,0.00850312,0.04042352,-0.04364629,0.12560056,-0.03104161,-0.0739301,-0.03935242,0.03350082,-0.07365307,0.00244865,-0.00042144,-0.03442328,0.05993623,-0.00399333,0.0532684,0.01648759,0.00357272,0.07738764,-0.01401898,-0.01780342,0.0623379,0.01678916,0.02547319,0.02543988,0.00749321,-0.00716966,0.07584894,-0.00739736,-0.24492456,-0.0597634,0.03026522,0.03954056,0.00859439,0.05582299,0.0780927,-0.08701814,-0.08409125,0.03978039,-0.02567266,0.07638952,-0.02328708,-0.05608948,0.01670775,0.00032494,0.04182756,0.01746784,0.06539427,-0.03161954,0.0334121,-0.04247422,0.19021763,0.0473037,0.07872418,0.02096181,0.0260745,0.03257643,0.08209758,0.04229785,0.03379147,0.00787074,0.0848778,0.00182697,-0.01397682,0.0058958,-0.0544631,-0.01074551,0.04258166,-0.01444537,-0.03131207,0.01006099,-0.00454929,0.00369845,0.09403619,-0.01768002,-0.05130269,-0.03061342,-0.0232684,-0.00667379,-0.00876875,0.01938543,-0.04153645,0.00712914,-0.02611226,0.05181661,-0.05522798,0.0113335,0.02274302,0.0065823,0.03253721,-0.006961,0.07326417,0.05055056,-0.00101604],"last_embed":{"hash":"25f686e0e8561c59f47e2e1f8453d647e8e2689bc8133319027df2f81c06fef6","tokens":83}}},"text":null,"length":0,"last_read":{"hash":"25f686e0e8561c59f47e2e1f8453d647e8e2689bc8133319027df2f81c06fef6","at":1743662838013},"key":"Clean Code notes.md#Comments rules","lines":[48,58],"size":290,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Source code structure": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04408348,-0.05764258,0.00615793,-0.09514331,-0.0141328,0.02818718,-0.02786046,0.03985972,0.06843889,-0.01201005,-0.0265706,0.02112054,0.03630258,0.06575073,-0.01424634,-0.01978142,-0.0453951,0.11472666,-0.09750801,-0.00877558,0.11869393,-0.01668392,0.00756615,0.01813358,0.02042156,0.10077862,-0.05841407,-0.01060349,-0.01400063,-0.2142693,0.00613333,0.05430083,0.06124976,0.00644684,-0.02461358,-0.00815038,-0.0496197,0.02902212,-0.10180432,0.04392149,0.01089876,0.01182485,-0.01137526,-0.03924074,-0.02934937,-0.08786707,-0.0470713,-0.05259788,0.00331298,-0.00765545,-0.00634459,-0.08973201,-0.06747083,0.05770934,0.01836602,0.05823831,0.07808115,0.02413138,0.00741907,0.03101659,0.07565086,-0.01638717,-0.16380826,0.10180193,0.07720821,0.03548424,-0.02712578,-0.01770822,0.00927946,0.09781396,-0.05671849,-0.01549717,-0.0217269,0.0616215,0.0146885,-0.06884664,0.03345738,-0.04642111,-0.0437014,-0.06604164,-0.02047685,-0.00814999,-0.02663433,0.00909414,-0.04485806,0.01431608,-0.0152642,-0.01860983,0.02455815,-0.03518074,0.02218754,-0.09906835,0.04064648,0.04850986,-0.03897074,-0.00545207,0.01727415,-0.02154665,-0.08038016,0.12486923,-0.05889991,0.00792164,-0.00790755,-0.00334725,0.0154314,0.02870596,0.04772361,0.01418691,-0.00827007,-0.03126538,-0.01424564,-0.05402975,-0.00094703,-0.03211603,0.03489774,0.01648213,0.04460199,0.00771872,-0.04275174,-0.04668052,0.02467091,-0.01892483,-0.00059205,0.01797431,0.0185808,0.00491806,-0.00922984,0.05813584,0.01982055,0.02612595,0.06406045,0.04545829,0.0040372,-0.00221796,-0.06500835,0.00067569,0.0394126,0.03875188,0.05247351,-0.06987887,-0.01202992,-0.00717548,0.04145973,-0.05362761,0.0145556,0.13202137,-0.11631124,0.01188568,0.01558392,-0.008187,-0.00493066,0.00143705,0.00096824,-0.05194641,-0.00767172,0.03982763,0.02334162,0.00021358,-0.05919427,-0.00332327,-0.00703649,-0.00981179,-0.09228507,0.10317379,-0.03880748,-0.01952374,-0.07060157,0.04580284,0.0354187,-0.02259555,0.004484,-0.01444113,0.02969725,-0.02450436,0.08991691,-0.0042069,-0.04337394,0.01728981,0.05745611,0.06544095,0.05090714,-0.06050654,-0.06859408,0.04095396,0.05539342,-0.02450837,-0.05463025,-0.01891054,0.03085872,0.04765458,-0.08524221,-0.04970717,-0.04842482,-0.01985323,-0.0578464,-0.02303862,-0.01589921,0.00283847,0.01433941,0.01124729,0.07309068,0.04998012,0.0128891,0.03585491,-0.05537828,0.0327308,0.06993789,-0.08440687,0.05141491,0.04160774,-0.06041831,0.00479214,0.02978966,-0.01284252,0.02369432,-0.0288427,0.03527332,0.04630357,0.04264298,0.03368168,0.01720804,-0.08863555,-0.03639377,-0.21457668,0.01147373,0.01508064,-0.02796398,-0.00546789,-0.03060034,0.05782502,-0.0001916,-0.04134605,-0.03184221,0.06545573,-0.003052,-0.05496769,-0.02728762,-0.05335666,0.05199588,0.02630811,-0.01184673,-0.01647007,-0.02167668,0.00225419,0.02835939,-0.0513632,-0.03365369,0.09353159,0.01398546,0.11601429,-0.06146242,0.08594812,0.03961039,0.07473142,-0.03227305,0.03819748,-0.07019274,0.04847221,0.04400249,-0.02263054,-0.05130632,0.00414188,-0.0556444,0.00852921,0.02821714,-0.02953249,0.00073102,-0.01974043,-0.03527109,-0.06300673,-0.10131292,0.00748203,0.02361271,0.05397053,-0.0677072,0.05420477,0.08067638,0.03922192,0.00431724,-0.05161074,-0.00916432,-0.01927599,0.01787789,-0.02671541,-0.02014583,-0.00087855,-0.01605335,-0.01528384,0.03132504,0.03647756,0.03451541,0.05131517,0.03421643,-0.04153403,0.06886508,-0.04651706,-0.05141842,-0.01181197,-0.01168875,-0.04328543,0.06528763,-0.00732325,-0.01867322,0.0641053,0.02348745,0.02077367,0.00304478,0.05806397,-0.00281392,0.02390206,-0.006774,0.07836733,-0.0122869,-0.0372721,0.01595395,-0.03496225,-0.00829257,0.06145001,-0.02395017,-0.25534803,0.00908384,-0.01108706,0.00047659,-0.03104669,0.00409059,0.08516375,-0.09145147,-0.06364824,0.00427372,-0.00060376,0.0314504,0.00570561,-0.06931108,0.03149898,0.02748304,0.04206193,-0.06674116,0.0826481,-0.02435205,0.0230727,-0.00623022,0.20292322,-0.01678844,0.04651529,0.04796866,0.01304029,0.0313467,0.04390678,0.03009007,0.05573382,0.038307,0.14353646,-0.02566347,-0.02891441,0.00164031,-0.00309848,0.01680445,0.07125178,-0.03426685,0.00625541,-0.01202788,-0.03611592,-0.01867843,0.08987232,-0.02831977,-0.01326261,-0.03536917,0.02224629,-0.02433179,-0.03583607,0.06415619,-0.00624572,-0.00984633,0.01746021,0.0171742,-0.03915969,0.01365761,-0.03178734,0.00669797,0.02343671,-0.0456647,0.04730473,0.05034508,0.00778987],"last_embed":{"hash":"c82f9834b0712d239144a3e21a2721a38af48dad66b5936031ec67b933afe60a","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"c82f9834b0712d239144a3e21a2721a38af48dad66b5936031ec67b933afe60a","at":1743662838024},"key":"Clean Code notes.md#Source code structure","lines":[59,71],"size":438,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Objects and data structures": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01339162,-0.03069816,-0.01263336,-0.0548794,0.03557983,0.00676564,-0.02091682,0.06160341,0.03719433,0.01370525,0.00128624,0.02316639,0.02733985,0.06378804,0.03278842,0.01273715,-0.00957707,0.0340573,-0.03225626,0.06146584,0.08016257,-0.02644132,-0.01226346,-0.01604609,0.03130308,0.0752736,-0.03390435,0.01583202,-0.01399434,-0.20533788,-0.02026844,0.00836007,0.00977497,-0.02070489,-0.0403782,-0.04123229,-0.00096509,0.00588708,-0.07135281,0.03419892,-0.0023992,-0.00339682,-0.0112778,-0.01394455,-0.04816222,-0.03429368,-0.03650445,0.01467816,0.04808418,-0.00109938,-0.04648341,-0.011191,-0.02374889,0.00112945,-0.00200053,0.04688375,0.04736953,0.03470108,0.00793115,0.00820359,0.0420876,-0.00945589,-0.13428007,0.09551153,0.06223837,0.07268057,-0.0065801,-0.03716691,0.01820519,0.09968744,-0.01997525,0.03693747,-0.0271627,0.0580685,0.02360347,-0.03237337,-0.04286914,-0.07214345,-0.00505802,-0.01836327,-0.07072833,-0.07956579,-0.01628713,-0.02805251,-0.02910743,-0.00121998,-0.04136346,-0.01324641,0.05870725,0.00249767,0.03361381,-0.05706372,0.05636728,0.05013065,0.02744134,0.02078151,0.02729803,-0.00700467,-0.02715451,0.13907026,-0.01063783,-0.00087429,0.01026727,-0.00035727,-0.01676221,-0.02333787,0.04942054,-0.06274987,-0.03160017,-0.03878019,0.01615921,-0.01336521,-0.04399668,-0.07303692,-0.00255776,-0.03607614,-0.02617417,-0.01140051,-0.00482326,-0.03864878,0.01473784,-0.04193146,0.04559747,0.04265914,0.05261661,-0.04136482,0.00259949,0.03907613,0.05133499,0.06551962,0.05875638,0.01910694,-0.03212523,-0.07264278,-0.04053857,-0.01378929,0.02028053,0.08428684,0.04800798,-0.07711423,-0.04813706,-0.04552044,0.06141454,-0.0660448,-0.06044831,0.1332657,-0.06331802,0.0278223,-0.0299168,-0.01338191,0.04509132,0.09002763,0.00171285,-0.03086812,-0.00141778,0.04817399,0.02137526,0.00329092,-0.07262383,-0.0256452,-0.04517763,0.05302942,-0.02565152,0.14103608,-0.02766633,-0.04020915,-0.01707679,-0.0031799,0.03746089,-0.04373143,-0.02662438,0.04130583,0.00527692,-0.03980226,0.09794481,0.00923518,-0.02796557,-0.04911426,-0.02040438,0.07321261,0.00020215,0.00870837,-0.056247,0.01152603,0.06943169,-0.03535756,-0.01752708,-0.00304062,-0.00501394,0.0340731,-0.12676157,0.00009055,-0.036041,0.00320461,-0.08441108,-0.06302636,0.02153002,-0.01491548,0.01027837,0.02819128,0.03741113,0.06258107,0.03085566,0.07051115,-0.04934701,0.02219357,0.07645417,-0.07362724,0.06853432,0.02532596,-0.05527837,-0.01246019,0.0288014,0.03042076,-0.00874851,-0.02093377,-0.00155933,0.07787728,0.00908023,-0.00378326,-0.04564547,-0.04279389,-0.02800613,-0.19933067,0.01755327,0.0309431,-0.02220278,0.02223194,-0.05956762,0.0413431,-0.02115873,-0.06255484,0.05389844,0.02552775,-0.02348069,-0.09129214,-0.01963052,-0.0755064,0.03066675,-0.0032367,-0.02972596,-0.04040083,0.00770856,-0.01035404,0.02789346,0.01073653,-0.0526452,0.02351057,-0.02518663,0.12712291,-0.0941462,0.11352854,0.05570886,0.07307464,0.00568835,0.00457016,-0.05112375,0.0363403,-0.02641919,-0.03391724,-0.06758507,-0.01434654,-0.04527194,0.02791876,0.01254913,-0.00465815,-0.05632979,-0.03299727,-0.07625667,-0.05886241,-0.03819202,-0.03163832,0.02944528,0.03839087,-0.03927875,0.05897241,0.09585814,0.00440561,-0.03833539,-0.05237584,-0.02090693,-0.02502872,0.06297647,-0.00558896,-0.01365276,-0.04946722,-0.05465875,0.03851725,-0.0177624,0.01818999,0.00195044,0.06306024,-0.03262895,0.02592495,0.1227994,0.00341563,-0.08399398,-0.02633417,0.03488103,-0.05089849,0.05973851,0.01239972,-0.01381297,0.01697334,0.03837388,0.01914948,0.03478964,0.03606283,-0.01324164,0.10278396,-0.02315076,-0.00386964,-0.02066779,-0.01315063,0.01767247,0.01081114,0.00760108,0.07226853,-0.02388028,-0.27730176,-0.04698212,0.0169087,-0.00498388,0.02773271,0.00554763,0.04701425,-0.06801233,-0.02913845,0.01645213,0.00690253,-0.01186039,0.05101254,-0.01512686,0.050126,0.03784593,0.06808753,-0.01643183,0.03597047,-0.02917369,0.07494824,-0.00942587,0.24056031,-0.01186367,0.0532128,0.03551479,-0.0391035,0.01631261,0.00080221,0.06809351,0.03558242,0.04379926,0.14169857,-0.05443615,-0.03446173,-0.01142479,0.00872039,0.00419956,0.03948124,0.01175905,0.0309708,-0.02087315,-0.08666552,0.0098948,0.09264299,0.00675781,-0.04766964,-0.08186433,-0.00108031,-0.03709767,-0.04345878,0.03618972,-0.0099116,-0.02907547,0.0342536,0.05069829,-0.03387372,0.01543624,-0.04521886,0.01271714,0.08436562,-0.01316058,0.0069983,0.07767046,-0.00865573],"last_embed":{"hash":"fa53e489e6d09feb0e70a1839976022069ad1f02aa9ce951e55a09149b2b8c5a","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"fa53e489e6d09feb0e70a1839976022069ad1f02aa9ce951e55a09149b2b8c5a","at":1743662838035},"key":"Clean Code notes.md#Objects and data structures","lines":[72,83],"size":420,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Clean Code notes.md#Code smells": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06870762,-0.0730941,0.02752893,-0.02361419,-0.00778575,0.01825825,-0.00457894,-0.03230897,-0.02660942,-0.04188264,-0.00620878,0.0016278,0.04455703,-0.00030997,0.0064454,0.02502302,-0.00272238,0.04168313,-0.06424142,0.02461641,0.1041305,-0.00500754,0.03214956,-0.00664822,0.00665422,0.11515796,-0.01162735,-0.01610119,-0.01692246,-0.25163865,0.01678626,0.09078825,0.06036653,0.00215533,0.03435756,0.04104713,-0.09027164,0.04929883,-0.08828151,0.07627446,-0.0284329,0.0429016,0.00837537,-0.08005568,-0.03294005,-0.08324799,-0.00576357,-0.01436987,-0.01067057,-0.0258223,-0.04880875,-0.05873412,0.00169549,0.04690639,0.02780849,0.01444431,0.02285384,0.06570023,-0.01893802,0.02026059,0.04086633,0.00119141,-0.14021288,0.09639251,0.11027014,0.05787524,-0.02219696,-0.04541712,0.02668491,0.0962924,-0.0612063,0.03716187,-0.05793374,0.07657468,0.05619319,-0.01644977,0.00366679,-0.02235025,-0.00078877,0.00884836,-0.0504907,-0.06583031,-0.04170977,0.00231882,-0.01658625,-0.01418792,0.00373932,-0.01631843,0.07078647,-0.01752451,-0.00840357,-0.04571773,0.08313502,0.03048853,0.0008932,-0.01336833,0.06065159,0.07690841,-0.08142113,0.12657435,-0.0614158,0.01712509,-0.01606446,-0.00477443,0.06962925,0.0159449,0.0592891,-0.04178619,-0.02470757,-0.00436912,0.00659812,0.021325,0.04689658,0.00259602,-0.00169692,0.03717294,-0.00737302,0.01338782,-0.01721132,-0.02045846,-0.00230949,0.00345967,0.04984133,0.01446574,0.02717264,0.01983742,-0.01636791,0.1018979,0.01985647,0.01518791,0.02073461,0.03057154,-0.00752155,-0.00706749,-0.01001749,-0.00946518,-0.07287131,0.05881933,0.00948735,0.04015487,-0.05367483,-0.01662286,0.04861684,-0.07402721,-0.07663215,0.08822899,-0.06682609,0.03994277,-0.0583627,0.01323307,0.01545785,0.01908441,-0.02691357,-0.04725133,0.03332338,-0.01973883,0.04947734,0.00012498,-0.04085021,0.00948567,0.046861,-0.00501175,-0.03223946,0.08530664,-0.00145455,-0.02348995,-0.00070558,0.02244467,0.03732346,0.00685136,0.01063763,0.00995577,0.04239001,-0.02814121,0.05125502,-0.04310555,-0.02466951,-0.03306083,0.09828503,0.03606794,0.03629597,-0.00957679,-0.04988623,0.05547092,0.08104251,-0.06861957,-0.00095307,-0.0402165,0.04601254,0.0560949,-0.03330886,-0.01456441,-0.02889816,0.02189036,-0.04294959,-0.01629544,-0.01631537,0.00097255,-0.03383077,-0.03772337,-0.00087712,0.03527332,0.05558825,0.00033218,0.00010623,0.00936989,0.00630062,-0.05476644,0.03671236,0.00238487,-0.07772496,0.0343169,-0.01307358,0.03923056,-0.00892937,-0.02546887,-0.03465074,0.04875511,0.02031709,0.04873724,0.01534502,-0.03074375,-0.03279255,-0.21881571,0.03847765,0.018864,-0.01392969,0.02956786,-0.08124439,0.01468826,-0.03056516,-0.00986721,0.00872998,0.06099313,-0.01458581,-0.01381809,-0.02557917,-0.00429297,-0.00078877,0.02000149,0.02431052,-0.05373755,-0.02377254,0.00138777,0.00886996,-0.01327964,-0.05504401,0.01130178,-0.01365221,0.15479453,-0.07902282,0.06310291,0.09038185,0.00149505,-0.02194563,0.00303539,-0.16634513,0.05729198,0.08618253,0.01494178,-0.02234519,0.03348194,-0.06568838,-0.00717797,-0.00732129,-0.03336531,-0.04348338,-0.02353425,0.00617309,-0.10422921,-0.10125197,-0.06920577,0.03286978,0.02518927,0.03398056,0.0009543,0.09269412,0.00921084,-0.04249837,-0.03687729,-0.02633283,0.03384047,-0.00408416,0.0369302,-0.02702779,0.06599213,-0.07621673,0.03704694,0.04450272,0.01772495,-0.03706123,0.02162649,-0.00622451,0.01665018,0.16176763,-0.01601802,-0.14606848,-0.04137804,-0.01887601,-0.07701728,0.0262085,-0.02207376,-0.03457329,0.01158855,-0.03076795,0.04324148,0.00707282,0.00015284,-0.00829858,-0.01785178,-0.01946899,0.01202564,0.02678938,0.04837329,0.01719018,-0.01396668,-0.04724202,0.05978739,-0.00348475,-0.21494094,-0.02137415,0.00683353,0.0309733,-0.06228407,0.06397422,0.02257063,-0.02983817,-0.00958251,0.00307894,0.01003504,0.01642074,0.02373548,-0.05089257,0.05684145,0.00016086,0.06412838,-0.03061774,0.04882678,-0.09157846,-0.03824026,0.00902535,0.21877261,-0.00370178,0.0024979,0.01314982,-0.00086795,0.0723395,0.04053015,0.01022388,0.05483999,0.01358004,0.08478627,0.02418518,-0.042031,-0.04252205,-0.00495737,-0.06050061,0.00965006,-0.0106545,-0.01230846,-0.00984574,-0.05203915,-0.07391456,0.11718852,-0.00249134,-0.06304325,-0.05587981,-0.022398,-0.02997982,-0.03280757,-0.02356906,-0.01419365,0.00754503,0.00848814,0.03402311,-0.0515679,0.00189789,-0.03890977,0.00913121,0.06376456,0.02132824,0.04932995,0.03037269,-0.02927462],"last_embed":{"hash":"3460204038e332abf4e8ec78a52282c1172cb9521e39a2913b1dc50bddfffe7b","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"3460204038e332abf4e8ec78a52282c1172cb9521e39a2913b1dc50bddfffe7b","at":1743662838046},"key":"Clean Code notes.md#Code smells","lines":[92,102],"size":449,"outlinks":[{"title":"Design Patterns and Coding Styles","target":"Design Patterns and Coding Styles","line":11}],"class_name":"SmartBlock"},
