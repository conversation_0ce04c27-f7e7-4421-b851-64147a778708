
"smart_sources:Compiler.md": {"path":"Compiler.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06573983,-0.06239465,0.00641498,-0.03086473,0.04534037,-0.07951818,-0.12357841,0.01477278,0.02084365,-0.03902795,0.01466328,-0.01297861,0.00868844,0.01079884,0.05079137,-0.01619002,-0.0312317,0.070643,-0.02466048,-0.0056091,0.06110844,-0.01938681,-0.0102049,-0.00839143,0.02448354,0.06373679,-0.02675401,-0.00859825,-0.05186977,-0.16062592,-0.00185588,-0.01775838,0.00639225,0.02833906,0.03013705,0.01673519,-0.04101738,-0.01471611,-0.06888737,0.05362497,0.0381196,-0.02804346,-0.02276617,-0.05065242,-0.06207397,-0.09608017,0.00220147,-0.0112585,-0.00727408,-0.02326942,-0.02753686,0.00132627,-0.03389779,0.02548771,0.01497809,0.0501864,0.06571748,0.09176318,-0.00010895,-0.01081274,0.0517016,-0.01848598,-0.23599188,0.04577684,-0.00217187,-0.00460616,-0.01272485,0.01257308,0.0402152,0.07486551,-0.04442242,-0.00453271,-0.05381355,0.11390217,0.02218719,-0.01463139,0.01932959,-0.00431565,0.01599724,0.00632671,0.0339735,-0.04062545,-0.01269782,0.01124484,0.00092304,0.02358377,0.00581096,0.04809408,0.14032269,0.01560818,-0.03860936,-0.12944528,0.04024734,0.01575072,-0.05080273,0.00820613,0.08629943,-0.03840546,-0.04319901,0.11585817,-0.02586558,0.01188432,0.02519227,-0.02840729,0.05302326,0.03097209,-0.02787726,-0.02672444,-0.0159829,-0.07064822,-0.04249413,0.03108536,0.0612038,-0.04814356,-0.00955624,-0.00554179,-0.03378531,-0.01598884,-0.04321015,0.03458722,0.03489392,-0.04053568,-0.00597306,-0.02064437,0.03198292,-0.02968221,0.00448147,0.06159614,0.05083326,0.07537985,0.02429392,0.0548845,-0.03724305,-0.05651724,0.00850173,-0.04027595,0.04240331,-0.0200195,0.04519248,0.01559948,-0.06051311,-0.03456355,-0.02803091,-0.04312084,-0.0372995,0.09872932,-0.02344563,0.00015469,-0.03917949,-0.0307015,-0.02579291,0.08453973,-0.06352863,-0.03331768,0.06400245,0.03369372,0.02754992,0.02545562,-0.05743775,0.04388577,0.00151233,-0.02343713,-0.02863275,0.13344374,-0.03478678,-0.05744377,-0.06198902,0.03322462,0.0899278,-0.02074701,-0.03452109,0.01034004,-0.00266566,-0.03447429,0.0724195,0.00304937,-0.05296364,0.0420589,0.0035133,0.05754264,0.04244054,-0.00372707,-0.07236281,0.06956591,-0.00073771,-0.0564779,-0.01922631,-0.0566446,-0.03432935,0.01587146,-0.07446814,0.01699369,-0.03778815,-0.05846574,0.0031405,-0.01133004,0.03095036,-0.00963358,0.09156772,-0.01717986,0.10710304,0.08602699,-0.01444342,0.05231835,-0.11631242,-0.01654565,-0.02123627,-0.04871348,-0.00054121,0.04370348,-0.03536476,0.03095285,0.06626765,-0.0038946,-0.04012958,-0.02171672,-0.01199912,0.07703194,0.05341332,0.02815325,-0.02260034,-0.0109729,-0.06439876,-0.17358485,0.00168746,-0.0020011,-0.06643301,-0.02955863,-0.0687547,0.01868411,0.05634135,-0.07855686,0.04678014,0.07420508,-0.00450377,-0.00334637,0.00947722,-0.04400466,0.02387091,0.04519476,-0.04014474,-0.02197968,0.02830206,-0.01021457,-0.00685811,0.017791,-0.05140406,0.03474357,0.01308553,0.14272998,0.07675613,0.04896649,-0.0149982,0.07469942,0.02399018,0.02779296,-0.13128716,0.02336135,-0.01043561,0.02045582,0.01870936,0.02076777,0.02140289,0.00392349,0.03682117,-0.01467039,-0.06842364,-0.02869592,-0.05267993,-0.03661235,-0.07878052,-0.00662433,0.04878068,-0.04733767,-0.05639632,0.01929206,0.04628294,0.04942811,0.0179173,-0.06008802,-0.02464042,0.06809043,0.03999731,0.0052941,-0.01017647,-0.02582474,-0.04914231,0.03788412,-0.01997123,0.05695552,0.00739672,0.07504709,-0.06051533,-0.03180078,0.06321925,-0.00620248,0.0245118,0.05151328,0.04575395,-0.04973155,0.00179619,-0.01535736,0.00551974,0.04205414,0.00657316,0.01805209,-0.00366434,0.04558959,-0.01284518,-0.04065056,0.01146139,0.05708259,-0.02522847,-0.02915074,-0.03165886,-0.05012404,0.00368606,0.09502451,0.02353973,-0.23835261,-0.00583023,-0.00441621,0.01612141,-0.03377496,0.0626119,0.02416,-0.06477591,-0.05392765,0.00677718,0.0166499,-0.0314999,0.03329454,0.03893978,0.04254317,-0.03101036,0.08505315,-0.00324764,0.05522013,-0.05317866,0.03177499,-0.01843308,0.19810638,0.00058802,-0.01143597,0.0407728,-0.06501056,0.00870141,0.0596165,0.0586402,-0.05673171,0.02520732,0.14651164,-0.03050353,-0.02673181,0.01926833,-0.01184503,0.02098213,0.04892283,0.01457327,-0.0055909,-0.00630017,-0.01260248,0.00011974,0.0996531,0.00760449,-0.03731379,-0.11181859,0.01709467,0.01738715,-0.07641744,-0.03517023,-0.02427171,-0.00401761,-0.00342128,0.00562856,0.00503897,0.00337166,-0.02953277,0.02414586,0.018233,-0.04172239,0.09097926,0.08201199,-0.00325793],"last_embed":{"hash":"480a6d0b22d1cd5621ea20b0951d23376b71e85f41d1104fd10a62a9ce157881","tokens":64}}},"last_read":{"hash":"480a6d0b22d1cd5621ea20b0951d23376b71e85f41d1104fd10a62a9ce157881","at":1743662852105},"class_name":"SmartSource","outlinks":[{"title":"Backend - Back-end","target":"Backend - Back-end","line":3},{"title":"Microservices","target":"Microservices","line":4},{"title":"PHP","target":"PHP","line":5}],"metadata":{"relates":["[[Backend - Back-end]]","[[Microservices]]","[[PHP]]"]},"blocks":{"#---frontmatter---":[1,6],"#":[7,8],"##{1}":[7,8]},"last_import":{"mtime":1737168401130,"size":123,"at":1743662830217,"hash":"480a6d0b22d1cd5621ea20b0951d23376b71e85f41d1104fd10a62a9ce157881"}},