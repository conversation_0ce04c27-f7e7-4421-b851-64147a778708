
"smart_sources:Microservices.md": {"path":"Microservices.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"5fb9ae15748b4a57618f8a0479ff3eb591c5fa508a0577739510801f69c2c225","at":1743662830257},"class_name":"SmartSource","outlinks":[{"title":"Backend - Back-end","target":"Backend - Back-end","line":3},{"title":"Cloud - SaaS","target":"Cloud - SaaS","line":4},{"title":"Machine learning - Deep Learning - AI - ML - DL","target":"Machine learning - Deep Learning - AI - ML - DL","line":5},{"title":"Top 10 câu hỏi phỏng vấn System Design và Microservices","target":"Top 10 câu hỏi phỏng vấn System Design và Microservices","line":15},{"title":"Cloud - SaaS","target":"Cloud - SaaS","line":16},{"title":"MonitoringAlerting dùng PrometheusAlertmanager, Logging thì ELK Stack, con Sentry cho Tracing.png","target":"MonitoringAlerting dùng PrometheusAlertmanager, Logging thì ELK Stack, con Sentry cho Tracing.png","line":21},{"title":"ElasticSearch","target":"ElasticSearch","line":102},{"title":"Typesense","target":"https://github.com/typesense/typesense","line":103},{"title":"Datamuse API","target":"https://www.datamuse.com","line":104},{"title":"Orama","target":"https://github.com/oramasearch/orama","line":105},{"title":"Keycloak","target":"https://github.com/keycloak/keycloak","line":108},{"title":"Giải pháp phân quyền sử dụng Keycloak","target":"https://viblo.asia/p/giai-phap-cho-bai-toan-phan-quyen-su-dung-keycloak-Ny0VG717VPA","line":109},{"title":"Supertokens","target":"https://github.com/supertokens/supertokens-core","line":110},{"title":"Ory","target":"https://github.com/orgs/ory/repositories?q=&type=all&language=&sort=stargazers","line":111},{"title":"Dex","target":"https://github.com/dexidp/dex","line":112},{"title":"Better-auth","target":"https://github.com/better-auth/better-auth","line":113},{"title":"Supabase-auth","target":"https://github.com/supabase/auth","line":114},{"title":"Dragonfly","target":"https://www.dragonflydb.io/redis-alternative","line":120},{"title":"ReadySet","target":"https://github.com/readysettech/readyset","line":123},{"title":"Soketi","target":"https://github.com/soketi/soketi","line":125},{"title":"Hướng dẫn sử dụng HAProxy cho load balancing ứng dụng","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":139},{"title":"Pingora","target":"https://github.com/cloudflare/pingora","line":140},{"title":"Yao","target":"https://github.com/YaoApp/yao","line":144},{"title":"Strapi","target":"https://github.com/strapi/strapi","line":145},{"title":"Imageproxy","target":"https://github.com/willnorris/imageproxy","line":147},{"title":"Multiwoven","target":"https://github.com/Multiwoven/multiwoven","line":149},{"title":"Minio","target":"https://github.com/minio/minio","line":151},{"title":"Cloudreve","target":"https://github.com/cloudreve/Cloudreve","line":152},{"title":"Signoz","target":"https://github.com/SigNoz/signoz","line":154},{"title":"Coroot","target":"https://github.com/coroot/coroot","line":157},{"title":"YOURLS","target":"https://github.com/YOURLS/YOURLS","line":159},{"title":"Polr","target":"https://github.com/cydrobolt/polr","line":160},{"title":"Dokploy","target":"https://github.com/Dokploy/dokploy","line":163},{"title":"Sidekick","target":"https://github.com/mightymoud/sidekick","line":164},{"title":"Harness","target":"https://github.com/harness/harness","line":165},{"title":"Daytona","target":"https://github.com/daytonaio/daytona","line":167},{"title":"Devcontainers","target":"https://code.visualstudio.com/docs/devcontainers/containers","line":168},{"title":"Lapdev","target":"https://github.com/lapce/lapdev","line":169},{"title":"n8n","target":"https://github.com/n8n-io/n8n","line":171},{"title":"Openreplay","target":"https://github.com/openreplay/openreplay","line":174},{"title":"Sonarqube","target":"https://github.com/SonarSource/sonarqube","line":175},{"title":"Encore","target":"https://github.com/encoredev/encore","line":187},{"title":"Novu","target":"https://github.com/novuhq/novu","line":188},{"title":"Apache Dubbo","target":"https://github.com/apache/dubbo","line":189},{"title":"LiveKit","target":"https://github.com/livekit/livekit","line":192},{"title":"Jitsi Docker","target":"https://github.com/jitsi/docker-jitsi-meet","line":193},{"title":"Digitalhippo","target":"https://github.com/joschan21/digitalhippo","line":195},{"title":"Vitess","target":"https://vitess.io","line":210},{"title":"Spinnaker","target":"https://spinnaker.io","line":223},{"title":"trunk-based development","target":"https://trunkbaseddevelopment.com/","line":226},{"title":"configure","target":"https://www.flipt.io/docs/configuration/overview","line":231},{"title":"OpenTelemetry","target":"https://opentelemetry.io/","line":234},{"title":"Prometheus","target":"https://prometheus.io/","line":234},{"title":"Filesystem, Object, Git, and OCI declarative storage backends","target":"https://www.flipt.io/docs/configuration/storage#declarative","line":235},{"title":"Exactly One Message with Kafka (EOS)","target":"https://viblo.asia/p/exactly-one-message-voi-kafka-eos-0gdJz6OEJz5","line":243},{"title":"Istio","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":246},{"title":"Novu","target":"https://github.com/novuhq/novu","line":250},{"title":"Self-hosted ngrok","target":"https://github.com/pgrok/pgrok","line":322},{"title":"Listmonk","target":"https://github.com/knadh/listmonk","line":323},{"title":"Ghostfolio","target":"https://github.com/ghostfolio/ghostfolio","line":324},{"title":"Teller","target":"https://github.com/tellerops/teller","line":325},{"title":"Canvas","target":"https://github.com/austintoddj/canvas","line":326},{"title":"Rocket.Chat","target":"https://github.com/RocketChat/Rocket.Chat","line":327},{"title":"BookStack","target":"https://github.com/BookStackApp/BookStack","line":328},{"title":"Hook0","target":"https://github.com/hook0/hook0","line":329},{"title":"Chatwoot","target":"https://github.com/chatwoot/chatwoot","line":330},{"title":"Untitled 10.png","target":"Untitled 10.png","line":407},{"title":"Untitled 1 5.png","target":"Untitled 1 5.png","line":409},{"title":"Untitled 2 2.png","target":"Untitled 2 2.png","line":411},{"title":"Untitled 3 2.png","target":"Untitled 3 2.png","line":413},{"title":"Java Microservices","target":"Java Microservices","line":418},{"title":"Solutions & System Designs & Design Patterns","target":"Solutions & System Designs & Design Patterns","line":418},{"title":"Operations Component.png","target":"Operations Component.png","line":424}],"metadata":{"relates":["[[Backend - Back-end]]","[[Cloud - SaaS]]","[[Machine learning - Deep Learning - AI - ML - DL]]"]},"blocks":{"#---frontmatter---":[1,6],"#1. Resources":[7,98],"#1. Resources#{1}":[9,9],"#1. Resources#{2}":[10,10],"#1. Resources#{3}":[11,11],"#1. Resources#{4}":[12,12],"#1. Resources#{5}":[13,13],"#1. Resources#{6}":[14,14],"#1. Resources#{7}":[15,15],"#1. Resources#{8}":[16,16],"#1. Resources#{9}":[17,17],"#1. Resources#{10}":[18,18],"#1. Resources#{11}":[19,20],"#1. Resources#{12}":[21,22],"#1. Resources#{13}":[23,23],"#1. Resources#{14}":[24,24],"#1. Resources#{15}":[25,26],"#1. Resources#{16}":[27,38],"#1. Resources#{17}":[39,40],"#1. Resources#{18}":[41,42],"#1. Resources#{19}":[43,47],"#1. Resources#{20}":[48,49],"#1. Resources#{21}":[50,54],"#1. Resources#{22}":[55,55],"#1. Resources#{23}":[56,56],"#1. Resources#{24}":[57,57],"#1. Resources#{25}":[58,61],"#1. Resources#1.1. Distributed transaction":[62,81],"#1. Resources#1.1. Distributed transaction#{1}":[64,64],"#1. Resources#1.1. Distributed transaction#{2}":[65,65],"#1. Resources#1.1. Distributed transaction#{3}":[66,66],"#1. Resources#1.1. Distributed transaction#{4}":[67,67],"#1. Resources#1.1. Distributed transaction#{5}":[68,68],"#1. Resources#1.1. Distributed transaction#{6}":[69,69],"#1. Resources#1.1. Distributed transaction#{7}":[70,70],"#1. Resources#1.1. Distributed transaction#{8}":[71,71],"#1. Resources#1.1. Distributed transaction#{9}":[72,72],"#1. Resources#1.1. Distributed transaction#{10}":[73,73],"#1. Resources#1.1. Distributed transaction#{11}":[74,77],"#1. Resources#1.1. Distributed transaction#{12}":[78,78],"#1. Resources#1.1. Distributed transaction#{13}":[79,79],"#1. Resources#1.1. Distributed transaction#{14}":[80,81],"#1. Resources#1.2. Outbox pattern":[82,98],"#1. Resources#1.2. Outbox pattern#{1}":[84,85],"#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG":[86,90],"#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{1}":[88,88],"#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{2}":[89,90],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern":[91,98],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{1}":[93,93],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{2}":[94,94],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{3}":[95,96],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{4}":[97,98],"#2. Các services":[99,334],"#2. Các services#{1}":[101,106],"#2. Các services#{2}":[107,117],"#2. Các services#{3}":[118,121],"#2. Các services#{4}":[122,123],"#2. Các services#{5}":[124,125],"#2. Các services#{6}":[126,132],"#2. Các services#{7}":[133,140],"#2. Các services#{8}":[141,142],"#2. Các services#{9}":[143,145],"#2. Các services#{10}":[146,147],"#2. Các services#{11}":[148,149],"#2. Các services#{12}":[150,152],"#2. Các services#{13}":[153,157],"#2. Các services#{14}":[158,161],"#2. Các services#{15}":[162,165],"#2. Các services#{16}":[166,169],"#2. Các services#{17}":[170,172],"#2. Các services#{18}":[173,175],"#2. Các services#{19}":[176,190],"#2. Các services#{20}":[191,193],"#2. Các services#{21}":[194,199],"#2. Các services#{22}":[200,203],"#2. Các services#{23}":[204,205],"#2. Các services#{24}":[206,208],"#2. Các services#{25}":[209,211],"#2. Các services#{26}":[212,213],"#2. Các services#{27}":[214,215],"#2. Các services#{28}":[216,217],"#2. Các services#{29}":[218,221],"#2. Các services#{30}":[222,237],"#2. Các services#{31}":[238,240],"#2. Các services#{32}":[241,244],"#2. Các services#{33}":[245,246],"#2. Các services#{34}":[247,248],"#2. Các services#{35}":[249,257],"#2. Các services#{36}":[258,283],"#2. Các services#{37}":[284,286],"#2. Các services#{38}":[287,288],"#2. Các services#{39}":[289,292],"#2. Các services#{40}":[293,294],"#2. Các services#{41}":[295,296],"#2. Các services#{42}":[297,304],"#2. Các services#{43}":[305,317],"#2. Các services#{44}":[318,319],"#2. Các services#{45}":[320,334],"#3. Design patterns":[335,351],"#3. Design patterns#{1}":[337,337],"#3. Design patterns#{2}":[338,338],"#3. Design patterns#{3}":[339,340],"#3. Design patterns#3.1. Resources":[341,344],"#3. Design patterns#3.1. Resources#{1}":[343,344],"#3. Design patterns#3.2. Kafka":[345,351],"#3. Design patterns#3.2. Kafka#{1}":[347,347],"#3. Design patterns#3.2. Kafka#{2}":[348,348],"#3. Design patterns#3.2. Kafka#{3}":[349,349],"#3. Design patterns#3.2. Kafka#{4}":[350,351],"#4. Framework / Libraries":[352,414],"#4. Framework / Libraries#{1}":[354,356],"#4. Framework / Libraries#{2}":[357,358],"#4. Framework / Libraries#{3}":[359,368],"#4. Framework / Libraries#{4}":[369,369],"#4. Framework / Libraries#{5}":[370,370],"#4. Framework / Libraries#{6}":[371,372],"#4. Framework / Libraries#{7}":[373,376],"#4. Framework / Libraries#{8}":[377,377],"#4. Framework / Libraries#{9}":[378,379],"#4. Framework / Libraries#{10}":[380,393],"#4. Framework / Libraries#{11}":[394,394],"#4. Framework / Libraries#{12}":[395,396],"#4. Framework / Libraries#{13}":[397,414],"#5. Frameworks / Libraries":[415,421],"#5. Frameworks / Libraries#{1}":[417,417],"#5. Frameworks / Libraries#{2}":[418,419],"#5. Frameworks / Libraries#{3}":[420,421],"#6. Netflix OSS":[422,429],"#6. Netflix OSS#{1}":[424,425],"#6. Netflix OSS#{2}":[426,426],"#6. Netflix OSS#{3}":[427,427],"#6. Netflix OSS#{4}":[428,429]},"last_import":{"mtime":1742632310073,"size":32423,"at":1743662830263,"hash":"5fb9ae15748b4a57618f8a0479ff3eb591c5fa508a0577739510801f69c2c225"}},
"smart_sources:Microservices.md": {"path":"Microservices.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06733244,-0.03547369,-0.02429283,-0.02710431,0.05535313,-0.05308284,-0.05950233,-0.01319329,0.00263543,0.03234039,0.02254174,-0.09232548,-0.00235809,0.01645325,0.05955156,-0.01947797,0.02848757,0.01983039,-0.05806815,-0.02660909,0.06495373,-0.02726171,-0.04362391,-0.01326143,0.04244512,-0.02551766,-0.00147062,-0.07148338,-0.02953725,-0.23292764,0.03377397,-0.01430742,0.0186617,0.05360739,0.05984667,0.03871651,0.00554812,0.00825518,-0.03009167,0.01292408,0.09091551,-0.03731786,-0.08841717,-0.00478048,-0.02391487,-0.0931189,-0.00403473,-0.01986088,-0.05790304,-0.04533084,-0.03413414,-0.0242262,-0.00357982,0.02401368,0.01471438,0.01934335,0.0898827,0.04629029,0.01166462,-0.033828,0.06053717,0.05048231,-0.23807798,0.10039635,-0.03838463,0.02083142,0.04165189,-0.03405837,0.00269179,0.04218434,-0.04394642,0.03892596,-0.00196392,0.0973365,-0.00156988,0.01709952,0.03542229,-0.05921279,-0.07667735,0.02407414,0.02664674,0.04559756,-0.01179749,-0.00356582,-0.08529625,0.03391879,-0.03718521,0.01942281,0.0865725,-0.02602616,-0.03650232,-0.00921532,0.02101178,0.00741493,-0.00567774,-0.01471901,0.04477488,-0.01935618,-0.04234043,0.10245366,-0.04258651,0.00295701,0.0182677,-0.0424531,0.0587325,0.04889983,-0.0571433,-0.05536009,0.01653043,0.03327348,-0.0497438,-0.00552326,0.01694741,-0.03068331,-0.02890963,0.01331483,0.01380786,0.03903529,0.02258968,-0.00181388,0.00109052,-0.00903698,0.0823184,-0.05893706,0.0519146,-0.0167422,0.04567089,0.05483981,-0.00671577,0.00778399,0.02777913,-0.02287079,-0.05549671,-0.04009396,-0.01146671,-0.02068664,0.03617998,-0.03445531,0.00668114,0.00693271,-0.0858714,-0.02515027,0.01018379,-0.04224746,-0.05755351,0.11572974,-0.01048231,-0.01133499,-0.06277337,-0.08206562,-0.02289328,0.05793842,-0.03600389,-0.06813583,0.03668247,0.01980993,0.05621938,0.07013114,-0.0796634,0.05937002,0.00911466,-0.09247015,-0.05644626,0.1472645,0.02142126,-0.07117163,-0.04661255,-0.02179951,0.02635948,-0.02380432,0.01174825,0.03068907,-0.04447955,-0.01898136,-0.01124732,-0.04001543,-0.02992298,-0.01828234,-0.01520877,0.02140391,-0.03505284,0.00991682,0.0602711,0.03827938,0.06266035,-0.05429623,0.00086332,-0.02114836,-0.00803505,-0.03347462,-0.05767399,0.01563644,0.02611932,-0.04395379,0.07150252,-0.0303498,0.04852393,-0.03571438,0.03952781,-0.04297876,0.01568696,0.03930188,-0.04503631,-0.00413559,-0.03392719,-0.03289498,-0.03845063,0.00937647,0.0233836,0.03449533,-0.01023991,0.00606651,0.02765212,-0.02995972,-0.04178608,-0.01695697,0.04895105,0.06876159,-0.00774974,-0.02096958,0.01929419,-0.00618046,-0.10950882,-0.18711694,0.01126849,0.02589793,-0.06286462,0.01495685,-0.04027823,0.0675972,0.05533486,0.03124421,0.04317514,0.06456608,-0.03228566,0.0339695,0.00999459,0.01610927,0.0861888,0.045789,0.01994148,0.00419223,0.04573185,0.02506226,0.04952379,0.02140768,0.00997024,0.03497,0.06043861,0.12839237,-0.00187138,0.06004297,0.00473507,0.02850805,0.01437177,0.02264096,-0.11708067,0.04683863,-0.01156817,0.03332197,0.01295277,0.0281071,-0.02279994,-0.05063068,0.05797552,-0.01816748,-0.1318164,-0.05755307,-0.01878861,-0.03150004,-0.01886792,-0.09970197,0.01017349,-0.0480627,-0.0326208,0.01206701,0.00448011,0.04622252,0.00853628,-0.02970885,0.02171333,-0.02381045,0.01030775,0.00235862,-0.02097951,-0.01004295,-0.09190518,0.06611148,-0.07979798,0.01334285,0.07376181,0.0079785,-0.02234194,-0.02871548,0.13894828,-0.01773565,0.0413545,0.09318901,0.02235378,0.01733637,-0.04622713,-0.04791562,0.02260519,0.05151499,-0.04362728,0.06355216,0.01781646,0.05630095,0.05206035,0.01682815,0.00326504,0.03617408,-0.03160968,-0.03710496,-0.01978443,-0.08203784,-0.04567171,0.05997959,0.03193126,-0.2033471,0.03508548,-0.00112424,0.04628185,-0.01599218,-0.03043042,-0.00906915,-0.04023857,-0.00919722,0.02622693,0.03199061,0.00845613,0.01735729,0.01761309,0.01229263,0.01295031,0.0637273,-0.02321162,0.09100406,-0.0389035,0.00787211,0.00758527,0.1989615,-0.00980523,-0.01183371,-0.0051988,-0.01582001,-0.02592129,0.01593316,-0.02360833,-0.03056002,0.0463517,0.14158607,-0.0007608,0.03527445,0.01925551,-0.03221704,0.00439616,0.0674725,0.03841526,0.0503441,0.00132136,-0.02188316,-0.01387678,0.10452338,0.01700463,-0.02305986,-0.12368384,-0.00908924,0.07146809,-0.01973137,-0.02100927,-0.0227015,0.0211866,0.03209385,0.04772295,-0.05144226,-0.05066736,-0.0314207,-0.00410611,0.06615501,-0.03007955,0.05700353,0.04291308,-0.02848574],"last_embed":{"hash":"5fb9ae15748b4a57618f8a0479ff3eb591c5fa508a0577739510801f69c2c225","tokens":481}}},"last_read":{"hash":"5fb9ae15748b4a57618f8a0479ff3eb591c5fa508a0577739510801f69c2c225","at":1743662878558},"class_name":"SmartSource","outlinks":[{"title":"Backend - Back-end","target":"Backend - Back-end","line":3},{"title":"Cloud - SaaS","target":"Cloud - SaaS","line":4},{"title":"Machine learning - Deep Learning - AI - ML - DL","target":"Machine learning - Deep Learning - AI - ML - DL","line":5},{"title":"Top 10 câu hỏi phỏng vấn System Design và Microservices","target":"Top 10 câu hỏi phỏng vấn System Design và Microservices","line":15},{"title":"Cloud - SaaS","target":"Cloud - SaaS","line":16},{"title":"MonitoringAlerting dùng PrometheusAlertmanager, Logging thì ELK Stack, con Sentry cho Tracing.png","target":"MonitoringAlerting dùng PrometheusAlertmanager, Logging thì ELK Stack, con Sentry cho Tracing.png","line":21},{"title":"ElasticSearch","target":"ElasticSearch","line":102},{"title":"Typesense","target":"https://github.com/typesense/typesense","line":103},{"title":"Datamuse API","target":"https://www.datamuse.com","line":104},{"title":"Orama","target":"https://github.com/oramasearch/orama","line":105},{"title":"Keycloak","target":"https://github.com/keycloak/keycloak","line":108},{"title":"Giải pháp phân quyền sử dụng Keycloak","target":"https://viblo.asia/p/giai-phap-cho-bai-toan-phan-quyen-su-dung-keycloak-Ny0VG717VPA","line":109},{"title":"Supertokens","target":"https://github.com/supertokens/supertokens-core","line":110},{"title":"Ory","target":"https://github.com/orgs/ory/repositories?q=&type=all&language=&sort=stargazers","line":111},{"title":"Dex","target":"https://github.com/dexidp/dex","line":112},{"title":"Better-auth","target":"https://github.com/better-auth/better-auth","line":113},{"title":"Supabase-auth","target":"https://github.com/supabase/auth","line":114},{"title":"Dragonfly","target":"https://www.dragonflydb.io/redis-alternative","line":120},{"title":"ReadySet","target":"https://github.com/readysettech/readyset","line":123},{"title":"Soketi","target":"https://github.com/soketi/soketi","line":125},{"title":"Hướng dẫn sử dụng HAProxy cho load balancing ứng dụng","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":139},{"title":"Pingora","target":"https://github.com/cloudflare/pingora","line":140},{"title":"Yao","target":"https://github.com/YaoApp/yao","line":144},{"title":"Strapi","target":"https://github.com/strapi/strapi","line":145},{"title":"Imageproxy","target":"https://github.com/willnorris/imageproxy","line":147},{"title":"Multiwoven","target":"https://github.com/Multiwoven/multiwoven","line":149},{"title":"Minio","target":"https://github.com/minio/minio","line":151},{"title":"Cloudreve","target":"https://github.com/cloudreve/Cloudreve","line":152},{"title":"Signoz","target":"https://github.com/SigNoz/signoz","line":154},{"title":"Coroot","target":"https://github.com/coroot/coroot","line":157},{"title":"YOURLS","target":"https://github.com/YOURLS/YOURLS","line":159},{"title":"Polr","target":"https://github.com/cydrobolt/polr","line":160},{"title":"Dokploy","target":"https://github.com/Dokploy/dokploy","line":163},{"title":"Sidekick","target":"https://github.com/mightymoud/sidekick","line":164},{"title":"Harness","target":"https://github.com/harness/harness","line":165},{"title":"Daytona","target":"https://github.com/daytonaio/daytona","line":167},{"title":"Devcontainers","target":"https://code.visualstudio.com/docs/devcontainers/containers","line":168},{"title":"Lapdev","target":"https://github.com/lapce/lapdev","line":169},{"title":"n8n","target":"https://github.com/n8n-io/n8n","line":171},{"title":"Openreplay","target":"https://github.com/openreplay/openreplay","line":174},{"title":"Sonarqube","target":"https://github.com/SonarSource/sonarqube","line":175},{"title":"Encore","target":"https://github.com/encoredev/encore","line":187},{"title":"Novu","target":"https://github.com/novuhq/novu","line":188},{"title":"Apache Dubbo","target":"https://github.com/apache/dubbo","line":189},{"title":"LiveKit","target":"https://github.com/livekit/livekit","line":192},{"title":"Jitsi Docker","target":"https://github.com/jitsi/docker-jitsi-meet","line":193},{"title":"Digitalhippo","target":"https://github.com/joschan21/digitalhippo","line":195},{"title":"Vitess","target":"https://vitess.io","line":210},{"title":"Spinnaker","target":"https://spinnaker.io","line":223},{"title":"trunk-based development","target":"https://trunkbaseddevelopment.com/","line":226},{"title":"configure","target":"https://www.flipt.io/docs/configuration/overview","line":231},{"title":"OpenTelemetry","target":"https://opentelemetry.io/","line":234},{"title":"Prometheus","target":"https://prometheus.io/","line":234},{"title":"Filesystem, Object, Git, and OCI declarative storage backends","target":"https://www.flipt.io/docs/configuration/storage#declarative","line":235},{"title":"Exactly One Message with Kafka (EOS)","target":"https://viblo.asia/p/exactly-one-message-voi-kafka-eos-0gdJz6OEJz5","line":243},{"title":"Istio","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":246},{"title":"Novu","target":"https://github.com/novuhq/novu","line":250},{"title":"Self-hosted ngrok","target":"https://github.com/pgrok/pgrok","line":322},{"title":"Listmonk","target":"https://github.com/knadh/listmonk","line":323},{"title":"Ghostfolio","target":"https://github.com/ghostfolio/ghostfolio","line":324},{"title":"Teller","target":"https://github.com/tellerops/teller","line":325},{"title":"Canvas","target":"https://github.com/austintoddj/canvas","line":326},{"title":"Rocket.Chat","target":"https://github.com/RocketChat/Rocket.Chat","line":327},{"title":"BookStack","target":"https://github.com/BookStackApp/BookStack","line":328},{"title":"Hook0","target":"https://github.com/hook0/hook0","line":329},{"title":"Chatwoot","target":"https://github.com/chatwoot/chatwoot","line":330},{"title":"Untitled 10.png","target":"Untitled 10.png","line":407},{"title":"Untitled 1 5.png","target":"Untitled 1 5.png","line":409},{"title":"Untitled 2 2.png","target":"Untitled 2 2.png","line":411},{"title":"Untitled 3 2.png","target":"Untitled 3 2.png","line":413},{"title":"Java Microservices","target":"Java Microservices","line":418},{"title":"Solutions & System Designs & Design Patterns","target":"Solutions & System Designs & Design Patterns","line":418},{"title":"Operations Component.png","target":"Operations Component.png","line":424}],"metadata":{"relates":["[[Backend - Back-end]]","[[Cloud - SaaS]]","[[Machine learning - Deep Learning - AI - ML - DL]]"]},"blocks":{"#---frontmatter---":[1,6],"#1. Resources":[7,98],"#1. Resources#{1}":[9,9],"#1. Resources#{2}":[10,10],"#1. Resources#{3}":[11,11],"#1. Resources#{4}":[12,12],"#1. Resources#{5}":[13,13],"#1. Resources#{6}":[14,14],"#1. Resources#{7}":[15,15],"#1. Resources#{8}":[16,16],"#1. Resources#{9}":[17,17],"#1. Resources#{10}":[18,18],"#1. Resources#{11}":[19,20],"#1. Resources#{12}":[21,22],"#1. Resources#{13}":[23,23],"#1. Resources#{14}":[24,24],"#1. Resources#{15}":[25,26],"#1. Resources#{16}":[27,38],"#1. Resources#{17}":[39,40],"#1. Resources#{18}":[41,42],"#1. Resources#{19}":[43,47],"#1. Resources#{20}":[48,49],"#1. Resources#{21}":[50,54],"#1. Resources#{22}":[55,55],"#1. Resources#{23}":[56,56],"#1. Resources#{24}":[57,57],"#1. Resources#{25}":[58,61],"#1. Resources#1.1. Distributed transaction":[62,81],"#1. Resources#1.1. Distributed transaction#{1}":[64,64],"#1. Resources#1.1. Distributed transaction#{2}":[65,65],"#1. Resources#1.1. Distributed transaction#{3}":[66,66],"#1. Resources#1.1. Distributed transaction#{4}":[67,67],"#1. Resources#1.1. Distributed transaction#{5}":[68,68],"#1. Resources#1.1. Distributed transaction#{6}":[69,69],"#1. Resources#1.1. Distributed transaction#{7}":[70,70],"#1. Resources#1.1. Distributed transaction#{8}":[71,71],"#1. Resources#1.1. Distributed transaction#{9}":[72,72],"#1. Resources#1.1. Distributed transaction#{10}":[73,73],"#1. Resources#1.1. Distributed transaction#{11}":[74,77],"#1. Resources#1.1. Distributed transaction#{12}":[78,78],"#1. Resources#1.1. Distributed transaction#{13}":[79,79],"#1. Resources#1.1. Distributed transaction#{14}":[80,81],"#1. Resources#1.2. Outbox pattern":[82,98],"#1. Resources#1.2. Outbox pattern#{1}":[84,85],"#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG":[86,90],"#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{1}":[88,88],"#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{2}":[89,90],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern":[91,98],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{1}":[93,93],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{2}":[94,94],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{3}":[95,96],"#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{4}":[97,98],"#2. Các services":[99,334],"#2. Các services#{1}":[101,106],"#2. Các services#{2}":[107,117],"#2. Các services#{3}":[118,121],"#2. Các services#{4}":[122,123],"#2. Các services#{5}":[124,125],"#2. Các services#{6}":[126,132],"#2. Các services#{7}":[133,140],"#2. Các services#{8}":[141,142],"#2. Các services#{9}":[143,145],"#2. Các services#{10}":[146,147],"#2. Các services#{11}":[148,149],"#2. Các services#{12}":[150,152],"#2. Các services#{13}":[153,157],"#2. Các services#{14}":[158,161],"#2. Các services#{15}":[162,165],"#2. Các services#{16}":[166,169],"#2. Các services#{17}":[170,172],"#2. Các services#{18}":[173,175],"#2. Các services#{19}":[176,190],"#2. Các services#{20}":[191,193],"#2. Các services#{21}":[194,199],"#2. Các services#{22}":[200,203],"#2. Các services#{23}":[204,205],"#2. Các services#{24}":[206,208],"#2. Các services#{25}":[209,211],"#2. Các services#{26}":[212,213],"#2. Các services#{27}":[214,215],"#2. Các services#{28}":[216,217],"#2. Các services#{29}":[218,221],"#2. Các services#{30}":[222,237],"#2. Các services#{31}":[238,240],"#2. Các services#{32}":[241,244],"#2. Các services#{33}":[245,246],"#2. Các services#{34}":[247,248],"#2. Các services#{35}":[249,257],"#2. Các services#{36}":[258,283],"#2. Các services#{37}":[284,286],"#2. Các services#{38}":[287,288],"#2. Các services#{39}":[289,292],"#2. Các services#{40}":[293,294],"#2. Các services#{41}":[295,296],"#2. Các services#{42}":[297,304],"#2. Các services#{43}":[305,317],"#2. Các services#{44}":[318,319],"#2. Các services#{45}":[320,334],"#3. Design patterns":[335,351],"#3. Design patterns#{1}":[337,337],"#3. Design patterns#{2}":[338,338],"#3. Design patterns#{3}":[339,340],"#3. Design patterns#3.1. Resources":[341,344],"#3. Design patterns#3.1. Resources#{1}":[343,344],"#3. Design patterns#3.2. Kafka":[345,351],"#3. Design patterns#3.2. Kafka#{1}":[347,347],"#3. Design patterns#3.2. Kafka#{2}":[348,348],"#3. Design patterns#3.2. Kafka#{3}":[349,349],"#3. Design patterns#3.2. Kafka#{4}":[350,351],"#4. Framework / Libraries":[352,414],"#4. Framework / Libraries#{1}":[354,356],"#4. Framework / Libraries#{2}":[357,358],"#4. Framework / Libraries#{3}":[359,368],"#4. Framework / Libraries#{4}":[369,369],"#4. Framework / Libraries#{5}":[370,370],"#4. Framework / Libraries#{6}":[371,372],"#4. Framework / Libraries#{7}":[373,376],"#4. Framework / Libraries#{8}":[377,377],"#4. Framework / Libraries#{9}":[378,379],"#4. Framework / Libraries#{10}":[380,393],"#4. Framework / Libraries#{11}":[394,394],"#4. Framework / Libraries#{12}":[395,396],"#4. Framework / Libraries#{13}":[397,414],"#5. Frameworks / Libraries":[415,421],"#5. Frameworks / Libraries#{1}":[417,417],"#5. Frameworks / Libraries#{2}":[418,419],"#5. Frameworks / Libraries#{3}":[420,421],"#6. Netflix OSS":[422,429],"#6. Netflix OSS#{1}":[424,425],"#6. Netflix OSS#{2}":[426,426],"#6. Netflix OSS#{3}":[427,427],"#6. Netflix OSS#{4}":[428,429]},"last_import":{"mtime":1742632310073,"size":32423,"at":1743662830263,"hash":"5fb9ae15748b4a57618f8a0479ff3eb591c5fa508a0577739510801f69c2c225"}},"smart_blocks:Microservices.md#1. Resources": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07945247,-0.02097703,-0.01228636,-0.03605451,0.03160907,-0.04441911,-0.0658717,-0.01434637,-0.00771604,0.04471242,0.02948139,-0.09413572,-0.01706486,0.0167474,0.04514576,-0.02197823,0.02535426,0.01753425,-0.03843667,-0.01152945,0.07109606,-0.049942,-0.03982889,-0.00899852,0.04451822,-0.02334666,-0.00252779,-0.06449637,-0.01764121,-0.24416624,0.01703608,-0.02350729,0.02291623,0.04838529,0.03875557,0.04049498,0.00323375,0.03554276,-0.03236337,0.01487452,0.10136849,-0.00901544,-0.09070494,-0.00387502,-0.03544832,-0.09821359,-0.002832,0.00703165,-0.02041487,-0.04263012,-0.03500941,-0.01956841,0.01300091,0.00807121,0.01845915,0.01591342,0.07634997,0.04039619,0.00691713,-0.0617488,0.06243192,0.02381324,-0.24421445,0.11093278,-0.03056548,0.01986061,0.04364992,-0.01763186,0.01444247,0.04320536,-0.05818249,0.05157111,0.02699796,0.08712824,0.01828602,-0.00374354,0.01954936,-0.06372244,-0.08853822,0.0286567,0.00415984,0.03199718,-0.0205437,0.00448892,-0.09555215,0.05947461,-0.03468962,0.02945117,0.06191298,-0.018537,-0.03355991,-0.00797225,0.01506252,0.01897086,-0.01346423,-0.02881058,0.04579302,-0.00439976,-0.0269787,0.1238525,-0.04637745,-0.00559627,0.02069453,-0.02850011,0.06320891,0.04945219,-0.05190352,-0.06826351,0.01105976,0.03662848,-0.01812024,-0.00243941,0.02612916,-0.03877392,-0.02338443,0.01197652,0.01242652,0.0253396,0.03281286,-0.00191022,0.00980056,0.00400283,0.08224495,-0.07554874,0.05471748,-0.01168762,0.0648798,0.02751437,-0.00292807,0.00752863,0.01693609,-0.02706053,-0.05702963,-0.04911602,-0.02003496,-0.03584731,0.04048274,-0.03353931,-0.0116281,-0.01522636,-0.08761157,-0.02700873,-0.0080579,-0.06823539,-0.04164684,0.11614263,-0.00215585,-0.00553912,-0.05899025,-0.08929506,-0.02514641,0.04173083,-0.01644036,-0.07090027,0.01567238,0.02364783,0.03832141,0.09121364,-0.05882218,0.05629195,-0.0048648,-0.08160017,-0.04174868,0.14293776,0.0151728,-0.06901448,-0.04750682,-0.01464181,0.01580581,-0.0186609,0.00698164,0.01230195,-0.0357164,-0.02686889,-0.00429872,-0.05402199,-0.01397437,-0.0153046,-0.01390225,0.02997543,-0.00748091,0.01445346,0.08152077,0.02124525,0.07476868,-0.07030537,0.00295648,-0.00818691,-0.01536934,-0.01251141,-0.0645632,0.00247505,0.04024032,-0.05372128,0.0523463,-0.02095136,0.04166258,-0.03726095,0.04334925,-0.04621144,0.03706153,0.03101268,-0.06917124,0.01781291,-0.00474625,-0.02909799,-0.05678643,0.02523717,0.01752397,0.03145168,-0.00308342,0.02115771,0.03050033,-0.02491472,-0.05026811,-0.02053742,0.06106094,0.07915666,0.01513733,-0.02413336,0.04086221,-0.00885517,-0.1090669,-0.20372742,0.01201959,0.02851375,-0.04161143,0.00631596,-0.04177055,0.06922462,0.02892742,0.02409797,0.03828154,0.08174519,-0.04231085,0.01851064,0.02439876,0.02747261,0.08688176,0.03137114,0.01886251,0.02303924,0.01350099,0.02479799,0.06494715,0.00055636,0.0205779,0.04712114,0.05530802,0.11840872,-0.01943715,0.049155,0.01252207,0.02545146,0.01376149,0.01870018,-0.10177051,0.03524418,0.01085755,0.01250421,-0.00193577,0.04113746,-0.00784597,-0.04427741,0.05102231,-0.0232491,-0.14471753,-0.05249196,-0.01940494,-0.0324484,-0.0330434,-0.09597882,0.00036564,-0.02438219,-0.05370623,0.01888117,-0.00611798,0.06259008,-0.00835779,-0.01317694,0.04394109,-0.02456819,0.00819407,-0.00313708,-0.02665282,-0.00844351,-0.06271788,0.06977029,-0.06360601,0.0022446,0.0844212,0.02060119,-0.02096,-0.01461668,0.13919628,-0.00853119,0.03827269,0.10220386,0.01203428,0.03304585,-0.03877979,-0.03314129,0.00200444,0.03075714,-0.05849867,0.0660392,0.01579779,0.04142142,0.06535833,0.03321812,0.01236017,0.00651143,-0.04243753,-0.04069318,0.01186596,-0.07761789,-0.04117963,0.04887652,0.04293496,-0.19252044,0.02871425,-0.01054538,0.02474415,-0.01931677,-0.03127851,-0.03136209,-0.02641141,-0.01308797,0.02686834,0.04742154,0.01532566,-0.00306159,0.02381141,0.01734643,0.02077197,0.03928295,-0.03818001,0.07310981,-0.05847884,0.00987327,-0.0116139,0.19230036,0.00578823,-0.02683801,-0.00750549,-0.00810134,-0.01960979,-0.00077913,-0.02677666,-0.02500616,0.06508614,0.13921694,-0.00175466,0.04138637,0.02433002,-0.03171249,-0.00372959,0.05905969,0.03898697,0.03752906,0.00855071,-0.01658073,-0.0244729,0.08844922,0.00357337,-0.04068423,-0.13222222,-0.0070518,0.06477258,-0.02841052,-0.00289817,-0.02383364,0.03091925,0.05188638,0.05762722,-0.04897679,-0.04017883,-0.02675674,0.00496482,0.07077042,-0.00755818,0.0244319,0.04680438,-0.00998747],"last_embed":{"hash":"c50c2fd8609c3ff3b2dd6997214737e11d70f1848a8947ef887cf72467a8a7a9","tokens":485}}},"text":null,"length":0,"last_read":{"hash":"c50c2fd8609c3ff3b2dd6997214737e11d70f1848a8947ef887cf72467a8a7a9","at":1743662877416},"key":"Microservices.md#1. Resources","lines":[7,98],"size":9268,"outlinks":[{"title":"Top 10 câu hỏi phỏng vấn System Design và Microservices","target":"Top 10 câu hỏi phỏng vấn System Design và Microservices","line":9},{"title":"Cloud - SaaS","target":"Cloud - SaaS","line":10},{"title":"MonitoringAlerting dùng PrometheusAlertmanager, Logging thì ELK Stack, con Sentry cho Tracing.png","target":"MonitoringAlerting dùng PrometheusAlertmanager, Logging thì ELK Stack, con Sentry cho Tracing.png","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0897221,0.01326416,-0.00904843,-0.04290173,0.018332,-0.0416193,-0.0149558,-0.02140314,-0.01495236,0.03267374,0.03101332,-0.11898723,-0.02370977,0.03035546,0.0253095,-0.00803798,-0.01850713,0.02080204,-0.03628534,-0.02575689,0.07788669,-0.04627413,-0.04180575,-0.03683736,0.03257301,-0.02726497,-0.01587421,-0.06275783,-0.00263071,-0.20943862,0.01426554,-0.00049755,0.00997936,0.00649376,0.0103074,0.02855444,-0.01560686,0.06595298,-0.0440393,0.01314421,0.09827654,0.02256498,-0.11080289,0.01179125,-0.02176882,-0.08602723,-0.00114944,0.02546938,0.01776863,-0.03211697,-0.01784895,-0.03812554,0.03825682,0.01538318,0.0211217,-0.02660718,0.04936071,0.03953237,0.04835839,-0.03654876,0.03479569,0.01757403,-0.23204154,0.08540375,-0.02691507,-0.00818154,-0.0016701,0.00507804,0.02358557,0.04508747,-0.0761381,0.07029662,0.02942067,0.07129303,0.0044884,-0.00464052,0.02401075,-0.06339902,-0.09247256,0.02692125,0.01279121,0.03233549,0.00174307,-0.0119732,-0.07093083,0.0611332,0.00143577,0.00321374,0.0373823,0.00487548,-0.03268278,-0.00730716,0.02252397,0.04245716,-0.03429092,-0.03776336,0.0412461,0.02800339,-0.01171851,0.15143526,-0.07917584,0.00692033,0.01661275,-0.03392608,0.04307414,0.03005333,-0.04533083,-0.02674445,0.01157611,0.01438718,-0.0042427,-0.00232381,0.03458152,-0.04211592,-0.0079026,0.02290574,0.00769532,-0.00028752,0.00782595,-0.00449929,0.0114653,-0.02474259,0.03815504,-0.05419433,0.05749789,-0.02869931,0.0917485,0.03335634,-0.02513339,-0.00519553,0.03965883,-0.04483709,-0.07816533,-0.05436974,-0.01868857,-0.00176641,0.06429525,-0.03256109,-0.00852363,-0.01606175,-0.07441035,0.0007143,-0.03672677,-0.07649681,-0.02446961,0.11499397,-0.01037989,-0.0214918,-0.05078816,-0.08079667,-0.03347684,0.04423949,-0.01194721,-0.05864033,0.03758077,0.02617961,0.03079852,0.06833204,-0.01622754,0.07290507,0.00815499,-0.05483811,-0.02047307,0.15504664,0.02696253,-0.03350247,-0.07290358,-0.03019601,0.01656636,-0.00676976,0.02327355,0.01777752,-0.02289877,-0.03778147,-0.01049025,-0.0650965,-0.02033876,-0.00800612,-0.04161571,0.02142881,0.03313009,0.00938138,0.07599632,0.02039101,0.05575459,-0.05844287,0.00843471,0.00225739,-0.0023911,0.00535269,-0.09603085,-0.0178945,0.05357459,-0.06022524,0.03884577,-0.02781061,0.0124489,-0.03944932,0.04387507,-0.06673697,0.05868658,0.04859986,-0.0685059,0.03797796,0.02904063,-0.00398139,-0.05118294,0.01625491,0.00736303,0.03729314,-0.02557625,0.00949651,0.0508646,-0.03735311,-0.04296896,-0.02529361,0.07861336,0.06229586,0.03178192,-0.04585713,0.04348422,-0.04356585,-0.09332339,-0.24506859,0.04437658,0.02125013,-0.04605206,-0.01292412,-0.03387493,0.04968388,0.00628915,0.01877192,0.04487509,0.06407493,-0.04918223,0.00831371,0.0336021,0.04409414,0.0950879,0.05981126,0.00922925,0.01957075,0.02264676,0.00863863,0.06135123,-0.00660023,0.05090762,0.05344309,0.06785313,0.118196,-0.0018518,0.0350121,-0.00555736,0.02664506,0.03292646,0.00032449,-0.10082155,0.00674512,0.00861551,-0.01691516,0.0175307,0.04871295,0.00266686,-0.02243357,0.04294666,-0.03675216,-0.1308853,-0.01467976,-0.00894651,-0.02670959,-0.02706026,-0.07493016,0.01042769,-0.00835164,-0.03296882,0.02838517,-0.02248221,0.08376136,-0.01040191,-0.02032985,0.08749001,-0.04899779,-0.01923932,-0.01702088,-0.05681404,-0.02317021,-0.04769459,0.03479249,-0.03332768,0.02063672,0.05689378,0.06479423,0.00822909,-0.00859261,0.10694893,0.01244271,0.02264289,0.08826461,0.00095159,0.02992995,-0.0529638,-0.03713717,-0.03362442,0.06656257,-0.0489642,0.06292986,0.03437651,0.05903019,0.04981184,0.05337593,0.00981674,-0.01912553,-0.01178147,-0.03829416,0.03011423,-0.04042424,-0.04646608,0.0449135,0.02648141,-0.2174841,0.02250576,-0.01348989,0.02303736,-0.02617818,-0.02320866,-0.0123322,-0.03345531,-0.02390271,0.05075968,0.02578008,0.05311651,-0.01929601,0.01698441,-0.00112099,0.03273034,0.0311451,-0.03899169,0.06605761,-0.05411852,0.00118871,-0.02728244,0.18590581,-0.00330921,-0.03100523,-0.01734675,-0.02837414,-0.02608723,-0.01342041,-0.03006399,-0.01653192,0.07696063,0.17808019,0.00224715,0.03464868,0.03767551,-0.04103035,0.00052808,0.05837584,0.02273507,0.03263757,0.0018159,-0.05546852,-0.04339097,0.07585488,0.04501677,-0.03058582,-0.10048904,-0.05010083,0.05415834,-0.03669848,0.02974853,-0.01445917,0.02703716,0.02415494,0.05022033,-0.03526125,-0.05285616,-0.02104417,-0.00132493,0.08341661,-0.02965108,-0.00959869,0.02125897,-0.02041148],"last_embed":{"hash":"ac8fe44a288ae2ccd1b6e887935167739515f5acc6db8eb839c558fe8d5e19e0","tokens":94}}},"text":null,"length":0,"last_read":{"hash":"ac8fe44a288ae2ccd1b6e887935167739515f5acc6db8eb839c558fe8d5e19e0","at":1743662877464},"key":"Microservices.md#1. Resources#{1}","lines":[9,9],"size":206,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05050566,0.03124405,0.05208788,0.00667547,0.03185376,-0.0388779,0.03210852,0.01141104,0.01081227,-0.01155635,-0.03678206,-0.15392607,0.04267152,0.05990559,0.08115096,-0.01039964,0.00148399,-0.02253128,-0.00835176,-0.02269502,0.06576639,-0.00632512,-0.00330365,-0.00778385,0.03086432,-0.04768647,-0.02820642,-0.02245738,-0.04484111,-0.21488234,0.01010869,0.00857239,-0.0431229,0.03468177,0.04033361,0.01461397,0.01400366,-0.02079231,-0.04180096,0.00636705,0.0703221,-0.0095416,-0.01016132,-0.03179574,0.02002108,-0.0547348,-0.01009328,-0.02526645,-0.01607999,-0.04604936,-0.05644919,0.00855552,0.02102645,0.04921849,0.01058887,0.01805381,0.03728163,0.00960714,0.02218411,-0.02696337,0.08259047,0.01957621,-0.25536254,0.05863697,0.00230805,0.04689475,0.06931613,-0.00476054,0.02087491,0.06618869,-0.03971534,0.00953621,-0.01271637,0.08866933,-0.00112208,-0.02919154,0.01059927,-0.03221976,-0.05009187,-0.00622897,0.02093321,0.06889634,-0.0311849,0.01054669,-0.06058504,-0.00863682,-0.01034833,-0.04450796,0.0662693,-0.02342824,0.02816702,-0.01092676,0.0071873,0.01336097,0.00227166,-0.02224538,0.02602015,0.00055552,-0.08193155,0.13484636,-0.03950037,0.01953534,-0.00035235,-0.02846758,0.04386302,0.01578073,-0.00033623,-0.0690974,0.01564283,0.05031652,-0.04792963,-0.05428981,0.0174018,-0.06386847,-0.07153656,0.03412845,0.00208989,0.01442673,-0.01665769,-0.0159269,-0.0357085,-0.01952085,0.0571527,-0.07180064,0.03944778,-0.05959629,0.03247878,0.03783106,0.00984202,0.03087079,0.03041416,-0.02265778,-0.04167258,-0.03749169,-0.02729682,-0.02781203,0.0003046,-0.01025691,-0.01640267,-0.04460837,-0.03475259,-0.0857242,0.01118858,-0.06543694,-0.09133116,0.00414393,-0.03126094,0.02239745,-0.06219218,-0.05497462,0.01325049,0.04883568,0.03012029,-0.06155235,0.01364573,0.06402346,0.04686232,0.1619608,-0.00982604,-0.00281945,0.01195796,-0.09582499,-0.05301223,0.11395504,-0.03017707,-0.08147597,-0.00828003,0.03217256,0.03629748,-0.06214803,0.07218633,0.02781598,-0.07419559,-0.03976517,0.00280989,-0.03088915,0.0213433,-0.00966191,0.01341692,0.01633024,-0.04466635,-0.00667499,0.04182214,0.04934603,0.02253173,-0.02882053,-0.02653083,-0.00600792,-0.0033956,0.00988165,-0.05076595,0.03393527,-0.01695632,-0.04639197,-0.03095247,-0.01837873,-0.00958726,-0.03144265,0.02584577,-0.03495621,0.05968338,-0.0261591,-0.04899377,0.01602495,0.00618579,0.04437833,-0.03586636,0.00515558,0.06699693,0.04839691,-0.02715642,0.04387085,0.06143534,-0.00516953,-0.02041086,-0.01098346,0.06106333,0.04146748,-0.02767327,0.0337335,-0.02732702,-0.0151294,-0.07480294,-0.19794792,-0.01835907,0.0756546,-0.01004537,0.08298071,0.00654724,0.04553758,0.08690462,0.06885798,0.04204402,0.09839523,0.03931883,-0.04193666,0.04689556,0.0540568,0.0663498,0.01667076,-0.04902041,0.04687824,-0.0205129,-0.0113573,0.0307812,-0.02575397,-0.04670674,0.02253471,0.02598945,0.14118637,-0.00268802,-0.01244108,-0.0191972,0.01713523,0.00228539,0.00928904,-0.10482468,0.12714592,0.00879126,-0.01882268,0.04753842,-0.00541792,-0.04845745,0.02531029,0.02618585,-0.04865836,-0.12679075,-0.02919327,-0.03644926,-0.05108633,-0.00208898,-0.11204054,0.00396756,-0.03724745,-0.0343179,0.02684573,0.01165833,0.03074372,-0.01050642,-0.0046681,-0.00146972,-0.03180616,-0.01616004,0.00272904,-0.02283092,0.0174318,-0.01331628,0.05011223,-0.02273418,-0.01685091,0.06319255,0.01328195,0.01754339,-0.00822801,0.11018938,-0.01262452,0.03765512,0.06135233,-0.01486614,0.02542148,-0.04050744,-0.0105295,0.01131195,-0.00609108,-0.02075783,0.04422693,0.04728906,-0.03359126,0.03421326,0.06065146,-0.00240127,0.08364547,-0.01045657,0.00305306,0.00987214,-0.04653803,-0.00889319,0.03915323,0.04457466,-0.24831261,0.05047669,-0.02765181,-0.01647926,-0.01468584,0.00142767,0.04271918,0.00814567,-0.07424119,0.03019831,0.08551624,0.0646068,0.04584228,-0.03764753,-0.01574474,0.01181558,0.01036538,-0.02320384,0.06031718,-0.02307081,-0.0032937,0.0029421,0.22489607,-0.0205097,-0.00875916,0.02133602,0.0288582,0.00619627,0.01457048,-0.03300267,-0.02077978,0.03372512,0.10736845,-0.08094409,-0.00257632,0.12628299,-0.08538619,-0.02831244,0.04182043,0.02981567,-0.02342693,0.0134479,-0.03187377,-0.01378932,0.08890557,-0.02204818,-0.00039353,-0.09841718,-0.00298682,0.01624268,-0.01850687,-0.01917156,-0.01786095,0.0043503,0.045329,0.08924112,-0.00765845,-0.0173645,-0.02365999,-0.01192517,0.02727077,-0.03093343,0.05868926,0.01373252,0.00773357],"last_embed":{"hash":"4080b8222e2690b18b7bb8c22fea199accc3229784e36b502004e7323d9ccfa2","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"4080b8222e2690b18b7bb8c22fea199accc3229784e36b502004e7323d9ccfa2","at":1743662877477},"key":"Microservices.md#1. Resources#{16}","lines":[27,38],"size":1670,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{17}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0741572,-0.01551301,0.00259448,-0.02580792,-0.00018296,-0.05683014,-0.04103337,-0.00363537,0.01843093,-0.04247089,-0.01164596,-0.09784646,0.01237904,0.02015127,0.04749728,-0.03638341,-0.02076362,0.03746003,-0.00542392,-0.03042561,0.06547282,-0.03967068,-0.05935569,0.00151267,0.04786099,0.03948206,-0.00375843,-0.049025,0.03069532,-0.19596918,0.00736351,0.03110275,-0.01052672,0.05034744,0.04702538,-0.02215006,-0.03732141,0.03926003,-0.03262334,0.07756146,0.06015063,0.03899509,-0.02465092,-0.01791125,-0.05456453,-0.1348712,0.01826403,-0.03711887,-0.04397951,0.01100845,-0.01114026,0.01514559,0.02482578,0.0965733,-0.00654762,0.02824529,0.07496876,0.03403555,0.03839935,-0.04194256,0.06915482,0.05058908,-0.2172489,0.07997707,-0.0009107,0.04098033,0.00876005,0.03937027,0.00671754,0.02763704,-0.03134012,0.0244454,0.0369462,0.05778907,0.0133015,-0.03197915,0.05018312,-0.02668397,0.00648638,-0.01697982,0.01661932,0.07847507,-0.04053431,0.00565551,-0.04375103,0.01275376,0.00829604,0.02065585,0.0559465,-0.04047367,-0.0011158,-0.01265694,0.04140078,0.03391084,-0.0432986,-0.04760506,0.02430671,-0.01581467,0.00943794,0.14706147,-0.02752582,0.05355388,0.00250753,-0.04612555,0.06800362,-0.02076769,0.00451019,-0.107606,0.0188781,0.08674031,-0.05116409,-0.04511575,0.03319347,-0.07975517,0.0106203,-0.00894522,0.0036626,-0.00768119,-0.00700266,0.05150695,-0.00061139,0.00916711,0.07061715,-0.05609782,0.03692922,-0.04306302,0.06896625,0.00348922,0.02665073,-0.0208251,0.07600449,0.00114506,-0.07276022,-0.03029876,-0.03609195,0.00492563,0.01600963,-0.04443556,-0.01965382,-0.02415905,-0.04380665,-0.0289762,-0.00507765,-0.08912983,-0.0381117,0.08913067,0.04436528,0.02627597,-0.0463107,-0.03034804,-0.05220737,0.03343382,0.02684427,-0.05357131,0.02141958,0.09380944,0.07474674,0.11953157,-0.03571581,0.03676524,-0.00101222,-0.05766022,-0.05666002,0.15334369,0.00721872,-0.09478394,-0.01522963,0.05012584,0.01008226,-0.04170442,-0.01483329,-0.00797185,-0.00616976,-0.05273726,0.07826433,0.00625842,-0.02657751,0.02083631,-0.01410204,0.03281118,-0.0068294,-0.02097254,0.04753372,0.03793675,0.03072047,-0.04504932,0.04941861,0.0475453,-0.0268634,0.03360315,-0.02348084,-0.00765714,0.05163589,-0.0278646,-0.00475323,-0.04517021,0.03632764,-0.04338049,0.01270354,-0.07568844,0.03847375,0.02428392,0.01001145,0.04180592,-0.00511175,-0.01259181,-0.08618703,-0.00085777,0.02349307,0.0179554,-0.00583033,0.02996033,0.03097276,-0.00076459,0.0079042,0.0200982,0.0724931,0.04472461,-0.02049321,-0.0241015,0.01981889,-0.02082504,-0.0866836,-0.21139205,-0.04983163,-0.02346572,-0.03702039,0.06167478,0.0112181,-0.01479735,-0.02163946,0.04122691,0.02032059,0.07456252,0.0293346,-0.04325113,-0.06290958,-0.01219003,0.08184522,0.05182338,-0.0348337,0.0331856,-0.02681121,-0.02955226,-0.02146841,-0.05943774,-0.06746434,0.04120649,0.01112563,0.12994608,-0.03797848,0.00643965,-0.03697412,0.06044395,-0.01924197,0.0077798,-0.0703059,0.06930689,0.04435599,-0.0395979,-0.03844659,0.06884956,-0.03781671,-0.00828383,-0.00336557,-0.04747772,-0.06812239,-0.01083377,0.00159767,-0.03288573,0.02710935,-0.0686192,-0.01647194,-0.01523811,-0.04571261,0.02284231,0.06228412,0.01662584,-0.01660665,-0.00824001,0.06314529,-0.03849953,0.00819166,-0.01094941,-0.02347177,0.0408744,-0.03733435,0.04924817,-0.04168087,-0.02816659,0.04019515,0.00981467,-0.00953587,0.00222844,0.11527074,-0.05527687,-0.00277802,0.10722649,-0.02560996,0.04042124,-0.05574258,-0.01532454,0.0236859,0.03362154,-0.0587204,0.03322501,0.07247993,-0.02612089,0.07175876,0.0256381,-0.04538852,0.03173705,0.00854723,-0.01596464,-0.04865077,-0.09542662,0.02443477,0.02988894,0.07620082,-0.25617588,0.04160428,0.02121649,-0.0278776,-0.01201695,-0.01131811,0.01619979,-0.02551112,-0.04854994,0.02070334,0.02082297,0.04040268,0.01264122,-0.01563931,-0.0228446,0.04499814,0.11592639,-0.02329771,0.01389556,-0.00898099,-0.02967765,0.00195533,0.20543264,0.01819145,0.0064757,-0.01984291,-0.04404351,-0.03189464,0.06520202,-0.0277441,-0.00179493,0.01167565,0.1208627,0.00705032,-0.03050038,0.03711252,-0.02496661,-0.00492193,-0.00316366,0.04833266,0.01978986,0.01973459,-0.04929341,-0.0585617,0.05299869,-0.01774483,-0.03560749,-0.13674895,-0.04068587,-0.00105105,0.00481835,-0.03282362,0.0288998,0.0505472,0.01086965,0.08929502,0.01161284,-0.04105682,-0.00983407,-0.06704669,0.02356696,-0.04055607,0.03078235,0.01768008,0.00461915],"last_embed":{"hash":"4d1dfff650772af387cee76ffd6dc2468d2596d4ccff3a25cf431d3d0c4ba0af","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"4d1dfff650772af387cee76ffd6dc2468d2596d4ccff3a25cf431d3d0c4ba0af","at":1743662877526},"key":"Microservices.md#1. Resources#{17}","lines":[39,40],"size":232,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01064166,-0.00146748,0.0056977,-0.06316304,-0.04793284,0.02521206,-0.04763823,0.00300826,0.00113051,-0.0243492,-0.02166698,-0.10374457,0.06282195,0.08703355,0.05865095,-0.02205234,-0.02923587,-0.01820429,0.0040755,0.00811283,0.06736974,-0.04541333,0.00954065,-0.03247818,0.01833835,0.05927654,-0.01015211,-0.03770816,-0.00880896,-0.20789082,-0.02467421,-0.02178277,-0.02612558,0.00359563,0.01629313,-0.01346916,0.00657054,0.02593921,-0.02029746,0.04514053,0.0518785,-0.0453265,-0.01362616,0.02057913,-0.00782363,-0.10220242,-0.00306503,0.02919394,-0.02228553,-0.08211631,-0.06331503,-0.0118936,-0.03411241,0.02718516,0.01354833,-0.00890424,0.09788621,0.09056176,0.02540054,-0.00161728,0.05892521,0.09650161,-0.21880533,0.09660599,0.00467219,0.03478283,0.02226236,0.0607918,0.0210645,0.05472603,0.00800746,-0.01393744,0.0449641,0.01943863,-0.0055151,-0.02172303,0.02050312,-0.02403224,-0.00928181,-0.02766323,-0.04594111,0.02138313,-0.01573558,0.04961879,-0.05279862,0.01083858,-0.04138892,0.00599675,0.0746857,0.02604027,0.02780828,-0.04321596,0.06499214,0.03442002,-0.06397113,-0.02102998,0.02049536,-0.00434889,-0.02628165,0.12861428,0.02515341,0.03237737,-0.02682818,-0.02000309,0.03013315,-0.05915077,0.03514085,-0.08941699,0.03892339,0.06296501,-0.0743046,-0.04638016,0.02278687,-0.06233405,-0.04934718,0.01951555,0.04348575,0.01490351,-0.02830948,0.01697923,-0.05436082,0.07791433,0.05389547,-0.04125636,0.0256461,-0.03281365,0.01879868,0.02005057,0.06182062,0.04811201,0.06868296,0.04480026,-0.04446892,-0.04550388,-0.07160094,-0.0274244,0.034243,-0.06714416,-0.08300684,-0.04842288,-0.03440646,-0.05242711,-0.01199102,-0.03067462,-0.06049291,0.04727318,-0.0133982,-0.01178336,-0.06799546,-0.07650512,-0.03232,0.06970445,0.01987823,-0.00350676,-0.00856721,0.06446412,0.01731183,0.11798978,-0.0672211,0.05148162,0.01117115,-0.10655808,-0.06617934,0.13584131,0.02019137,-0.13229305,-0.05074636,-0.02764607,0.00959208,-0.04970129,-0.00595263,0.01947622,-0.0266536,-0.03307374,0.03099984,-0.01086962,-0.03000263,0.01637502,-0.02123683,0.01198752,-0.06847179,0.01836067,0.01055878,0.02313484,0.01825914,-0.00622858,0.02331934,-0.00434607,0.03330414,-0.0398916,-0.05885803,0.01801432,-0.00209498,-0.04295194,-0.00711368,-0.02043168,0.00672087,-0.04746987,0.0451681,-0.01851706,0.09108997,0.03566631,-0.0243108,0.04049025,-0.03890404,-0.01475774,-0.02544629,0.00987357,0.03037788,-0.01284061,-0.07360961,0.02132267,-0.00701326,0.03941264,0.02009868,-0.00949963,0.08138391,-0.01362884,0.01438012,-0.01344833,0.03799695,0.01314846,-0.06487183,-0.22413072,-0.03247852,0.01605543,0.00904912,0.02668406,-0.0348587,0.02588666,0.0204596,0.03745293,0.0579428,0.10586554,0.05660925,-0.04969993,0.00978572,-0.00377444,0.02527596,0.0439296,0.01994603,-0.00473919,-0.01817809,0.00578212,0.00097897,-0.02928073,-0.01972469,0.05972526,0.03740491,0.12489734,-0.01866075,0.02354052,-0.00012265,0.0245979,-0.01703165,0.0707012,-0.09338133,0.09339438,0.03041762,-0.01818878,0.05006718,0.04809854,0.0147179,0.01946858,0.03004581,-0.0528251,-0.10496354,-0.04631319,-0.03163306,-0.03247696,-0.05003006,-0.07611149,-0.01008749,-0.02427347,0.04037569,0.03504213,0.03262093,-0.0045635,-0.00294035,-0.01965935,0.00813388,0.03427974,-0.01417247,0.01296012,-0.02530143,-0.01984482,-0.02873527,0.07795922,-0.03887582,0.00716457,0.04911863,0.03434745,0.00680801,-0.01255512,0.08568249,-0.00879791,0.06190474,0.05514731,-0.00031932,0.0277883,-0.03964379,-0.02999762,-0.00241543,-0.01233883,-0.04955623,0.06077645,0.00363181,-0.0034239,0.08815996,0.03457597,-0.01396519,0.0974872,-0.02445689,-0.00305898,0.01432063,-0.01759128,-0.02349711,0.07826354,0.04471865,-0.27731514,0.04035549,-0.02001031,-0.01287893,-0.02542848,0.00609426,0.00674505,-0.00245821,-0.04625784,0.03481397,0.07209444,0.06774798,0.02181443,-0.01300988,-0.01182695,0.02883006,0.02514344,-0.05598242,0.05913044,-0.01381796,0.00054722,-0.02051948,0.18783183,0.05285785,-0.01310966,0.07754427,-0.00437146,0.01270996,0.01672918,0.03069923,-0.06238755,0.01072709,0.12696992,0.03717566,0.01442843,0.05376769,-0.04260371,-0.0271665,0.00963006,0.02203261,-0.00104518,0.00858034,-0.01864913,-0.00603431,0.08179808,-0.01431734,-0.09828622,-0.10937053,0.03189659,0.00192318,-0.03612161,-0.07141876,-0.02105889,0.03817148,0.01805815,0.04519898,0.01309398,-0.02964895,-0.02788415,-0.05563442,0.01257332,-0.0178583,0.00188311,0.01300213,-0.0103215],"last_embed":{"hash":"7dc3c31ad56f7f9d5f1975bab158f0c32ee2e226a56daa4d1c174c3a16f527b8","tokens":307}}},"text":null,"length":0,"last_read":{"hash":"7dc3c31ad56f7f9d5f1975bab158f0c32ee2e226a56daa4d1c174c3a16f527b8","at":1743662877540},"key":"Microservices.md#1. Resources#{19}","lines":[43,47],"size":695,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{21}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04979569,0.00950491,-0.00087073,-0.00812985,-0.01667326,-0.03699492,0.02504119,-0.03232555,0.04776473,-0.02875282,0.02038639,-0.1295443,0.03782455,0.07199913,0.10199898,-0.03693118,0.01990971,-0.01626144,-0.00497975,-0.03928538,0.07386,-0.05841795,-0.04064417,-0.01093131,0.00293124,-0.00967975,-0.01535467,-0.07632246,-0.01769236,-0.2160293,-0.01736022,-0.05161015,-0.00123334,0.01632033,0.02597416,-0.00678823,0.01633839,0.06940243,-0.08650485,0.10402324,0.07356628,0.04483771,-0.08015189,-0.03776269,-0.00954006,-0.06865999,0.01026827,-0.01190759,0.04691331,0.00858269,-0.02670886,0.00525435,-0.02944515,0.00192575,-0.01553661,0.03304013,0.04078813,0.04426091,0.02383268,-0.02495669,0.07644462,0.03913545,-0.19394836,0.07947272,0.03059844,0.00089464,0.02566274,0.04243755,0.05943064,0.01995842,-0.06056251,0.04471888,-0.0347527,0.06128389,0.00395647,0.00111546,0.06041626,0.01311726,-0.06833594,-0.03057067,-0.04247371,0.05483518,0.01043535,0.01763418,-0.03571425,0.02750806,-0.02488293,-0.00240456,0.07684314,-0.03577029,0.00978184,0.00781121,0.05879879,0.04560213,-0.11388024,-0.06470606,0.02556596,0.07517508,-0.05039499,0.13810016,0.02965154,-0.00180457,0.0247352,-0.05756532,0.08246183,0.02662499,0.05377216,-0.06100282,0.02916588,0.02518309,-0.02199985,0.00851417,-0.0462874,-0.05209699,-0.00675755,0.06178201,0.0253994,0.0653296,0.0053957,-0.00605045,-0.03547383,0.00396734,0.06421677,-0.04446746,0.06116161,-0.04352208,0.03661077,0.02986401,0.03187441,0.02576986,0.03611169,0.00794408,-0.02726288,-0.06481452,-0.07235225,-0.02310698,0.00291512,-0.02329409,-0.0124839,-0.04367153,-0.06031345,0.01441815,0.00928858,-0.04914128,-0.05294123,0.03487275,-0.00609905,0.0381291,-0.06701631,-0.0292486,-0.0070721,0.01920004,-0.00449982,-0.05183107,-0.01824656,0.03557954,-0.0096933,0.14025144,-0.01699498,-0.01266655,0.02901863,-0.07884543,-0.05337171,0.16267271,0.0144921,-0.09597816,-0.07114264,0.05695307,0.03350036,-0.06631131,0.04329218,0.03901184,-0.02770801,-0.00029894,0.06693729,-0.00222779,-0.11219831,0.02632487,-0.06363145,0.01760097,-0.0146618,-0.00243175,0.02806107,0.05737276,0.01848789,-0.03043208,0.04677581,-0.01667431,0.03934093,0.01952011,-0.10647035,-0.02136948,-0.02528026,0.01280348,0.00437126,0.00354746,0.00213249,-0.00986276,0.03470298,-0.02701877,0.07487203,-0.00107407,-0.07869667,0.04734785,-0.06033989,-0.07598995,-0.08845627,0.00260616,0.04031437,0.01295798,-0.02572521,0.00655581,-0.00198632,-0.00067376,-0.02570374,-0.03130033,0.04181775,0.04901347,-0.00788724,-0.01451072,0.07335446,-0.00523817,-0.07902677,-0.17456734,0.05292822,-0.00917999,-0.01198244,0.03855297,-0.04319554,0.08785931,0.02026161,-0.01865597,-0.03300142,0.01645606,0.00648415,-0.037487,0.06071299,0.03671563,0.06917959,0.05628153,-0.03048717,0.00304675,-0.02804605,0.02966168,-0.01215337,-0.04489081,0.02880951,0.09995401,0.00762882,0.11443862,-0.03458858,0.01604894,0.00461505,0.04181994,-0.00007825,-0.01481441,-0.10142967,0.0443124,0.02116644,-0.03137806,0.0212171,-0.01222479,0.02258152,0.01884283,0.03296299,0.00177344,-0.10489495,-0.03310878,0.00051778,-0.08651274,0.02639753,-0.07978365,-0.11537801,-0.01505059,-0.01105989,0.01112389,0.01182859,0.02667478,-0.03010305,0.01103232,0.02757845,-0.04205967,0.01029536,-0.00488133,-0.01364505,0.00558915,-0.04683453,0.02490979,0.01731349,-0.00532291,0.00938071,0.03877268,0.0234776,-0.01217585,0.09071749,0.01996987,-0.01803995,0.01336,-0.01717866,0.01460953,-0.00598715,-0.01593985,-0.01304882,0.08368613,-0.04234624,0.06390055,0.01558199,0.02414238,0.03876406,0.01045297,-0.00730408,0.04347562,-0.01894834,-0.03577098,-0.02640565,-0.07545707,-0.08232529,0.08116851,0.0630386,-0.24567878,0.09040631,0.01230347,-0.01334857,0.00616457,-0.00057797,-0.00418969,-0.01427136,-0.04231765,0.05138343,0.10601329,0.04844597,0.02168301,-0.01540294,-0.02235517,0.04140958,0.03704417,-0.01935847,0.07742777,0.00593665,-0.01137697,-0.05172281,0.17809945,-0.0112175,-0.01379799,-0.03565628,-0.04650936,-0.0339639,0.03862936,-0.00282451,0.00708767,0.03256736,0.07743959,-0.01667631,-0.00883418,0.05780644,-0.03235836,-0.0571761,0.03772231,0.02905975,0.01230279,0.02995939,-0.05709898,-0.01416316,0.07659633,-0.02434946,-0.00717866,-0.11361238,-0.01395644,0.01415991,0.05684773,-0.03621486,0.01735447,0.01662937,-0.00293049,0.0598419,-0.01206326,-0.05530418,-0.03639321,-0.01852294,0.0779297,0.02384507,-0.00771019,-0.04472364,0.03105222],"last_embed":{"hash":"996036f53aea7edb8843b2c8618c5a8380b8945695350a71d76c2a14d5395618","tokens":181}}},"text":null,"length":0,"last_read":{"hash":"996036f53aea7edb8843b2c8618c5a8380b8945695350a71d76c2a14d5395618","at":1743662877573},"key":"Microservices.md#1. Resources#{21}","lines":[50,54],"size":433,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{22}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0840589,0.01820853,0.04752278,-0.03966661,0.00367026,0.00039934,0.05113947,0.02053498,0.00539017,-0.00480996,-0.04113736,-0.11491872,-0.00399204,0.04213061,0.07405945,-0.01574097,0.01939609,-0.00967194,0.00147603,0.00285623,0.15110318,-0.02586284,-0.02230505,0.03970631,0.02874239,-0.07061702,-0.01927621,-0.00049582,-0.00584735,-0.16344945,-0.02571964,0.0285087,-0.0282637,0.044053,0.00839429,-0.00200842,-0.00259652,0.03424003,-0.05380328,0.05760591,0.0524771,-0.0002267,-0.01863809,-0.05568276,0.04359281,-0.08377339,-0.01051665,-0.00408163,0.062085,0.00457711,0.00451554,-0.00076299,0.02708612,0.03711253,0.11609663,-0.01212336,0.04570287,-0.00326181,0.05124626,-0.01642681,0.09681041,0.07985681,-0.2017431,0.02681808,0.02202121,0.0130403,0.02239551,0.03308335,0.03535846,0.02646617,-0.01753061,-0.01714565,-0.0235842,0.04993749,-0.03658763,-0.06364696,0.0016388,-0.00331436,-0.08030427,-0.0185155,-0.00314766,0.03080045,-0.02875242,-0.01327537,-0.07765371,-0.01340327,0.00852117,-0.01694892,0.03987569,0.01911579,0.00683917,-0.01237835,0.03274711,0.0056403,-0.03170722,-0.07599853,-0.00949309,-0.01680808,-0.07780176,0.12942991,-0.01224505,-0.0187955,0.01249171,-0.01815118,0.05338655,-0.02094062,0.00172603,-0.06545696,-0.01108837,0.03203092,0.01819794,0.02941366,-0.02151379,-0.07429931,0.01840094,0.04402759,-0.01673347,0.01450634,-0.00951696,-0.0124531,-0.01758865,0.03796731,0.04170217,-0.0492491,0.00762351,-0.06588231,0.03163384,0.03867809,-0.03877503,-0.0139707,0.03359499,-0.0511118,-0.03548622,-0.06284735,-0.03228725,0.02160267,0.03644162,-0.0426211,-0.02293964,-0.03317378,-0.04254129,-0.0858636,0.02393232,-0.10000698,-0.03114713,-0.01766031,-0.09424947,0.05059827,-0.01949023,-0.03037041,-0.02163845,-0.00045962,0.0050258,-0.08059325,-0.00298838,0.03504185,0.04390037,0.14856286,-0.03581211,0.0545633,0.01881358,-0.05582665,-0.07432755,0.10502134,0.03173073,-0.11968473,-0.03105745,-0.00035762,-0.01369438,-0.05295191,0.04758122,0.08180909,-0.04938574,0.01194887,0.060525,0.0279972,0.03839223,0.00206058,-0.02630945,0.0495721,-0.12461717,-0.04198477,0.01523364,0.0299495,0.05740881,-0.03920687,0.01555874,-0.01375751,0.02512098,0.02720799,-0.08700517,0.04961429,-0.03384425,-0.00856011,0.00914509,0.00500547,-0.01706397,-0.05900459,0.00289242,-0.01908715,0.05414438,0.00187715,-0.05352432,0.00005336,-0.01649636,0.0043808,-0.04928625,-0.04870011,-0.02068117,0.00707831,0.00022489,0.01462073,0.10293999,0.03868425,-0.04412742,-0.0361172,0.09396255,0.07553053,0.00728524,0.01328547,0.0611308,0.05772948,-0.02565793,-0.2282979,0.02120777,0.00446268,-0.00346061,0.08991268,-0.02822039,0.03749854,-0.0428067,0.05575399,-0.00674111,0.10838544,0.02078818,-0.05508627,0.05718552,0.0300355,0.06180762,0.04559429,-0.03117042,0.01111528,0.0423693,0.03243735,0.05641272,-0.04782585,-0.01979486,-0.02422592,0.02994691,0.12780815,-0.02600143,0.0622309,-0.02662243,0.03015484,0.0207642,0.03636404,-0.08481777,0.0566613,0.04102372,-0.07153806,0.0188007,0.03216042,-0.00872696,-0.01497166,0.04441646,-0.01373992,-0.05494718,0.04410553,-0.05539915,-0.08301879,0.01697291,-0.06914219,-0.01067136,-0.03514536,-0.00514106,-0.00419362,0.06195679,0.03088008,0.02329092,-0.01279556,0.01950752,0.01843485,-0.01896703,0.01558772,0.04112064,0.00612334,-0.07011885,0.01739575,-0.00397756,-0.0190913,0.02980706,0.04881908,0.0405049,-0.04264546,0.04666327,0.02490186,0.02264346,-0.01609559,-0.00350717,0.03103791,-0.11282564,-0.02489418,-0.00564006,-0.00830993,-0.07094284,0.08886646,0.00163687,-0.04396379,0.04232964,0.01655149,0.02447255,0.06097929,-0.00346528,0.01339066,0.00448865,-0.0500609,0.01180622,0.06934809,0.0053697,-0.25890583,0.04282101,-0.06439178,-0.01314216,-0.0592373,0.0493039,0.01231097,-0.02099421,-0.02636214,0.02505446,0.05634416,0.05712278,-0.00102131,-0.01484097,-0.0139641,-0.02504783,0.04298196,0.01419965,0.04766604,0.03442463,0.0117813,-0.00714825,0.20045559,-0.01427382,0.03877138,-0.02002613,-0.0039502,-0.01804877,0.02583589,-0.04446143,0.00556817,0.00057501,0.07108083,-0.02315467,0.06137542,0.05752023,-0.02438385,0.01314609,0.01501949,-0.00647608,-0.08986448,0.02444682,-0.06276999,-0.00754192,0.13808291,-0.04484301,-0.04271522,-0.11847056,0.04149982,0.02397297,-0.01925567,-0.04378343,0.02448253,0.01384681,0.03565058,0.03666123,-0.06948923,-0.04759989,-0.01490851,-0.00853947,0.06265981,-0.00093865,0.01720466,-0.02597794,0.04286406],"last_embed":{"hash":"dcebcfd90d0b0946e3ad361d4859b312ef948bc83a0605541205c30291b194c9","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"dcebcfd90d0b0946e3ad361d4859b312ef948bc83a0605541205c30291b194c9","at":1743662877593},"key":"Microservices.md#1. Resources#{22}","lines":[55,55],"size":300,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{23}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05834801,0.00617388,0.02993564,-0.02855887,-0.01632393,0.00440164,-0.00936932,-0.01551672,0.03813546,-0.00739318,-0.05227062,-0.10762686,0.05419797,0.0444677,0.10089715,-0.05397849,0.0187008,0.05070733,0.02007343,-0.00350396,0.0558317,-0.04562474,-0.03936022,0.02252388,-0.00473295,0.00957456,-0.03133234,-0.00547324,0.00846181,-0.18137859,-0.00146667,0.02670239,0.02693385,0.01263914,0.04977628,0.03028671,0.01304851,0.02441803,-0.03883829,0.07924832,0.09585733,0.04417114,-0.01295912,-0.03083889,-0.00201323,-0.08720484,0.02355381,0.01895659,0.03206869,0.00560058,-0.0196219,-0.00935556,-0.00256319,0.01332642,0.05055004,0.03476748,0.06183811,0.0114957,0.03047548,-0.03359836,0.08553778,0.03490555,-0.21662103,0.11706708,0.01653619,0.0197058,0.02718413,0.01044083,0.00471306,0.05769453,-0.0163259,-0.0219377,-0.01754038,0.06736674,-0.0121882,0.00960626,0.05413739,-0.05336125,-0.0765577,-0.00749235,-0.00417812,0.06735343,0.00191578,0.07369032,-0.05633518,0.03451521,-0.03332223,0.00935542,0.05463581,-0.01811394,-0.01238686,0.00542083,0.01086003,0.05972319,-0.04934563,-0.04061627,0.02852571,0.00219824,-0.05367042,0.14907159,0.0139537,-0.02221591,0.03301443,-0.06957565,0.07339069,0.00529505,0.01411127,-0.06093386,0.00052354,0.09713682,-0.02794779,-0.01240131,-0.00652538,-0.05553105,-0.04668994,0.02088406,-0.00856521,-0.00047083,-0.023384,0.02868076,-0.05539363,0.0485491,0.08459033,-0.06081747,0.02437613,-0.04108492,0.00975908,0.06830045,-0.00001076,0.00901477,0.02343507,0.02903721,0.00521983,-0.03240763,-0.0385574,-0.03255692,0.00658655,-0.02302627,-0.06619421,0.0024324,-0.03337553,-0.04160604,0.00853629,-0.05913357,-0.03208015,0.06494641,-0.02943019,0.02363831,-0.04582608,-0.04496717,0.00617684,0.02875498,-0.03097167,-0.00051506,-0.04490846,0.04996897,0.04653104,0.13625148,-0.04648128,0.05708042,-0.01876005,-0.08719507,-0.04126537,0.17376673,0.02288045,-0.12053159,-0.06314506,-0.00584664,0.01243591,-0.06415778,0.00880308,0.0429051,-0.02498222,-0.01215075,0.07535309,-0.00418403,-0.02500592,-0.00324794,-0.03228617,0.00223261,-0.06715255,-0.02490112,0.04887041,0.02010394,0.05174501,-0.00615881,0.01073061,-0.00555984,0.0080677,0.02566724,-0.09247951,0.01034863,0.06005777,-0.05514543,0.03707633,-0.02451919,-0.03109268,-0.04285711,0.05894162,-0.00104605,0.04119821,0.04248209,-0.02774038,-0.00007608,-0.03955639,-0.02509494,-0.02384311,-0.03208526,-0.01292088,0.04433379,-0.02971353,0.0105162,0.06837825,0.02534631,0.00310345,0.00984836,0.03595238,0.05095299,-0.06121688,-0.02162117,0.00815349,-0.01690952,-0.10270168,-0.21852602,-0.00608704,-0.02198374,0.01164111,0.02658421,-0.02093726,0.07227679,0.03652961,0.01274666,-0.01284174,0.08135841,0.02928395,-0.02439548,0.01355239,0.03084528,0.0783097,0.04257138,-0.06196982,-0.01924824,0.00929652,0.06565644,0.03317706,-0.06462101,-0.01784227,0.04932337,0.01389612,0.1015491,-0.02163948,0.02764339,-0.00742301,0.07361725,-0.01665171,0.02367594,-0.09784009,0.0643813,0.03363642,-0.00601109,-0.0041598,0.00515218,-0.01546046,-0.00776282,0.00712823,-0.02668717,-0.10421988,-0.02023835,-0.05970786,-0.02618078,-0.01713868,-0.10065048,-0.09168817,-0.06143864,-0.03022577,0.0174287,0.04844094,0.00200054,-0.03348538,0.02116898,0.00825075,-0.02211425,-0.01525588,0.00178265,-0.02463398,0.04285964,-0.0845467,0.0526152,-0.04285632,-0.02102335,0.01893512,0.02055085,0.02318136,-0.01620961,0.09005,0.01319298,0.04874586,0.04799503,-0.02512497,0.00344344,-0.06731275,-0.00574544,-0.00283178,0.00648838,-0.03031333,0.06942023,0.05122735,-0.01743899,0.04891285,0.02083223,-0.00620546,0.04259219,-0.0313708,-0.01072398,-0.01202283,-0.07024854,-0.05354195,0.06596845,0.05510885,-0.27125424,0.03722258,-0.00224221,0.01060425,-0.02962083,0.0027914,-0.00583976,-0.0257768,-0.03770043,0.01345707,0.1265367,0.04485425,0.06296115,-0.03764733,-0.01562097,0.01693484,0.06822251,-0.00463565,0.02482233,0.03982476,-0.01913306,-0.02894109,0.1952973,0.01585596,0.01777524,-0.00715893,-0.04573384,-0.02036524,0.01317715,-0.02900472,-0.01760356,0.02694886,0.08809283,-0.03109848,0.04602469,0.04252229,-0.04840354,-0.0186557,0.01724068,0.03610998,-0.03017989,0.04135751,-0.09459254,-0.00390214,0.08316073,-0.02903273,-0.00292791,-0.12196821,-0.02227895,0.00498611,-0.03485022,-0.04772228,0.00132135,0.03882298,0.02483151,0.06841727,-0.04309706,-0.04319424,-0.04030921,0.01707042,0.06490751,-0.0830702,-0.00569976,-0.00271062,-0.01121355],"last_embed":{"hash":"77defce5584bc9e419a5972942654a04e0754efefae3cf4e55691c43c959f9d3","tokens":109}}},"text":null,"length":0,"last_read":{"hash":"77defce5584bc9e419a5972942654a04e0754efefae3cf4e55691c43c959f9d3","at":1743662877609},"key":"Microservices.md#1. Resources#{23}","lines":[56,56],"size":245,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#{25}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04782036,0.05831182,0.02328629,-0.00508777,-0.0162618,-0.02325748,-0.05266343,-0.00744406,0.0222619,0.01702542,-0.07207163,-0.1219392,0.02330002,0.05903884,0.09098531,-0.05701661,0.02324221,0.01143494,0.01260758,0.00201858,0.08743087,-0.03561409,-0.01917319,0.02423833,0.01322407,0.01725941,-0.0390145,0.00414395,0.02487268,-0.18324117,-0.00305911,0.00083613,-0.00010802,0.01688807,0.06549357,0.03906472,-0.00689708,0.03839089,-0.0036247,0.03479683,0.09905498,0.04111833,-0.04533962,-0.04034264,0.00128238,-0.09489611,-0.0000635,0.02026807,0.02660106,-0.03511341,-0.03145504,-0.01080652,0.00767799,0.00993134,0.03916036,0.05302659,0.07002084,0.02174163,0.01999696,-0.04448093,0.10066205,0.06206419,-0.23464236,0.13258488,-0.0132763,0.01813208,0.01887902,-0.01810846,-0.00560284,0.03011056,-0.02495745,-0.02262755,0.01907357,0.03267081,0.00603485,-0.00417219,0.05101376,-0.05337854,-0.06674392,-0.00039426,-0.00961143,0.04939355,-0.02181993,0.06911363,-0.07387218,0.02963356,-0.01999291,0.0389677,0.04526869,0.021268,-0.01762962,-0.01565316,0.01279173,0.04608831,-0.05323371,-0.04784088,0.03703571,0.02858244,-0.03819577,0.14001547,-0.00269354,0.01494663,0.05502225,-0.05049441,0.05792495,0.00787756,0.03254068,-0.03985722,0.03500153,0.10043274,-0.05162243,-0.03171792,-0.00466823,-0.05082513,-0.02567729,0.02506434,0.01316203,-0.00521054,-0.03349026,0.01358333,-0.05276709,0.01946487,0.07053266,-0.06439983,0.03341846,-0.04371807,0.02547785,0.07425686,-0.00325628,0.01680623,-0.00580287,0.00436696,-0.03938117,-0.06068509,-0.02928786,-0.02799215,0.01404441,-0.03769385,-0.06962579,-0.00213851,-0.04460565,-0.06345958,-0.00991221,-0.05742822,-0.01443956,0.0633813,-0.03010465,0.03447553,-0.0236876,-0.0471279,0.01643437,0.06791425,-0.01328592,-0.01284105,-0.04309747,0.06049784,0.02867741,0.11658502,-0.03340449,0.0446893,-0.00564097,-0.09919809,-0.04448076,0.17456798,-0.00532068,-0.1081731,-0.07918116,-0.00307396,0.0171064,-0.05225352,0.00101153,0.03700329,-0.0131699,-0.02468527,0.06549808,-0.02001738,-0.02403385,0.01159271,-0.03525083,0.01101117,-0.08528043,-0.01871133,0.05383582,0.02818403,0.05468562,0.02106919,0.01686076,0.00524094,-0.00656096,-0.01650132,-0.06945113,0.02249739,0.03833454,-0.06257492,0.03001938,-0.01270494,-0.034051,-0.03917678,0.06266111,-0.01980266,0.07538053,0.02572825,-0.02380364,0.00937057,-0.03581035,-0.00140475,0.00083468,-0.01281051,0.02232227,0.03120644,-0.03342209,0.01039953,0.0551121,0.0484826,0.02017376,-0.02948512,0.05981089,0.03855564,-0.04055391,-0.0229164,0.04063179,-0.04291768,-0.11765358,-0.21236622,-0.01382813,-0.00928335,-0.0064732,-0.01970597,0.01467551,0.09734319,0.05153864,0.0150771,0.0425569,0.09669704,0.02353888,-0.07209344,0.01308468,0.02040919,0.03811158,0.02567567,-0.06542324,-0.03106225,-0.04237509,0.06292779,0.03639071,-0.05538191,-0.00673752,-0.00382747,0.00679605,0.11730979,-0.0220792,0.05120102,-0.00506007,0.05487542,-0.05964125,0.04557918,-0.07211604,0.067784,0.00147042,-0.03721691,-0.02324919,0.015953,-0.05921366,-0.01770909,0.02948668,-0.02327668,-0.09262445,-0.05452912,-0.03554072,-0.0378715,-0.02008488,-0.10052367,-0.04152083,-0.05856245,-0.02309207,0.00174527,0.01944776,0.01554151,-0.0232694,-0.00284739,-0.0012678,-0.00694391,0.00818669,-0.00348641,-0.01842463,0.04601122,-0.1145526,0.07949119,-0.04488149,0.00157505,0.0122985,0.03535193,0.04521939,-0.00712416,0.09282793,0.00512957,0.03304438,0.07902251,-0.02010623,0.01890423,-0.04155223,-0.01812964,-0.0194634,0.00460558,-0.04520622,0.0379271,0.02644006,0.03566035,0.05268731,0.01482073,-0.00414045,0.01041321,-0.04956602,0.01801413,0.00223566,-0.04800024,-0.00655219,0.06146307,0.02029255,-0.26921785,0.04052283,0.00166545,0.00320252,-0.04263272,-0.01041198,-0.03576608,-0.01430637,-0.03044338,-0.01738654,0.10965791,0.04421656,0.07957343,-0.00228707,-0.02218169,0.02921595,0.0545952,-0.01365895,0.02441434,0.00567293,-0.02806499,-0.0408072,0.18839005,0.00685209,-0.00455164,0.03277872,-0.06485178,0.00695209,0.01169726,-0.00632773,-0.02893096,0.03391396,0.12434976,-0.00110842,0.04915315,-0.0019536,-0.02364763,0.00265259,0.04020552,0.01041464,-0.04214828,0.04438049,-0.05736531,-0.01095278,0.06913388,-0.00667991,-0.01578279,-0.09178875,-0.0195055,0.03443805,-0.03691882,-0.03401433,-0.00262347,0.05194625,0.05086425,0.03951018,-0.02084674,-0.04276377,0.00332655,0.03558427,0.04334874,-0.05810058,-0.02626096,0.02174004,-0.00366281],"last_embed":{"hash":"6bd4d2602bccaac9af66a0bf7aca9cdffec9d4dfcb08bff5d2911d1d25d5bdb1","tokens":192}}},"text":null,"length":0,"last_read":{"hash":"6bd4d2602bccaac9af66a0bf7aca9cdffec9d4dfcb08bff5d2911d1d25d5bdb1","at":1743662877623},"key":"Microservices.md#1. Resources#{25}","lines":[58,61],"size":415,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.1. Distributed transaction": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06731255,-0.01397214,-0.01075042,-0.02519082,0.00042168,-0.03502443,-0.06448752,-0.00670398,0.04701057,0.00341189,0.00527734,-0.10414563,0.04160294,0.04848666,0.03170941,-0.00325509,0.00852672,-0.02663547,0.03242248,-0.01652306,-0.01342786,-0.02895794,-0.04038662,0.00342276,0.07853768,0.00471167,-0.03746367,-0.03492733,-0.0479948,-0.22186726,0.00921154,-0.01180912,-0.03511046,0.04239821,0.03153481,0.02072778,-0.01521165,0.02546705,-0.0501225,0.01993812,0.02069613,0.03853877,-0.03673998,-0.06366806,0.00399653,-0.10837541,0.01433903,0.01248753,0.02638542,-0.03190492,-0.06033732,0.02142061,0.00169191,0.04571908,0.03263126,0.01677777,0.05123238,0.07844124,0.0107673,-0.02954844,0.05713302,0.05320467,-0.19967388,0.07690278,0.03261046,0.02629188,0.05711236,0.02427134,0.03055571,0.06877133,-0.01398851,-0.02773866,-0.00653544,0.08292273,0.02582579,-0.06169147,0.04847863,-0.01526478,-0.05496548,0.00830687,-0.04179051,0.03517952,-0.00889402,0.03407914,-0.10938661,0.02883256,-0.01251419,-0.01871949,0.06293953,-0.03696468,0.01494759,0.06228019,0.04704638,0.02094557,-0.05735848,0.00812515,0.01278837,0.04574625,-0.0021676,0.1119897,0.06213015,0.04912078,0.00368517,-0.03832699,0.10428949,0.01388906,0.00600205,-0.07901662,0.01709791,0.0384387,-0.02465124,-0.03605713,0.03884717,-0.08862945,-0.03686572,0.04929478,0.00585282,0.02549809,0.01397634,0.01028383,-0.01382147,0.04057069,0.07972335,-0.05559644,0.03259896,-0.05587306,0.03657062,0.03854879,0.06299569,0.00260888,0.03857857,0.03992851,-0.02104457,-0.03067658,-0.03181499,-0.03818655,-0.0182288,-0.04829657,-0.01728466,-0.06341551,-0.02866923,-0.0292637,-0.00717675,-0.09741025,-0.08564091,0.11279039,0.04369003,0.05297831,-0.03999254,-0.05889765,-0.01219613,-0.00764037,0.06165208,-0.11320607,0.00646337,0.06513644,0.0647499,0.08836534,-0.08082822,0.02814836,0.02710659,-0.08105792,-0.01198862,0.15975212,0.00028027,-0.10293093,-0.008485,0.0054495,0.04967803,-0.06173097,0.00471453,0.03988257,-0.04541145,-0.01832834,0.04497802,-0.0651269,0.00399572,-0.04198566,-0.00463513,0.01339274,-0.03599091,-0.0120837,0.04422046,-0.00301901,0.06077803,-0.04586555,-0.02311398,0.02629109,-0.03873723,0.02801138,-0.02553813,-0.00823795,0.01164133,-0.00756704,0.00198335,-0.07311385,0.0009494,-0.02539369,-0.00313402,-0.01304596,0.09863278,0.01401955,-0.07356036,0.01881515,-0.0525372,-0.01611143,-0.04251063,0.03148333,-0.00833486,0.02808679,-0.05341936,0.02124632,-0.00855131,-0.00358633,-0.08282495,0.02200249,0.0379142,0.07568151,0.01110145,-0.01196087,0.03731295,0.07527122,-0.03322802,-0.20695974,-0.01723776,0.03399358,-0.0294955,0.06195825,0.01042461,0.04904643,0.01913193,-0.00010638,-0.00565172,0.07727158,0.0478443,-0.04971012,0.00931576,0.03651218,0.03484831,-0.0020092,0.02436117,0.0196862,-0.01412677,-0.00525887,0.04858921,-0.11765874,-0.05492541,0.07854526,0.01806428,0.08871168,-0.02306879,-0.01874096,-0.00960779,0.03850862,0.00339582,0.02165104,-0.07395019,0.02973934,-0.0128173,-0.04538371,0.02480311,0.02317449,-0.01805818,-0.00825211,0.03065537,-0.01914796,-0.07969433,-0.0392234,-0.02197619,-0.06108557,-0.01840473,-0.04571858,-0.04810835,-0.01933285,-0.02841621,-0.00812785,0.07067408,0.01678429,-0.00185589,-0.00867365,-0.00466837,0.02085034,-0.00613436,-0.0158102,-0.0012198,0.02459793,-0.04790847,0.07696135,-0.05175099,0.0129966,0.0368709,-0.00056477,-0.03773177,-0.00387335,0.10238197,-0.02216268,0.0033013,0.03169997,-0.02285328,-0.01723329,-0.04680239,-0.06056591,0.01736992,0.05604606,-0.07011416,0.04827422,0.06586302,-0.0288362,0.06858676,0.06206161,-0.00719903,0.0547306,-0.01620854,-0.00663737,0.02169313,-0.06833327,0.01080115,0.06598803,0.0620738,-0.26184419,0.04165487,-0.04890797,-0.00693401,-0.0770919,0.0475078,0.03635491,0.02056681,-0.07595534,0.0050717,0.05798603,0.09507383,0.0268134,0.03504524,-0.02375666,0.02116731,0.10165157,-0.06455339,0.02818455,-0.03967488,-0.02064751,0.02405401,0.18503346,-0.00348317,0.01523462,0.05308606,-0.03065295,0.05789343,0.00532375,-0.03073855,-0.00390199,0.000605,0.04933368,-0.005187,-0.00890786,0.02754817,-0.03258357,-0.00074801,0.04923762,0.03509955,-0.03909473,0.04364695,-0.00512887,-0.01747307,0.1272168,-0.01835593,-0.0275312,-0.11157949,0.02122709,0.0380321,-0.03163676,-0.0190394,-0.00519489,0.02454782,-0.02118315,0.08058237,-0.02150063,-0.02722619,-0.04115927,-0.008219,0.05905608,-0.02466083,0.00683146,0.00676246,-0.04267928],"last_embed":{"hash":"66907df39e98aa0ff1e06584b616d461535f646de5be6a86adcd6c51e0c1d503","tokens":414}}},"text":null,"length":0,"last_read":{"hash":"66907df39e98aa0ff1e06584b616d461535f646de5be6a86adcd6c51e0c1d503","at":1743662877644},"key":"Microservices.md#1. Resources#1.1. Distributed transaction","lines":[62,81],"size":1421,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.1. Distributed transaction#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03709014,-0.00697765,0.02308496,-0.04842043,-0.0131581,-0.024195,-0.04164449,-0.02386936,0.04295894,0.00047062,0.02611316,-0.04614929,0.0212326,0.01798605,0.04703203,-0.04824695,0.00014934,0.01390975,-0.00716304,-0.04500994,0.03826302,-0.00304317,-0.0410083,-0.02503658,0.05707961,0.0279914,-0.01622055,-0.02290741,-0.02072174,-0.17487037,-0.01552116,-0.00386396,0.0044485,0.04966214,0.0195529,0.04506333,-0.06390078,-0.01357022,-0.08234324,0.02425392,0.01324264,0.00269251,-0.01469849,-0.04682186,0.02744061,-0.15387844,-0.01867755,0.01753178,-0.0651147,-0.02318119,-0.01178289,-0.00917577,0.00663309,0.03046682,0.04359876,0.02535213,0.03902155,0.01248302,0.00958759,0.00029473,0.0679086,0.04552793,-0.2119825,0.06770597,0.01200551,-0.00303639,0.07797254,0.01464503,0.03666827,0.11223564,-0.00098656,-0.01236979,-0.01762211,0.0719846,0.03091424,-0.00245817,0.0054514,-0.05936044,-0.06222893,0.02219655,-0.0285139,0.02513908,-0.05413332,0.01517672,-0.09127754,0.02371552,-0.01073377,-0.00319513,0.05620632,-0.0019243,-0.04895697,0.01796575,0.00215557,0.01927561,-0.00478001,-0.0308014,0.00368301,0.03352218,0.00863617,0.15547548,0.01595815,0.01832933,0.02063,0.01761425,0.09035193,-0.01045169,-0.00755838,-0.07824983,0.01047074,-0.01987707,-0.02177336,-0.00403127,0.01552217,-0.08029996,-0.03800262,0.07068026,-0.00848081,0.05568659,-0.02042723,0.01439201,0.00602731,-0.00049334,0.03346714,-0.02950354,0.02233708,-0.06087448,0.05089878,0.0429652,0.05467352,-0.00054061,0.0470493,0.00508144,-0.05128586,-0.0274071,-0.05647086,-0.05899193,0.01937946,0.00581275,-0.06974734,-0.01726314,-0.02461175,-0.00736873,0.00827972,-0.0820571,-0.05450087,0.12084136,0.04004268,-0.02312696,-0.01013439,-0.01292028,-0.04691471,0.01158702,0.03028328,-0.09004758,-0.04472996,0.05669732,0.03315333,0.07706495,-0.10523021,0.05457999,-0.00705479,-0.02875614,0.00290905,0.16012667,-0.00045789,-0.14187101,0.01222767,0.02716023,0.02853658,-0.06417842,-0.0282581,0.04895642,-0.06073199,0.00477562,0.09829194,-0.00242403,0.04829569,0.00267573,-0.02910467,0.05032894,0.00765696,-0.02157661,0.05034198,0.03438678,0.02645807,-0.02155843,-0.02419937,0.01971836,-0.04050139,0.00511275,0.00813075,0.03216979,0.02852837,-0.0233735,0.00379122,-0.05110595,-0.03966253,-0.03186106,-0.05196319,-0.00221172,0.06892809,0.00090446,-0.07499347,0.01116466,-0.06635865,0.00188322,-0.03513542,-0.0096229,-0.06992562,0.01621232,-0.04163638,0.03432468,0.03691423,0.04129135,-0.06153455,0.04405195,0.00307584,0.09339997,0.01186263,-0.03486639,-0.04065174,0.05336506,-0.01962419,-0.24129657,0.03528235,0.02287935,-0.05200576,0.01920033,0.04343439,0.01321344,0.0084387,0.01637207,-0.01194328,0.07335597,0.00772297,-0.06150793,0.01399498,0.05195197,0.02263986,-0.00550966,-0.00876223,-0.01036162,-0.01116324,0.01169375,0.03890073,-0.02482277,-0.05744806,0.0225687,0.07010413,0.10549463,0.02314202,0.0428246,-0.01300314,0.05678368,0.01710392,-0.01692825,-0.03336677,0.03226949,-0.02616147,0.00058261,0.02279781,0.03703376,-0.06267647,-0.02942764,0.01909195,-0.00183023,-0.04915481,-0.00268359,0.01311623,-0.05096488,-0.05142496,-0.07190034,-0.00555675,-0.00251808,-0.04704695,0.00978448,0.0464076,-0.00170605,0.00754647,-0.023694,-0.00948204,-0.02341902,-0.03234101,-0.02098026,0.01322951,0.01333661,-0.03311439,0.07294659,-0.0486163,0.04650829,0.01782958,0.04421985,-0.06480874,-0.00307094,0.07367993,-0.06516665,-0.02873237,0.05654353,0.00424956,0.01126133,-0.01581333,-0.07464903,0.04959426,0.06359282,-0.05079664,0.01880046,0.04986518,-0.05692468,0.09144736,0.07427105,-0.07674444,0.03502902,-0.06997669,0.01364457,0.01356831,-0.08453582,0.03041747,0.03543485,0.07794046,-0.25438863,0.02923872,0.00075967,0.05537349,-0.09619602,-0.01563953,0.03118159,-0.01286453,-0.06289241,0.03174987,0.10676923,0.04754689,-0.03078454,0.0283983,0.00652337,0.0638048,0.10588164,-0.03982295,0.0419163,-0.07101877,0.06115126,-0.00624323,0.21312757,0.06024605,0.02630751,0.0396839,0.01141398,0.03363316,-0.01050594,-0.00842261,0.02141075,-0.00803978,0.08166643,-0.00252144,-0.00667295,-0.02329775,0.0513939,0.03639571,0.05275809,0.02589226,-0.05970669,0.02630315,-0.01288477,0.00545113,0.09384283,0.02139529,-0.0292128,-0.10388555,-0.00971858,0.05304416,-0.02408093,-0.03951285,-0.0199619,0.03162467,-0.00088676,0.08084183,-0.04522334,-0.00190156,0.02685534,0.01505445,0.04434287,-0.05970049,0.01129224,0.00941063,-0.05835872],"last_embed":{"hash":"06b0df6ef730ddf6fae5aac06587201147d8079abdabad95e43cdf386e3767f6","tokens":75}}},"text":null,"length":0,"last_read":{"hash":"06b0df6ef730ddf6fae5aac06587201147d8079abdabad95e43cdf386e3767f6","at":1743662877683},"key":"Microservices.md#1. Resources#1.1. Distributed transaction#{11}","lines":[74,77],"size":247,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01553978,0.02683958,0.01504349,-0.00878493,-0.00074075,-0.02751858,0.02922434,0.00954771,0.00370544,0.00465378,-0.02164258,-0.11269446,0.03792842,0.04301037,0.04872634,-0.01478401,0.02259457,-0.03033552,-0.00181361,-0.01873127,0.10603973,-0.04873091,-0.02894837,-0.00970724,0.04950112,-0.01488664,-0.02596026,-0.01876636,-0.04138451,-0.18092047,0.00536729,0.02993563,-0.06248071,0.01177491,0.06900895,-0.01277933,-0.0009966,0.00075966,-0.07144866,0.01277508,0.06077084,-0.01049926,0.01428856,-0.05604668,0.02204566,-0.07322437,0.00274328,-0.01902522,-0.01014656,-0.02516823,-0.04838033,0.02793748,-0.01082923,0.02716151,0.01424407,-0.00290809,0.03456172,0.04998403,-0.01492149,-0.01239607,0.09037733,0.06486895,-0.23618633,0.06695286,-0.00026559,0.01260144,0.07017494,0.01771491,0.05300676,0.10654312,-0.02007884,-0.0111991,-0.01048259,0.05851867,-0.00464457,-0.01078438,0.03155801,-0.02358774,-0.09411752,0.01866298,-0.04825784,0.06069075,0.00096003,-0.01859751,-0.07373753,-0.02435132,-0.02431098,-0.03219962,0.05987017,0.01698009,0.01753303,0.04654226,0.07662401,0.00388894,-0.03487518,-0.05939034,0.02786252,0.02953425,-0.07050221,0.11265331,-0.01549045,0.00163792,-0.01181989,-0.03801038,0.04907395,0.02663318,0.00584098,-0.09085269,0.01516592,0.05606261,-0.06334013,0.01137987,0.0184122,-0.0519998,-0.01675306,0.03207174,0.01375764,0.02207554,0.02500063,0.01271617,-0.04922818,-0.00070952,0.05228298,-0.06931202,0.02476852,-0.03954441,0.04032056,0.03628799,0.0337406,0.03206871,0.04757301,-0.01402134,-0.01328514,-0.05710188,-0.01445024,-0.07625156,-0.01583987,-0.00993655,-0.0373748,-0.08363645,-0.03105711,-0.10070954,0.02837355,-0.04092922,-0.07675925,-0.00398978,-0.01257836,-0.01401071,-0.04904204,-0.03260808,-0.02629705,0.03041469,0.02027609,-0.02499309,-0.00701356,0.04521557,0.04508709,0.13906768,-0.03633298,0.0344879,0.03917716,-0.10479239,-0.02559062,0.07648583,-0.02910222,-0.12329657,-0.01896547,0.01699767,0.03372339,-0.07876473,0.02426106,0.02671788,-0.09297936,-0.02816518,0.04082654,-0.04186332,0.01818364,-0.03904311,-0.04279387,0.00536433,-0.05968664,-0.01660651,0.06426266,0.04831975,0.0029569,-0.0740784,0.01731481,0.00744962,0.01854158,-0.0044414,-0.06086798,0.0541599,-0.00557627,-0.06558873,0.00317688,-0.03909447,0.00276259,-0.01681212,-0.00612184,-0.04859395,0.07335212,-0.05434562,-0.07584421,0.02457625,-0.02631076,-0.02919476,-0.02686094,0.00058398,0.07374544,0.03077983,-0.02354546,0.05896836,0.04614304,0.02262747,-0.03827808,0.0354765,0.0680891,0.0966875,-0.01541929,-0.0085203,-0.00746253,-0.01067402,-0.07661442,-0.18707506,0.02071227,0.06789777,-0.01409739,0.02926159,-0.01888809,0.02900782,0.05898456,0.07799781,0.06086579,0.05946591,0.02741431,-0.04337483,0.04806925,0.02643739,0.08730493,-0.00225395,0.02077327,0.06438281,0.0105864,0.01188295,0.04978172,-0.03251956,-0.05576686,0.02649066,0.05535565,0.10976394,-0.01199177,0.00762406,0.03304299,0.03147986,-0.01545797,0.01674144,-0.0769728,0.12634878,-0.00353734,-0.01881386,0.0422288,0.00854131,-0.01302866,-0.00701305,0.01520244,-0.04270019,-0.12590967,-0.02961909,-0.01143077,-0.05943465,-0.03891914,-0.10816539,-0.0412576,0.00213007,-0.01627559,0.00445324,0.04424287,0.02227084,-0.02056447,0.01295511,-0.01906938,-0.00748709,-0.0152679,-0.02587901,0.03328127,0.02172794,-0.01854674,-0.01205301,-0.0650419,0.0058264,0.02908368,0.01071363,0.03204285,-0.03415476,0.09430598,-0.02616199,0.01976825,0.05356656,-0.0252561,-0.01245359,-0.02861129,-0.05238196,-0.00343833,0.05272052,-0.03955948,0.05399523,0.00948138,-0.01451957,0.0334055,0.06000202,-0.00372713,0.04184416,-0.01952948,-0.01563083,0.02112839,0.00015771,-0.00087604,0.06182418,0.0464119,-0.27067485,0.05731976,-0.0483604,0.02524242,-0.01509795,0.0054133,-0.01155588,0.06924126,-0.05777355,0.03025285,0.07639593,0.05000804,0.00969566,-0.05774778,0.00144515,-0.02145169,0.01325073,-0.05761071,0.05147737,0.01790246,-0.00631797,0.00332894,0.22037314,0.02348679,0.03350761,0.0254624,0.05901276,-0.01077259,0.02893744,-0.02651616,0.02961972,0.02076269,0.11988864,-0.05440633,0.02376972,0.04913245,-0.04192708,-0.0018669,0.03114606,0.01568102,-0.04761985,0.03793319,-0.03790795,-0.02792537,0.11790158,-0.05481,-0.0699768,-0.09867359,0.05166084,0.02688666,-0.00797674,-0.03662707,-0.02141615,0.06808065,-0.00193698,0.08610104,-0.04616686,-0.01786414,0.01340331,-0.02556935,0.01777754,-0.02541883,0.03299796,0.02245961,0.02836721],"last_embed":{"hash":"c9d250b26ccb0c7feb551422ec6b69086d9a9fe86900be27a2967de0c08f54f6","tokens":467}}},"text":null,"length":0,"last_read":{"hash":"c9d250b26ccb0c7feb551422ec6b69086d9a9fe86900be27a2967de0c08f54f6","at":1743662877698},"key":"Microservices.md#1. Resources#1.2. Outbox pattern","lines":[82,98],"size":2129,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02378308,0.04961961,0.01889786,-0.0184083,0.00585126,-0.01606008,0.03355293,0.0230791,-0.00760767,0.0035932,-0.02296977,-0.09016821,0.03944086,0.04154542,0.05401093,-0.01554868,0.00890213,-0.01775039,0.00311563,0.00632409,0.11608779,-0.03250775,-0.03014901,-0.01965011,0.02880468,0.00114741,-0.03647412,-0.01583381,-0.03557055,-0.18136643,-0.0220905,0.01578363,-0.05927796,0.02249534,0.07774495,-0.01792754,0.00857033,0.00001479,-0.06626793,0.02031466,0.04233724,-0.03044725,0.02507707,-0.04288081,0.01731337,-0.07687304,-0.00886156,-0.01618197,-0.00901495,-0.03280122,-0.03774288,0.02070517,-0.01991208,0.0351627,0.011375,0.00451682,0.03711611,0.03822903,-0.0104587,-0.00380127,0.09039322,0.06804255,-0.2445102,0.0749411,0.00131009,0.03059417,0.06862657,0.01162133,0.05006623,0.10274147,-0.00773735,-0.0094331,-0.0144674,0.07422458,-0.00207084,-0.00179944,0.02185276,-0.02979026,-0.08595445,0.03593759,-0.03705544,0.0568084,-0.00251813,-0.01156313,-0.0775575,-0.02032727,-0.03161214,-0.04482529,0.04827143,0.00837835,0.00577989,0.05314486,0.07011346,-0.00479025,-0.04706786,-0.05017808,0.03001005,0.00933159,-0.06779246,0.1214688,-0.02590619,-0.01010485,-0.02110015,-0.04186041,0.04649848,0.03613611,0.00869051,-0.09570817,0.00039328,0.05783331,-0.05907604,0.00763333,0.00567303,-0.03239638,-0.00260715,0.03170412,0.01700337,0.04020929,0.01374975,0.00981719,-0.03402088,-0.00250254,0.05631854,-0.0731499,0.04176303,-0.03748696,0.04636335,0.02654641,0.0260789,0.02022762,0.04702462,0.01052042,-0.00342115,-0.05833617,-0.00943391,-0.0842283,-0.02423225,-0.01526446,-0.03738892,-0.08696298,-0.0327291,-0.09428018,0.02694293,-0.02856999,-0.07029447,-0.00152456,-0.01824595,-0.03142008,-0.05124485,-0.03145038,-0.02088979,0.02076317,0.00845289,-0.02014282,-0.02211609,0.04525357,0.02769941,0.14492252,-0.02772176,0.03525008,0.03325699,-0.10119615,-0.01642507,0.08877876,-0.03441462,-0.11628674,-0.01889225,0.02460301,0.03890593,-0.06533872,0.00814456,0.02105393,-0.07938509,-0.02533462,0.02215571,-0.03556702,0.01416539,-0.03763879,-0.0429915,0.00185868,-0.05925164,-0.00861941,0.08139101,0.05912318,-0.00194256,-0.07745242,0.02375833,-0.00092731,0.04153861,-0.00783337,-0.06939129,0.0547146,-0.0165846,-0.07627469,0.00929016,-0.03977837,0.0201648,-0.02051318,0.00035341,-0.05276106,0.05482464,-0.04631032,-0.07104699,0.02179937,-0.03400061,-0.03276634,-0.02527879,0.0041211,0.06084028,0.02618696,-0.03068515,0.05704065,0.05273448,0.00834327,-0.03942797,0.04938962,0.06273381,0.08436441,-0.03091699,-0.01437493,-0.00074493,-0.00860588,-0.06947421,-0.18693721,0.02933721,0.06289977,-0.02764952,0.02818314,-0.02684464,0.02530482,0.05501671,0.08339384,0.05603622,0.04940798,0.01572262,-0.040041,0.05526425,0.02262624,0.10266893,-0.01197523,0.0057191,0.05480242,0.02457407,0.00203121,0.03136773,-0.0254756,-0.05752505,0.03807782,0.07183982,0.11515602,-0.00718101,0.02111386,0.00668201,0.01866889,-0.02902238,0.02344112,-0.07578429,0.12775323,0.00299274,-0.02039598,0.06028691,0.01378238,-0.00801381,0.0041881,0.00922398,-0.04136748,-0.12089896,-0.02246695,-0.01185074,-0.06951276,-0.05075793,-0.11472367,-0.0413792,0.00587463,-0.01832039,0.00302469,0.0475742,0.01692405,0.00315545,0.00686828,-0.01943379,0.00256145,-0.01251046,-0.01492065,0.03456467,0.01902766,-0.03574445,0.00149911,-0.07728725,-0.0021155,0.03074991,0.01978827,0.03321175,-0.0216208,0.0970583,-0.03663689,0.023136,0.05156016,-0.03069077,-0.00140937,-0.020938,-0.04721954,-0.00431789,0.05017304,-0.02850726,0.05058993,-0.00410047,-0.01804211,0.02040812,0.05036511,0.02081235,0.05088065,-0.02485415,-0.02103673,0.03082065,0.00200855,-0.00615153,0.04408859,0.03655018,-0.26983225,0.04947934,-0.03915516,0.02082952,-0.01403177,0.01109267,-0.00226713,0.05683731,-0.04579691,0.02926279,0.08322047,0.0398174,0.0026271,-0.03443183,0.01118175,-0.00195615,0.01121787,-0.0694853,0.04228723,-0.00564561,-0.00518867,-0.00121039,0.22695617,0.01542381,0.02319048,0.02849064,0.08520526,-0.01594095,0.03402599,-0.04080438,0.03235641,0.00466815,0.11625284,-0.06252884,0.02298112,0.03482233,-0.03271249,0.0061623,0.02425437,0.01680038,-0.04223821,0.04254042,-0.02934915,-0.02973456,0.11834276,-0.03788623,-0.07742545,-0.0973662,0.06538485,0.01470304,-0.01556635,-0.039131,-0.00353549,0.07477619,0.00787359,0.09474107,-0.05436398,-0.0330699,0.00649134,-0.02246223,0.01270809,-0.02589665,0.03273765,0.01068691,0.02916234],"last_embed":{"hash":"376cdb1e9e855bcd3bf37c560ed9cf10224ee15d6fc96a7ee4cd813a346090ea","tokens":205}}},"text":null,"length":0,"last_read":{"hash":"376cdb1e9e855bcd3bf37c560ed9cf10224ee15d6fc96a7ee4cd813a346090ea","at":1743662877744},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#{1}","lines":[84,85],"size":553,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01917728,-0.01665581,0.02775297,-0.02037685,0.00637155,-0.03650159,0.05115342,-0.00274892,-0.00749991,0.01941464,-0.02323329,-0.1107211,0.03209508,0.03383563,0.0500625,-0.00248031,0.03037615,-0.05180243,0.00279432,-0.03472291,0.08950604,-0.05395407,-0.02428038,-0.00271005,0.06529973,-0.01484994,-0.0123882,-0.02960273,-0.04287267,-0.17824134,0.03999389,0.04839151,-0.03311357,-0.00455043,0.04340213,-0.02118902,-0.02321854,0.01010043,-0.06801593,0.01200726,0.0596733,0.03000831,0.00975946,-0.07260288,0.01491024,-0.05994078,0.02971415,-0.01665442,-0.01729526,0.00822679,-0.04882263,0.00287275,-0.0006438,0.01544135,0.03379057,-0.00392126,0.00765425,0.03129981,-0.03777291,0.0105441,0.06061469,0.06875821,-0.21881729,0.04388478,0.02723965,0.00515119,0.05245909,0.01020037,0.06526025,0.09869872,-0.03097015,-0.02950874,-0.00332447,0.0570526,-0.01032592,-0.01564564,0.04119556,-0.01803542,-0.09457577,0.02887183,-0.07298338,0.068366,0.00525183,-0.01930518,-0.0719499,-0.04872018,-0.02808721,-0.02119009,0.04721861,-0.00027239,0.04374172,0.03820568,0.07859576,0.01087389,-0.03342681,-0.03564361,0.01243453,0.05113535,-0.04394296,0.11265887,-0.02109388,0.00430366,-0.02199835,-0.03986081,0.06808548,0.01226978,-0.00238211,-0.08521178,0.01144779,0.04383692,-0.05906688,0.01488501,0.04727409,-0.03395346,-0.00468599,0.03522003,0.00693428,-0.00691948,0.03264858,0.01747456,-0.0618086,-0.02174176,0.06437299,-0.05659913,0.02067482,-0.03366822,0.02456898,0.04869438,0.04209657,0.02189004,0.04976705,-0.02549805,-0.01942245,-0.02907961,0.00152535,-0.04897573,0.00474906,0.02544227,-0.03276792,-0.0757827,-0.0325119,-0.08842132,0.00831458,-0.0514194,-0.07585462,0.00802393,-0.01139646,-0.01084973,-0.01275637,-0.0261511,-0.03711296,0.04455924,-0.00593844,-0.02654502,0.00132284,0.04631738,0.06666321,0.16025195,-0.03809872,0.03522278,0.00428593,-0.09868456,-0.02454808,0.09308938,-0.04117207,-0.11288221,-0.02516018,-0.00391756,0.03618928,-0.07815959,0.01982976,0.02951167,-0.09003146,-0.02560348,0.07770608,-0.06039181,0.02782322,-0.03133951,-0.03167405,0.00044073,-0.03741241,-0.05072753,0.01786491,0.03351987,0.01077839,-0.09575675,-0.00776136,0.02618939,0.01266229,0.01357819,-0.0624758,0.0420916,-0.01070121,-0.03167073,0.01313985,-0.05021453,0.00331226,0.00009349,0.00143878,-0.04316196,0.07767645,-0.04647617,-0.07332002,0.01119868,-0.02948491,-0.03169805,-0.00529404,-0.02693398,0.07827605,0.00258635,-0.02352921,0.05276239,0.03358293,0.05574908,-0.04105356,0.00552879,0.04758728,0.10781771,-0.00282436,-0.00853079,-0.01891032,-0.02466687,-0.07229026,-0.18934496,0.01720515,0.06549484,-0.01935601,0.00424703,0.01502889,0.04297094,0.05592107,0.09027896,0.06807546,0.05186493,0.01251287,-0.05602498,0.02688516,0.03233248,0.04992031,0.02583933,0.01268615,0.05348416,0.01227016,0.03327056,0.05915925,-0.0468733,-0.04624925,0.0271286,0.01202442,0.11724751,0.00256945,-0.00733296,0.0460056,0.02968665,-0.00038827,0.03412392,-0.08289625,0.09890279,0.00228658,-0.00718724,0.0246416,0.00118374,-0.00506406,-0.02481244,0.00409314,-0.01182391,-0.12451001,-0.02440734,0.00838692,-0.05877561,-0.03137795,-0.11508893,-0.0284819,0.00774875,-0.0225013,-0.00895357,0.05208664,0.02055796,-0.05273899,0.00326506,-0.01832999,-0.01445824,-0.00537936,-0.0310053,0.02926555,-0.00595979,0.00617473,-0.02343447,-0.02302321,0.02639643,-0.00586015,0.02156629,0.01494012,-0.02937295,0.0955298,-0.01456209,-0.01675768,0.05310294,-0.03712709,-0.00603414,-0.02347649,-0.08018786,-0.0066538,0.06942242,-0.02767805,0.05933011,0.0150071,-0.00955371,0.04932234,0.06800669,-0.02134115,0.01227345,-0.01704533,0.00302431,0.01889098,-0.01055283,0.00048837,0.06015965,0.07174559,-0.27642405,0.06397036,-0.03469104,0.0558837,-0.02850109,-0.02416667,0.00644087,0.06625833,-0.07120974,0.00413856,0.0397437,0.03636787,0.0099264,-0.0662701,-0.00786062,-0.02564167,0.01907186,-0.04057525,0.06952633,0.02877875,-0.00286331,0.00850182,0.21155952,0.00720433,0.04703075,-0.00175124,0.05279982,-0.00528017,0.03372932,-0.00628389,0.07364985,0.01594876,0.1387008,-0.06665494,0.04411451,0.01518831,-0.03593329,0.00990282,0.05151473,0.00909477,-0.06310626,0.00346249,-0.05853408,-0.02432173,0.12041468,-0.07767248,-0.06733637,-0.07430381,0.06003602,0.03523932,-0.00866858,-0.03957505,-0.04344175,0.07718274,-0.02705432,0.09334623,-0.05359898,-0.00875126,0.02935437,0.00399584,0.01617764,-0.04676865,0.03550538,0.03373282,0.0050529],"last_embed":{"hash":"ca6e671205e57c1fdb6a2161c2abdc2aa2c0285795d98804381c45486f0a67ec","tokens":308}}},"text":null,"length":0,"last_read":{"hash":"ca6e671205e57c1fdb6a2161c2abdc2aa2c0285795d98804381c45486f0a67ec","at":1743662877762},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG","lines":[86,90],"size":790,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01490929,-0.01084981,0.03536911,-0.01909398,-0.00033125,-0.03669193,0.07250392,-0.0052925,-0.00791742,0.02222798,-0.02221321,-0.12197959,0.02796379,0.04420758,0.05004326,-0.00936071,0.02806954,-0.04699101,-0.00302737,-0.03709001,0.09609711,-0.04409435,-0.03204255,-0.00191052,0.05695112,-0.01383956,-0.00579358,-0.0307727,-0.02309996,-0.16556886,0.03971748,0.052728,-0.02911951,-0.0254575,0.03292284,-0.02112232,-0.02798359,0.01355662,-0.06672971,0.00782345,0.06549714,0.03660022,0.02183614,-0.07091571,0.0109577,-0.06236022,0.02141239,-0.00634987,-0.01329943,0.00565929,-0.02778335,0.00786153,0.00113473,0.01765453,0.01891439,-0.00565152,0.01704638,0.01775167,-0.03353583,0.00588687,0.05410119,0.07068492,-0.2102948,0.03530347,0.02583389,0.00117302,0.05832986,0.0198974,0.06939371,0.09988009,-0.02867717,-0.02211827,0.00635758,0.04909619,-0.00909263,-0.01030045,0.04691828,-0.01742258,-0.10099611,0.03158047,-0.08851851,0.07044818,0.00385029,-0.02850845,-0.07298071,-0.05334876,-0.02058051,-0.01742588,0.04724472,-0.00044989,0.04251596,0.02994261,0.0775362,0.01099693,-0.03911663,-0.03330762,0.00969102,0.04922235,-0.04644575,0.12495276,-0.03531288,0.00077019,-0.0239415,-0.05181239,0.07272589,0.00623995,-0.00189032,-0.07027993,0.0107384,0.02825716,-0.06031084,0.02934598,0.04361548,-0.04378163,-0.00769859,0.03435229,0.01393918,0.00167809,0.04215191,0.00728781,-0.06124123,-0.02653633,0.07015557,-0.05607594,0.01027251,-0.03610418,0.02111783,0.04740116,0.04233016,0.02432219,0.04488221,-0.03438402,0.00907269,-0.03108476,0.00524781,-0.03972036,0.00546825,0.03136666,-0.0090797,-0.07634314,-0.03041808,-0.08316715,0.00070069,-0.0516111,-0.06255423,-0.00453445,-0.0081941,-0.00884388,-0.01199666,-0.02880849,-0.01902677,0.03404536,-0.01226507,-0.01768695,-0.00393216,0.05276769,0.05426795,0.15610184,-0.03221608,0.0392397,0.00441358,-0.09500092,-0.02171828,0.10366531,-0.05220302,-0.09885561,-0.0353904,-0.01387078,0.03543164,-0.0706135,0.023856,0.03195395,-0.07836131,-0.01235653,0.07405319,-0.05583532,0.03353987,-0.03270699,-0.04357233,0.00879748,-0.01526226,-0.03362715,0.01096657,0.03656697,0.00988341,-0.09364025,-0.02049378,0.02544916,0.01869186,0.01301727,-0.0531943,0.04962265,-0.0063479,-0.04191278,0.02527326,-0.03375746,-0.01588245,-0.00040432,0.00955743,-0.04225539,0.08966544,-0.04912051,-0.07379401,0.01505544,-0.01528609,-0.03776022,0.00670504,-0.02698713,0.08065271,-0.0038785,-0.02822484,0.04699603,0.02873926,0.06591209,-0.02645574,0.01038502,0.05012177,0.10781478,-0.01291668,-0.01660817,-0.01679092,-0.04197288,-0.08361212,-0.1971546,0.0246451,0.06451502,-0.02321932,-0.00784975,0.00291747,0.05042428,0.04864465,0.0901814,0.04699601,0.04674443,0.00775264,-0.06203873,0.02422212,0.02860405,0.05467578,0.01928957,0.0084536,0.04982612,0.02449636,0.02946129,0.05128176,-0.05127974,-0.03388571,0.02465874,0.00748834,0.11329251,0.01678187,-0.00684605,0.03875487,0.02367024,0.01259448,0.03448877,-0.08412621,0.081496,-0.00534701,-0.01332796,0.02366095,0.01114604,0.00384881,-0.02027206,0.00493221,-0.01789058,-0.10566957,-0.01425729,0.00657326,-0.05563958,-0.03982121,-0.10224964,-0.02674586,0.00173504,-0.01801339,-0.00672836,0.05927899,0.01208396,-0.06764103,0.01232219,-0.01850031,-0.01914501,-0.01868582,-0.02820351,0.03246075,-0.00896079,-0.00148418,-0.03074127,-0.02821778,0.02196147,-0.00374913,0.04031852,0.02275539,-0.03601652,0.09799609,-0.03686819,-0.0318849,0.05403232,-0.03926388,0.00414817,-0.02278968,-0.08984786,-0.02076106,0.07315111,-0.00910979,0.0719556,-0.0003846,-0.02555531,0.05403592,0.05273551,-0.03184121,0.01212217,-0.01714451,0.01565013,0.02282528,-0.01285032,-0.01196289,0.05340827,0.06503216,-0.27689174,0.07304034,-0.02787174,0.06656817,-0.03743571,-0.01929928,0.00869008,0.0701273,-0.07826737,0.00904304,0.05488365,0.0199939,0.00105081,-0.06836549,-0.01506059,-0.03703255,0.01750794,-0.05403564,0.07257197,0.01810187,-0.0085153,-0.00455749,0.21504214,0.0185555,0.04597699,-0.01076424,0.05278204,-0.01371269,0.02556192,-0.00996083,0.08030929,0.01572344,0.14216462,-0.05741875,0.05203092,0.01233476,-0.03872361,0.00866787,0.05329249,0.00865826,-0.05911009,-0.00085226,-0.0746804,-0.02511857,0.11552367,-0.08134937,-0.08480927,-0.06336574,0.05003203,0.03429086,-0.00025922,-0.03926168,-0.03508625,0.07738394,-0.03175854,0.0937925,-0.03408389,-0.02065743,0.04101146,-0.00094085,0.0265863,-0.04016397,0.03087692,0.03608847,0.0039948],"last_embed":{"hash":"8a4b8c0883aad07b3b0069472af2fb12ba911ad9485f8e96cafd64bd974f4957","tokens":147}}},"text":null,"length":0,"last_read":{"hash":"8a4b8c0883aad07b3b0069472af2fb12ba911ad9485f8e96cafd64bd974f4957","at":1743662877791},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{1}","lines":[88,88],"size":317,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02594422,-0.0193085,0.01520503,-0.03082268,0.02567374,-0.03945387,0.03924961,0.01941045,-0.01648428,0.0209594,-0.01589391,-0.08689813,0.03827315,0.02653585,0.05333378,0.01298069,0.03658052,-0.0314775,0.01546476,-0.02326971,0.08606694,-0.03968448,-0.0061713,-0.02763024,0.0423073,-0.01484322,-0.02760536,-0.03322252,-0.05949332,-0.18516195,0.02308698,0.03659771,-0.02715168,0.02044677,0.06061185,-0.01539797,-0.02194987,0.01565595,-0.05610044,0.01769298,0.04025474,0.00830052,0.00766544,-0.07276208,0.00973166,-0.03675921,0.03601789,-0.03285887,-0.02865501,0.00483937,-0.06863132,0.00539599,-0.01437726,0.02961518,0.04376871,-0.00947615,0.01221476,0.034052,-0.0385676,0.03667887,0.0606588,0.07315239,-0.23022279,0.04489698,0.01819695,0.02440678,0.04336622,-0.00656106,0.04546745,0.10530648,-0.02189002,-0.06080853,-0.00789757,0.07243249,0.00183916,-0.00602782,0.01696876,-0.03476725,-0.07820823,0.04678817,-0.05610577,0.05123672,-0.00053577,-0.00777848,-0.06630787,-0.03689716,-0.04835717,-0.03004032,0.03897395,-0.00940579,0.04712659,0.04898886,0.06745758,0.01318962,-0.0557791,-0.04953872,0.02595041,0.04096117,-0.03286636,0.12159591,-0.01201338,0.00213994,-0.02484553,-0.02859842,0.06354902,0.01977787,-0.00251349,-0.10406893,0.0034498,0.05085208,-0.04722434,0.00244769,0.0471878,-0.0159749,-0.00371644,0.03100091,-0.01096073,-0.00735281,0.01682227,0.03987039,-0.04617314,-0.0271198,0.05306796,-0.05220997,0.04861634,-0.01731198,0.03054419,0.0381766,0.03881157,0.00670579,0.05248665,-0.0067841,-0.05279261,-0.0319621,0.01323302,-0.06156367,0.01482708,0.02485855,-0.04857308,-0.09061522,-0.01569998,-0.09085417,0.01818977,-0.05504896,-0.078649,0.02819719,-0.00606239,-0.00321985,-0.01831272,-0.03467112,-0.04974557,0.05919631,0.00087043,-0.04566792,-0.00576762,0.0479789,0.07144872,0.14981185,-0.03968585,0.04687063,-0.00744255,-0.09816978,-0.01824809,0.09452052,-0.03481362,-0.11495538,-0.02233034,0.00221219,0.03749864,-0.07314649,0.00609291,0.01531608,-0.08990161,-0.01763087,0.08185571,-0.06305145,0.02744472,-0.03452586,-0.01002621,0.00048155,-0.03869012,-0.06618021,0.01948277,0.0377686,-0.0042798,-0.11121755,-0.00827316,0.02041561,0.01307135,0.01975754,-0.07769402,0.02533745,-0.02917106,-0.02885876,0.01704015,-0.05056153,0.02235692,0.01335265,0.00702104,-0.07417644,0.07546449,-0.02468982,-0.07583752,0.01433582,-0.04944845,-0.01068201,0.0064192,-0.0396518,0.0470149,0.01498365,-0.02680688,0.03777083,0.02906075,0.02400371,-0.04478066,0.01193217,0.03628201,0.09045064,0.01225037,-0.00064278,-0.01168789,0.00282913,-0.05935301,-0.19346038,0.01888469,0.05010021,-0.03305446,0.00781997,0.02008168,0.03793737,0.04668062,0.08420081,0.0777463,0.04742416,0.0111118,-0.04652078,0.02917513,0.04334626,0.05741496,0.03364702,0.01975785,0.04833011,0.00060834,0.02141332,0.06417601,-0.02509574,-0.06084994,0.05235264,0.016944,0.12517852,-0.00345265,-0.00134099,0.040408,-0.00111983,-0.0273062,0.03197194,-0.08294144,0.09715693,-0.0048066,-0.00424774,0.01133318,0.00275008,-0.00707559,-0.0269432,-0.00027523,-0.01905679,-0.12338578,-0.02788755,0.01632196,-0.07794767,-0.03166861,-0.12053026,-0.01732806,0.00722834,-0.02046981,-0.01621,0.05121462,0.03815695,-0.02634929,0.00269815,-0.02584173,-0.00715126,0.00757349,-0.02548753,0.02138047,-0.01707372,-0.00976962,-0.01275944,-0.01578022,-0.00001213,0.00873328,0.00267724,-0.00375242,-0.02267041,0.08752201,0.01029413,-0.01089088,0.04161873,-0.03073818,-0.01330622,-0.02546817,-0.06131746,0.00825794,0.07048884,-0.0488246,0.04603655,0.02157255,-0.0021179,0.05053375,0.07923068,0.00561618,0.02548052,-0.0207924,-0.00784087,0.02191313,-0.00459091,0.0244284,0.05745716,0.05297653,-0.27880791,0.06649865,-0.02687245,0.05275463,-0.01646297,-0.03645235,0.01709717,0.06987812,-0.06782769,-0.01433607,0.03079255,0.03790538,0.01015284,-0.04909375,-0.00052031,-0.01101218,0.01910987,-0.04065983,0.07992516,0.01924104,0.01204243,0.02275096,0.21625665,-0.01792581,0.04824095,0.00469462,0.07226641,-0.00794399,0.02990237,-0.00827277,0.07580565,0.00934348,0.11697823,-0.06126764,0.02964803,0.00046963,-0.02147584,0.0106783,0.03772635,-0.00360899,-0.05945212,-0.01270987,-0.04043827,-0.01752768,0.12788248,-0.0465063,-0.049645,-0.08290957,0.07220439,0.03615111,-0.01198235,-0.03701547,-0.0436094,0.07956372,-0.02810643,0.11715428,-0.06041175,-0.00587843,0.00906202,-0.00578322,0.01254206,-0.0307002,0.04002922,0.03155037,0.02036198],"last_embed":{"hash":"1d9349e2e8a529677daa651a91dbba801fc8746e1ea1aa95ba8acff07579b5e4","tokens":193}}},"text":null,"length":0,"last_read":{"hash":"1d9349e2e8a529677daa651a91dbba801fc8746e1ea1aa95ba8acff07579b5e4","at":1743662877808},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#1.2.1. CÁCH Outbox Pattern HOẠT ĐỘNG#{2}","lines":[89,90],"size":430,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00968117,0.02070492,0.01104486,-0.01812932,0.01197806,-0.0226492,0.05995039,0.01441767,0.02852461,0.00734713,-0.02895149,-0.09544875,0.01994341,0.03046723,0.0732758,-0.00205975,0.03561266,-0.0231491,-0.01734715,-0.01664785,0.10240637,-0.02993492,-0.00501681,-0.01382875,0.06696191,-0.02853443,-0.04833082,-0.04293855,-0.04739654,-0.17927904,-0.01625246,0.05148956,-0.01856283,0.00958324,0.0596386,0.00011996,-0.03841869,-0.00839153,-0.0621528,0.01285215,0.04222074,0.00342651,0.03047117,-0.03885601,0.01525337,-0.06170663,-0.01203929,-0.00831725,-0.01216809,-0.03588724,-0.03665819,0.00331407,0.00185455,0.04492261,0.01584094,0.02694549,0.03119545,0.01787375,0.00227901,-0.00756054,0.07011004,0.06323534,-0.24223679,0.0585179,0.01287175,0.02309139,0.0595528,0.0073866,0.04933351,0.10400809,-0.0279556,-0.0007527,-0.02925745,0.06464633,0.02715689,0.0070959,0.05486839,0.00198347,-0.09548689,0.04090502,-0.0500591,0.08852976,-0.01954734,-0.0243912,-0.09509484,-0.02874296,-0.02713501,-0.06046068,0.05408028,-0.00782917,0.02312942,0.03570352,0.08168648,0.00983756,-0.04318959,-0.04726389,0.00415873,0.02326116,-0.08105329,0.12476123,-0.03230824,-0.01537388,-0.00466599,-0.05672744,0.04941327,0.02771929,0.01834231,-0.08324534,-0.00570384,0.04197544,-0.07741296,0.02303374,0.0385669,-0.05543722,-0.02588635,0.03736085,0.02177176,-0.03644852,0.03894564,0.01853232,-0.06209594,-0.02300289,0.04957786,-0.06445482,0.04666935,-0.04842047,0.04404011,0.03808549,0.03572045,0.03746784,0.04209054,0.00955103,-0.00052637,-0.03296665,-0.00452129,-0.06458037,-0.01084714,0.03433777,-0.03928069,-0.06421518,-0.03432997,-0.08015203,0.00852657,-0.04521194,-0.04864422,0.0210835,-0.00398462,-0.01951784,-0.01706985,-0.02079208,-0.02195369,0.02348404,-0.02407758,-0.03705786,-0.01180054,0.04543867,0.04829719,0.16389787,-0.0332394,0.03107213,0.01712445,-0.11237203,-0.01509773,0.09419864,-0.04797319,-0.1218848,-0.03578744,0.00316381,0.02955651,-0.08038688,0.04000411,0.03630201,-0.05755445,-0.01255019,0.03205924,-0.03941936,0.02905367,-0.04940517,-0.02735107,0.01073145,-0.03107038,-0.02705612,0.05068657,0.0612539,-0.01551076,-0.10112621,-0.01182436,0.02412698,0.02242644,-0.02911524,-0.08396014,0.03486375,-0.03265034,-0.02909323,0.00232154,-0.03785275,0.00831882,-0.02033769,0.01610182,-0.05851626,0.07186963,-0.03794381,-0.0784414,0.03092919,-0.0387354,-0.03901749,0.00575848,-0.01932035,0.06513816,0.02512597,-0.02288778,0.05163678,0.03952608,0.03622714,-0.01814303,0.0325904,0.07262123,0.0834647,-0.00559289,-0.00544621,-0.02706521,-0.00533055,-0.07680047,-0.18673715,0.03699041,0.05240099,-0.02094048,0.01684141,-0.02920084,0.03534864,0.05537234,0.10124162,0.04769653,0.06937605,-0.01235157,-0.06394558,0.02659568,0.03365803,0.06539061,-0.01639957,-0.00520897,0.03231839,0.03092995,0.0134495,0.04109572,-0.03713528,-0.0525226,0.0544419,0.03166084,0.11306028,0.0446912,0.00861447,0.02295695,0.00877808,0.01438941,0.02182228,-0.08403364,0.10627835,-0.01562503,-0.01703537,0.04570355,0.02012696,-0.00380604,-0.02512522,0.01691716,-0.01777245,-0.0942494,-0.03754067,0.01285257,-0.06073054,-0.03829831,-0.11699928,-0.01639559,0.01012822,-0.04043377,0.00931925,0.04343538,0.03510509,-0.04656914,-0.00540842,-0.02723296,-0.00652867,-0.00322998,-0.0375579,0.02684323,-0.00139731,-0.00804297,-0.01412201,-0.06016304,0.01337754,0.02444009,0.02981829,0.02737979,-0.0240297,0.12622146,-0.04315737,-0.01130378,0.05608309,-0.01164191,-0.00039156,-0.03286852,-0.06967602,0.0062842,0.04590556,-0.02122074,0.05542276,0.00340113,-0.02126734,0.03449781,0.06763645,-0.01170191,0.03982613,-0.0186121,0.00744256,0.03161294,-0.0119056,-0.00890687,0.0508939,0.01382431,-0.2559422,0.04202912,-0.0130043,0.03493018,-0.02391084,-0.02998539,0.01410492,0.0591991,-0.06083319,0.00715758,0.04286622,0.03174546,-0.00386786,-0.03988523,-0.00048984,-0.0107164,0.02557087,-0.0527945,0.06294346,0.01949198,0.01333539,-0.00137243,0.23273771,0.00978396,0.02689231,0.00340552,0.06374171,-0.00592996,0.06605888,-0.0074906,0.05931198,-0.01624847,0.11972544,-0.07803987,0.03552822,-0.01168619,-0.02807977,-0.00110392,0.03707313,0.02952249,-0.03269444,0.01318995,-0.04610642,-0.00643534,0.08954442,-0.06777641,-0.06014837,-0.08226787,0.05587043,0.0234119,-0.02166267,-0.02402473,-0.03750432,0.06652249,-0.01364035,0.09243556,-0.06157429,-0.03167177,0.01362526,-0.00020877,0.02659566,-0.04693975,0.02173177,0.03740093,0.00740984],"last_embed":{"hash":"1cc938c01fa0a6ff85b188b73a5bca5cd0c625544d270cced2d39945977f73d1","tokens":286}}},"text":null,"length":0,"last_read":{"hash":"1cc938c01fa0a6ff85b188b73a5bca5cd0c625544d270cced2d39945977f73d1","at":1743662877826},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern","lines":[91,98],"size":760,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01601401,-0.00422322,0.01711545,-0.02081527,0.00732574,-0.00904507,0.07208597,0.01524134,0.02367473,0.01308114,-0.02659598,-0.10081761,0.02608332,0.0312916,0.06423143,0.00160982,0.02800106,-0.02461809,-0.00836451,-0.03422295,0.12903735,-0.02365012,-0.00247692,-0.03341884,0.06417449,-0.01364585,-0.04637641,-0.05078768,-0.02820665,-0.18801662,-0.02471183,0.07961553,-0.03127653,0.00395348,0.04351386,0.0075965,-0.05186725,-0.0135684,-0.05610062,-0.01023674,0.02824943,0.01091115,0.02511305,-0.0336928,0.01601657,-0.05231202,-0.0185761,0.0076501,-0.01349833,-0.0408536,-0.03063176,0.00152853,0.00017932,0.04007434,0.04009354,0.01379995,0.05132137,-0.00574634,-0.00244063,-0.00506006,0.072599,0.07721946,-0.23678881,0.04365992,0.03687318,0.03156655,0.05248046,-0.00318516,0.05235118,0.07637367,-0.01289791,-0.00430299,-0.01102891,0.0516862,0.04444431,0.00617269,0.04160727,-0.02045091,-0.08140123,0.04748296,-0.07517503,0.08683998,-0.00482201,-0.02262174,-0.08064121,-0.04361257,-0.02434701,-0.03563807,0.04953531,-0.01370053,0.01330224,0.02558574,0.08376962,-0.01003404,-0.01891888,-0.04018331,-0.0109888,0.01852562,-0.05096106,0.14208931,-0.0405068,-0.01242459,-0.02624967,-0.05632924,0.04973857,0.02589527,-0.00481374,-0.08596247,-0.0071596,0.03848107,-0.0488877,0.02152606,0.02297018,-0.05607501,-0.01995279,0.03204985,0.03861578,-0.03468361,0.04722949,0.02858791,-0.06219876,-0.03557939,0.05005638,-0.05307536,0.01767656,-0.03421437,0.0462654,0.0405091,0.03653941,0.04208312,0.02650439,0.00033846,-0.00746038,-0.0341985,0.00472627,-0.04399506,-0.00733538,0.00696236,-0.04295142,-0.0611589,-0.04090993,-0.07754849,0.00452543,-0.03397783,-0.05148021,0.0504543,0.00886697,-0.01282456,-0.01421342,-0.02430066,-0.01258756,0.03297029,-0.02821625,-0.0360605,-0.00672295,0.04005345,0.04459424,0.13828687,-0.02244371,0.01958747,0.00165207,-0.09919778,-0.01327622,0.10069902,-0.06333876,-0.09967875,-0.02690269,-0.0044488,0.04484985,-0.06713818,0.03082365,0.03527506,-0.03439922,0.00679222,0.0526832,-0.03642567,0.03130585,-0.06523602,-0.0407266,0.02904547,-0.00769345,-0.03374745,0.03948079,0.06477944,-0.00512745,-0.09939682,-0.02165759,0.02436435,0.01940107,-0.01720955,-0.0690712,0.02323532,-0.03726577,-0.02291434,0.02003944,-0.03864655,-0.01275057,-0.0146439,0.01193856,-0.04841479,0.0939415,-0.01707292,-0.07677741,0.0445706,-0.00920045,-0.03423647,0.0108904,-0.03921666,0.06315417,0.02220805,-0.00620618,0.0546108,0.01775982,0.04519334,-0.02093167,0.0418032,0.06335746,0.08365359,0.00945314,-0.01082171,-0.02387247,-0.05070888,-0.10224333,-0.20052327,0.03502947,0.0506174,-0.02841244,0.0059992,-0.03473715,0.026918,0.0494489,0.09694908,0.04175694,0.061592,-0.02086046,-0.06922826,0.00541289,0.02057292,0.05753788,0.00328393,0.00579727,0.04532709,0.0359673,0.00991434,0.04225472,-0.0280053,-0.05448699,0.04806665,0.03035055,0.12444375,0.04653723,0.0079659,0.0224544,-0.00525447,0.01189563,0.00337829,-0.10238314,0.0911078,-0.02979737,-0.02218683,0.03198908,0.017091,-0.02518128,-0.02231188,0.01745203,-0.01605334,-0.0984491,-0.01706783,0.01447423,-0.05180011,-0.03545995,-0.10694772,0.00048191,0.01195893,-0.03468214,0.00545018,0.03600152,0.02276601,-0.03875869,0.00227038,-0.02975666,-0.0128366,-0.00609695,-0.04291037,0.02608316,-0.01559569,0.00484527,-0.02698375,-0.06194798,0.01018012,0.00321274,0.04405177,-0.00790304,-0.02033105,0.11889604,-0.03443385,-0.03042778,0.04881449,-0.01121517,0.0086941,-0.01256882,-0.08770797,-0.00091517,0.03132188,-0.03143038,0.0661469,-0.01617463,-0.03481875,0.06903396,0.07965819,-0.00810588,0.03580208,-0.01679708,0.02772854,0.05265585,-0.01440648,-0.01292587,0.04273516,0.00385802,-0.28172353,0.06148088,-0.00346074,0.03902853,-0.02580272,-0.02224434,0.03189104,0.06300978,-0.04187719,-0.01163539,0.03783756,0.02749289,0.0030235,-0.04171795,0.01351996,-0.00958929,0.03528773,-0.06068008,0.07688718,0.00209055,0.00311386,0.01520847,0.24397822,0.00332063,0.03673238,0.00731355,0.05370089,-0.02012081,0.05656045,-0.00375964,0.045432,-0.01855033,0.12702993,-0.07535879,0.04749292,0.00873038,-0.03223357,0.02082215,0.04137396,0.03257509,-0.02960992,-0.01456513,-0.06282189,-0.0075132,0.09426439,-0.06233444,-0.05973584,-0.05031354,0.03236222,0.02238816,-0.02556841,-0.00024703,-0.03394311,0.05352817,-0.01800358,0.08086043,-0.05263387,-0.03639123,0.02680856,0.00314668,0.03600301,-0.04104603,0.01298399,0.03951074,0.01435293],"last_embed":{"hash":"82a0d22c92f215a266b27e86dfca8ef68e24854ed6fd041f990c19d1b1f44a6a","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"82a0d22c92f215a266b27e86dfca8ef68e24854ed6fd041f990c19d1b1f44a6a","at":1743662877853},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{1}","lines":[93,93],"size":215,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02623402,0.02627809,0.02788948,-0.03283558,0.0116905,-0.03599178,0.05233486,0.00159408,0.02888953,0.0372029,-0.02256741,-0.07648373,0.03595728,0.03443307,0.09828959,-0.01599562,0.03151807,0.0063159,-0.020088,-0.00099602,0.12776057,-0.03836389,-0.02765765,-0.02762802,0.02179498,-0.01120433,-0.04342403,-0.0458445,-0.02991299,-0.1694209,-0.01344887,0.0428251,0.01236934,-0.00462955,0.06091695,-0.02519956,-0.02725341,0.0209129,-0.0462899,0.02453253,0.03563498,-0.00680208,0.00448703,-0.04908196,-0.00013926,-0.04021291,0.00187821,-0.00332881,-0.0170092,-0.03126741,-0.01157533,-0.00545084,-0.03067637,0.04281714,0.0109895,0.01988091,0.02428871,0.00331039,0.00267059,0.00558834,0.0470957,0.07032155,-0.23409207,0.04892774,-0.0036328,0.02506387,0.06621684,0.00172649,0.04266889,0.08716927,-0.02567033,-0.0167934,-0.00877289,0.07516622,0.03010288,0.02569851,0.02899638,-0.01277248,-0.0963474,0.05918042,-0.05253263,0.04449016,-0.0092684,-0.0052714,-0.0717209,-0.02505116,-0.05653997,-0.06251664,0.04249701,-0.00016589,0.00195633,0.03995194,0.0806979,0.00180361,-0.08423994,-0.05562179,0.02668958,0.02909764,-0.07939345,0.14176126,-0.0359983,-0.00987148,0.00472641,-0.05237286,0.06360801,0.02142305,0.0449365,-0.07550403,0.01093676,0.0465507,-0.07093851,0.02394943,0.05552415,-0.01063278,-0.01833202,0.03945354,0.000434,-0.01656963,0.02202916,0.04716326,-0.04422672,-0.01561661,0.05238867,-0.04268558,0.07742805,-0.03614533,0.03887213,0.02274934,0.0162277,0.0273674,0.03867401,0.02800996,-0.00469906,-0.05629298,-0.00595436,-0.08210497,0.0341008,0.06837068,-0.04377313,-0.08497044,-0.00808845,-0.08929533,0.01258387,-0.04789809,-0.02807483,-0.01211927,0.00325623,0.00831364,-0.00364137,-0.05441353,-0.02808507,0.02854476,-0.04778162,-0.04209441,-0.01910356,0.0355471,0.03318414,0.13558361,-0.02666803,0.06965107,-0.01261652,-0.10992942,-0.00767789,0.09150129,-0.05054884,-0.11260148,-0.04858422,0.00213817,0.04911789,-0.04313418,0.028785,0.02659607,-0.04862472,-0.00291476,0.04518633,-0.03575074,0.02742917,-0.03440064,-0.01542812,0.02442877,-0.01029327,-0.01602481,0.01557967,0.07563442,-0.01301928,-0.08692657,-0.00949122,-0.00425615,0.03255916,0.01870599,-0.10854257,0.02494462,-0.02338369,-0.06012326,0.03732815,-0.03509694,-0.00153667,-0.01925074,0.03349055,-0.09448732,0.07359899,-0.02055243,-0.07940754,0.02841161,-0.0241298,-0.02485276,0.01815066,-0.01607375,0.05114796,0.0495548,-0.05703394,0.02857599,0.05945091,0.01225724,0.01152071,0.02135318,0.04191281,0.09031843,-0.01046707,-0.0223094,-0.01218367,-0.02654571,-0.05038084,-0.18905923,0.03814288,0.06328671,-0.0148383,-0.00904724,-0.0325367,0.06656196,0.0222877,0.07458521,0.04177118,0.06509981,-0.00419579,-0.06747646,0.01974167,0.01455429,0.07902547,0.02852224,-0.01631991,0.03358625,0.02380144,0.0242645,0.02925272,-0.00144261,-0.04413415,0.05873628,0.02588717,0.1169154,0.0251389,0.03168928,0.00280488,-0.02395396,-0.00147792,0.01595187,-0.08360658,0.11166227,-0.02308414,0.0000487,0.01445277,0.0202037,-0.01305144,-0.00639223,0.02061282,-0.02959983,-0.08296449,-0.02581756,0.01259282,-0.06031635,-0.04478991,-0.10851026,-0.0250401,-0.04253624,-0.04775331,-0.00667229,0.03035147,0.04194815,-0.02965949,-0.00711826,-0.02707426,-0.02420323,-0.01669806,-0.01498482,0.01268595,0.00055558,-0.03418688,0.01739506,-0.05098398,-0.02416339,0.04606854,0.01721722,0.0007361,-0.01938247,0.12606828,-0.01142835,-0.02562462,0.0459369,-0.02153295,0.00859704,-0.03144232,-0.05987758,-0.02463182,0.04208449,0.00004988,0.01474157,-0.00551955,-0.03177914,0.00936989,0.03617429,0.02149613,0.02978399,-0.02964121,-0.01727023,0.04328616,-0.00414523,-0.00973889,0.03834707,0.0331903,-0.26821208,0.04967466,-0.00459125,0.08243591,-0.03034304,-0.02094072,0.02440971,0.04951078,-0.05499967,0.01329244,0.0285434,0.0333236,0.01118639,-0.06454212,0.01407875,0.00055143,0.03727788,-0.03379413,0.07132802,-0.00566868,0.02456028,0.01147502,0.22482479,-0.00992308,0.04430845,-0.00761116,0.08093303,-0.03362129,0.03072159,0.00137078,0.05261948,-0.03783572,0.14441349,-0.06552652,0.04970963,-0.03676774,-0.01124096,-0.02247995,0.02013512,0.01533087,-0.04082515,0.00639393,-0.05051642,-0.01348824,0.07842153,-0.06531037,-0.07848127,-0.08777237,0.06062727,0.00231812,-0.02245518,-0.05918917,-0.03190242,0.0891821,-0.04640105,0.11898121,-0.04591819,-0.00939458,0.01219655,-0.00182634,0.03055476,-0.02889734,0.02252678,0.03766346,0.0223329],"last_embed":{"hash":"6f190377933e5c6849358fb67000986c774847f5b4062bf7da6a3616859f09e6","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"6f190377933e5c6849358fb67000986c774847f5b4062bf7da6a3616859f09e6","at":1743662877866},"key":"Microservices.md#1. Resources#1.2. Outbox pattern#1.2.2. Lợi ích của Outbox Pattern#{3}","lines":[95,96],"size":318,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06982211,-0.04554497,-0.01789813,-0.02664643,-0.00254826,-0.03975718,-0.06697746,0.03534469,-0.00750929,-0.00109258,0.02194061,-0.08492426,0.04555209,0.03311515,0.12758462,-0.00169311,0.01657162,0.00813929,-0.07433363,0.00901894,0.09791464,-0.01742301,-0.0270878,-0.03164113,0.01285011,0.02645064,-0.02513157,-0.0546023,-0.00509721,-0.17875586,0.00846601,-0.04857082,0.0496712,0.05046544,0.07827388,-0.00883998,-0.01374189,0.03668047,-0.05660577,0.02154105,0.06279352,-0.01362077,-0.02253708,0.00950902,-0.00938329,-0.09592416,0.00344038,0.00732186,-0.01883832,-0.00585578,-0.01518685,0.00289084,-0.00061788,0.04568144,0.02787599,0.01628068,0.02733413,0.03501646,0.01584956,-0.0117084,0.13190012,0.03516264,-0.17606127,0.1516099,0.0199654,0.01646278,0.00158307,-0.02618291,-0.01563575,-0.01709937,-0.01175081,0.0301615,-0.05604012,0.08030736,0.01531123,-0.03538974,-0.02999988,-0.06133963,-0.01016066,-0.01155783,-0.0674289,-0.02193644,-0.02540969,0.06359803,-0.03218344,-0.02384667,-0.06418174,-0.01272232,0.08683042,-0.01963286,0.01759475,-0.02884014,0.03598516,0.01029689,-0.03304733,-0.04450483,0.01471961,0.07171421,-0.05990466,0.13114658,0.0580271,0.0156527,-0.02069268,-0.03192497,0.09396893,-0.04299928,-0.01181852,-0.10447876,0.02860838,-0.0025571,-0.00414851,-0.02302999,-0.00221694,-0.04885164,-0.10506962,0.05603668,-0.04349083,0.05037007,0.00715738,0.03931182,-0.02492929,-0.04477744,0.06700211,-0.02399498,0.06527432,-0.02203367,0.07104383,0.0187622,-0.03630529,0.0410442,0.01673713,0.01808815,-0.07761124,-0.04619692,-0.00379548,-0.00884361,0.00757166,-0.01729829,-0.01698755,0.02075954,-0.04593731,-0.09212288,0.00713056,-0.02702705,-0.02200489,0.04126196,0.00935565,0.05333759,-0.06733233,-0.05416567,-0.02228439,0.08989818,-0.03622691,-0.04764514,0.04140009,0.00638182,0.08851539,0.08371307,-0.10899129,0.01134393,-0.00121296,-0.01667007,-0.02941363,0.15857965,0.00523661,-0.124816,0.01459331,0.05135397,-0.05856267,-0.02985897,0.03814916,0.00976621,-0.01225401,-0.00680249,0.04848664,-0.01579363,-0.03715974,-0.0241115,0.01615751,0.02663624,-0.08021383,-0.02521203,-0.00706145,0.03689686,0.05410211,-0.01262563,0.02793968,-0.03611559,-0.04362676,-0.01115863,-0.00268379,-0.00444299,-0.00289883,-0.02597857,0.03946582,-0.02696895,0.04093628,-0.05700364,0.04955569,-0.07107529,0.09985748,0.0760907,-0.01061605,0.01426469,-0.00420897,0.02347154,-0.05460204,0.01930637,0.04162047,0.03177415,0.00089793,0.03129808,0.02419083,0.00088121,-0.04712456,-0.01978262,0.05379076,0.00186579,0.00962021,0.03392506,0.00448428,0.02947585,-0.04121339,-0.18061051,-0.00386134,0.01268121,-0.01072931,-0.01832999,-0.08035423,0.02920388,0.0108275,0.01446194,0.06700066,0.11702681,0.06411289,-0.00667911,0.04226023,-0.0041126,0.0499696,0.04437879,-0.00814134,0.04751883,0.00203712,-0.00323298,0.00960976,0.01750764,-0.01672925,0.05527384,0.00462889,0.13878945,0.05825062,0.00698305,-0.06819965,-0.03485995,-0.02867301,0.00456407,-0.13195284,0.04037945,0.04495025,-0.01134537,0.02626435,0.05591416,-0.07519624,-0.01565832,0.04014736,0.00706334,-0.05263144,0.00480198,-0.06272899,-0.04884155,-0.02419905,-0.06378288,0.01136779,-0.01888251,0.03078425,0.05448278,0.01592151,0.00693988,-0.0061573,0.01471031,0.01553193,-0.00166166,-0.00187224,0.01641652,-0.00190149,0.00787938,-0.06158726,0.03303301,-0.070958,-0.03528513,0.03278955,0.00033468,-0.09184784,-0.00747726,0.1232905,-0.06259248,0.08489592,0.00018895,0.02774298,0.03229091,-0.05260113,-0.03130341,-0.01259765,0.03503184,-0.05140174,0.06351477,-0.03308939,-0.01435003,0.02639782,0.05301281,0.01278814,0.07050397,-0.05039509,-0.012862,-0.00192465,-0.04929966,-0.02016356,0.06528437,0.08212585,-0.2241278,0.04915992,-0.02533087,-0.00953094,-0.04904318,-0.02910653,0.01861295,-0.08547578,-0.01783354,-0.00348071,0.01284782,0.03386816,0.07206914,-0.04312981,-0.01976511,0.02438834,0.04067954,-0.03451433,0.03360442,-0.05271044,0.00549876,0.01068731,0.22771533,0.02875437,-0.0193195,0.03706633,-0.04661253,-0.010548,0.05229972,0.00687029,0.01789762,0.00137727,0.07277525,-0.02648711,-0.01254329,0.02030828,0.01668566,-0.06406289,-0.0187718,0.03052038,-0.02539083,0.02039563,-0.02055074,0.00661989,0.09197128,-0.00504266,-0.08062779,-0.09782308,-0.00475731,0.05787201,-0.01863913,-0.06743667,-0.04550391,0.02688288,0.01302644,0.1014508,-0.03903937,0.01894905,-0.02107806,-0.03622909,0.02453624,0.01343197,-0.01425749,0.0011141,0.0317894],"last_embed":{"hash":"f15ef65875f86afbdb6b87646afbb9834579d467597d06796ed6b31dc0bbde2f","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"f15ef65875f86afbdb6b87646afbb9834579d467597d06796ed6b31dc0bbde2f","at":1743662877882},"key":"Microservices.md#2. Các services","lines":[99,334],"size":13108,"outlinks":[{"title":"ElasticSearch","target":"ElasticSearch","line":4},{"title":"Typesense","target":"https://github.com/typesense/typesense","line":5},{"title":"Datamuse API","target":"https://www.datamuse.com","line":6},{"title":"Orama","target":"https://github.com/oramasearch/orama","line":7},{"title":"Keycloak","target":"https://github.com/keycloak/keycloak","line":10},{"title":"Giải pháp phân quyền sử dụng Keycloak","target":"https://viblo.asia/p/giai-phap-cho-bai-toan-phan-quyen-su-dung-keycloak-Ny0VG717VPA","line":11},{"title":"Supertokens","target":"https://github.com/supertokens/supertokens-core","line":12},{"title":"Ory","target":"https://github.com/orgs/ory/repositories?q=&type=all&language=&sort=stargazers","line":13},{"title":"Dex","target":"https://github.com/dexidp/dex","line":14},{"title":"Better-auth","target":"https://github.com/better-auth/better-auth","line":15},{"title":"Supabase-auth","target":"https://github.com/supabase/auth","line":16},{"title":"Dragonfly","target":"https://www.dragonflydb.io/redis-alternative","line":22},{"title":"ReadySet","target":"https://github.com/readysettech/readyset","line":25},{"title":"Soketi","target":"https://github.com/soketi/soketi","line":27},{"title":"Hướng dẫn sử dụng HAProxy cho load balancing ứng dụng","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":41},{"title":"Pingora","target":"https://github.com/cloudflare/pingora","line":42},{"title":"Yao","target":"https://github.com/YaoApp/yao","line":46},{"title":"Strapi","target":"https://github.com/strapi/strapi","line":47},{"title":"Imageproxy","target":"https://github.com/willnorris/imageproxy","line":49},{"title":"Multiwoven","target":"https://github.com/Multiwoven/multiwoven","line":51},{"title":"Minio","target":"https://github.com/minio/minio","line":53},{"title":"Cloudreve","target":"https://github.com/cloudreve/Cloudreve","line":54},{"title":"Signoz","target":"https://github.com/SigNoz/signoz","line":56},{"title":"Coroot","target":"https://github.com/coroot/coroot","line":59},{"title":"YOURLS","target":"https://github.com/YOURLS/YOURLS","line":61},{"title":"Polr","target":"https://github.com/cydrobolt/polr","line":62},{"title":"Dokploy","target":"https://github.com/Dokploy/dokploy","line":65},{"title":"Sidekick","target":"https://github.com/mightymoud/sidekick","line":66},{"title":"Harness","target":"https://github.com/harness/harness","line":67},{"title":"Daytona","target":"https://github.com/daytonaio/daytona","line":69},{"title":"Devcontainers","target":"https://code.visualstudio.com/docs/devcontainers/containers","line":70},{"title":"Lapdev","target":"https://github.com/lapce/lapdev","line":71},{"title":"n8n","target":"https://github.com/n8n-io/n8n","line":73},{"title":"Openreplay","target":"https://github.com/openreplay/openreplay","line":76},{"title":"Sonarqube","target":"https://github.com/SonarSource/sonarqube","line":77},{"title":"Encore","target":"https://github.com/encoredev/encore","line":89},{"title":"Novu","target":"https://github.com/novuhq/novu","line":90},{"title":"Apache Dubbo","target":"https://github.com/apache/dubbo","line":91},{"title":"LiveKit","target":"https://github.com/livekit/livekit","line":94},{"title":"Jitsi Docker","target":"https://github.com/jitsi/docker-jitsi-meet","line":95},{"title":"Digitalhippo","target":"https://github.com/joschan21/digitalhippo","line":97},{"title":"Vitess","target":"https://vitess.io","line":112},{"title":"Spinnaker","target":"https://spinnaker.io","line":125},{"title":"trunk-based development","target":"https://trunkbaseddevelopment.com/","line":128},{"title":"configure","target":"https://www.flipt.io/docs/configuration/overview","line":133},{"title":"OpenTelemetry","target":"https://opentelemetry.io/","line":136},{"title":"Prometheus","target":"https://prometheus.io/","line":136},{"title":"Filesystem, Object, Git, and OCI declarative storage backends","target":"https://www.flipt.io/docs/configuration/storage#declarative","line":137},{"title":"Exactly One Message with Kafka (EOS)","target":"https://viblo.asia/p/exactly-one-message-voi-kafka-eos-0gdJz6OEJz5","line":145},{"title":"Istio","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":148},{"title":"Novu","target":"https://github.com/novuhq/novu","line":152},{"title":"Self-hosted ngrok","target":"https://github.com/pgrok/pgrok","line":224},{"title":"Listmonk","target":"https://github.com/knadh/listmonk","line":225},{"title":"Ghostfolio","target":"https://github.com/ghostfolio/ghostfolio","line":226},{"title":"Teller","target":"https://github.com/tellerops/teller","line":227},{"title":"Canvas","target":"https://github.com/austintoddj/canvas","line":228},{"title":"Rocket.Chat","target":"https://github.com/RocketChat/Rocket.Chat","line":229},{"title":"BookStack","target":"https://github.com/BookStackApp/BookStack","line":230},{"title":"Hook0","target":"https://github.com/hook0/hook0","line":231},{"title":"Chatwoot","target":"https://github.com/chatwoot/chatwoot","line":232}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05205751,-0.00436851,0.00227833,-0.03223762,0.01190584,-0.0146287,-0.05914964,0.02464095,-0.0089457,0.00548041,0.02409253,-0.07993834,0.05486692,0.02239892,0.1306446,0.04167403,0.01283376,0.02623982,-0.04133216,-0.02496312,0.12201428,-0.01989644,-0.02175751,-0.01206595,0.0358659,0.03398481,-0.02279052,-0.04649832,0.00524022,-0.16014506,0.00735769,-0.05305567,0.05111165,0.04756422,0.06860652,-0.01109574,-0.01886315,0.03409189,-0.0613719,0.03755306,0.02969989,-0.02556677,-0.01307646,0.01714529,-0.03843772,-0.07180731,0.04272639,-0.00455096,-0.00334195,-0.00281557,-0.02749017,-0.00881448,-0.01869521,0.00086881,0.02807516,0.03097674,0.04946249,0.04221482,0.01218439,-0.00593927,0.14035578,0.01923128,-0.18486953,0.13625437,0.03440083,0.02866307,-0.00698164,-0.0235019,-0.024722,-0.01239293,0.00083749,0.03920975,-0.03606829,0.09139641,0.0203214,-0.03856658,-0.03436407,-0.04980006,-0.01408729,-0.0260144,-0.09378176,-0.03967262,-0.02784515,0.04322076,-0.0202532,-0.04383118,-0.08645017,0.0095346,0.08827294,-0.03545193,0.00648263,-0.01387171,0.02960578,0.01164344,-0.03352887,-0.03194291,0.00937026,0.07196297,-0.03281341,0.14411917,0.02675495,0.01378738,-0.01009167,-0.02313096,0.08853511,-0.03524617,0.00543609,-0.08629675,0.04869061,0.01065259,0.00741261,-0.02659414,0.00747572,-0.05021033,-0.09969231,0.02510285,-0.04769209,0.03565003,0.0098622,0.05564839,-0.03533614,-0.05698885,0.06431417,-0.03218951,0.06620254,-0.02089869,0.05629367,0.03590443,-0.00766001,-0.01329532,0.01899364,0.01969034,-0.08578034,-0.03152435,-0.01688218,-0.01067256,0.02994703,-0.04485635,-0.03586845,0.01747986,-0.04259145,-0.07293697,0.01047219,-0.0382758,-0.03000318,0.07162722,0.00692747,0.03841804,-0.06966957,-0.08658438,-0.05579839,0.08814421,-0.01318709,-0.03113696,0.01935059,0.01115679,0.0886236,0.07166392,-0.09705061,0.02539178,-0.02237464,-0.01432277,-0.00557576,0.16690318,-0.02043824,-0.11146969,0.02376569,0.03870783,-0.06656074,-0.04092497,0.03364437,0.05588892,-0.02959533,-0.00146235,0.06874079,0.00183811,-0.03082745,0.01336686,0.00717194,0.01234042,-0.08390217,0.00849643,0.01327637,0.01587882,0.03058483,-0.02350938,0.01823971,-0.05114072,-0.03978087,0.0202391,0.00349746,-0.02538376,0.01400791,-0.01696414,0.03863925,-0.03481768,0.0446884,-0.04655543,0.07568883,-0.07538342,0.08596422,0.04190434,-0.01856158,0.02067747,0.00158988,0.02220187,-0.0590071,0.01522593,0.03472641,0.01090787,0.01904821,0.01224769,0.02938658,0.00691244,-0.05122431,-0.02915251,0.06468344,0.00748311,0.00048845,0.0556171,0.0067397,0.01357731,-0.04521386,-0.17623989,-0.00942513,0.02119808,-0.00068769,-0.03473342,-0.09940136,0.04392413,0.01331374,-0.02176197,0.06623839,0.10044947,0.06511652,-0.00764595,0.02393422,-0.01819513,0.02469061,0.03406835,-0.0147794,0.02588693,0.01789906,0.04489091,0.00890175,-0.00084597,-0.02458468,0.06029037,0.00786251,0.11414877,0.05270309,-0.01279791,-0.08497995,-0.02488732,-0.02529428,-0.00221761,-0.1269592,0.01170258,0.02503328,-0.00391035,0.03875412,0.03397368,-0.05166698,-0.02757875,0.02632985,0.01029813,-0.06205346,-0.00198641,-0.04662048,-0.02030611,-0.03255469,-0.043918,0.01030179,-0.01889426,0.00226045,0.072188,0.01293429,0.01291568,-0.00193519,-0.00162871,-0.0081073,-0.01476869,-0.05493269,0.00456711,-0.00863433,0.03611388,-0.0679297,0.03007923,-0.06235221,0.00692832,0.00182222,-0.03283197,-0.10041597,0.01167316,0.08443155,-0.06596061,0.08708092,-0.0236968,0.00868201,0.02163682,-0.05443328,-0.01968515,-0.02460819,0.04008966,-0.05580899,0.0970128,-0.03441708,-0.01238451,0.0213912,0.05470391,-0.00143129,0.05623403,-0.05821788,0.01542199,-0.01265617,-0.05461014,-0.03461572,0.07024428,0.08521684,-0.22528973,0.0560398,-0.01455596,-0.00343761,-0.05247267,-0.02036369,0.00412567,-0.07949046,0.0081359,-0.00294914,0.02183838,0.00682633,0.06334708,-0.07101899,-0.01174991,0.05599887,0.05316359,-0.03758517,0.04932894,-0.05845573,0.0253433,0.04005223,0.24071683,0.04567516,-0.01531406,0.0469445,-0.06053862,-0.03359944,0.04806711,0.01367652,0.03700361,0.00754799,0.07393717,-0.0249163,-0.01598453,0.01216147,0.0064923,-0.02329333,-0.00979167,0.02809908,-0.02616455,0.02295786,-0.01199847,-0.00806252,0.08456511,0.0134685,-0.08736611,-0.10876529,-0.01527571,0.03053353,-0.02453656,-0.03974481,-0.03163543,0.02597502,0.01754201,0.12409851,-0.05196825,0.041766,-0.03675934,-0.03483201,0.02079071,0.00596561,-0.01859009,0.00172186,0.04116328],"last_embed":{"hash":"74dbff8ec36b82b316c1c03083355467caef53b515a0503693e18b49020a26e1","tokens":141}}},"text":null,"length":0,"last_read":{"hash":"74dbff8ec36b82b316c1c03083355467caef53b515a0503693e18b49020a26e1","at":1743662877930},"key":"Microservices.md#2. Các services#{1}","lines":[101,106],"size":347,"outlinks":[{"title":"ElasticSearch","target":"ElasticSearch","line":2},{"title":"Typesense","target":"https://github.com/typesense/typesense","line":3},{"title":"Datamuse API","target":"https://www.datamuse.com","line":4},{"title":"Orama","target":"https://github.com/oramasearch/orama","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06283317,-0.05938921,-0.02340407,-0.00044437,-0.04121863,-0.03369021,-0.02271811,0.00362188,0.01284406,-0.03752659,0.01278816,-0.11457268,0.04910058,0.01137423,0.09079348,-0.04289757,0.02290934,0.00942441,-0.05376018,0.02923749,0.05663557,-0.01108778,-0.01933365,-0.03018386,-0.00496644,0.02482468,-0.03520877,-0.06273042,-0.03450432,-0.16827016,-0.01300605,-0.02574703,0.02323274,0.05524366,0.04884673,-0.0163496,0.01362532,0.05139151,-0.03870936,0.05188043,0.06311277,-0.01535934,-0.03113314,-0.02022388,0.0595434,-0.12869902,-0.0581161,-0.0387993,-0.00237169,0.00881666,0.0148974,0.06040112,-0.0058168,0.09862477,-0.03941501,-0.00710996,-0.00567958,0.04266414,0.0478248,0.00165309,0.08199129,0.05552391,-0.18842481,0.13613629,-0.03754763,0.02392585,0.00531988,-0.01003673,-0.00007962,0.01261311,-0.04180401,-0.00573008,-0.05074402,0.09333454,0.00022849,-0.0384054,-0.03414119,-0.04310118,-0.03566109,-0.00898859,-0.00074429,0.04928298,0.01494236,0.07679799,-0.07987916,0.02189856,-0.04054379,-0.03745157,0.0492001,0.00950373,0.00820144,-0.03217622,0.0152139,0.01369951,-0.0939156,-0.07071238,0.00911423,0.02210697,-0.12963383,0.13229419,0.04537009,0.0029604,-0.04621601,-0.06421261,0.06902087,-0.00053147,-0.04403047,-0.09175377,-0.01251795,0.00047242,-0.01538168,-0.0064488,-0.00686656,-0.03800453,-0.06180392,0.08725175,-0.01090494,0.06053778,0.00475592,0.01226581,0.04719705,-0.01701503,0.06036263,-0.04014724,0.03176381,-0.04478597,0.05470645,-0.00068412,-0.06734146,0.06073702,0.02181036,0.02724253,0.00027235,-0.03486129,0.01526849,-0.00300892,-0.01248031,0.02514732,0.00116132,-0.00114598,-0.00976956,-0.05691829,0.02652672,-0.02148,-0.02185372,0.01679773,-0.02389878,0.03036079,-0.03412203,-0.01152558,0.05950814,0.08941086,-0.03047108,-0.04798519,0.03815024,0.0319219,0.05267562,0.0840726,-0.10441884,0.00371716,0.03420489,-0.02909545,-0.05537746,0.13525771,0.04752742,-0.11278488,-0.00475521,0.03265909,-0.01033105,-0.01708966,0.02657872,-0.02652549,-0.0348707,-0.01175234,0.01008035,-0.00620557,-0.01983047,-0.02758395,0.000478,0.04209772,-0.03523602,-0.0629331,-0.02813667,0.07111266,0.10285565,0.01194859,0.00963699,0.0030362,-0.04759626,-0.01756945,-0.04825407,0.04995808,-0.06426246,-0.07269101,0.01119488,-0.00436317,0.0077747,-0.06448164,0.00193588,-0.03502364,0.09101334,0.10945576,-0.03331137,0.0044058,-0.0015461,0.01006354,-0.06429828,0.00660827,0.02263082,0.0312589,-0.01663524,0.04182006,0.04956839,-0.06279941,-0.03176251,0.01014686,0.02000624,0.02090982,-0.02229245,-0.02924433,0.01108025,0.04743459,-0.03352775,-0.18769625,0.04046065,-0.01460247,-0.01969299,0.03466584,-0.03435627,0.01725115,-0.01166251,0.0225432,0.04026372,0.13960664,0.05437702,0.01023362,0.06629317,-0.04213693,0.11144173,0.06525097,-0.00217602,0.08111009,-0.00952533,-0.04419247,0.01852693,0.02568335,-0.00851968,0.04140587,0.01140934,0.13411792,0.05208848,0.0638814,-0.02049121,-0.0088076,-0.02697209,0.01154874,-0.09546308,0.09569076,0.06291131,-0.03229924,0.03585015,0.05664426,-0.04043998,0.00959256,0.0267207,-0.03229843,-0.03175908,0.02475647,-0.06919411,-0.08659402,0.033154,-0.08078336,-0.00807459,-0.00940454,0.06750054,0.02183149,0.04739856,-0.01324847,-0.0123431,0.02330759,0.01949085,-0.01059178,0.03234779,0.03086605,0.02709307,-0.02602835,-0.04622928,0.02844372,-0.03627696,-0.06417707,0.07184292,0.04786847,-0.02489682,-0.00411265,0.11998289,-0.04173631,0.04527977,0.04124082,0.04908903,0.05321951,-0.07853576,-0.0560457,-0.01826632,0.0174658,-0.0333077,0.04166356,-0.03090709,-0.01999857,0.0236134,0.01845036,0.04318007,0.07403831,-0.02945919,-0.05501514,0.02217737,-0.0182463,0.03486979,0.03535856,0.03616971,-0.23494712,0.01306839,-0.04782177,-0.00787726,-0.03479018,-0.02821046,0.04747539,-0.04558369,-0.07264727,0.01226182,0.05068266,0.04903457,0.05763571,0.02034171,-0.05651736,-0.0104081,0.04777515,-0.02810675,0.01375712,-0.00191242,-0.01943555,-0.04741476,0.18210058,-0.01752561,-0.01305534,-0.00418903,0.00657036,0.01433364,0.0694228,-0.03436728,-0.01330814,0.01541942,0.04631896,-0.05086723,-0.00332328,0.07094781,-0.03167882,-0.0658721,-0.03888442,0.02696187,-0.028901,0.01920939,-0.06601152,-0.0260687,0.080917,-0.0512744,-0.01938906,-0.06242698,0.03521066,0.05399291,0.02294671,-0.10783872,-0.0385855,0.00331096,0.02394929,0.04936745,-0.02117268,-0.03635281,-0.02001541,-0.02494704,0.01251246,0.01220523,0.01017127,-0.00292738,0.00848877],"last_embed":{"hash":"b61ca76b3e59ddd5c71252265da493d32aa1bf2a923460bc59ba7e5ef3f700ad","tokens":306}}},"text":null,"length":0,"last_read":{"hash":"b61ca76b3e59ddd5c71252265da493d32aa1bf2a923460bc59ba7e5ef3f700ad","at":1743662877946},"key":"Microservices.md#2. Các services#{2}","lines":[107,117],"size":633,"outlinks":[{"title":"Keycloak","target":"https://github.com/keycloak/keycloak","line":2},{"title":"Giải pháp phân quyền sử dụng Keycloak","target":"https://viblo.asia/p/giai-phap-cho-bai-toan-phan-quyen-su-dung-keycloak-Ny0VG717VPA","line":3},{"title":"Supertokens","target":"https://github.com/supertokens/supertokens-core","line":4},{"title":"Ory","target":"https://github.com/orgs/ory/repositories?q=&type=all&language=&sort=stargazers","line":5},{"title":"Dex","target":"https://github.com/dexidp/dex","line":6},{"title":"Better-auth","target":"https://github.com/better-auth/better-auth","line":7},{"title":"Supabase-auth","target":"https://github.com/supabase/auth","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03539344,-0.02003082,0.02542257,-0.03439003,0.03531416,-0.0572497,-0.04693599,-0.00263773,0.01786979,-0.0014454,0.02558575,-0.11060526,0.0564484,0.06904235,0.05857266,-0.01253583,0.02962909,0.04280941,0.00126087,0.02654136,0.05195072,-0.01430167,0.01648767,-0.05802394,-0.01644206,0.05833725,-0.03224399,0.00917592,-0.04674182,-0.203151,-0.00301698,-0.09490381,0.02855125,0.03376496,0.08417703,-0.01181038,-0.01188642,0.00568283,0.00188917,0.0271473,0.0699068,0.02616769,-0.04100054,-0.00978063,-0.01429842,-0.12535553,0.05445393,-0.02933521,-0.05267122,-0.01499335,-0.04185769,0.03295146,-0.00034111,0.01280241,0.02891845,0.02257099,0.06538641,-0.00369862,0.05628338,-0.01563576,0.10935333,0.01760478,-0.23939143,0.04881507,-0.00390319,0.05467273,0.00185023,-0.04207294,-0.00466841,0.01448355,0.00181627,-0.01658171,-0.02494944,0.11574281,-0.00267863,-0.00141775,0.0018152,-0.04706302,-0.04792052,-0.00491696,-0.00330244,0.01161474,-0.01418043,0.01708429,-0.00518611,-0.02291333,-0.02256849,-0.00507814,0.13413322,-0.02067604,-0.01080628,-0.01966311,0.01712482,0.014528,-0.01590241,-0.03535957,0.06651673,0.04234298,-0.03028043,0.1185087,0.04822137,0.0756061,-0.02080553,-0.0200127,0.11606863,-0.01983081,0.01345426,-0.06468156,0.02278522,0.01729062,0.03172231,-0.04298133,0.0302491,-0.03440012,-0.03807804,0.03540098,0.02537031,0.07479358,-0.01353649,0.03065765,-0.01131131,-0.04455532,0.10424469,-0.07379858,0.11234456,-0.01210317,0.0284969,0.02369464,0.01812967,0.00447832,-0.0050973,-0.02330204,-0.02160828,-0.02705003,0.00699236,-0.01482765,0.01381651,-0.02605513,-0.02724992,0.03950312,-0.05386918,-0.04829503,0.01927401,-0.07243335,-0.05525914,0.11813585,0.02003002,-0.00845781,-0.05246941,-0.09361126,-0.04920093,0.02307839,0.00834197,-0.05307927,-0.02970002,0.03114594,0.04252287,0.13177863,-0.10813399,0.01947034,-0.02913346,-0.06484183,0.00016722,0.13047652,0.0237113,-0.09602749,0.00668466,0.06241537,-0.00886392,-0.06028754,0.05858271,0.01967788,0.00909118,-0.04847162,0.02074389,-0.02022683,0.03686737,-0.00425816,-0.04029194,0.01505702,-0.03926981,-0.06187261,-0.01095485,0.03647501,0.01960571,-0.04703821,-0.03400014,-0.02043489,-0.02287755,0.03054014,-0.02302945,-0.03247096,0.04387881,0.0063756,0.00741237,-0.00802861,0.01942957,0.01721649,-0.03738049,-0.11019381,0.09966552,0.02481907,-0.014542,0.02496344,0.00160051,0.02624304,-0.0683377,-0.01191176,0.0349375,0.0277103,-0.04966711,0.00142629,0.00239258,-0.01326777,-0.06747902,-0.04413325,0.02797765,0.04525588,0.00529786,0.02340569,-0.00403013,0.0436142,-0.08067834,-0.23394808,-0.03476729,-0.00155355,0.00547587,0.02593793,-0.02829751,0.0270875,0.05023976,0.00024255,0.00410152,0.07527602,0.06283997,-0.02809761,0.00442206,0.04322013,0.04792853,0.00489572,-0.00028408,-0.02701721,0.0180977,0.00167645,0.01157633,-0.03482941,-0.00896699,0.05231804,-0.01179417,0.13998304,0.03816347,0.00702247,-0.05553181,-0.014369,-0.03245811,0.05019022,-0.07424524,0.04949157,0.04435208,0.00540803,0.01574819,0.01718722,-0.04698271,-0.05643981,0.01448307,-0.02276896,-0.06569036,-0.00976252,-0.02257231,-0.01231469,-0.0620458,-0.09436491,-0.04941973,0.01359806,-0.00930675,0.03113589,0.0123504,0.04750261,-0.00356115,-0.02919232,-0.08345661,0.00098661,-0.00296817,0.03550623,-0.0092119,0.02924215,-0.08700863,0.06527692,-0.05642686,-0.01390074,0.0336171,-0.0097716,-0.04102275,0.01287411,0.08258319,-0.04082416,0.01106504,0.01002084,-0.01476031,0.01194653,-0.0396173,-0.02516711,0.03678761,0.07389053,-0.05314906,0.02201981,0.01446616,0.03680997,0.04509462,0.03963594,0.03461998,0.05145818,-0.04045201,-0.02695386,-0.03445769,-0.02600544,-0.0469356,0.0446531,0.04139984,-0.23530428,0.02435068,-0.00614928,-0.0108249,-0.00295237,-0.04641969,0.01998134,-0.00418797,-0.01238944,-0.00982668,0.06959945,-0.00410784,0.02914518,-0.04888157,0.01672712,0.06152962,0.04563899,-0.04284485,0.0256432,-0.09710392,-0.00078592,-0.01889071,0.20389906,0.06318862,-0.01049221,0.05928075,0.03198038,0.04644068,0.0519128,0.0029098,-0.00059874,-0.0410793,0.10940555,-0.04683179,0.02069598,0.08135572,0.03981813,-0.00405893,0.04561654,-0.00859652,0.0257457,0.00097089,0.01705854,0.01592432,0.08286498,-0.01227823,-0.05915823,-0.10821432,0.02118239,0.02846469,-0.00425132,-0.0422537,-0.00730113,0.0444447,0.03911177,0.03460319,-0.05726655,-0.00751361,-0.04321009,-0.04495635,0.01436134,-0.01044389,0.00876909,-0.01652215,0.04023702],"last_embed":{"hash":"742f87eade823033e00062125b7dabca871cc28cb4b675337288f70cf75cfb43","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"742f87eade823033e00062125b7dabca871cc28cb4b675337288f70cf75cfb43","at":1743662877985},"key":"Microservices.md#2. Các services#{6}","lines":[126,132],"size":418,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09220999,-0.03554286,-0.01701643,-0.04052855,-0.05627177,-0.0659139,-0.05181634,0.02892402,0.01715636,0.00385829,-0.00048358,-0.16957828,0.05297874,0.0014898,0.06007553,0.00607442,0.04014841,0.01509599,-0.03259035,0.03984477,0.07596214,-0.00484399,-0.049512,0.03156678,-0.03068891,0.03462978,-0.02921025,-0.01197675,-0.0088684,-0.17371945,0.03959998,-0.05266294,-0.01325372,0.06552243,0.02294576,0.01514646,-0.0005073,-0.0334455,-0.03335839,0.03767722,0.02170937,0.02683472,-0.01236834,-0.00319183,0.02368348,-0.09644301,0.00542358,0.03985674,0.02039942,-0.01984789,-0.00234716,0.00855278,0.02893806,0.02981696,0.02763785,-0.01419447,0.11682046,0.04068415,0.02679138,0.08165598,0.11722715,0.05857132,-0.19962984,0.07941104,-0.01319505,0.06775873,-0.03459706,-0.02301976,0.01978969,-0.0079709,-0.03391439,0.00292225,-0.03023723,0.07809651,0.00522396,0.02438495,0.02202982,-0.0133764,-0.05805546,0.02132869,0.05758758,0.03540773,-0.0251419,0.0322405,-0.01792458,-0.02078329,-0.0447208,-0.02328154,0.04000594,-0.00186485,-0.04168214,0.01834368,-0.00253987,0.04162067,-0.03899609,-0.03151464,-0.00550201,0.01539614,-0.0907237,0.11428356,-0.01351935,-0.01021588,-0.00008806,-0.0815153,0.06916875,-0.04533802,0.01193531,-0.04436797,0.01929783,0.0081458,0.02123685,-0.00929227,0.0201558,-0.05413962,-0.04287883,0.01143406,0.00691147,0.01349797,-0.03649453,0.00633727,0.01717519,0.0420659,0.09747183,-0.04303864,0.03081447,-0.02803729,0.03553648,0.08440162,0.00573214,0.05802145,0.02102659,0.02208315,-0.00325687,-0.02086307,-0.0334319,-0.03663285,0.03840229,-0.01632993,-0.01617389,-0.01631303,-0.07302134,-0.02711168,-0.00859587,-0.05453073,-0.06066364,0.09927637,0.01101061,0.04188465,0.02041436,-0.05553493,0.01376432,0.01090954,-0.01182021,-0.05517993,0.00421704,0.03244854,0.07245523,0.12408047,-0.07720913,0.00361308,0.01783936,-0.06120428,-0.03483021,0.10776145,-0.00235129,-0.11558059,-0.00974928,0.02618167,-0.0305038,-0.04561386,-0.00426459,0.01346323,-0.01872692,0.04256652,0.01858459,-0.02751303,-0.04389715,-0.01288013,-0.03048176,-0.00921929,-0.05361193,-0.03537217,-0.02074273,0.02777694,0.09490973,-0.01347434,-0.02664295,-0.0377786,-0.02592126,-0.06087868,-0.11221893,-0.03931803,-0.02942604,-0.04854202,0.03094204,-0.01776022,-0.04248469,0.00777741,-0.02317351,-0.02783432,0.09768117,0.05032649,-0.01613588,0.02190083,-0.05628521,0.04751776,-0.04090095,0.04348718,0.03008704,0.04518171,-0.01862437,0.04647979,0.04443146,0.04952807,-0.02187314,0.04325205,0.05038525,0.03400116,-0.01435226,0.00190898,0.03301646,0.0152339,-0.06087905,-0.22920755,-0.02962628,-0.06218052,-0.03629274,0.07554032,-0.04460819,0.0612594,0.04610259,0.01390224,0.04699171,0.11443084,0.01929222,-0.03465657,0.06002541,-0.00035164,0.07389341,0.03777033,-0.03417295,0.02080087,0.01079708,0.03269612,0.00639084,-0.05498197,-0.00719655,0.04694916,0.06102082,0.1257446,-0.02171102,0.03520279,-0.02781095,0.05080679,-0.06074679,0.0364296,-0.10792142,0.08643534,0.02177521,0.02306543,-0.03582721,0.01936438,-0.01642996,-0.01041032,0.03430071,0.01780111,-0.11599784,-0.01744564,-0.0391771,-0.02565224,-0.03608799,-0.09427327,-0.05246994,-0.0512518,0.00718559,-0.05098934,0.03297924,0.00634288,0.00704585,0.06513808,-0.01820633,0.01580518,0.01882508,-0.00922154,-0.00949491,0.02159755,-0.02672415,0.02837984,-0.0522618,-0.00574956,0.00812334,0.00896424,-0.01523912,-0.02093611,0.10987828,-0.02297913,0.06869552,0.01743142,-0.0070632,0.03965845,-0.05359359,0.00142977,-0.02264616,0.06630848,-0.00327006,0.03543502,0.04770264,0.010487,0.05345795,0.04287557,0.02092781,0.04067231,-0.05650729,0.00248694,0.00226316,-0.03360022,-0.02219888,0.0494125,0.05991312,-0.27677107,-0.00273878,0.00533737,-0.00225156,-0.07955652,-0.00149877,0.06202224,-0.03742464,-0.05293103,0.04667456,0.05422763,0.05808926,0.03250931,-0.00844251,-0.01859728,0.01583334,0.04642045,-0.07318076,0.0239615,-0.06442159,-0.00722541,-0.04994508,0.20492828,0.0208667,-0.02964737,0.05631472,-0.02841736,0.07344607,0.03542455,-0.04004855,-0.01102248,-0.01957715,0.06820163,-0.03040645,0.0472115,0.00081778,0.02200001,-0.0126457,0.00107002,0.02700244,-0.00106985,0.00317314,-0.01672579,-0.01358927,0.05964788,-0.01054227,-0.03364678,-0.06007033,0.0288217,0.01550642,-0.03462926,-0.08180179,0.01010872,0.0470526,0.05265283,0.06842875,-0.04933177,-0.04261411,-0.0726403,-0.03304124,0.0338215,-0.02389576,0.00637353,0.01751896,0.00234215],"last_embed":{"hash":"fb3a4c00a9c07bc6068babfa785ee1aa9221091165db4e9c9fcc922a775fb82a","tokens":156}}},"text":null,"length":0,"last_read":{"hash":"fb3a4c00a9c07bc6068babfa785ee1aa9221091165db4e9c9fcc922a775fb82a","at":1743662878001},"key":"Microservices.md#2. Các services#{7}","lines":[133,140],"size":400,"outlinks":[{"title":"Hướng dẫn sử dụng HAProxy cho load balancing ứng dụng","target":"https://viblo.asia/p/tong-quan-ve-istio-service-mesh-cho-nguoi-moi-bat-dau-Ny0VGnY0LPA","line":7},{"title":"Pingora","target":"https://github.com/cloudflare/pingora","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05239236,-0.03691125,0.04800164,0.00125052,-0.00002163,-0.02409214,-0.10126244,0.04021153,-0.02213897,-0.0215004,0.04643227,-0.04475908,0.10731833,0.01472545,0.09407199,0.01624444,0.03930609,0.06283366,-0.01058602,-0.02777748,0.00355138,-0.07001695,-0.04556029,-0.01163197,-0.02699458,0.04913344,-0.00876292,0.01508613,-0.02293843,-0.17943333,-0.00014049,0.00630244,-0.0414965,0.0398583,0.06346692,-0.05471285,0.02640142,0.01590613,-0.03929746,0.08009224,0.05613711,0.02412219,-0.06929861,-0.01732458,-0.01230719,-0.07739873,0.04237849,0.00053289,0.06197938,-0.00687361,0.03679474,-0.00539206,-0.02945373,0.02639689,0.02158136,-0.01712853,0.0592787,0.00621741,0.02876617,-0.04329262,0.07334137,0.01411529,-0.18801934,0.08416532,0.01184668,0.04116757,0.01261661,0.02126738,0.02612135,0.0061554,-0.03746017,0.03519896,-0.03546192,0.0754583,-0.01496944,-0.01234043,-0.02031617,-0.00915635,-0.00770812,-0.00293275,0.00070936,0.05166593,-0.0197421,0.04323255,-0.01339804,0.01854164,-0.01680216,-0.0060103,0.09915262,-0.00150054,-0.03771938,0.00619752,0.02333618,-0.02991758,-0.09119415,-0.05050546,0.05644644,-0.02314815,-0.08318035,0.11718741,0.03091955,-0.03524608,-0.03281119,-0.01570355,0.10033235,-0.02135409,-0.03011966,-0.02294507,0.03023066,-0.01052214,0.01603871,0.03328198,0.03013152,-0.07584511,-0.00787439,-0.02950077,-0.04631279,0.02421696,0.02691398,0.06062855,0.0369154,0.02585172,0.05793298,-0.04087362,0.04475496,-0.06867842,0.0524449,0.00543531,-0.00493767,0.04997902,0.01485339,0.04234304,-0.04959879,-0.04820454,0.00374738,0.00458202,-0.00573168,0.02687162,0.00145441,-0.02679671,-0.03843896,0.00720702,0.02614707,-0.05055517,0.08009733,0.02760506,0.01287507,0.09260952,-0.04791465,-0.02470361,-0.02391256,0.07868227,0.0361822,0.01219315,0.03825673,0.01580738,0.06087129,0.0529785,-0.08653895,0.03686667,0.03667361,-0.03562197,0.0081017,0.1050128,-0.00026333,-0.12881018,-0.02806821,0.02975948,0.0055024,-0.03724066,-0.05435888,0.01028547,-0.01278089,0.01108008,0.05585654,0.01919363,-0.08826916,0.07918645,-0.00932183,0.06085229,-0.02526875,0.01835806,-0.00652281,0.03015663,-0.0265836,-0.0915502,-0.02772313,-0.00106367,-0.02811347,-0.01132316,-0.04630952,-0.03186805,0.01254653,-0.03370028,0.02942948,-0.02243671,0.00148693,-0.01876669,0.06630182,-0.0243742,0.05426203,0.0480605,-0.00069694,0.00333641,0.00605407,-0.01982913,-0.04394828,0.00163942,-0.00293238,-0.00796777,-0.02099317,-0.0346217,0.04459443,0.03002021,-0.05658825,-0.00162368,-0.03669079,0.04483309,0.01236116,0.0239296,0.00345122,0.06041385,-0.05511143,-0.23604201,-0.02024112,-0.02915571,-0.01662887,-0.0194912,-0.05046066,0.03919605,-0.03320367,-0.03130837,0.02489101,0.08598533,-0.03569498,0.06055755,0.0399105,-0.02234367,0.05840947,0.01750447,-0.03966136,-0.05189889,0.02055095,-0.00786934,-0.01329813,-0.05775192,-0.0511262,0.0287194,0.04210224,0.12244123,0.02146348,0.02385341,-0.08370219,-0.01145593,0.0725962,-0.01903572,-0.14060947,0.02444273,0.00776815,0.04822308,0.01761558,0.0571897,0.00576335,-0.02823363,0.03513229,0.03142706,-0.15121628,0.03351223,-0.01468143,-0.07394888,-0.04594057,-0.07261651,-0.0068355,-0.0503067,-0.01846309,0.04124426,0.06882685,0.02468853,-0.02805155,-0.01265753,0.03559284,0.01249136,0.0098222,0.00432251,0.00495112,0.02337858,-0.03711575,0.00448537,-0.02659522,0.01974346,0.02236858,-0.00673511,-0.08975523,-0.01730445,0.06435386,-0.02570827,0.0611833,0.02842103,-0.00366418,0.05057887,-0.02055109,-0.03114012,-0.0086485,0.07590418,0.04732261,0.06800513,0.02297002,-0.05092819,-0.00254865,0.01877429,0.02324863,0.05720117,0.02607749,-0.0735421,-0.01075885,0.01454659,0.01832457,0.10541597,0.00647754,-0.26108643,0.02428384,-0.01287314,-0.02861745,-0.07718991,0.00083956,-0.02485831,-0.05638719,-0.08882085,0.02237309,0.04534708,0.02307505,0.0302292,-0.0118876,0.04205815,0.03840444,0.12166717,-0.03103443,0.02905696,-0.07587288,0.00767548,0.00746496,0.21293435,-0.01965878,0.03237663,0.06302169,-0.10499108,0.00011776,0.03224936,0.01133466,-0.00045945,0.02786334,0.04663359,-0.038369,-0.01882546,0.02842584,-0.0366912,0.0215661,-0.01242375,-0.00867598,-0.01440633,0.0083974,0.00629307,-0.01426197,0.10097767,-0.06390931,-0.03423116,-0.05503642,-0.01129732,-0.01924728,-0.0662156,-0.04580155,0.00208356,0.01344276,-0.01778948,0.04592616,-0.02200471,0.00884023,-0.04110777,-0.0612694,0.03947192,-0.12338494,0.06931348,0.11012916,0.00789814],"last_embed":{"hash":"c5b59fefee9a0543840dc535828c5509dd4d399ece1eb09cd7a8aec0f6391db6","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"c5b59fefee9a0543840dc535828c5509dd4d399ece1eb09cd7a8aec0f6391db6","at":1743662878012},"key":"Microservices.md#2. Các services#{15}","lines":[162,165],"size":345,"outlinks":[{"title":"Dokploy","target":"https://github.com/Dokploy/dokploy","line":2},{"title":"Sidekick","target":"https://github.com/mightymoud/sidekick","line":3},{"title":"Harness","target":"https://github.com/harness/harness","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.00134871,-0.05003534,0.04469388,-0.02619929,-0.00566589,-0.06698784,-0.05237607,0.02870026,-0.00825041,0.0177525,-0.02685657,-0.08675036,0.04954902,0.01048153,0.06269982,-0.00912405,0.04615096,0.01812807,0.03371693,-0.00542086,0.05687241,-0.01615472,-0.06341148,-0.04234142,-0.04359772,0.0719737,-0.00518497,0.02057665,-0.04359752,-0.14767018,-0.01215155,-0.02217845,-0.01907725,0.04406742,0.06064915,-0.0135929,0.03882806,0.03791363,-0.04309327,0.01056179,0.02474836,-0.03852822,-0.06117307,0.02555729,-0.00874886,-0.08122589,0.01729728,0.00164629,0.05217649,-0.08731151,0.0026853,0.02461714,-0.05168579,0.00095394,-0.01212818,0.04403336,0.04179147,-0.02709705,-0.00830148,0.01302266,0.08426951,-0.00672908,-0.21045737,0.07690123,0.03924391,0.05765104,-0.02955663,0.01682969,0.00416724,-0.03286991,-0.05341713,-0.03906584,0.01602701,0.05798531,0.03427634,-0.03932236,0.00387578,0.00477512,0.00777866,-0.01593176,-0.00341143,-0.01101284,-0.02400427,0.02126625,-0.00865406,-0.0050603,-0.00604942,0.0065796,0.11093041,0.02986721,-0.04606069,-0.04076721,0.07377915,-0.0208239,-0.09412342,0.02653491,0.06073844,-0.00340268,-0.02506232,0.17051363,0.0280581,-0.00469546,-0.04626293,-0.03247141,0.07954363,0.06403001,-0.00603562,-0.04839697,0.06476837,0.02858233,-0.03088007,0.0156003,0.01296185,-0.037959,-0.05905242,-0.00859173,-0.05543034,0.04828537,0.00068832,0.02268842,0.07281328,-0.07264324,0.08792319,-0.05974868,0.05390076,-0.02936033,-0.00299973,0.01597171,-0.02408985,-0.01367592,-0.00524343,0.04156761,-0.03211661,-0.00602061,0.01442815,-0.0242123,0.00125868,-0.0344649,-0.01962312,0.00373106,-0.04281733,0.00413837,0.02470035,-0.05713183,-0.01877132,0.07126913,-0.02976602,0.04805334,-0.0294771,-0.01892193,-0.00169116,0.12112759,-0.02672748,-0.01281016,0.03146227,0.03137909,0.05506125,0.01710912,-0.08334254,0.03924645,0.02692986,-0.04309489,0.05078486,0.19220014,-0.02342138,-0.07497144,-0.08301561,0.06012891,-0.01304069,0.01396147,-0.01131625,0.03573603,-0.01290012,-0.02747453,0.04127254,-0.02034227,-0.03019995,0.09787008,0.05105501,0.03198245,-0.0645265,-0.02418632,-0.00036951,0.04253107,0.01737167,-0.1059257,-0.0279234,-0.07166601,-0.01034758,0.04396914,-0.07556749,-0.00092835,0.00802421,0.02004699,0.05220098,-0.00191913,0.02906858,-0.02352745,0.02494163,-0.04219358,0.08045039,0.07367668,0.00095318,0.04611665,-0.08120517,-0.01325691,-0.03692415,-0.02455156,0.01035205,0.01944496,-0.07221895,-0.01194624,0.06368872,-0.01463006,-0.02872623,0.06142992,0.01317523,0.08056007,0.02130511,0.03926867,0.02790168,0.03781322,-0.09041749,-0.19300123,-0.00294805,-0.021485,-0.04795514,-0.03550457,-0.03748554,0.05809686,0.03893634,-0.0502652,0.03755178,0.11200894,-0.0469225,0.0302453,0.05215729,-0.01361534,-0.00615936,0.02534544,-0.04458911,-0.02829829,-0.02414407,0.02300952,-0.03288933,-0.01281025,0.0070547,0.07741066,0.03410138,0.09880973,0.01085906,0.04953265,-0.04561435,0.05530087,0.00717819,0.02634583,-0.14659624,-0.00410112,0.0155271,-0.01040841,-0.00254496,0.00984958,0.01078246,0.02523882,0.01451519,-0.00348144,-0.07570255,-0.02722367,-0.08607016,-0.03851839,-0.02199189,-0.06575353,-0.03897414,-0.04226194,-0.00082799,0.03203826,0.06601993,0.03948087,-0.00751924,-0.03987413,-0.0094433,0.01963823,0.00466388,0.00794336,0.0154073,0.02355474,-0.07837664,0.02570479,-0.00500541,-0.02859314,-0.06922428,0.02475935,-0.1145069,-0.01515592,0.04058829,-0.00660193,-0.01329746,0.05003927,-0.02867522,0.00392246,-0.05461843,-0.04065387,-0.04367094,0.02719732,0.00084435,0.10013307,0.04433837,-0.0074194,0.03321138,-0.05122836,0.06376091,0.02300987,-0.04191743,-0.05409227,0.00758903,-0.07604045,-0.01560021,0.12119664,0.05661483,-0.24391185,0.06048901,0.01318365,-0.01963621,-0.03509475,-0.00384738,0.04242313,0.00606644,-0.05501781,0.00768814,0.01743808,-0.04617994,0.05989656,0.02595372,0.02409775,0.05731908,0.08821093,-0.02726377,0.05032896,-0.07392528,0.00769726,0.03958633,0.19607957,-0.03080078,0.03278461,0.08721813,-0.03707986,0.03247876,0.07172593,0.02493671,0.03560476,0.01785892,0.08458546,-0.07427063,-0.04102014,-0.00945978,0.01200336,-0.01949409,0.00947577,0.00152776,0.0011966,-0.00784254,-0.00909057,0.00048704,0.05077682,-0.05470949,-0.00327299,-0.09542522,0.03375102,-0.00316487,-0.04694355,-0.00823787,0.0293362,0.01610889,0.01249838,0.06989412,-0.02651495,-0.01572834,-0.04990131,-0.03862828,-0.00005239,-0.06742472,0.02857146,0.06795464,0.0260818],"last_embed":{"hash":"cb0c97f917617473ed10426f2255af447c43014809b389da9402083f7b070897","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"cb0c97f917617473ed10426f2255af447c43014809b389da9402083f7b070897","at":1743662878028},"key":"Microservices.md#2. Các services#{16}","lines":[166,169],"size":271,"outlinks":[{"title":"Daytona","target":"https://github.com/daytonaio/daytona","line":2},{"title":"Devcontainers","target":"https://code.visualstudio.com/docs/devcontainers/containers","line":3},{"title":"Lapdev","target":"https://github.com/lapce/lapdev","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06689844,-0.05621272,-0.0170484,-0.02045078,-0.01433054,-0.05880253,-0.05537519,0.04365075,0.00340378,0.00166705,-0.04208049,-0.0591506,0.0133608,0.03043124,0.10943526,-0.00207559,0.07029587,0.03058987,0.00188347,0.00822106,0.10333687,-0.02425341,-0.07088084,0.03221068,0.01809634,-0.00345824,-0.02794459,0.01843155,-0.0404438,-0.14753875,-0.03629871,-0.03915107,-0.00510127,0.0301333,0.03460911,0.01499103,-0.01201896,-0.00193135,-0.06924922,0.06072779,0.02776767,-0.04031676,-0.01161233,-0.00605461,-0.01093997,-0.11214548,0.00959377,0.01154036,0.01733981,-0.06646564,-0.03197506,-0.00081071,-0.00035548,0.00176721,0.04796051,0.02350158,0.07441447,0.02458878,0.03405942,-0.02853142,0.07930388,0.0322696,-0.19562955,0.09654876,-0.00699918,0.03785601,0.02624859,-0.00464847,0.04155216,-0.02768062,-0.0519644,-0.02156778,-0.05124817,0.10225721,-0.02689088,-0.03727051,0.04704446,-0.03477538,-0.04702856,0.03795084,0.01574865,0.01594108,-0.01387519,0.02450338,-0.01888503,-0.00384023,-0.01695327,0.00463556,0.04927804,0.00626784,-0.03955993,0.03334843,0.0205957,-0.00199318,-0.09650093,-0.0230815,-0.00400759,-0.00775352,-0.00038249,0.12497184,0.04416228,-0.011902,-0.00782957,-0.0230782,0.10452089,0.01021872,-0.04969174,-0.04592801,0.00153246,0.06129393,-0.0177167,-0.01395064,0.01906776,-0.01640379,-0.05677183,0.05694577,0.00681861,0.04530869,-0.00664938,0.05004791,0.00121425,-0.01325748,0.10149188,-0.09740357,0.0802045,-0.06432325,0.05577833,0.02939117,0.01036714,0.04871709,-0.01273736,0.03771611,0.01584698,-0.01661564,0.02737302,-0.0272876,0.00475529,-0.04014198,0.01160556,-0.00742311,0.00049394,-0.0402084,0.00991674,-0.02943105,-0.06109881,0.04869363,0.00380254,0.07517198,-0.0627935,-0.00556873,0.02044693,0.08407427,0.01219919,-0.04290273,-0.02081623,0.04746193,0.03264135,0.15314126,-0.0467558,0.02458403,0.00632356,-0.07739203,0.00042053,0.10722171,-0.00731291,-0.10183839,-0.003092,0.032896,0.02159785,-0.03055586,0.00225071,0.05117612,0.02968521,-0.0538713,-0.00308388,-0.00983356,0.02482592,-0.03497868,0.06119513,-0.0117195,-0.10659533,-0.02069996,0.05199596,0.09668468,0.02452937,-0.0663213,-0.05785068,-0.0362213,0.02252844,0.00451013,-0.06052176,-0.01091267,-0.00529556,-0.00498362,0.03924009,0.00617434,0.01629358,-0.05196028,-0.02000218,-0.01286733,0.08101226,-0.01194376,-0.05943382,0.02980383,-0.05980103,-0.01380258,-0.02174229,0.02090551,-0.01846311,0.0464118,-0.00293767,0.01396421,0.01869578,0.01697419,-0.09322121,0.01615427,0.04962759,0.09535104,0.01281847,0.0279529,0.03118473,0.03998354,-0.05354407,-0.21120253,0.00507439,0.03981349,-0.09472407,0.0761442,-0.02104378,0.0502334,0.05188374,0.03040606,0.06045957,0.13089532,0.05238552,-0.0417243,0.00672266,-0.00208269,0.05067129,-0.012431,-0.04342256,0.0066511,0.00581316,-0.00932425,0.03421951,0.04498459,-0.02105862,0.04450078,0.03562519,0.09420332,0.00033015,-0.01332663,-0.04305316,0.01986615,-0.00756421,0.02553981,-0.11327709,0.03596366,0.03096045,-0.01967342,0.03136193,0.02087387,0.00096693,-0.02548057,0.02936196,-0.03829487,-0.11550575,-0.01149773,-0.04874078,-0.02923123,-0.0518378,-0.0607175,-0.07671308,-0.01213104,-0.01400089,0.03221159,0.06613708,0.07060595,-0.01561032,0.00772065,-0.05730937,0.0217842,-0.01234302,0.04540789,0.0509764,0.01891561,-0.0951604,0.0529989,-0.02585641,-0.02892828,0.03181577,0.04372366,-0.02123156,-0.03669529,0.10445379,-0.0108285,0.03711535,0.00367062,-0.0080041,-0.01411395,-0.12450264,-0.03395686,0.02837442,0.04561061,-0.06592368,0.07668965,0.02232983,-0.02404634,0.02747917,-0.02049487,0.03377233,0.00726689,-0.02059258,-0.00161541,-0.02449877,-0.07192577,0.02791743,0.06823589,-0.02353609,-0.25228769,0.01376379,-0.06704728,-0.05965607,-0.06779804,0.02197279,0.02879568,-0.01636222,-0.06743083,0.00932331,0.09439261,0.03198452,0.0116026,-0.01053238,0.02431503,0.08291934,0.04816498,-0.04826853,0.00584914,-0.07333338,0.01681606,-0.03234458,0.17615114,0.04835831,0.03670778,0.01034344,-0.04697268,0.02456104,0.03913872,-0.01026752,-0.02408412,0.02462833,0.06743746,-0.05637991,0.00865912,0.01664035,0.01725239,0.00757042,-0.02264398,-0.01437456,-0.0529306,0.05711968,-0.00706439,-0.0078954,0.0792357,-0.00271654,-0.10275901,-0.09285418,0.04076442,0.01705196,-0.07039949,-0.05613456,-0.01905634,0.05096928,0.05092019,0.02045221,-0.06290774,-0.01660535,-0.02752919,-0.01796705,0.07790707,-0.00130343,0.04730817,0.03341389,-0.02879775],"last_embed":{"hash":"6697fec1d361085d643ef18ec25b4c4647531ad54790cf3da8c8afc6fea51768","tokens":181}}},"text":null,"length":0,"last_read":{"hash":"6697fec1d361085d643ef18ec25b4c4647531ad54790cf3da8c8afc6fea51768","at":1743662878042},"key":"Microservices.md#2. Các services#{19}","lines":[176,190],"size":550,"outlinks":[{"title":"Encore","target":"https://github.com/encoredev/encore","line":12},{"title":"Novu","target":"https://github.com/novuhq/novu","line":13},{"title":"Apache Dubbo","target":"https://github.com/apache/dubbo","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{30}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04067422,-0.01387074,0.05572326,-0.02217904,0.00404628,0.01055574,-0.00757873,0.06774915,-0.02697348,-0.03851163,0.0377727,-0.05443297,0.01532351,-0.03164748,0.04882485,0.03378259,0.0002268,0.00575592,-0.01174665,0.01852782,0.07208029,-0.05964486,-0.02380817,-0.00312587,-0.02032324,0.0404952,-0.05930905,-0.02261309,-0.09717613,-0.19050151,-0.04159774,-0.00943026,-0.02126539,-0.00606446,0.02828974,0.01537275,-0.00841071,0.00633919,-0.011828,0.03115436,0.0081942,0.02836242,-0.02096074,-0.00210132,-0.05739346,-0.07181832,0.01142999,-0.00544815,0.04776727,-0.00794156,0.05388976,0.00014476,0.00704144,-0.01243121,-0.05019826,0.03958698,0.07956508,0.0622509,0.03010547,0.01322099,0.11858895,0.02654611,-0.16607508,0.06185765,0.03401139,0.07314599,-0.03953664,-0.03692471,0.05938269,-0.00772079,-0.04355657,0.00367362,-0.02668732,0.12001503,0.02347169,-0.02995223,-0.00327266,0.02787673,-0.03877702,0.03801549,0.06888974,0.01879505,-0.026913,0.0178484,-0.04260296,0.00805959,-0.02354212,-0.04324808,0.07702254,-0.03560533,-0.03702275,0.01615095,0.01490799,-0.00161754,-0.05848359,-0.00928986,0.01429889,-0.02930007,-0.06358835,0.11655045,0.05392848,-0.02155576,0.00800646,0.01211349,0.08620279,-0.02714943,-0.00818877,-0.05767419,-0.01075749,0.05978264,-0.0786993,0.0095461,-0.00526578,-0.08298506,-0.00186872,0.05474906,-0.03796129,0.06910677,0.02713027,0.00571929,0.01752852,0.04766482,0.104671,-0.01030627,0.08341162,-0.03973421,-0.00530313,0.00299007,0.01791773,0.02863516,0.05671345,0.03713336,-0.03302338,-0.04167853,-0.01886862,-0.00610814,0.00024507,0.01354385,-0.04015353,0.00938791,0.00886523,-0.01692449,0.05196324,-0.0750574,0.03698264,0.06373389,-0.01821969,0.05260015,-0.01019449,-0.041879,-0.0398477,0.0401212,0.00632788,-0.05801293,-0.00394022,0.05886004,0.07305803,0.04786348,-0.06940062,-0.00543708,0.00161744,0.00575075,-0.00369937,0.13617787,0.01415574,-0.12351693,-0.02648681,0.01455568,0.04158823,-0.01889712,0.0119718,0.01954473,0.06171956,-0.06294888,0.04342668,-0.0640958,-0.07976196,0.05277991,0.02891762,0.00590809,-0.03378513,-0.04727488,0.03684253,0.0741892,0.0341651,-0.07496877,0.00858026,-0.02677811,-0.03178596,-0.04325964,-0.10179636,0.01137323,-0.06958215,-0.02194695,0.02526423,-0.01107045,-0.01475006,-0.03428935,-0.03676292,-0.02379677,0.04368152,0.02155789,0.02857923,0.07581601,-0.03368621,-0.01612514,-0.07800119,-0.04290334,0.01856838,0.00577633,0.00512657,0.0111675,0.06192292,-0.00222503,-0.06164538,-0.01580469,-0.01836537,0.063811,0.02293826,0.03886106,-0.00619564,0.0910152,-0.03061274,-0.21568748,-0.03803204,0.00806792,-0.02910445,0.05570636,-0.04675698,-0.00081002,0.0390937,-0.02313671,-0.0243505,0.11477696,0.00567475,-0.07734039,0.04922916,0.00245274,-0.01451826,-0.01183963,0.00214899,-0.05002394,0.03223214,0.0078184,0.00708791,-0.07389852,-0.06894345,0.04013724,0.01954576,0.12673055,-0.05255968,0.0400005,0.02123807,0.02277201,0.03375335,0.01993365,-0.1527956,0.0339559,0.03851391,0.01550122,0.00244558,-0.04632957,0.00744623,-0.01480285,0.02096912,0.01877991,-0.10810958,0.02429661,-0.03099067,-0.11861413,0.0202912,-0.04607974,-0.09685169,-0.04266229,-0.0325589,-0.00636037,0.06484891,0.0435728,-0.04968685,0.02518287,0.00957138,-0.04557832,-0.01611044,0.00254835,0.0526276,0.01295467,-0.01021146,0.04297003,-0.04031242,-0.02231071,-0.04478475,-0.00364392,-0.10199835,0.00507452,0.10402119,-0.00598872,0.06317965,-0.00320957,-0.03840833,0.02130708,0.00011584,-0.00753002,0.00248214,-0.02539727,-0.00342063,0.10053925,0.01900931,-0.00598202,0.02236047,0.03011,0.02590425,0.0571194,0.01344786,0.00059291,-0.00310438,-0.06721707,-0.00049559,0.09558005,0.03639,-0.24824446,-0.0123197,0.03391849,-0.02719581,-0.04473976,-0.01139951,0.05931989,0.03758518,-0.05385457,0.06115185,0.02776227,0.01188688,0.03946313,0.03556618,0.04487278,0.03363888,0.10339478,-0.07354157,0.00886826,-0.05721223,0.05415859,-0.01096442,0.20099893,0.0392581,0.00884894,0.04256748,-0.01089977,0.09883589,0.05199363,0.02048967,-0.00583698,-0.02675384,0.01551325,-0.04623104,0.01806014,-0.01940943,0.03442741,-0.00274176,-0.01075118,0.00483415,0.01312521,0.00084514,0.02221583,-0.04270636,0.09017472,-0.06645612,-0.03754477,-0.1278535,0.01046871,-0.03882223,-0.03326366,-0.05505429,0.01796638,0.03274396,-0.0011556,0.01485171,0.02484315,0.01413783,-0.05267345,-0.02890135,0.05910805,-0.09080978,0.045753,0.03145226,0.01775848],"last_embed":{"hash":"17f442899caca6becc20203192957a564f032fda9f9f28508791beb3ca319284","tokens":375}}},"text":null,"length":0,"last_read":{"hash":"17f442899caca6becc20203192957a564f032fda9f9f28508791beb3ca319284","at":1743662878058},"key":"Microservices.md#2. Các services#{30}","lines":[222,237],"size":1451,"outlinks":[{"title":"Spinnaker","target":"https://spinnaker.io","line":2},{"title":"trunk-based development","target":"https://trunkbaseddevelopment.com/","line":5},{"title":"configure","target":"https://www.flipt.io/docs/configuration/overview","line":10},{"title":"OpenTelemetry","target":"https://opentelemetry.io/","line":13},{"title":"Prometheus","target":"https://prometheus.io/","line":13},{"title":"Filesystem, Object, Git, and OCI declarative storage backends","target":"https://www.flipt.io/docs/configuration/storage#declarative","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{35}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02394973,0.003702,-0.00617702,-0.03180539,-0.0142075,-0.04648632,-0.01614344,0.02741714,0.01441933,0.01261411,-0.06067308,-0.05986329,0.01051787,0.00314628,0.0818925,0.05483331,0.02647837,0.00417599,-0.0657898,-0.05397006,0.09426542,-0.01876715,0.01166689,0.0482602,-0.01094042,0.04589082,-0.05730178,-0.04596212,-0.06570473,-0.13543026,0.01331036,-0.03869123,0.03855018,-0.00341474,0.0163603,-0.0852182,-0.0121012,0.00690395,-0.06314959,0.01811649,0.02658197,0.00445504,0.00984711,-0.05258631,-0.0488287,-0.14268585,-0.0099201,-0.02152004,-0.03565579,-0.0481272,0.09292988,0.02402544,0.02217234,0.03484312,0.00868249,0.0207299,0.00976493,0.09198039,-0.00606251,0.04047527,0.06203088,0.04846054,-0.15031491,0.04731266,0.01338288,0.01042073,0.00552796,-0.00219657,0.0219291,0.01334166,-0.02000872,-0.0139396,-0.06099808,0.12001292,-0.00863777,-0.02301959,0.10266199,0.01929016,-0.03557469,0.04862602,-0.02739514,0.09734423,-0.00312954,0.02145552,-0.03777122,0.01277333,-0.00162459,-0.02749489,0.03654523,-0.03943111,-0.06138973,-0.01825882,-0.00362852,-0.00021295,-0.08035965,-0.0672031,-0.01309289,-0.0106957,-0.05291139,0.13838133,-0.0058152,0.0239542,-0.0220491,-0.0556423,0.08938955,-0.03817544,0.03470377,-0.03622713,-0.04898091,0.02909668,-0.03501876,-0.02247124,0.01366203,-0.05427648,-0.02760448,0.05820746,-0.01358117,0.02757221,0.006053,0.01566101,-0.00040891,0.01037288,0.07274228,-0.04693256,0.10717581,-0.01921778,0.0688388,0.04304605,0.05438814,0.03500049,0.01524248,0.07362761,-0.04993054,0.00572981,0.0216084,0.00281388,-0.0268469,-0.00973817,0.03553689,0.02613536,-0.00736059,-0.01925873,0.04615934,-0.05572791,-0.03881505,0.08773121,0.00185501,0.04094192,-0.02594443,-0.00971935,-0.03057629,0.04522727,0.0013033,-0.05594807,0.00458693,0.0159788,0.01874764,0.06528068,-0.0413036,0.00709204,-0.01545715,-0.0237158,0.00911977,0.09318406,0.02790131,-0.11080154,0.02977808,0.03709962,-0.01299845,-0.07474221,0.00071521,0.0535164,-0.0143509,0.01971231,0.04886411,-0.02549868,0.01260259,-0.01184842,0.08872239,-0.00645076,-0.03578021,-0.04541858,0.04549491,0.04858572,0.018492,-0.05291973,-0.02969018,-0.01061488,0.02598149,-0.02183267,-0.04976459,0.00124013,-0.00284657,0.01005999,-0.02649071,-0.00917937,0.0192835,-0.0792644,0.03242945,-0.04651319,0.05278556,-0.00685561,-0.02078153,-0.0048357,-0.03665605,-0.0373734,-0.01147091,0.03953148,0.04036681,-0.03215985,-0.00394997,0.01329079,0.05382299,0.01191395,-0.09767278,0.05912513,0.02796127,0.04934514,0.02319894,-0.01717369,0.0316891,0.09358017,-0.04319272,-0.19665465,-0.02186736,0.06602702,-0.07400025,-0.0563496,-0.06148029,0.05510842,0.04905001,0.02078173,0.02491366,0.15148221,0.05497947,-0.01120415,0.03337959,0.0879335,0.09403249,-0.02690453,-0.02276203,0.01352097,0.00747506,-0.00402587,0.04191472,-0.02510344,-0.02704572,0.05937441,0.01318999,0.11203129,0.01888643,-0.02890043,-0.00239346,-0.00403378,0.04107986,0.05337358,-0.18477678,0.01461858,0.01992738,-0.03547322,-0.01827867,0.00562094,0.03113554,-0.04350413,0.03313434,-0.00641034,-0.13942663,0.04390256,-0.06676582,-0.03727423,-0.05056008,-0.08812916,-0.02590937,0.00884585,-0.05618186,0.08389968,0.10757486,0.02673911,-0.02611587,0.04108519,0.01157052,-0.0209432,-0.00168923,0.01738796,-0.00190825,0.04063471,-0.08716225,0.01911388,0.01386592,-0.05350131,0.02907051,-0.02143868,-0.02549359,-0.07804085,0.08996464,-0.12056655,0.02345285,0.04956616,-0.01491408,0.05269351,-0.06796332,-0.06410317,0.01515964,0.03980631,-0.01360509,0.03435344,0.03851175,-0.04819435,0.00986708,0.05219598,-0.01715878,0.02045333,-0.0131755,-0.00888856,-0.03525726,-0.02867416,-0.00715831,0.05786594,-0.00053038,-0.24182436,-0.00317076,0.00711933,-0.05737414,-0.04052801,0.01636428,0.00370811,-0.04537836,-0.05903563,0.01093154,0.00130029,0.01791728,0.02942798,-0.02616148,0.0169101,0.08145566,0.03963281,-0.05885876,0.02842365,-0.1018158,-0.02028502,-0.0128564,0.20630094,0.04895362,-0.04500808,0.00056229,-0.02630685,0.0177757,0.09150297,-0.01532427,0.04727597,0.01196968,0.07839265,-0.08128272,0.01095355,-0.03492243,0.02870162,0.01810077,0.01948239,-0.00295322,-0.00661928,0.05876964,-0.05395121,-0.0342898,0.0126485,0.00744251,-0.06331674,-0.05117755,0.00287855,0.00663106,-0.0043817,-0.06050613,-0.00399821,0.09849041,0.02563583,0.05697991,-0.02735177,-0.02344782,-0.05301632,-0.01383837,0.04267621,-0.00959072,0.05208232,0.0623058,-0.00126957],"last_embed":{"hash":"512e978dee1213ac00f29d3b5c39e20b129b95b479c409f0f65f9dbcc47eebe1","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"512e978dee1213ac00f29d3b5c39e20b129b95b479c409f0f65f9dbcc47eebe1","at":1743662878091},"key":"Microservices.md#2. Các services#{35}","lines":[249,257],"size":577,"outlinks":[{"title":"Novu","target":"https://github.com/novuhq/novu","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{36}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05318959,-0.06812165,-0.03416632,0.00330163,-0.02692625,-0.02800916,-0.05890325,0.07489783,0.03253086,0.02690091,-0.02205712,-0.04496641,0.02997989,0.06454143,0.04431158,0.0083067,0.02445196,0.01049155,-0.05365609,-0.02585282,0.05728049,-0.05722018,-0.00004661,0.01186664,0.01530288,0.04748763,-0.07818606,-0.04629002,-0.04358816,-0.19116482,-0.02468587,0.00094139,0.00735975,-0.02511106,0.08239697,-0.04783978,-0.01659035,0.02630348,-0.00187901,0.02634723,0.0396062,-0.00677038,-0.00520276,-0.0200431,0.03163569,-0.07479885,0.04290065,-0.04617636,0.01268769,-0.04214478,-0.02160598,-0.02342767,-0.13342537,-0.01525328,0.00980725,-0.04267469,0.0546232,0.04660977,0.01533599,-0.01514376,0.04344321,0.03083487,-0.21542865,0.12551267,0.04582797,0.05005835,-0.02989894,-0.01765135,0.05612071,-0.01725189,-0.00689531,-0.00987612,0.03484787,0.08890275,0.02047973,-0.0408351,0.04168349,-0.00643373,0.02903389,-0.00163693,0.01645392,0.01713876,-0.08050252,0.01824287,-0.01998563,-0.01516314,-0.00556655,-0.05018986,0.09780022,-0.07362817,-0.01984219,-0.05378168,0.07091548,0.0085868,-0.04981996,0.02262197,0.04778729,0.00148775,-0.06331357,0.11615837,0.01702165,0.00249781,0.00943178,-0.01052583,0.0308597,-0.07688884,-0.04908618,-0.06783261,-0.02404609,-0.00039415,-0.05312908,-0.03792706,0.00122155,-0.07333104,-0.09373637,-0.04727513,-0.02788281,-0.03260939,-0.03025068,0.03102392,-0.02136502,-0.02264073,0.06028906,-0.01689832,0.04532748,-0.04212325,0.02192875,0.03018367,0.03375553,0.01412813,0.05329825,0.06715619,-0.04087215,-0.03832281,0.01435344,-0.02234338,0.0005146,0.03267844,-0.05109072,-0.02034185,-0.03490116,-0.03638591,0.02849996,-0.01266139,-0.00976084,0.02626872,0.0339917,0.06509209,-0.01557968,-0.05446657,0.01730667,0.07894722,-0.03662555,-0.04273071,0.00888031,0.05091046,0.03226844,0.07234891,-0.03447859,0.04960823,-0.01870869,-0.07054678,-0.05999674,0.09732824,-0.03630994,-0.1760432,0.00282911,0.05626533,-0.00808448,-0.02226947,0.04579949,0.00277731,-0.05173934,-0.04291647,0.06890959,-0.01707425,-0.05612785,-0.0076762,0.02006356,0.04044586,-0.04238988,-0.03879678,0.05572762,-0.01909582,0.02303632,-0.04620805,-0.00629886,-0.05035807,0.05158668,-0.00080567,-0.05323654,0.00525744,0.01249006,0.00120071,0.00623502,-0.02818806,0.01408604,-0.03353554,0.0476421,0.02119183,0.08118262,0.08457016,0.02921071,0.04417079,-0.0120502,0.01822544,-0.03609153,-0.00207911,0.04342807,0.00041494,-0.04795568,0.00577864,0.05782311,-0.00097991,0.00031715,0.00201062,-0.02103832,0.06873208,-0.00332473,-0.00799096,-0.00035557,0.04314972,-0.05930195,-0.21050055,-0.00313071,-0.00938407,-0.0167247,-0.01318898,-0.04546222,0.04000193,-0.0220674,0.01059128,0.03887172,0.15304729,-0.02028535,0.04622519,0.03095799,-0.02516503,0.0551937,-0.01355901,-0.0343015,-0.00700459,0.05114852,0.06070051,0.0039152,-0.0404137,-0.07490002,0.04870448,0.02148656,0.15333943,-0.01720278,0.00579994,-0.05754612,-0.02429684,-0.00836709,0.02492212,-0.085829,0.06585454,0.05663558,0.04461665,0.00016336,0.02018563,-0.02298503,-0.03332937,-0.00092021,0.04204576,-0.15888003,0.01203044,-0.06436636,-0.08156627,-0.07020148,-0.04206231,0.00937166,-0.03210239,-0.02152214,0.03937046,0.07133371,-0.0192182,-0.03029152,0.01417532,0.02322604,0.01268092,0.03856104,-0.00547635,-0.02032416,-0.00999149,-0.02458963,0.03195334,0.01538474,-0.033413,-0.02281923,0.03973405,-0.03555448,-0.01725784,0.12987325,-0.00469148,0.10301554,0.03062556,0.0043493,0.03223514,-0.0079095,0.02957126,-0.00488388,0.03683054,0.02057183,0.06439932,0.06985851,-0.03175166,0.02809072,0.10474439,-0.03219727,-0.03890421,-0.07062815,-0.00660839,-0.0177079,-0.03401828,0.00670697,0.05203289,0.01487094,-0.23453142,0.00058159,-0.0479376,-0.02660134,-0.00479661,-0.00463846,0.0939957,-0.03005445,-0.00295673,0.02029629,-0.0031367,0.00875926,0.03401265,0.00224057,0.04707585,0.00902462,0.06732062,0.01426529,0.0447086,-0.07116321,-0.02757013,-0.01917479,0.22248314,-0.00196477,-0.01391834,0.02011286,-0.01155166,-0.01558135,0.06135677,0.05487867,0.02920902,0.07634039,0.12315251,-0.04323067,-0.06221516,0.02879437,-0.07438296,0.034068,0.00902387,-0.00596056,-0.00007411,0.01254154,0.02352535,-0.0182291,0.02441597,-0.01908989,-0.02110188,-0.08954564,0.02285684,-0.00561724,-0.02665214,0.01404166,0.01939793,-0.0109966,0.03028694,0.05555565,0.02715522,0.02629907,0.00651163,0.03964898,-0.01059019,-0.02178046,0.03270349,0.06610668,0.0226634],"last_embed":{"hash":"d6a663544b460cd64be6cde85357cdebd3d6101ff4c7c432f933e0adfe8ae4e2","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"d6a663544b460cd64be6cde85357cdebd3d6101ff4c7c432f933e0adfe8ae4e2","at":1743662878106},"key":"Microservices.md#2. Các services#{36}","lines":[258,283],"size":2277,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{42}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03917848,-0.08598021,0.01328556,0.03870542,0.01250459,0.00699108,-0.03838498,0.03339927,-0.01031927,0.02053062,-0.01416022,-0.02628838,0.09401161,0.01873402,0.09456799,0.07004015,0.02468315,0.01485359,-0.03914117,0.02559054,0.08485471,-0.02219358,-0.04606935,-0.01511158,-0.01156163,0.03306619,-0.01686237,-0.03340607,-0.00796816,-0.16216615,0.02202874,-0.02174391,-0.04780963,-0.06861547,0.01184418,-0.0564188,-0.0171117,-0.01452795,-0.01202105,0.03830529,0.06611095,-0.00809487,-0.06581493,-0.02700714,0.04723065,-0.10699154,0.07148689,0.02141847,0.03114584,-0.03505838,0.02746422,0.02861507,-0.02406385,0.000689,0.01120859,0.01343286,0.05595462,-0.01986916,0.00157593,0.00047037,0.0497896,0.07441314,-0.21732788,0.09175277,-0.00140652,0.0115149,0.05271658,-0.02292324,0.02598451,-0.016285,-0.02389177,0.03629437,0.01615222,0.09477375,-0.02663236,0.017379,0.02478958,-0.0023048,-0.00322357,0.00789876,0.00110166,0.02095223,-0.02233707,0.03256395,-0.04493845,0.00279047,-0.02932732,-0.02069937,0.07404511,0.00919454,-0.04440491,0.01131803,0.0506318,-0.00645503,-0.11110314,-0.05746985,0.04856567,-0.00088137,-0.0382159,0.1228594,0.03590976,-0.0144151,-0.02380285,-0.00787553,0.12082333,0.00911613,0.04414986,-0.01846375,0.00463741,0.02174796,-0.05157856,-0.05371078,0.00238394,-0.047036,0.00475651,0.00169017,-0.02135095,0.0416856,0.04311087,-0.00392833,0.03143117,-0.05133756,0.09746679,-0.04220219,0.06956319,-0.00471374,0.012468,0.02673836,0.01085711,0.00918302,0.01350769,0.0281352,0.03371733,-0.04279464,-0.00387157,-0.0429794,0.02858853,0.03715219,0.04746515,-0.03229409,0.01761028,0.01619693,0.01880664,-0.07695404,0.0034434,0.00957805,0.00911266,0.03993769,-0.0321075,0.01076738,-0.03201798,0.12757295,0.00857749,0.01200201,0.01091979,0.02535147,0.07509473,0.05227286,-0.07454256,0.04816854,0.04032782,-0.00363231,-0.00900135,0.12512743,-0.00286236,-0.12785079,0.01305947,0.03366624,-0.01982251,-0.03536605,-0.06746728,0.01640842,-0.02901947,0.00564248,-0.00132562,-0.00563949,-0.05233862,0.03023546,0.00180342,0.04928039,-0.01093019,-0.0581519,-0.02375381,0.04069714,0.04467336,-0.09802099,0.00070952,-0.05948079,-0.00675753,-0.01232651,-0.14900428,-0.00715087,-0.04621549,0.02020252,-0.00177229,-0.0199766,-0.00373184,-0.06783348,0.06062603,-0.04244967,0.0833283,0.0781722,-0.02002689,0.04394338,-0.05493803,-0.00471412,-0.00319209,-0.00839994,-0.00780077,-0.00989531,-0.0439113,0.01963642,0.02560239,0.03113145,-0.02779919,0.04212971,0.02867357,0.06276489,0.02711347,-0.02406572,0.09958268,0.03926411,-0.10223519,-0.19334468,-0.03139064,-0.0311599,-0.09073238,0.05181115,-0.05559761,0.0641324,0.03351436,-0.01322078,-0.00300727,0.0981241,-0.01237923,0.04287458,0.00537356,-0.04243418,-0.02362722,0.08807342,-0.04061988,-0.02996171,-0.01992274,0.03802474,-0.02372605,0.03243982,-0.02764835,0.06439271,0.00769449,0.12512612,-0.01578254,0.10579621,-0.06033744,-0.03027092,-0.01682163,0.04563281,-0.13164443,0.03873811,0.02959297,-0.02005338,0.00785659,-0.05764336,0.00840723,0.01457374,0.02966121,-0.01506709,-0.1611619,0.01433446,0.02970702,-0.03621041,-0.03041356,-0.06278142,-0.04942177,-0.0442773,-0.04531133,0.00743551,0.04796774,-0.0287296,0.00605804,-0.0344506,0.00437243,-0.00710596,0.06276712,0.01583908,0.04315414,0.00144566,-0.08835637,0.06098342,-0.01411774,0.00642781,-0.00026777,0.05386637,-0.04523619,-0.01971897,0.0869972,-0.0143666,0.06509352,0.04702798,0.02420086,-0.01621475,-0.04608058,-0.00477732,0.01402618,0.02466884,0.02055219,0.03127313,0.03078949,-0.03679122,0.05528057,-0.0090835,0.06496926,0.03705321,-0.00636155,-0.01590877,-0.00772527,-0.07453424,-0.00666885,0.08854423,0.05627636,-0.27178246,0.0611942,-0.03628727,-0.03424544,-0.0437679,-0.03398173,-0.02208202,0.02040031,-0.10148304,0.0305141,0.07105566,-0.02028654,0.0124186,0.02986734,0.01259951,0.05320011,0.0490162,-0.03131054,0.04310631,-0.04317657,0.01286776,-0.01582968,0.19416915,0.01765278,0.04662765,0.02050745,-0.0366305,-0.03971704,0.03242881,0.00401994,0.00182608,-0.05150731,0.08683435,-0.03174726,0.01578328,-0.0008439,-0.03515838,-0.04090195,-0.03046975,-0.03162613,-0.04344225,-0.00151785,-0.01267389,0.00770192,0.05193551,-0.01517255,-0.03884492,-0.07047002,0.03301716,0.03329508,-0.01841175,-0.06225904,-0.01889205,0.07505295,-0.0264263,0.00080459,-0.04255921,-0.02765557,-0.03516977,-0.04767346,-0.0065826,-0.00763937,0.05610555,0.01414396,-0.01887084],"last_embed":{"hash":"c32e5335dc2c78f9e98413eaf88f4b0b66e67e8b1aebdd932046ee8ccac34ed7","tokens":154}}},"text":null,"length":0,"last_read":{"hash":"c32e5335dc2c78f9e98413eaf88f4b0b66e67e8b1aebdd932046ee8ccac34ed7","at":1743662878139},"key":"Microservices.md#2. Các services#{42}","lines":[297,304],"size":426,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{43}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05930405,0.00809116,0.01236544,-0.02875925,-0.00718064,-0.08921553,-0.00944441,0.01982043,0.07174302,-0.03003283,-0.02236258,-0.09581564,0.04369961,0.07088059,0.09909289,-0.00547649,0.04124635,0.01771997,-0.00446354,0.00641725,0.08192715,-0.04062466,-0.03245615,0.01248549,-0.0361964,0.01808519,-0.0170893,-0.03257869,-0.00616278,-0.18273076,0.01734374,0.00758562,0.01614265,0.02614604,0.04309612,0.02210178,-0.01458109,-0.01104648,-0.00161957,0.018797,0.04274179,-0.03747521,-0.02455127,-0.02390503,0.03039917,-0.09676969,-0.00346357,-0.00642474,-0.027502,-0.00009509,0.00304582,0.01298645,-0.02057581,0.03686903,0.05890606,0.03447961,0.04601413,0.05083706,0.01915278,0.01407889,0.09709986,0.08293679,-0.22779204,0.10934941,-0.03556982,0.04841117,0.05291225,-0.01290791,0.04529571,0.06012822,-0.03175916,0.01546907,0.00040384,0.07486968,0.01835077,0.00816632,0.04916619,-0.0506135,-0.01037803,-0.03968832,0.02326907,0.05679951,-0.03219187,0.00001838,-0.0487577,-0.02079461,-0.05167074,-0.02401054,0.05952313,-0.05191336,0.00840948,-0.01095694,0.0223118,-0.00318442,-0.0251583,-0.03589397,0.02478461,0.04391512,-0.04812515,0.11990747,0.02804846,-0.03024141,0.01307211,-0.0396565,0.06285981,0.01636136,-0.02840607,-0.08332799,0.00996075,0.01442831,-0.0768465,-0.01595348,0.04661676,-0.05238515,-0.02095276,0.024655,-0.01176779,0.0285502,0.04190203,-0.00487302,-0.03773113,-0.00884445,0.04286671,-0.0562037,0.04954021,-0.06403219,0.04078044,0.02293916,-0.02059231,0.02439114,0.05914738,0.03622695,-0.02473132,-0.05871361,0.00886775,-0.04953479,0.00328003,-0.05417206,-0.0400666,-0.03716059,-0.05435253,-0.06024058,0.0016005,-0.03097592,-0.063738,0.05381382,0.00775643,0.0493576,-0.06304541,-0.06614203,0.01796886,0.07889477,0.01384255,-0.05510582,-0.01246698,0.01615265,0.07909575,0.10953013,-0.10629087,-0.00258189,0.02994985,-0.05821775,-0.04375967,0.18572783,-0.02926822,-0.10635666,-0.05738759,0.03262127,0.02074128,-0.02337613,0.05288131,0.00994386,-0.0732417,-0.01060208,0.05813101,-0.02477123,-0.06230891,-0.00671715,-0.0252204,0.00371786,-0.08154761,-0.05649256,0.02957848,0.06803329,0.0382662,-0.044701,-0.01448254,-0.03435883,0.01702596,-0.02717174,-0.05008369,0.07274955,0.00241921,-0.04356901,-0.0190599,-0.00626233,0.04118304,-0.00165181,0.01219574,-0.05683879,0.0972784,-0.00299946,-0.04351296,0.00593415,-0.00272837,-0.01451028,-0.04428157,0.00854644,0.03139941,0.03625126,-0.02460103,-0.00458012,0.07731359,0.01405215,-0.03489204,0.00900305,0.03454229,0.06652091,-0.03557003,0.05027889,-0.00801454,0.01357413,-0.07028981,-0.17734225,-0.01114133,0.01195892,0.00537111,0.00698772,-0.07071447,0.04378618,0.03326825,0.05077666,0.07186096,0.03108526,0.02161679,-0.01091005,0.07126214,0.00071108,0.02904182,0.05798885,0.02412754,0.00017742,0.03104225,0.0120173,0.01520098,-0.03131511,-0.07662027,0.03089035,0.01551337,0.12414013,0.03183594,-0.03001416,-0.01640415,0.02227528,0.00631143,0.01389795,-0.10797761,0.08998699,0.01391648,0.00567658,0.01011122,0.02188339,-0.02520201,-0.02755556,0.02619331,-0.01741463,-0.11374446,-0.01432757,-0.04607129,-0.04054105,-0.01946879,-0.13998736,0.01501892,-0.05949213,0.05208047,-0.01542183,0.04065072,0.02104249,-0.0317173,-0.0328133,-0.00436679,0.01000516,-0.01591994,0.01827656,-0.013913,0.0215479,-0.05262166,0.05469678,-0.04094236,0.03968111,0.04555058,0.03552904,-0.00815491,-0.05526487,0.1084684,-0.03885838,0.04514067,0.01859556,-0.05164784,0.03486313,-0.00770331,0.00021628,-0.01203411,0.03826789,-0.00423102,0.03654525,-0.00256962,0.01591683,0.00060578,0.0575016,-0.00024363,0.07523175,-0.01145171,-0.00287887,0.02308925,-0.03086627,0.02115493,0.07667112,0.0418145,-0.26736131,0.02010861,0.00011666,0.02555717,-0.05525187,0.00927505,-0.00190703,-0.04342425,-0.01126046,0.00954138,0.04811114,0.01869917,0.04694794,-0.0477253,-0.04314368,-0.01308687,0.03093386,-0.073635,0.0331146,0.0034514,-0.04511769,0.00887039,0.22744937,-0.00181754,-0.00805297,0.05215947,-0.00625801,-0.00825871,0.0053513,-0.03438477,0.00017924,0.00131788,0.14225572,-0.06535578,0.00035523,0.11711962,-0.06644719,0.02188373,0.04586897,0.0208951,-0.02758761,0.0288232,-0.03458768,-0.01461185,0.09123434,-0.01208541,-0.0464451,-0.04134885,-0.0008455,0.05224353,-0.03507498,-0.07382695,-0.04180776,0.01665425,0.00021183,0.01188074,-0.04251396,-0.01620435,-0.02837248,-0.01956493,0.01302403,-0.022432,0.04668565,0.04639097,-0.03005431],"last_embed":{"hash":"ec9511abf226283f2b982dd3245cf1e0ed118e6a51c0747e4998563119617546","tokens":426}}},"text":null,"length":0,"last_read":{"hash":"ec9511abf226283f2b982dd3245cf1e0ed118e6a51c0747e4998563119617546","at":1743662878154},"key":"Microservices.md#2. Các services#{43}","lines":[305,317],"size":1139,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#2. Các services#{45}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04211912,-0.06397562,-0.02303953,-0.04628554,-0.04313393,-0.05562119,-0.05881743,0.01155526,-0.01799613,-0.03152851,0.0026205,-0.03781852,0.02093922,0.0486259,0.09669545,-0.01104638,-0.0022344,0.00871733,-0.03564464,0.00446286,0.04014004,-0.04008737,-0.05615617,0.05191932,0.01781758,0.0199611,-0.08493798,-0.04155752,-0.06256189,-0.14988385,0.0023705,0.00061789,-0.02242221,0.02040508,0.07579585,-0.05036551,0.03327454,0.00228464,-0.07134904,0.05403836,0.02825031,-0.04056755,-0.02340104,-0.02368993,0.02952759,-0.09519529,0.0154733,-0.00477796,-0.00301652,-0.05825596,-0.04170203,0.01193714,-0.05683587,0.04633924,-0.01061115,0.02705247,0.0452716,0.04154797,-0.02689849,0.04698999,0.05826549,0.01859754,-0.23650889,0.12145761,-0.01223738,0.07843903,0.01917247,0.01300052,0.00022955,-0.03706851,-0.01715097,0.00403546,-0.05485751,0.08603317,0.0110498,-0.03386291,0.04485019,0.02765315,-0.04274626,-0.00880339,-0.04723695,0.03446222,0.01528021,0.03654077,-0.04155285,0.01508467,-0.00868582,-0.00900239,0.06964539,0.02335483,-0.02353455,0.03139981,0.03039335,-0.01189746,-0.07308006,-0.05660576,0.00556775,0.04639178,-0.06702921,0.11090846,0.05309006,0.03512833,-0.01465683,-0.01041212,0.06732329,-0.04064096,-0.03019107,-0.08369596,0.03170382,0.0676422,-0.03523881,-0.00365007,0.02945448,-0.05157734,-0.05592623,0.05327838,0.01280624,0.02247774,-0.02294192,0.01676155,0.00255929,0.00677718,0.10332806,-0.01148577,0.08350728,-0.03639187,0.03124079,0.01041879,0.07182769,0.08999177,0.06222818,0.07092776,-0.008758,-0.03625595,-0.05506276,0.00709883,-0.01665692,-0.03678398,0.06873088,-0.0277674,-0.00237962,-0.03549672,-0.00074503,0.00835413,-0.02882343,0.06755683,-0.01926578,0.07131378,-0.05317498,-0.01739198,-0.00756901,0.06550656,-0.00366039,-0.05526106,-0.01064914,0.0106054,0.0382752,0.08304255,-0.06700493,0.02227619,0.00756409,-0.03508014,-0.00400716,0.09414787,0.04184054,-0.14636777,-0.01846529,0.05449309,-0.02829663,-0.0448534,-0.00797158,0.02824392,0.02442954,-0.01003871,0.05258473,-0.00064279,-0.02843343,0.01271157,0.03114303,0.03715136,-0.06319597,0.0133384,0.03036731,0.0816882,0.02607153,-0.08520281,0.01290709,-0.01296415,-0.00491463,-0.00238369,-0.08543269,-0.02227958,-0.01048306,-0.00959235,-0.01951041,-0.00424559,-0.00567837,-0.02343062,0.01937813,-0.05154583,0.05232076,0.0322201,-0.06452217,0.00876316,-0.04443069,-0.00086645,-0.03846516,0.03678469,0.08829585,-0.00360151,-0.00460426,-0.00815208,0.00231144,0.00484113,-0.06168515,-0.00738228,-0.03828879,0.07121429,0.00561673,0.01570435,0.04349174,0.03888148,-0.1096913,-0.21958339,0.01246718,0.02279506,-0.06213667,-0.03193599,-0.05886361,0.09174192,0.06262329,-0.02596883,0.0903706,0.11723335,-0.01507591,0.01152328,0.01573207,0.03939158,0.03251138,-0.00237062,-0.01191835,0.02800751,-0.01453039,0.00410806,0.00481406,0.02576391,-0.01899106,0.06935038,0.01146613,0.10382152,0.03039575,-0.03858377,-0.06254301,0.03575777,0.00654379,0.04751194,-0.11226886,0.05476065,-0.00116616,0.04468521,0.03155231,-0.00851242,0.01029014,-0.05171415,0.01431372,-0.02431199,-0.08634898,-0.06401543,-0.02450737,-0.06518359,-0.07817946,-0.06815579,-0.02320658,-0.0305446,-0.00014093,0.03888476,0.0265261,0.05064482,-0.03989888,0.00627878,-0.04224879,-0.05005451,0.02479828,-0.02463835,0.01116903,-0.02383972,-0.08202262,0.07481348,-0.01509053,-0.04165292,0.02526373,0.02425512,-0.03510309,-0.00590375,0.07828224,-0.01736461,0.07363801,0.03194474,-0.0148797,0.05884343,-0.04669584,-0.03412781,0.00418106,0.03947716,0.00286892,0.0770456,0.04316965,-0.05070972,0.04863434,-0.01842749,0.04038533,0.05433407,-0.01576178,-0.05850417,-0.01704689,-0.05982058,0.00568399,0.10245775,0.03931637,-0.24207373,-0.00800699,-0.03599813,-0.04380644,-0.0356966,-0.00652028,0.03940638,0.02838421,-0.04875199,0.01454766,0.06709359,0.02020836,0.05731192,-0.03246258,-0.01379528,0.04218377,0.03565903,-0.07544165,0.04154154,-0.05818009,-0.00792855,0.01096275,0.20456995,0.02119611,0.00991029,0.0140725,-0.00914635,0.07402977,0.0770398,0.031632,0.00385374,-0.00344136,0.03080029,-0.08202406,-0.00899573,-0.01242919,0.01704822,0.00921956,0.04426073,0.03294607,-0.00575897,0.03330615,-0.0247212,-0.02833516,0.12746736,0.01500402,-0.11818769,-0.05326328,0.03911553,-0.00569303,-0.03310731,-0.03547272,-0.02965173,0.0516413,0.03336276,0.04599723,-0.03067778,-0.01820967,-0.00454612,-0.01075712,0.03922707,-0.05368276,0.05184538,0.03396248,0.00510273],"last_embed":{"hash":"c26347e220c174bc734299fe913e92aef7849426012d6498e2521927760b15fc","tokens":411}}},"text":null,"length":0,"last_read":{"hash":"c26347e220c174bc734299fe913e92aef7849426012d6498e2521927760b15fc","at":1743662878190},"key":"Microservices.md#2. Các services#{45}","lines":[320,334],"size":1058,"outlinks":[{"title":"Self-hosted ngrok","target":"https://github.com/pgrok/pgrok","line":3},{"title":"Listmonk","target":"https://github.com/knadh/listmonk","line":4},{"title":"Ghostfolio","target":"https://github.com/ghostfolio/ghostfolio","line":5},{"title":"Teller","target":"https://github.com/tellerops/teller","line":6},{"title":"Canvas","target":"https://github.com/austintoddj/canvas","line":7},{"title":"Rocket.Chat","target":"https://github.com/RocketChat/Rocket.Chat","line":8},{"title":"BookStack","target":"https://github.com/BookStackApp/BookStack","line":9},{"title":"Hook0","target":"https://github.com/hook0/hook0","line":10},{"title":"Chatwoot","target":"https://github.com/chatwoot/chatwoot","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#3. Design patterns": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06041692,-0.07171878,-0.02158125,-0.00600012,-0.01372653,-0.01803344,-0.09830527,0.00258826,0.01114933,0.02907233,0.01328394,-0.04995751,0.02252904,-0.01770759,0.05618179,-0.02390716,-0.00299138,0.00212582,-0.02431675,-0.02831618,0.05743894,-0.01611048,-0.06014502,-0.03945116,0.06525622,0.05394522,0.00183049,-0.07594168,-0.05138819,-0.23974508,0.01916725,-0.02064482,0.01383049,0.04047493,-0.02804898,0.02741452,-0.04035126,0.06038034,-0.0262804,0.00535606,0.00368829,0.00247239,-0.03096786,0.01196027,-0.07989679,-0.08047815,-0.00945009,0.01285571,-0.0175881,-0.0530242,-0.06672326,-0.04430464,-0.02002181,0.0431336,0.02419106,0.01831388,0.06554917,0.03047537,0.0178285,0.01220809,0.08319969,0.02773123,-0.18220192,0.08298756,0.03402537,0.04769042,0.00817098,0.03242445,0.05556733,0.07265046,0.00041021,0.00069853,-0.02828769,0.06040067,0.02938406,0.05973768,0.01231917,-0.03813653,-0.06059678,0.05434125,-0.00158037,0.02300284,0.00269108,0.05705421,-0.12285839,0.02408444,-0.00716318,-0.02416317,0.02729234,0.01519793,-0.02967669,0.02978105,0.03875636,-0.04060059,-0.07325329,-0.00199311,-0.00522353,0.00188148,-0.02939455,0.13703214,0.02606091,0.01338952,0.01561562,0.00605353,0.07068484,0.02006017,-0.02085898,-0.09119847,0.00278927,0.00361715,-0.01054903,-0.01337988,0.04621712,-0.04226588,-0.0506653,0.03941188,-0.00877871,-0.02116225,-0.02590831,-0.00357477,-0.05679447,0.0776884,0.06686954,-0.04492316,0.03534124,0.01666805,-0.0097576,0.01989117,0.04420226,0.0216538,0.02191943,0.01358213,-0.01847344,-0.03532862,-0.03120126,-0.05778227,0.01264231,-0.00796684,-0.02551566,0.02701291,-0.0424061,-0.03722448,-0.0124235,-0.06996031,-0.05263085,0.10948598,0.05133391,0.00681726,0.00301027,-0.03076368,0.00091038,0.07228526,-0.04023194,-0.11373163,-0.0220607,0.04680686,0.03897472,0.13112034,-0.08575035,0.07041717,-0.02673953,-0.05000017,-0.04418332,0.1112721,-0.03613045,-0.11165517,0.00149546,0.05861181,0.00421305,-0.02471772,-0.03277812,0.05047524,-0.0047156,0.01211214,0.05179588,-0.02106812,-0.02056439,0.00796464,-0.02414579,-0.01700936,-0.03539041,0.00008546,-0.00893668,0.03078422,0.0408984,-0.05292215,0.00379857,0.00602222,-0.02460024,-0.00543461,-0.06651239,0.00742946,0.05753938,0.06205429,-0.00255326,-0.04586619,0.04896413,-0.00202584,0.02741964,-0.0626245,0.08019221,0.05592607,-0.06613821,0.00834484,-0.05965267,0.03652831,-0.01217226,-0.03574788,0.00073075,0.01021166,-0.00294687,0.03657223,0.01181727,0.03889826,-0.02130056,0.02989476,0.04607849,0.05854416,0.04854025,0.02086115,0.04056941,-0.01891933,-0.06578592,-0.22736812,-0.0191884,0.00773557,-0.0413432,0.03094807,0.00689166,0.0309251,0.01895688,0.02552072,0.03822938,0.07740255,0.01774117,-0.08457476,-0.026692,0.02343044,0.03237397,-0.00769432,0.01140323,-0.06253262,-0.02967331,0.01649765,0.03495893,-0.0064457,0.02892684,0.11172302,0.06605542,0.11969936,-0.04489097,0.0043264,-0.01033868,0.07161688,-0.02941666,-0.00524513,-0.05568054,0.05686836,-0.02377231,-0.0098985,-0.02956412,0.02365323,-0.04613485,-0.00001294,0.0109893,0.00850435,-0.08652321,-0.02198483,-0.04243722,-0.02816127,-0.03345038,-0.07013432,-0.04910842,0.01593042,-0.0595539,0.00127178,0.03494488,0.0268537,0.01576802,-0.04763787,0.00079836,0.0021228,0.03879119,0.0046168,0.00164358,0.01580004,-0.05937217,0.07097495,-0.01237941,0.05060744,0.08442855,-0.02899159,-0.03578333,-0.00916817,0.12122083,-0.02896462,-0.00255494,-0.00761538,0.01061075,0.00495656,-0.02164969,-0.0321076,0.04836799,0.06049649,-0.08262642,0.06505759,0.03405717,0.03566249,0.09892111,-0.00857511,-0.0407309,0.06872807,-0.05811173,0.00353694,0.02013362,-0.07818955,0.00298486,0.07009974,0.04810648,-0.24880813,0.09131195,-0.04400282,0.05758302,-0.00862697,0.01537622,0.01149305,-0.0544567,-0.04330198,-0.00036637,0.06843953,0.05940479,0.01301433,0.01557207,0.05253943,0.05678551,0.0601073,-0.01901151,0.0537319,-0.080541,0.03045242,-0.02231763,0.21884175,0.0019195,0.00724021,0.0045458,0.03278547,0.00784182,-0.0642034,0.03205789,-0.03761943,-0.03717609,0.1231527,-0.02116741,0.02978994,-0.03331982,-0.01371941,0.00138797,0.02125613,0.02854483,-0.01360188,0.01473858,-0.02528042,0.0063315,0.06268431,-0.01257081,-0.05400289,-0.1090524,-0.01479282,-0.00821764,-0.02187831,-0.02587072,0.00157571,-0.00557542,0.01119827,0.02510704,-0.04660468,0.00377931,-0.0681321,0.00026986,0.07059133,0.03365684,-0.00032093,0.00989929,-0.00249908],"last_embed":{"hash":"eedef74958ffe12b2583f505d1a6db6e2369f20ea50c7880e5ea5b4d1e5a0397","tokens":430}}},"text":null,"length":0,"last_read":{"hash":"eedef74958ffe12b2583f505d1a6db6e2369f20ea50c7880e5ea5b4d1e5a0397","at":1743662878224},"key":"Microservices.md#3. Design patterns","lines":[335,351],"size":1175,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#3. Design patterns#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05007308,-0.03830281,-0.01994408,-0.01623452,0.02143534,-0.01067514,-0.06134161,0.04519208,-0.00706177,0.02371919,0.00795582,-0.03188868,0.03492824,-0.04175192,0.03982063,-0.00912638,0.04832579,0.02284149,-0.0003028,0.00415701,0.10474546,-0.01687633,-0.06098131,-0.01332998,0.02544783,0.07099111,-0.01088078,-0.04558144,-0.05563383,-0.16636695,0.01865974,-0.03059644,0.00529659,0.04273385,-0.02280692,0.01301395,-0.02221174,0.07603839,-0.05246543,0.02937537,0.0138532,-0.0362422,-0.05068878,-0.02971182,-0.08275092,-0.04150092,-0.00934207,0.02148663,-0.04318647,-0.0585254,-0.0591434,-0.05718708,-0.00507139,0.04017428,0.05175948,0.0444175,0.03005441,-0.01037282,0.0034568,0.02027111,0.07275642,0.00213272,-0.18951868,0.12075208,0.02993533,0.00774804,0.029659,0.01489349,0.04810766,0.07362233,0.01739657,-0.00043406,-0.07389399,0.09132659,0.02480872,0.05798095,0.03662493,-0.05696782,-0.06008044,0.02354283,0.01527693,0.00760692,-0.01094468,0.05485358,-0.05358338,-0.01944322,0.01078282,-0.032748,0.02361525,0.00938061,-0.02057246,0.00197494,0.01234715,-0.04384896,-0.04169048,0.00130338,-0.04255442,0.00422451,-0.01220148,0.16353506,-0.0165962,-0.00352075,0.02181241,0.00089612,0.08725415,-0.00640868,-0.02469467,-0.05232729,-0.02630373,0.00119196,-0.01830041,0.01601422,0.06318651,-0.05505078,-0.00590423,0.01427753,-0.01314575,-0.02565468,0.01239917,0.02489549,-0.058923,0.07385896,0.06912041,-0.03477178,0.03465333,-0.01354536,-0.0283711,0.04645443,0.00006265,0.06057178,0.00689636,0.013666,-0.01937888,-0.00662726,0.00804331,-0.04208608,0.00592929,0.0304868,-0.05879448,0.0250836,-0.02682447,-0.01364011,-0.01990499,-0.08994763,0.01155712,0.11822307,0.0089982,-0.00478393,-0.02764163,-0.00637175,-0.04996576,0.08450229,-0.05589283,-0.08814181,-0.05653198,0.08327044,0.0322025,0.09998453,-0.0486477,0.08672813,-0.01506954,-0.01084393,-0.00057703,0.0971875,-0.04612351,-0.11181096,-0.01585933,0.02710271,-0.02091881,-0.02037074,-0.04592152,0.03744186,0.01135444,0.0172174,0.08075368,0.01355521,-0.02649988,0.01703787,0.00270206,-0.02390791,-0.01562803,-0.00557916,-0.01277612,0.07370228,0.00029321,-0.05529932,0.01584943,-0.00618734,0.02621246,0.04082407,-0.09343112,-0.02845028,0.03917798,0.00362482,0.00776802,-0.03530705,0.0218375,0.01253884,0.03977828,-0.07140694,0.05451572,0.03393719,-0.07943452,0.028159,-0.04188235,0.00824502,-0.02406731,-0.02887648,0.0474,-0.02437356,-0.03114084,-0.01304737,0.02147855,0.03956104,0.00911056,0.01335955,0.04117922,0.04199887,0.07047571,0.03464945,0.01952694,-0.00800218,-0.07312197,-0.20428582,0.00886363,-0.0147574,-0.03017782,0.01086176,-0.01649302,0.05440602,0.06432445,-0.01328125,0.02063011,0.09665195,0.00405849,-0.0526808,-0.01338873,-0.01348754,0.04043793,0.01437237,-0.03584944,-0.06367401,-0.00412572,0.03173327,0.04125114,0.04851324,0.03272483,0.10338655,0.04535006,0.12348133,-0.00193959,-0.02407149,-0.02057218,0.07724885,-0.03567271,-0.01666192,-0.07801798,0.04767862,-0.02521046,0.00331667,-0.02391721,-0.00950933,-0.10069841,-0.03887771,0.0066057,0.0286253,-0.06617437,-0.02193937,-0.06640299,-0.04163298,-0.06473596,-0.07651523,-0.07302858,-0.01691531,-0.10012067,-0.00840451,0.03530017,-0.00343444,-0.01508035,-0.05570977,0.01067157,0.00143225,0.02645658,0.02481075,-0.01007467,0.01837958,-0.10626451,0.0283573,0.00340095,0.01368844,0.04005151,-0.0568094,-0.03159131,0.03596005,0.14347078,-0.0005232,-0.00962797,-0.05109737,-0.00376441,0.02615969,0.00659124,-0.02136493,0.02184461,0.06918941,-0.09826095,0.08995198,0.00611354,0.06594382,0.04053219,-0.03219577,-0.06302889,0.08920546,-0.05802162,0.01413199,0.01773511,-0.06786556,0.01521128,0.05830924,0.03286565,-0.23181854,0.09664718,-0.0214455,0.06831169,0.00814519,-0.04490618,0.02021535,-0.04096197,-0.03271846,-0.01110279,0.09171166,0.03067766,0.01931114,0.01094737,0.07330002,0.05521462,0.05876995,-0.01485516,0.00857549,-0.09513909,0.06390678,-0.01984026,0.24211138,0.01413561,0.02610426,-0.00624984,0.01973817,-0.01061395,-0.06527234,0.05510394,0.00988117,-0.08741,0.08933847,-0.02539509,0.00246213,-0.04216674,-0.01566995,0.00458569,0.01656524,0.04548525,-0.02563292,0.00800159,-0.03020052,0.04668832,0.05973025,0.03679328,-0.04718377,-0.0454019,-0.02541056,0.03378087,-0.02403032,-0.00118879,-0.02028361,-0.03925015,0.00275261,0.03950236,-0.08360866,-0.01558114,-0.05810577,0.00648827,0.03928318,0.02387268,0.02961085,0.04102509,0.02999002],"last_embed":{"hash":"a577b8fd7c4ef956fdaa9c6d68ca27b3b30e77f4ccba86f8f5daa4fd290c2656","tokens":83}}},"text":null,"length":0,"last_read":{"hash":"a577b8fd7c4ef956fdaa9c6d68ca27b3b30e77f4ccba86f8f5daa4fd290c2656","at":1743662878260},"key":"Microservices.md#3. Design patterns#{1}","lines":[337,337],"size":236,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#3. Design patterns#3.2. Kafka": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04694046,-0.08013523,-0.00689354,-0.00196392,-0.01451756,0.00012041,-0.11235798,-0.0065065,-0.00034008,-0.00565423,0.00251691,-0.09308972,0.02475142,-0.01430935,0.08043417,0.00536063,-0.05794232,0.05003167,-0.05820165,-0.01539459,0.0723505,0.02871916,-0.05916,-0.04858319,0.01248316,0.06581859,-0.00985461,-0.04029948,-0.04095983,-0.25825825,0.01656089,-0.05097596,0.0355525,0.01614453,-0.00075226,0.01584249,-0.04798418,0.03117444,-0.00317931,0.03286595,0.02281703,0.01312651,0.00679051,0.03147524,-0.08795325,-0.06985675,-0.02795713,0.00008083,-0.00942782,-0.06413557,-0.08695649,-0.04910439,-0.00378135,0.04733343,0.01862272,-0.0098114,0.10403891,0.04045182,-0.01024806,0.02156119,0.1047722,0.03492078,-0.20883371,0.09476045,0.03339273,0.03704877,-0.00126473,0.0062394,-0.00317043,0.07360585,-0.01216366,-0.00808134,-0.00600488,0.05619409,-0.00200649,0.03629763,-0.01967847,-0.0174904,-0.04739228,0.05247471,0.0206411,-0.01222921,0.00739702,0.03681733,-0.12069102,0.03165088,-0.02403041,-0.04421815,0.02168851,0.05696041,-0.03373839,0.00742968,0.05143631,-0.03571571,-0.08961306,-0.02477325,-0.02875737,0.03665903,-0.08359111,0.10580581,0.05246351,-0.00345269,0.05043829,0.01202945,0.01344412,0.0180638,0.00028847,-0.08584696,0.00399458,0.01295831,-0.02777417,-0.02112134,-0.00568125,-0.02849399,-0.04247313,0.02038021,-0.01082104,-0.02709183,-0.04847967,0.00744283,0.00268562,0.03683222,0.05536536,-0.04653703,0.04707243,0.01381815,0.02541677,0.03701762,0.00311001,-0.0008009,-0.00321365,0.02084994,-0.04782152,-0.02645811,-0.02916165,-0.05046774,0.00596714,-0.02558158,-0.02894786,0.02147101,-0.07561256,-0.07188085,0.02967315,-0.07087215,-0.04175305,0.07708871,0.04521295,0.03462273,0.00164377,-0.04098322,-0.00500224,0.05905252,-0.04025083,-0.08897714,0.00343909,-0.0237972,0.03262735,0.069024,-0.08398119,0.03982262,-0.02573023,-0.04615641,-0.06242456,0.0955331,-0.01189175,-0.09020273,0.03565799,0.02363854,0.03378098,-0.0020936,-0.01244342,0.04305281,0.00836473,0.01110099,0.03533968,0.00258474,-0.04490313,0.01711696,0.00471655,-0.00036615,-0.03599667,0.03007836,-0.00610328,0.02142762,0.05374196,-0.03953199,0.01593556,0.02987997,-0.03677289,-0.05073077,-0.05994806,0.01189269,0.03176817,0.04497025,0.0161202,-0.01890705,0.06682367,0.00035602,0.0532788,-0.06778844,0.07576977,0.0691873,-0.03197413,0.02304635,-0.02282211,0.04453166,-0.02737755,-0.02239921,-0.01026088,0.06479827,-0.02384894,0.07933908,0.0107706,0.02361174,-0.0443487,0.02056596,0.03728466,0.05170377,0.01850965,0.03873695,0.06867746,-0.0425969,-0.04948936,-0.23314278,-0.04626416,0.00613628,-0.05141348,0.01783867,0.00036713,0.02924139,0.02638636,-0.00217004,0.0642817,0.05182771,-0.00599008,-0.06474128,-0.03397072,0.03161486,0.06007156,-0.01946793,0.01810022,-0.03815448,-0.02775197,-0.00237556,0.02011866,-0.0198576,0.0223508,0.10071103,0.05435361,0.1192766,-0.03137023,0.03895082,-0.00914708,0.03826894,-0.01338647,-0.02195686,-0.01358927,0.06785489,0.03134246,0.02203929,-0.04379936,0.00245307,-0.02149654,0.00379222,0.04328722,-0.00025363,-0.07997005,-0.04875695,-0.07929151,0.01686935,-0.00500953,-0.08847049,-0.03694525,0.01552816,-0.00155905,0.0029711,0.01207023,0.03950121,0.05402644,-0.04666493,-0.00371523,0.00983366,0.0405113,0.02228031,0.00887884,0.00185177,-0.03842793,0.04475355,-0.00087769,0.02060143,0.07072797,-0.01132235,-0.05854711,0.00718714,0.09074426,0.02266353,0.05138393,0.05445587,0.01377555,-0.0097308,-0.02764337,0.00746171,0.04671637,0.07364231,-0.05484599,0.03473474,-0.00729651,0.05277063,0.08574741,-0.03169672,0.04100726,0.06927661,-0.04187764,-0.04381462,0.03991242,-0.03461725,-0.00386876,0.08142268,0.01151594,-0.26604444,0.08128206,-0.02763744,0.03860927,-0.00620525,0.04719235,-0.02196048,-0.04241648,-0.0171264,-0.01694241,0.00322015,0.06199268,0.02226358,0.02569409,0.06055332,0.01728934,0.04021712,-0.02466533,0.06476928,-0.09059398,-0.00332003,-0.00799474,0.21656761,-0.02853526,-0.00891214,0.01333604,0.05254501,-0.00848953,-0.03821528,0.02264639,-0.02107775,-0.02113473,0.12157724,-0.05562137,0.05680091,0.03275241,-0.02769727,-0.00936802,0.02613612,0.03651031,0.00554785,0.01366033,-0.01126948,0.00633308,0.07780522,-0.0457116,-0.03021938,-0.10194264,0.0203097,-0.02872819,-0.03583284,-0.0427571,-0.00233742,0.01345155,0.02565293,0.06929319,-0.06688959,0.00610845,-0.09588714,-0.03148594,0.03690311,0.00047726,0.01701711,-0.01490104,0.02691814],"last_embed":{"hash":"225dffe51d83b706a65d75f73fbe080d62ac4a7a8d2c48db50b10166d8bcb4ce","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"225dffe51d83b706a65d75f73fbe080d62ac4a7a8d2c48db50b10166d8bcb4ce","at":1743662878273},"key":"Microservices.md#3. Design patterns#3.2. Kafka","lines":[345,351],"size":403,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#4. Framework / Libraries": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03627658,0.04836332,-0.02872828,-0.03205191,-0.01274232,-0.03685071,-0.05211067,-0.02201842,0.0160314,-0.01744065,-0.02652716,-0.12208303,0.03365756,0.00256187,0.07816527,0.01154994,0.01371827,-0.06046757,0.01485503,-0.04588738,0.06564347,-0.01777841,-0.03948131,-0.05737362,0.02739885,-0.0248331,-0.01485835,-0.07208482,-0.01732368,-0.18946739,0.00811607,-0.01667902,0.00194057,0.04104992,0.06125127,0.00141613,-0.01604217,0.02101257,-0.04884348,0.03001072,0.02639965,0.02852024,-0.01951818,-0.0550392,0.01467339,-0.0923067,-0.0325818,-0.0286082,-0.02600277,-0.0680773,0.02974983,0.02856695,-0.02157296,0.02714972,-0.00194525,0.03292031,0.05644765,0.01667122,0.02197219,-0.01198546,0.06894355,0.06768867,-0.23041607,0.0807422,-0.00517234,0.02599662,0.05344749,0.02142329,0.02579298,0.09376796,-0.02676876,0.03777448,0.01129367,0.07569522,0.01236999,-0.01099617,0.03563194,-0.02447865,-0.04595168,-0.01955998,0.01046589,0.06990372,-0.0067264,0.05008708,-0.06763273,0.0201943,-0.0156513,-0.00879832,0.03912506,-0.02308283,-0.01963678,0.02077564,0.04869346,-0.02120972,-0.04113789,-0.04479299,0.01189195,0.04369862,-0.06222923,0.13854031,-0.02117642,0.01172116,0.04143001,-0.02500116,0.06630379,0.03230914,0.05509976,-0.07376759,0.0397324,0.06813166,-0.09344018,-0.04335626,0.07633286,-0.06380364,-0.02953822,0.02367682,-0.04888469,0.00081183,0.01403162,0.01809067,-0.00913347,0.0463727,0.04943985,-0.06399109,0.07824567,-0.02739462,0.03600507,0.03233979,0.02092989,0.03308229,0.02027321,0.00648204,0.00610835,-0.04614061,-0.03693171,-0.04739984,-0.00409695,-0.02345584,-0.00138604,-0.02386421,-0.04110951,-0.0804524,-0.05634699,-0.02003259,-0.06557378,0.03117447,0.03892067,0.04367191,-0.00205477,-0.05183006,0.00471898,0.07268936,-0.00738555,-0.03102992,0.00956831,0.02982695,0.05081806,0.1356032,-0.06521685,-0.01439891,0.04601998,-0.06840544,-0.01529874,0.14443311,-0.04240324,-0.0933344,-0.03839872,0.02988782,0.03165681,-0.03293508,0.00966721,-0.00082299,-0.0588981,-0.04810642,0.04738377,-0.01613842,-0.04876677,0.00374972,-0.02061777,-0.00489884,-0.02721142,-0.01121188,0.01884313,0.01809635,0.04347881,-0.06074317,-0.00867108,-0.0017226,0.02542855,-0.00396838,-0.08684237,0.01083998,0.00783364,-0.02852106,-0.01652941,-0.04839771,0.04343999,-0.03662725,0.05021509,-0.05054736,0.04522426,0.01980597,-0.08205353,0.01242258,-0.00978401,-0.01383507,-0.02780851,0.02075706,0.01586045,0.03113646,-0.04580114,0.01377109,0.09610013,-0.02143115,-0.04511456,-0.01635771,0.03847487,0.05965647,-0.01825543,0.00505177,0.04024651,0.02629334,-0.09930629,-0.23214009,-0.04078174,0.05872549,-0.03781365,0.06390622,0.00345311,0.02351776,0.01769496,0.1005071,0.03989348,0.0990309,0.0094355,-0.01902691,0.09321997,0.03391571,0.06744775,-0.00694811,0.03194445,0.03089983,0.00434017,0.06030202,-0.00799759,-0.00440638,0.0157168,0.05054808,0.04636079,0.07771071,0.00351688,0.02980358,0.01436261,0.02453815,-0.01613848,0.02954222,-0.09272563,0.04811101,0.00723023,-0.0309042,0.03927423,0.00444404,-0.02735777,0.03940773,0.05126299,-0.02065798,-0.13131846,-0.04666235,-0.01536043,-0.03376685,0.04500078,-0.1014416,-0.02246958,-0.03258191,-0.01896259,-0.02628059,0.03661115,-0.00052504,-0.04592899,-0.03432678,0.00576142,-0.01833423,-0.00231617,-0.02128262,0.00008461,0.02037727,-0.00177717,0.03593674,-0.08615638,0.01363598,0.03830991,-0.00477424,0.03946899,-0.02668723,0.11383324,-0.00437068,0.05893695,0.03964256,-0.04486862,-0.00681169,-0.0394332,-0.03488618,-0.00822555,0.03753713,-0.03236366,0.02554354,-0.00606478,0.00074952,-0.00182148,0.03345888,0.00685318,0.00865913,-0.01637701,-0.02068619,0.0046198,-0.04906923,-0.00125932,0.0675156,0.05724562,-0.28396404,0.04055135,-0.02022557,0.00574384,-0.01555777,-0.00394146,0.00577462,-0.01822838,-0.08652448,-0.01969538,0.02492245,0.07297,0.03877489,-0.03703823,-0.01030094,0.03326034,0.02506038,-0.08098904,0.01032343,-0.01133976,0.00805286,0.00077216,0.19343053,-0.00526542,0.02933202,0.03644525,-0.01320301,0.02191191,0.00310377,-0.0207789,-0.02245785,0.0259851,0.13172497,-0.07682733,0.01998105,0.05996789,-0.05060319,0.02329851,0.04550197,0.05627472,-0.03597554,0.03140207,-0.03632819,-0.02147927,0.07405696,-0.03727534,-0.03721293,-0.11189504,0.03706766,0.09288501,-0.01547099,-0.0687672,0.02804453,0.02048464,0.00159028,0.04460456,0.02650737,-0.01619072,0.01676349,0.00746745,0.0382448,-0.02908969,0.04802055,0.03058423,0.04107379],"last_embed":{"hash":"0b6772eda7a6c337b59a82618928da2194598a3047304fd8eed4abb4262647ce","tokens":491}}},"text":null,"length":0,"last_read":{"hash":"0b6772eda7a6c337b59a82618928da2194598a3047304fd8eed4abb4262647ce","at":1743662878288},"key":"Microservices.md#4. Framework / Libraries","lines":[352,414],"size":4134,"outlinks":[{"title":"Untitled 10.png","target":"Untitled 10.png","line":56},{"title":"Untitled 1 5.png","target":"Untitled 1 5.png","line":58},{"title":"Untitled 2 2.png","target":"Untitled 2 2.png","line":60},{"title":"Untitled 3 2.png","target":"Untitled 3 2.png","line":62}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#4. Framework / Libraries#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01405499,0.04701757,-0.03390565,-0.0506882,-0.00915401,-0.04296344,-0.07001581,-0.02602595,0.02028048,-0.01858279,-0.02001812,-0.11131255,0.03898087,0.01403285,0.08190841,-0.01420034,-0.03135236,-0.03804043,0.03729954,-0.03679962,0.06915151,0.00651329,-0.05058182,-0.09441696,0.01146109,0.00602421,0.00490482,-0.07140119,-0.00083753,-0.20379911,-0.00412586,-0.02700367,-0.0015531,0.0004412,0.05148893,-0.01681831,-0.01323868,0.05967661,-0.02907047,0.01584708,0.03112748,0.06290355,-0.04229294,-0.03283037,-0.01423578,-0.0849334,-0.02982894,-0.01333798,-0.025254,-0.07131079,0.0122837,0.04028038,-0.03673348,0.03585895,-0.029046,0.05196712,0.08192911,0.00745602,0.0268378,0.00197258,0.0472929,0.05826284,-0.2164098,0.07258977,0.01530778,0.03575853,0.05539218,0.02958554,0.02880977,0.07182975,-0.03680067,0.04091299,0.0225677,0.04252623,0.00637399,0.00728532,0.01856149,-0.0188727,-0.02226023,-0.02473794,0.01130707,0.01795805,0.0172074,0.07337157,-0.04465616,0.02397898,-0.02423644,0.0040234,0.03997324,-0.02032365,-0.03371434,-0.01106675,0.04877459,-0.02064304,-0.06944256,-0.03705812,0.03421098,0.05455082,-0.02720355,0.15943794,-0.0190079,0.00604426,0.03556423,-0.0116787,0.07397979,0.00669389,0.054919,-0.06561771,0.07136086,0.06187727,-0.08653562,-0.02657794,0.07091228,-0.07075068,-0.00451232,0.01686944,-0.06990582,0.00532233,0.02025962,0.00652646,-0.0104027,0.0467465,0.04143563,-0.04276362,0.06905946,-0.01709631,0.03017218,0.01272846,0.02656024,0.04862859,-0.00191572,0.00461774,-0.01837395,-0.06394911,-0.02262882,-0.02700987,0.01490807,-0.0219993,0.00067899,-0.03045459,-0.04195473,-0.04898853,-0.08340364,-0.00292988,-0.04587896,0.0564307,0.04084402,0.0637797,0.00884569,-0.04465442,0.0023205,0.10529543,-0.01688256,-0.00880972,-0.00014389,0.00383494,0.04637528,0.11293817,-0.06510148,-0.02651523,0.01817345,-0.03292217,-0.00000943,0.15988435,-0.04469273,-0.07772116,-0.03766318,0.04155369,0.02146223,-0.01907078,-0.009661,-0.01965581,-0.02959261,-0.03195017,0.06522802,-0.02942763,-0.09573265,-0.00369663,-0.03418606,-0.01276661,0.01651088,-0.00632454,-0.02023119,-0.00692514,0.04627968,-0.06034773,-0.0148013,-0.03010839,0.02317382,0.01693209,-0.09053701,0.01550554,0.01996572,-0.00696996,-0.0127814,-0.0414305,0.04007617,-0.03643276,0.08340172,-0.06112892,0.05153718,0.05756113,-0.07152886,0.02328913,0.01689334,0.00727783,-0.03009575,-0.0002937,-0.00244859,0.03095675,-0.05014649,0.01855872,0.08342163,-0.00806371,-0.0296807,-0.03350687,0.00776197,0.05604842,-0.02235852,0.00087373,0.05974159,-0.00015699,-0.07443375,-0.24508072,-0.01760019,0.05030594,-0.06323945,0.04613478,0.01651026,0.02779655,-0.02270471,0.04710229,0.0448529,0.07352039,0.01996977,-0.01886722,0.07392009,0.00645629,0.04733365,0.01844845,0.02036866,-0.0017637,-0.00155612,0.06366444,-0.02432326,-0.0054432,0.04094488,0.07173652,0.04351344,0.08452613,-0.01942083,0.04156586,-0.00479093,-0.00637309,-0.01267349,0.02056265,-0.0854233,0.027377,-0.00882429,-0.01875105,0.04572873,-0.01435161,-0.03445664,0.04954068,0.04140468,-0.02447608,-0.10255694,-0.01942069,-0.00076179,-0.00124203,0.04340991,-0.06282085,-0.00054983,-0.05078769,-0.00326257,-0.02382729,0.03691665,-0.01956567,-0.03727358,-0.05686381,0.04937032,-0.0147218,-0.00458883,0.00193354,-0.01145597,0.01842178,-0.00514566,0.06096771,-0.09802706,0.00329114,0.0168342,0.00063511,0.02678825,-0.03061082,0.10293774,-0.01437963,0.05310762,0.04197675,-0.05177903,0.00757329,-0.03023937,-0.03085441,-0.02535917,0.02599925,-0.01993846,0.02988356,-0.02340597,-0.02268828,-0.00947748,0.01183242,0.01853278,0.00201753,-0.01695102,-0.03471871,0.01500801,-0.04791911,0.02263494,0.07767227,0.05380519,-0.29802841,0.05220067,0.0055819,0.01328268,-0.00438167,0.00980454,0.01764369,-0.04453696,-0.08052044,-0.0481543,0.01134309,0.06938788,0.0705771,-0.03774227,-0.01479889,0.04320162,0.03991629,-0.08955716,0.0210142,-0.02101492,0.01432959,-0.0141826,0.18676274,-0.01360467,0.02601559,0.04171873,-0.03471326,0.01564551,0.02832317,0.00859604,-0.04995926,0.0272804,0.12834185,-0.06030899,0.00149906,0.07305605,-0.05691278,0.00919774,0.05095313,0.05820194,-0.01674207,0.00340791,-0.03810291,-0.04746022,0.06841657,-0.04626836,-0.03948797,-0.09726955,0.02070168,0.07140516,-0.02418502,-0.06469879,0.04009669,0.00681348,-0.00083234,0.04119786,0.06402755,-0.01849336,0.01010615,0.01294803,0.05554029,-0.02841028,0.04111021,0.04220283,0.05703415],"last_embed":{"hash":"9985118fba5b850737b958722760be40cf3f66aa83d04964a5c0ed79ce9299d3","tokens":184}}},"text":null,"length":0,"last_read":{"hash":"9985118fba5b850737b958722760be40cf3f66aa83d04964a5c0ed79ce9299d3","at":1743662878337},"key":"Microservices.md#4. Framework / Libraries#{1}","lines":[354,356],"size":429,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#4. Framework / Libraries#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06529178,0.04715154,0.02746272,0.03233135,0.00439449,-0.03346951,-0.00779007,0.03470948,0.00246293,-0.012089,-0.03773284,-0.0801636,0.0266319,0.00547545,0.04050314,0.01929512,0.04692558,-0.02162972,0.00701876,0.00018102,0.10449716,-0.00628785,-0.01578532,-0.00466938,0.04807228,-0.04666027,-0.04000744,-0.05595778,-0.04732301,-0.16299838,-0.03209874,-0.01196541,0.03469028,0.02994207,0.04467401,0.02894207,-0.01773448,-0.04382605,-0.06290372,0.0717918,0.04114227,0.02265024,0.00842529,-0.05697264,0.04428991,-0.13781723,-0.04669052,-0.01886016,-0.02152323,-0.06455812,-0.01339865,0.01126221,0.01834424,0.01964821,0.00560382,0.01758764,0.0067095,0.05010686,0.06305427,-0.02920051,0.07480815,0.07147712,-0.24827728,0.10403201,-0.01064666,-0.00316247,0.04934137,-0.01173183,0.03792634,0.1001094,-0.03348316,0.02642162,-0.01431618,0.10879135,-0.0213326,-0.00078878,0.0215475,-0.04019294,-0.05377852,0.00086706,-0.01002547,0.07864463,-0.00822057,-0.04431044,-0.09331022,-0.01158891,-0.04773182,0.00308284,0.07410118,0.02058212,-0.02138509,0.01961046,0.04523875,0.02914326,-0.03563059,-0.05234824,-0.02365622,-0.00333555,-0.09973466,0.12724407,-0.01383313,0.02729135,-0.0029059,-0.05136432,0.00894756,0.01085816,0.01018268,-0.06530539,0.01883473,0.05873962,-0.05991158,-0.06331822,0.0764522,-0.06425337,-0.00503707,0.04652093,-0.0181011,0.00619166,-0.03934219,0.01479038,-0.02615242,-0.01509064,0.03868707,-0.07087402,0.0955178,-0.03624147,0.00217665,0.0551599,0.00009319,0.02139389,0.01856776,-0.0254002,-0.00432568,-0.02107798,-0.05043598,-0.05021681,-0.0308699,0.03018649,-0.04683297,-0.0548107,-0.02009729,-0.09690045,-0.01196351,-0.03658105,-0.04000144,0.03991039,0.01123213,-0.02181178,-0.04557923,-0.03457401,0.01794478,0.03226321,-0.0065162,-0.05324393,-0.00376517,0.06059245,0.04338178,0.14272967,-0.01947194,0.0145975,0.0200201,-0.07817654,-0.05229884,0.10085829,-0.01808407,-0.06234763,-0.03064622,0.0073294,-0.02735022,-0.03224882,0.05246964,0.02028173,-0.04087956,-0.03292237,0.02626224,-0.02682118,0.03485732,0.02576786,0.00299968,0.01581416,-0.06505669,-0.01513828,0.02921651,0.06584454,0.04191773,-0.0668526,0.00563473,0.01868203,0.05859535,-0.01233915,-0.07669978,0.01403747,-0.00159885,-0.04320209,-0.02721898,-0.05597104,0.01900633,-0.02601183,0.01678511,-0.03023689,0.0744327,-0.014378,-0.07133956,0.04317643,-0.04665438,-0.04360315,-0.04229186,0.02245607,0.04527448,0.02106174,-0.04819834,0.02690247,0.04528679,-0.0095988,-0.03091408,0.01422583,0.10284393,0.04505121,-0.0261228,-0.02681192,-0.00555974,0.01887609,-0.08446288,-0.20834757,0.01646873,0.0170666,-0.01549058,0.03631368,-0.01335484,0.03953141,0.09358269,0.08372763,0.03256422,0.09134766,0.01125883,-0.03548017,0.07983351,0.04242358,0.06689862,-0.03476879,-0.02884358,0.0126284,-0.00719466,-0.01560523,0.06599235,-0.03255199,-0.03074375,0.02775894,0.03468939,0.11011163,0.02094216,0.02271489,0.06503277,0.03298039,0.03089883,0.0218552,-0.09944328,0.07751881,0.03547176,-0.052807,0.04044455,0.02623946,-0.01174325,0.02633089,0.04599733,-0.00515225,-0.11895227,-0.0389864,-0.01959212,-0.07451631,0.01772565,-0.10178507,-0.04304937,-0.03739449,-0.03453284,-0.00086429,0.0525341,0.00483734,-0.02350464,-0.01250126,0.00252776,-0.03530284,-0.00057278,0.0016596,-0.00011586,0.00144881,-0.01996727,0.01697318,-0.03882563,0.00054756,0.01647382,0.00552613,0.04180404,-0.0024949,0.08914938,-0.01998519,0.00995899,0.06149996,0.0594605,-0.00220852,-0.05413815,-0.02973089,0.00582607,0.05978739,-0.01984381,0.03942066,0.04069212,0.00752252,0.04134899,0.03310759,-0.00305374,0.0047252,-0.04173581,-0.0036491,-0.00646222,-0.03321176,-0.02417014,0.04597229,0.04046424,-0.25398564,0.00349005,-0.01849327,0.018998,-0.03541614,0.00662336,-0.01926253,-0.00084116,-0.05825849,0.03796661,0.06274743,0.076667,-0.00733362,-0.03810333,-0.01613621,0.02947986,0.05980647,-0.03002814,0.02766052,-0.00366039,-0.01272938,-0.00866166,0.20399065,0.00987253,0.04923154,0.01105929,0.02142351,0.04344645,-0.0043937,-0.02930936,0.00039049,-0.02493441,0.15169717,-0.07350122,0.02265985,0.03362971,-0.02268917,0.01370801,0.03877549,0.02549976,-0.03879413,0.04815279,-0.06277704,0.00994147,0.07275996,0.01782166,-0.04563804,-0.1309351,0.01523281,0.0471425,0.00660914,-0.05341317,-0.0243815,0.02288744,0.02418457,0.08346538,-0.05430399,-0.01704686,-0.01673063,-0.00883647,0.0114921,-0.02917265,0.07615972,0.01585025,0.00905789],"last_embed":{"hash":"fbf256ad3642c7f8dda9b46689027b713664aee626c270518525829e1be92878","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"fbf256ad3642c7f8dda9b46689027b713664aee626c270518525829e1be92878","at":1743662878349},"key":"Microservices.md#4. Framework / Libraries#{3}","lines":[359,368],"size":398,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#4. Framework / Libraries#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07346936,0.00607118,0.02932793,0.03286345,0.00621186,-0.01639824,-0.00464151,0.02749245,0.01491486,0.00150692,-0.05288059,-0.06689791,0.00710219,0.02937735,0.06428184,0.01156091,0.03567444,0.00589287,-0.01959207,0.01211138,0.06380916,-0.02063622,-0.03016878,0.00256821,0.04256072,-0.00945485,-0.02347008,-0.04405509,-0.04091496,-0.17170052,-0.02936664,0.00919127,0.04008155,0.02482847,0.04296447,0.00154462,0.01629572,-0.01794691,-0.02413428,0.02399147,0.04827829,-0.01587556,-0.01686211,-0.03407577,-0.0080848,-0.11515831,-0.03472082,-0.00602199,-0.01058061,-0.02336843,-0.02586277,0.01138838,0.00060811,0.01669291,0.01358344,-0.01113997,0.03715401,0.05337839,0.06232431,-0.0183895,0.0752139,0.04395389,-0.25832677,0.09924937,-0.05107084,0.02050164,0.03884278,-0.0074907,0.02379918,0.0884978,-0.02112229,-0.02908476,0.0073291,0.0905612,0.02380922,-0.0007812,0.01217439,-0.03227978,-0.05257181,0.01369797,-0.01794218,0.09844746,-0.02342456,-0.02792859,-0.14422442,0.00282706,0.00122667,-0.02516131,0.09425651,0.01502314,-0.0215058,0.03794549,0.02798931,0.00634561,-0.07254742,-0.0579026,-0.0146187,0.02619982,-0.062973,0.11861335,0.03109211,0.01573337,-0.00512097,-0.03727669,-0.02185013,0.01508659,-0.00741123,-0.07873283,-0.00649972,0.0147345,-0.04055294,-0.02268218,0.05533816,-0.04505729,-0.00972997,0.02435169,0.02993553,0.02486975,-0.02297805,0.04207659,0.02049806,0.01906936,0.05106746,-0.05694909,0.06645016,-0.03367631,-0.00109533,0.01806424,0.02231141,0.02340545,0.01850891,-0.03741372,0.00432358,-0.04303116,-0.01833055,-0.06612777,-0.0168337,0.00502309,-0.02377929,-0.03623192,-0.02247654,-0.08935553,0.00725381,-0.05763575,-0.01820773,0.02360716,0.03253097,0.03877828,-0.03210006,-0.02022019,0.02882307,0.00939377,-0.01869986,-0.06060367,-0.01982491,0.03437401,0.04835453,0.1383336,-0.03621491,0.03705452,0.01838377,-0.1310212,-0.01760367,0.07261401,-0.01719634,-0.07993872,-0.02621187,-0.00452674,-0.02650114,-0.02575024,0.01133978,0.03925805,-0.08684106,-0.07923931,0.0143467,0.00399534,0.00935459,-0.00000721,-0.00477498,-0.00915799,-0.07229738,-0.02260325,0.01209966,0.03087512,0.08263484,-0.06928714,-0.0321159,0.01010139,0.00894781,-0.00643397,-0.06651948,-0.00127646,-0.00260937,-0.02236695,0.00008674,-0.05591289,0.01407145,0.0185339,-0.00303562,-0.0196523,0.0624333,0.0157536,-0.05600552,-0.02098171,-0.0465799,-0.01518822,-0.03271032,0.03038271,0.06735584,0.02869572,-0.00197617,0.02564574,0.02546135,0.00131438,-0.04892367,0.01605777,0.09116346,0.02942542,-0.02222344,-0.02894219,0.01806127,0.00434657,-0.07736805,-0.22783306,0.03325946,-0.01806798,0.01020187,0.03554681,-0.02819112,0.0609534,0.04900699,0.07611238,0.10894281,0.06971774,0.01341003,-0.04238071,0.07139142,0.04801769,0.11078151,-0.02362547,-0.0146662,-0.01013758,0.00878036,-0.02696552,0.03046958,-0.04613509,-0.03026912,0.03753291,0.06324606,0.1386099,0.03099309,0.01247594,0.02066578,0.03983077,0.0299646,0.01551371,-0.09159045,0.06000985,0.05171919,-0.05222844,0.00653967,0.01910299,-0.01677226,0.01719688,0.07375302,-0.03939921,-0.11396826,-0.04315933,-0.02292008,-0.07231195,0.0099079,-0.09661737,0.01357567,-0.03375497,-0.0376971,-0.00952286,0.05503197,0.03449868,-0.0231154,0.02014167,-0.02530265,-0.01185055,0.02256017,-0.03446469,0.01193354,-0.00461135,-0.03945392,0.02399285,-0.06844903,-0.02661787,0.0859191,0.01413787,0.03856152,0.00054931,0.10777177,-0.00119013,0.02147159,0.06760609,0.02629535,0.02117629,-0.0698598,-0.01088387,0.04690516,0.08292043,-0.0709363,0.0255295,0.00141153,-0.00080564,0.04542173,0.04544232,-0.02238982,0.02250508,-0.09349018,-0.01861583,0.01501656,-0.04870753,-0.04812619,0.06011411,-0.00568639,-0.266624,0.02559928,-0.03746516,0.01885608,0.0309273,0.03587992,-0.0253353,-0.03140044,-0.0396794,0.03855736,0.03785494,0.08249198,0.02399909,-0.00220564,0.01124796,0.00535915,0.05121664,-0.02696203,0.03821988,0.00760886,0.00583326,0.00113188,0.20499964,0.03445816,-0.00101655,-0.03367981,0.00786134,0.05736828,0.00735568,-0.00786169,-0.00981151,-0.00935007,0.13172263,-0.05346976,0.05770287,0.03536961,-0.03004495,0.02664883,0.01821806,-0.00339354,-0.05291696,0.05162204,-0.06519143,-0.0122995,0.10253436,-0.02865607,-0.03357324,-0.12695524,-0.00045971,0.03172052,0.0161409,-0.06196399,-0.00029608,0.03010132,0.03936096,0.06149669,-0.05054564,-0.02305648,-0.01705877,-0.02903119,0.02306297,-0.02765586,0.02429771,0.00699465,0.02137291],"last_embed":{"hash":"375f52cd2875071476a7986c3bf765e8f3c3baa0dce1ceff5530bcae5739e311","tokens":214}}},"text":null,"length":0,"last_read":{"hash":"375f52cd2875071476a7986c3bf765e8f3c3baa0dce1ceff5530bcae5739e311","at":1743662878378},"key":"Microservices.md#4. Framework / Libraries#{9}","lines":[378,379],"size":534,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#4. Framework / Libraries#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06168266,-0.02279498,0.03917139,0.03304859,-0.0089872,-0.0190381,0.00263478,-0.00320108,0.02293244,0.00710582,-0.05170136,-0.08691944,0.0375204,0.03426486,0.04381526,0.02348691,0.05920793,0.00016815,-0.04386159,0.00246979,0.04146263,-0.00978874,-0.00799532,-0.01233887,0.03347347,-0.01716576,-0.01935534,-0.02364427,-0.03419428,-0.20459774,-0.03074791,0.02689059,0.04669758,0.02477142,0.05343015,0.00437192,0.02470281,-0.02705047,-0.0443803,0.03557945,0.03858637,0.01674605,-0.01810248,-0.03921038,-0.01736235,-0.10462224,-0.00320409,-0.00048149,-0.00403995,-0.05981603,-0.02873218,-0.00724217,-0.0085561,-0.00388813,0.0162754,0.01257459,0.02407255,0.04747481,0.07237753,-0.01397727,0.05471989,0.0307847,-0.26565668,0.10700489,-0.0353749,-0.02225773,0.04441246,-0.03883765,0.00613911,0.07960985,-0.04197605,0.00425618,0.00455673,0.09460268,0.02627046,-0.01512373,0.03082454,-0.06490676,-0.06957693,0.06284896,-0.02511557,0.09212614,-0.01236003,-0.01903579,-0.09256173,0.01100092,-0.00918263,-0.0012229,0.07393842,-0.01024142,0.0244684,0.018021,0.02253885,-0.00279977,-0.07081735,-0.05161526,0.00902505,-0.00393999,-0.07795263,0.11767901,-0.00414952,-0.00354318,-0.02197691,-0.02703019,0.00619963,0.05126581,-0.00345583,-0.0527502,0.01581829,0.03095492,-0.06178421,-0.01329825,0.0665718,-0.05028815,-0.0036877,0.02607233,-0.00606633,-0.01009529,-0.04335399,0.04682193,0.00893629,0.0046821,0.06112251,-0.05887439,0.07523627,-0.02588831,0.0273453,0.03599541,0.01431376,0.00533329,0.02399946,-0.03643177,0.01803168,-0.04543737,-0.06911401,-0.04093742,-0.00730688,-0.01262029,0.0021623,-0.04881472,-0.04032091,-0.08803544,-0.02722343,-0.04441804,-0.06476908,0.03541608,0.01584147,0.03305939,-0.06593547,-0.05696985,0.02322566,0.04395109,-0.00322039,-0.01823327,-0.00065025,0.03152906,0.05848081,0.17638779,-0.02995586,0.07113051,-0.01004964,-0.14465034,-0.04589607,0.10572919,-0.04607835,-0.06277346,-0.02881805,-0.00261848,0.00547695,-0.05552395,0.02251527,0.02833434,-0.08200138,-0.03483144,0.00387477,-0.00661725,0.02064822,0.03534832,-0.03215702,0.01448017,-0.05922028,-0.0472923,0.04602161,0.04589508,0.0755012,-0.0791425,-0.03873399,0.00134685,0.02995136,-0.01199663,-0.07588828,0.02660707,0.03643725,-0.01541215,-0.0030571,-0.0642239,0.02340995,-0.02472323,0.02142102,-0.01997599,0.05243911,-0.01902975,-0.02771019,-0.00798289,-0.03672066,-0.03179957,-0.05333285,0.01265789,0.05484638,0.02368693,0.02335555,-0.01213757,-0.00675486,-0.00336363,-0.01902791,0.00991109,0.0605028,0.06742137,-0.00134968,-0.01384853,-0.01182901,0.03910087,-0.11983471,-0.21306983,0.00655807,0.01958122,-0.00246772,0.03613878,0.02571315,0.04141532,0.10866289,0.06821886,0.08273344,0.06301105,0.02735297,-0.02906941,0.06904564,0.05452396,0.05505975,-0.00289738,-0.01049309,-0.00115165,-0.00298885,-0.01926312,0.03309547,-0.04666492,-0.01346768,0.0546787,0.05034392,0.12076144,0.01899406,-0.00041032,0.01820168,0.04871104,-0.002486,0.02282388,-0.05505541,0.07224575,0.01740611,-0.04726825,0.01690723,0.0304914,-0.01310172,-0.00078805,0.04200042,-0.00946446,-0.12862423,-0.03472246,-0.00139612,-0.07750508,0.05386122,-0.10038755,-0.04905128,-0.0072897,-0.01941159,0.00282133,0.0199656,0.02940585,-0.02200762,-0.0178758,-0.00003746,0.00808094,0.02517622,-0.0092123,0.00545406,0.00826658,-0.01584524,0.00046848,-0.0348076,-0.00281272,0.04529864,0.03059646,0.05841782,0.0005753,0.1135941,0.0187769,0.0205601,0.08110975,0.00329751,0.01074401,-0.07695381,0.00414861,0.0268564,0.10067861,-0.05458725,0.03988081,0.00650985,0.02664762,0.02363159,0.01862874,-0.01918782,0.04093662,-0.03700682,-0.02517232,0.00714426,-0.04675047,-0.02366325,0.03799538,0.01669137,-0.25255537,0.03105657,-0.05190173,-0.01930496,-0.0001741,-0.00709204,-0.04356106,-0.03429453,-0.09684546,0.03864929,0.0002942,0.08372879,0.00887582,-0.00235423,0.02576236,0.06304128,0.01850791,-0.04539751,0.02446013,0.03696029,0.00573735,-0.00022094,0.1854941,0.0261175,-0.00314648,0.0105233,-0.00008459,0.01305516,-0.00393282,-0.02542302,-0.00942268,0.03434826,0.1350669,-0.06413828,0.03527493,0.06360917,-0.05094476,0.006287,0.03084695,0.04082601,-0.04626476,0.04982981,-0.05521537,-0.00518878,0.07502823,-0.02615909,-0.01241416,-0.11113676,0.02624881,0.05466238,-0.00909502,-0.04876521,0.00281781,0.03841768,0.01389806,0.06922806,-0.06546386,-0.04430041,-0.01422234,-0.02770248,0.02199173,-0.00252794,0.04045797,-0.00984528,-0.00701674],"last_embed":{"hash":"069025a9b986fdc3e6757331fa2a9239237064020e1478ff9ff6630029448fe1","tokens":353}}},"text":null,"length":0,"last_read":{"hash":"069025a9b986fdc3e6757331fa2a9239237064020e1478ff9ff6630029448fe1","at":1743662878394},"key":"Microservices.md#4. Framework / Libraries#{10}","lines":[380,393],"size":957,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#4. Framework / Libraries#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08954608,0.01538394,0.03512426,0.03718987,0.01702349,0.01400283,-0.01484758,-0.01989984,0.03730538,0.03833962,-0.04572402,-0.08710512,0.03946839,0.05797328,0.0387684,0.00496568,0.04280327,-0.00213098,-0.01959179,-0.03845214,0.03623088,-0.03965513,-0.01234658,-0.02094251,0.02058889,-0.04925868,-0.01436817,-0.02523742,-0.02101458,-0.20691246,-0.00536258,-0.00558072,0.0068152,0.01943385,0.0078446,0.03507445,-0.01253852,-0.01753359,-0.01717934,0.02808546,0.04772045,0.01549619,-0.00726852,-0.03430083,0.00060102,-0.05339949,-0.01508147,-0.00479356,0.00996563,-0.04131775,-0.02999829,0.02605775,-0.00178921,0.01655166,0.01547536,0.02752727,0.04694925,0.0408902,0.03000742,-0.02795617,0.07673123,0.04615451,-0.22911444,0.08918249,-0.03821725,0.01801656,0.04032347,-0.03541024,-0.02558058,0.08958881,-0.03326424,-0.01992597,0.01118833,0.11478712,0.02860746,-0.02881506,0.02158164,-0.01051147,-0.06856163,0.05475118,-0.00923035,0.06322606,-0.04364689,0.00856056,-0.10463743,0.01676281,-0.0263455,-0.04293531,0.06103256,-0.01902792,0.02545133,0.00310928,0.01833245,0.01144517,-0.04690336,-0.04652313,0.01993051,-0.01419448,-0.10253429,0.09775801,0.03637858,-0.01031333,-0.05804387,-0.00762468,0.0315428,0.00215287,-0.00913341,-0.11077726,-0.01270732,-0.00441799,-0.05461754,-0.00572485,0.03618983,-0.05102876,-0.01937422,0.08855562,0.00432511,0.0375819,-0.0342025,0.04816973,0.00101498,0.01832923,0.08715966,-0.06025291,0.03083337,-0.0493264,-0.0010263,0.05164427,0.00079148,0.05407662,0.02037818,-0.0348624,0.00879489,-0.09255038,-0.03230903,-0.0533733,0.02269157,-0.05993993,-0.03946523,-0.09043448,-0.0251421,-0.08092765,-0.00994747,-0.0768289,-0.01900607,0.06662084,0.00397919,0.02829673,-0.04642815,-0.0337631,-0.00565741,0.01205339,-0.00011454,-0.06465477,0.01316798,0.00386522,0.00680459,0.13593045,-0.05434942,0.04260007,-0.02631702,-0.15976247,-0.02903169,0.12115028,-0.02904874,-0.06131728,-0.00853401,-0.01874683,0.0345852,-0.03475245,0.05050676,0.06511565,-0.06467558,-0.01101209,-0.00240446,-0.01785236,0.053345,-0.00711058,-0.00160537,0.00208165,-0.06656303,-0.01658517,0.01929065,0.04627074,0.05717362,-0.07529332,-0.0567113,0.01644623,0.02025777,-0.00003348,-0.07007238,0.04001778,0.01284737,-0.04309427,0.00941142,-0.03102087,0.0114983,-0.00075636,0.02888932,-0.01860918,0.0469,0.02006827,-0.0528021,-0.02508819,-0.04392955,0.00169256,-0.0104191,-0.00952304,0.03104768,0.03449505,-0.03754288,0.00837313,0.04152081,-0.00235863,-0.04526093,0.03457225,0.04446345,0.04886557,-0.03583264,0.00018278,0.04465155,-0.01458266,-0.05105616,-0.21114729,0.02410125,-0.00390521,-0.02360761,0.05211367,-0.06037904,0.04438475,0.05739302,0.06066066,0.08169208,0.07717245,0.00114176,-0.0488301,0.05970524,0.03381552,0.06724034,0.01935439,0.00731797,0.00968256,0.00095319,0.00478063,0.05087896,-0.08985849,-0.05429506,0.00828257,0.03922589,0.12927641,0.01790424,0.03210352,0.01599135,0.01340051,0.01415594,-0.00014205,-0.12181546,0.08352232,0.05537053,-0.07578312,-0.00864681,0.044866,-0.01119608,-0.01546679,0.06293785,-0.04209004,-0.09811055,-0.02385692,-0.00809461,-0.06752538,0.01659841,-0.08780119,0.00111174,0.00175107,-0.01682649,-0.03256248,0.02898755,0.044661,-0.00843375,0.00467074,-0.02920406,0.02727983,0.01907216,-0.01759667,0.0003297,-0.01139812,-0.02720703,0.01808659,-0.06131847,-0.00782032,0.07412007,0.05932542,0.01819167,-0.0140194,0.07983889,-0.02888888,0.0462908,0.06811919,0.00668261,-0.00989855,-0.02542607,0.0194721,0.03065841,0.09464428,-0.05711729,0.06266434,-0.01449302,0.00567355,0.05227508,0.10284222,0.01014624,0.03876578,0.00348658,0.01306126,0.02436124,-0.02714192,-0.0370936,0.09347908,-0.00332657,-0.25410691,0.05879581,-0.040286,0.00949524,0.01166016,0.04263235,-0.02334813,-0.00960118,-0.04627874,0.01593843,-0.00921413,0.07633344,0.0100615,-0.00628197,-0.01228536,-0.01813835,0.01719652,-0.02193373,0.00460894,-0.00960652,-0.02613753,-0.01541766,0.20937487,0.06006935,0.00280122,-0.00976272,-0.00223131,0.03334147,0.01711613,-0.02968475,-0.0117275,0.03537815,0.13833722,-0.05325918,0.05179042,0.05405253,-0.02152497,0.05719446,0.00435993,0.0325621,-0.07103501,0.02528241,-0.05574405,-0.01613797,0.07931978,-0.04041417,-0.05931022,-0.07650161,0.02288741,0.05467511,0.02865729,-0.0609288,-0.01442241,0.05294245,0.03960629,0.08243649,-0.02022483,-0.02159927,-0.03972561,-0.00214213,0.01560587,0.01091642,0.0318155,0.02047633,-0.03287527],"last_embed":{"hash":"5d6ffb2bd290d09023f355e9f8e607edb2639c09c9c785c30b4734de8fffdf4e","tokens":358}}},"text":null,"length":0,"last_read":{"hash":"5d6ffb2bd290d09023f355e9f8e607edb2639c09c9c785c30b4734de8fffdf4e","at":1743662878427},"key":"Microservices.md#4. Framework / Libraries#{13}","lines":[397,414],"size":930,"outlinks":[{"title":"Untitled 10.png","target":"Untitled 10.png","line":11},{"title":"Untitled 1 5.png","target":"Untitled 1 5.png","line":13},{"title":"Untitled 2 2.png","target":"Untitled 2 2.png","line":15},{"title":"Untitled 3 2.png","target":"Untitled 3 2.png","line":17}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#5. Frameworks / Libraries": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08326463,-0.02165845,-0.01465799,0.00986853,-0.01866189,-0.02721993,-0.10668194,0.03089252,0.00635004,0.01743771,-0.02005554,-0.03251509,-0.00083154,0.04336126,0.04927539,-0.05419765,0.01215441,0.03999707,-0.09104484,0.01129507,0.07473626,-0.02700395,-0.00625826,-0.03371739,0.05396885,0.04602149,0.01464941,-0.00642085,-0.01538297,-0.19683321,-0.02761528,-0.0553102,-0.02451664,0.01397146,0.00751008,0.01380104,0.01022259,0.01217867,-0.03214468,0.04867658,0.04120533,0.00347802,-0.03059853,-0.03112321,-0.06204799,-0.09423218,-0.00625293,0.03238194,-0.04470173,-0.02012212,-0.08851192,-0.00662547,-0.02458154,0.04180621,0.03945418,0.10814049,0.08777234,0.07398454,0.0289172,0.01370814,0.02440826,0.0034605,-0.1752502,0.09576444,0.02627232,0.02867849,0.00652617,-0.01611852,0.0360036,-0.00809752,-0.01705478,0.02405281,-0.00764991,0.07172497,0.03141451,-0.01749032,0.03886678,-0.06740531,0.03345183,0.01668362,-0.01886581,-0.08856855,0.02534389,0.07813897,-0.08008039,0.02561888,-0.06443723,0.00151056,0.09337422,0.00485627,-0.02542861,0.00117634,0.0000606,-0.00472514,-0.06785813,-0.03642051,-0.00367152,-0.00877609,-0.03967365,0.11812522,0.00597078,0.04858926,0.00148362,0.00967439,0.00240635,-0.03322978,-0.03414689,-0.09278613,0.02417499,-0.00124341,-0.02862004,0.00873345,-0.00318612,-0.034998,-0.0394259,0.01188018,-0.01533639,0.01537316,-0.03063548,0.00191399,-0.02107226,0.01158072,0.07747505,-0.05050293,0.04502604,-0.00858891,0.00835114,0.01777458,0.03953239,0.05502067,-0.02867895,0.02075252,-0.06478283,-0.08646701,0.0216972,0.01149891,0.00227213,-0.04866603,0.00295377,0.03417356,-0.04857709,-0.04252531,-0.0269553,-0.05941784,-0.04245861,0.08320578,0.06123154,0.06671217,-0.04366603,-0.02957761,-0.00377681,0.09035201,-0.02500023,-0.03211729,0.01555417,0.07499338,-0.01503528,0.05299896,0.0033644,0.02663295,-0.02830925,-0.00925006,-0.0357011,0.09849536,-0.07147951,-0.13800563,-0.02579305,0.00850469,-0.02870401,-0.02983792,-0.0195157,0.03975312,0.02451797,0.01795823,0.06497238,-0.01595789,-0.02935688,-0.00940862,-0.01436232,0.02555305,-0.03690708,-0.01016693,0.01913749,0.06306496,0.05998725,-0.04627905,0.02700229,0.01151022,0.03435201,-0.03386208,-0.07820253,0.01657277,0.0277177,-0.02674621,0.04502947,-0.03621323,0.04998757,-0.03951798,0.08786397,0.00460187,0.08618254,0.0817056,-0.01889068,0.07772481,-0.01389091,-0.04389396,-0.05201269,0.01055568,0.0354514,0.00158485,-0.02180617,0.00028739,0.04578316,0.06263741,-0.04530595,-0.04108477,0.05169361,0.05070714,0.03373532,0.00810434,0.01220293,-0.04934328,-0.10327764,-0.20775181,0.01559077,0.0311082,-0.06855279,-0.02326551,-0.04835184,0.01498618,-0.00538599,-0.05092071,0.03473773,0.08045927,0.02492324,-0.00512344,-0.02605122,-0.01483688,0.03245319,0.00069952,-0.03203162,-0.00667473,-0.02418546,0.02604159,0.01582836,0.00593514,-0.04167357,0.04861317,0.04768726,0.13939056,-0.06452044,-0.00613052,-0.04245982,0.04115925,0.00680829,-0.00529172,-0.06995489,0.02059619,-0.00667524,0.04803217,0.00931756,0.00983594,-0.01928496,-0.00618043,0.05030155,-0.02166498,-0.11021048,-0.03628956,-0.08322909,-0.02537568,-0.05523187,-0.0602573,0.00710102,-0.03855969,-0.00423982,0.0674611,0.05373497,0.00776161,-0.00602634,-0.01820939,0.05212595,-0.00630101,0.01014102,0.02988079,0.00501016,-0.02915406,-0.03378747,0.09190797,-0.07859304,0.00311653,0.05203628,-0.02124508,-0.08110604,-0.01768573,0.09358379,-0.04598702,0.07302512,0.09882297,0.04894605,-0.01217975,-0.07180309,-0.05326183,-0.00824219,0.05923414,-0.00231694,0.06622429,0.01600424,0.03683651,0.07025862,-0.0579716,0.03667386,-0.01036286,-0.05007824,-0.02260332,-0.00888212,-0.08493409,-0.01553232,0.08895083,0.03079994,-0.24197158,0.06140658,-0.0262649,0.00745551,-0.06226402,0.04962103,0.01865644,-0.02774205,-0.03914053,0.04809416,0.0135224,0.03849331,0.0683904,-0.01974482,0.03926044,0.04958497,0.10126074,-0.00955187,0.03100679,-0.06988066,0.01450898,0.01468447,0.22834788,-0.02361321,0.02680739,0.08369338,0.04660801,0.05335418,-0.00086427,0.0337312,-0.04079473,0.00226724,0.08817295,0.01101973,-0.00974269,-0.03713159,0.00705591,-0.04183077,0.03564241,0.04259031,0.03239089,0.01482496,0.02817233,-0.00918905,0.09471522,-0.02826387,-0.06169972,-0.09316055,-0.0177838,0.02306401,-0.0394997,-0.0640555,-0.00031005,0.02009895,-0.00235651,0.013297,-0.02593735,-0.0005231,-0.05798345,-0.00938842,0.02407428,-0.00234021,0.05755235,0.06994839,0.01098476],"last_embed":{"hash":"20ab1bdb76260099bd311a71ddcb8c276987434eeb1552dcd4fdf07b8b318bc9","tokens":117}}},"text":null,"length":0,"last_read":{"hash":"20ab1bdb76260099bd311a71ddcb8c276987434eeb1552dcd4fdf07b8b318bc9","at":1743662878460},"key":"Microservices.md#5. Frameworks / Libraries","lines":[415,421],"size":339,"outlinks":[{"title":"Java Microservices","target":"Java Microservices","line":4},{"title":"Solutions & System Designs & Design Patterns","target":"Solutions & System Designs & Design Patterns","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#5. Frameworks / Libraries#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0884406,-0.00176849,-0.03333433,-0.00196177,-0.01213802,-0.01088236,-0.08833904,-0.00458371,0.01574793,0.01608977,-0.01893429,-0.01502912,-0.01266285,0.05067844,0.03458555,-0.06982899,0.00720415,0.06584256,-0.07247174,0.02754795,0.0876184,-0.00230788,-0.00186853,-0.04122663,0.04666717,0.06493956,0.01281193,-0.00011849,-0.01033465,-0.14718181,-0.04231131,-0.07181543,-0.01514015,-0.00526948,0.00018897,0.02762445,0.0185243,0.02234914,-0.01834281,0.06542417,0.03078714,0.01809862,-0.01195021,-0.0287146,-0.08107598,-0.08935464,-0.03798661,0.035112,-0.03555236,-0.00833633,-0.06059935,-0.01014057,-0.03771812,0.0471365,0.02208125,0.10662162,0.0649609,0.09698316,0.01713472,0.03105581,0.03766933,0.00224986,-0.15036926,0.09573993,0.03050318,0.01869257,0.00253328,-0.02173055,0.04893162,-0.00835676,0.00057154,0.05071669,0.0110287,0.07338866,0.06051704,-0.01975843,0.03766009,-0.05938994,0.06196969,0.02503401,-0.02412307,-0.10045055,0.02862359,0.06267165,-0.06994984,0.00210258,-0.09106667,-0.00127172,0.10465387,-0.00371292,-0.03678579,-0.00526055,-0.01546578,0.0211965,-0.08101758,-0.01745583,-0.02292503,0.02310487,-0.02542486,0.12854724,-0.00448939,0.07052912,0.01136857,0.01434049,-0.00010328,-0.0391494,-0.04275399,-0.06882956,0.02732064,0.00785608,-0.01112845,0.03868175,-0.01801389,-0.01068692,-0.04060942,-0.01272012,-0.02483198,0.01895259,-0.01805769,-0.02324475,0.01676486,-0.02249143,0.0872734,-0.0600392,0.0322224,-0.0116861,-0.01008767,0.01933821,0.02261636,0.04882092,-0.04285288,0.03384784,-0.05700368,-0.06936829,0.02084749,0.00872145,-0.01034563,-0.04261514,-0.00665576,0.06226429,-0.05749536,-0.00120966,-0.02020964,-0.07501376,-0.0414418,0.10170069,0.07365841,0.08708888,-0.08358189,-0.01216272,0.00660724,0.10221556,-0.02779732,-0.01220901,-0.0040331,0.06860436,-0.01171476,0.01395443,0.01721404,0.03337287,-0.02695283,0.00100083,-0.03085677,0.09900226,-0.07231965,-0.14072281,-0.02565533,0.00734571,-0.02245648,-0.03846999,-0.01365063,0.0504583,0.02528484,0.00822838,0.05421117,0.00207771,-0.04964482,-0.01113998,0.02252446,0.01231741,-0.03193292,-0.01689903,-0.01495479,0.02968018,0.05653651,-0.03754605,0.03023246,0.00728158,0.04607979,-0.03759111,-0.080605,0.00586625,0.02021746,-0.05105431,0.03679887,-0.03682939,0.0553451,-0.02652087,0.07037536,0.02623007,0.09410786,0.06048945,0.00060848,0.07205676,-0.01465508,-0.03812239,-0.04209767,0.00584696,0.02084162,0.00420663,-0.02676388,-0.00745312,0.03972691,0.03525254,-0.05229329,-0.0262398,0.04290531,0.05614802,0.04006411,-0.00576895,0.00630868,-0.04690475,-0.09198333,-0.20274584,0.02582417,-0.00632501,-0.04923708,-0.02529892,-0.05742228,0.01602132,-0.01477962,-0.09657601,0.04014439,0.07388439,0.00765185,-0.00956862,-0.01758665,-0.04159525,0.01602905,0.00352506,-0.0336473,-0.03999424,-0.01719553,0.01253324,0.00928647,-0.00519854,-0.04163684,0.0186839,0.03990645,0.13674958,-0.05665225,0.00330081,-0.04488702,0.0403631,0.01699358,-0.01773932,-0.07632283,-0.00051247,0.01254437,0.03524055,0.02421663,-0.01080365,-0.02150639,0.00237517,0.04778451,0.00054345,-0.10594499,-0.04235463,-0.06890966,-0.03520561,-0.07115211,-0.04576163,-0.00208102,-0.0361934,-0.01892261,0.05318011,0.0552156,-0.03364043,-0.01233419,0.00401908,0.02085582,-0.00388284,0.01689222,0.03642531,0.01756825,-0.02960883,-0.01696751,0.07998605,-0.05582885,0.00022519,0.02507513,-0.01772396,-0.09518813,-0.00855212,0.09270938,-0.02925977,0.05405926,0.0952036,0.04900741,-0.01995567,-0.0648094,-0.02842368,-0.00574095,0.04329382,-0.00239887,0.05539337,0.013715,0.03564759,0.06230297,-0.05489737,0.04111777,-0.01494473,-0.0715495,-0.03644896,-0.01907879,-0.08073373,-0.03400051,0.09562777,0.04034775,-0.2467213,0.04631604,-0.01596013,-0.00407755,-0.06164151,0.06655153,0.03013803,-0.02663315,-0.02573402,0.0346722,0.01939803,0.04011802,0.04718291,-0.02118132,0.02819275,0.06507266,0.11044222,-0.02770476,0.0356329,-0.06964992,0.02085673,0.03189053,0.23552614,-0.01466604,0.0563527,0.08863463,0.04155406,0.07734513,0.01300488,0.03862443,-0.00331884,-0.01128548,0.09503897,0.0224417,-0.027855,-0.03388313,0.01074087,-0.04078669,0.02373618,0.04387686,0.04327729,0.01315039,0.02095615,0.00144745,0.10529763,-0.03248975,-0.06795216,-0.11457911,-0.02890612,-0.00020345,-0.04720343,-0.08164122,0.01062325,0.00437346,-0.02302448,0.01427999,-0.00635934,-0.001417,-0.06075187,0.00151123,0.01163807,0.01409507,0.03799483,0.06723236,-0.01077832],"last_embed":{"hash":"ca26144dcf8b0b57442be41e50b2fb47a10c3cc007d85b3ad90cb87906dd0964","tokens":68}}},"text":null,"length":0,"last_read":{"hash":"ca26144dcf8b0b57442be41e50b2fb47a10c3cc007d85b3ad90cb87906dd0964","at":1743662878474},"key":"Microservices.md#5. Frameworks / Libraries#{2}","lines":[418,419],"size":215,"outlinks":[{"title":"Java Microservices","target":"Java Microservices","line":1},{"title":"Solutions & System Designs & Design Patterns","target":"Solutions & System Designs & Design Patterns","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#6. Netflix OSS": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05176824,0.04912876,0.04201989,-0.03812878,0.0114041,-0.03104421,-0.00189028,0.01688805,0.04930434,0.01154278,0.01872379,-0.14443816,-0.02682418,0.03845445,0.09470843,-0.03522064,0.03051813,-0.01624056,-0.00556789,-0.00520971,0.10546022,-0.08154441,-0.01113466,-0.00590713,-0.02288024,-0.04298497,-0.038301,-0.01858818,-0.00269354,-0.18288495,-0.01818112,0.00425055,-0.03194906,0.02305847,0.06653335,0.00193781,0.01747157,0.03308548,-0.04894181,0.03918643,0.05382192,-0.03749965,-0.01536144,-0.06448056,-0.01216117,-0.07404104,0.03339326,-0.02750399,-0.00722908,0.00182784,0.002807,-0.00622251,0.00799801,0.03305607,-0.00627478,-0.00712421,0.05375972,0.0336949,0.0177948,-0.04903269,0.10676517,0.02646443,-0.24641751,0.0623983,-0.01447373,0.02284583,0.05210345,-0.01496351,0.01166004,0.0466657,-0.06359715,0.07496327,0.00288929,0.06029342,-0.00463426,-0.00861072,0.04358788,-0.03312218,-0.0819215,-0.00973437,-0.01633345,0.0494929,-0.02427643,-0.00616629,-0.03864426,0.00767773,0.00227022,-0.0458681,0.08343939,0.01766994,-0.04384255,-0.00381409,0.04885902,-0.03002925,-0.06245828,-0.10231601,0.04509782,0.02104201,-0.06342913,0.11617088,-0.01657915,0.00250666,0.02277567,-0.08076456,0.0771107,0.01920267,0.00881015,-0.03883676,0.04594373,0.05259876,-0.04644698,0.0011448,0.06421497,-0.05979171,-0.06730045,0.04037187,-0.05729549,0.02293423,-0.00065152,0.03480707,-0.00380483,0.00901679,0.11154374,-0.0686421,0.03237523,-0.03128472,0.04909534,0.02105197,0.00559068,0.0317921,0.01285263,-0.01567015,0.04419908,-0.03754736,-0.01983177,-0.03514185,-0.00202789,-0.0327642,-0.02523928,-0.06393374,-0.07698973,-0.05458747,0.00086462,-0.04639151,-0.05536562,-0.0038748,-0.04545186,0.03712784,-0.03818519,-0.01309121,0.00195998,0.02136043,0.00327554,-0.09398802,-0.05761795,0.03591609,0.08263129,0.13144213,-0.05402458,-0.00796379,0.06214136,-0.11189013,-0.06140758,0.08170073,0.03496266,-0.13288966,-0.00861677,0.0256,0.02830424,-0.07584933,0.01395858,0.00128486,-0.02950617,0.00346509,0.04706176,-0.04026434,-0.01554535,0.04825708,-0.00575155,0.03710965,-0.09970368,0.00906775,0.05266787,0.06650777,0.03034903,-0.04343676,0.00277086,-0.01372657,0.01753684,-0.01997712,-0.07406787,-0.00032296,-0.02988059,-0.03586786,0.03710571,-0.01810596,0.0249409,-0.07458552,0.00707723,-0.0139047,0.04738686,-0.04097078,-0.06532768,-0.04216037,-0.02671766,0.01697375,-0.08356185,0.00565912,0.02961032,0.00670753,-0.00702586,0.0431939,0.05229284,0.02002304,-0.06299074,0.00326059,0.0229602,0.09873922,0.01708486,-0.00653678,0.02476676,0.01975779,-0.06240048,-0.1813689,-0.02512319,0.03077878,-0.02674263,0.01810357,0.00188927,0.07213121,0.06778228,0.06692012,0.02226091,0.04095379,0.01865524,0.02572135,0.0827146,0.03444804,0.02375866,0.01745724,-0.00770273,0.03362573,0.04967837,0.04659575,0.04699508,0.00447448,-0.04769528,0.01094889,0.0512463,0.10454414,0.01487808,0.0303301,0.00546066,0.03707755,0.03354499,-0.00315345,-0.11387882,0.06442329,0.00814458,0.02132108,-0.0042777,-0.0206437,-0.03757688,-0.02210357,0.03266827,-0.02245267,-0.08142126,-0.03675329,-0.0541653,-0.06577434,-0.02914572,-0.12499134,-0.01882083,-0.05466188,-0.04644381,-0.00181646,0.01870224,0.03841328,-0.02590636,0.04356782,-0.00103417,-0.01829042,-0.02737219,0.00905307,0.01887642,0.00173688,-0.0199283,0.04518316,0.00117883,0.0007605,0.04778343,0.02289856,0.00302607,-0.05153771,0.10255809,-0.02310934,0.01889864,0.03490075,-0.03132551,0.05277732,-0.03479197,0.01319293,-0.02704396,0.03674507,-0.05073873,0.02973511,-0.02307398,-0.03097559,0.01492666,0.05017555,-0.00779651,0.07224841,-0.05070331,-0.01941688,-0.00520845,-0.04269702,-0.01267272,0.08395578,0.06484112,-0.2555936,0.03592064,-0.0302527,-0.01098008,-0.04183476,-0.01696052,-0.00601845,0.01339715,-0.03727356,0.01407957,0.0655925,0.01640918,0.03614794,-0.03944787,0.02350003,0.07001282,0.06399121,-0.02109273,0.06597509,-0.01325217,-0.01979617,-0.00677808,0.23316595,0.01886212,0.0163875,0.01142401,0.03060143,-0.00459869,0.02808487,-0.00702895,-0.02801775,0.03244834,0.0865031,-0.05860405,0.0296822,0.05531281,-0.03466156,-0.03165889,0.07168476,0.00016898,-0.0014398,0.04826586,-0.01521779,0.00509544,0.0876286,-0.03333408,-0.02512969,-0.07966499,0.02136454,0.03241192,-0.00821913,-0.02806605,0.00124178,0.0655506,0.02432193,0.04771707,-0.03924045,0.00767231,-0.01547463,-0.00357509,-0.01671601,-0.03540721,0.06691784,0.04136784,0.01598655],"last_embed":{"hash":"99bf3d7489fdd6a3dcaee071649c3c1a39b3d46170ab1bef65e7ede55d2579be","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"99bf3d7489fdd6a3dcaee071649c3c1a39b3d46170ab1bef65e7ede55d2579be","at":1743662878488},"key":"Microservices.md#6. Netflix OSS","lines":[422,429],"size":1297,"outlinks":[{"title":"Operations Component.png","target":"Operations Component.png","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#6. Netflix OSS#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03553748,0.03254825,0.04598021,-0.0432209,0.01851865,-0.05237885,-0.02111628,0.03351356,0.05205068,0.01474852,0.04295497,-0.09969486,-0.00611559,0.03253732,0.10003639,-0.02821329,0.05148941,-0.02057589,-0.01640947,0.01368915,0.11741298,-0.06194871,-0.02871968,-0.04430993,-0.02031698,-0.00270294,0.00394201,-0.031878,-0.00943448,-0.18374105,-0.03968731,-0.00722542,0.00632741,0.00696974,0.0747197,0.00197628,0.0317193,0.01698405,-0.05256617,0.04732278,0.01738624,-0.03829052,-0.01927362,-0.0441987,-0.01688846,-0.06699895,0.04793648,-0.00161119,-0.00951895,-0.01785517,-0.00902416,0.00753509,0.01224091,0.0296387,0.01437729,-0.01849529,0.06492788,0.00204112,0.00298483,-0.06105414,0.12510173,0.03737254,-0.26922035,0.07293443,-0.01356331,0.0151614,0.03529059,-0.04001566,0.02273206,-0.00115653,-0.07590333,0.06277168,0.01941708,0.08434612,0.00919083,-0.01692171,0.04825494,-0.08033953,-0.0802772,-0.02924762,0.00690454,0.02936227,-0.02785202,-0.00550177,-0.02955431,-0.01318682,-0.01575556,-0.03922955,0.05754485,0.00835776,-0.03605429,0.0087336,0.04585873,-0.03206716,-0.10333104,-0.09453781,0.05028961,0.00379418,-0.0391656,0.13317607,-0.00715527,0.02852778,0.00064832,-0.08082633,0.07571156,0.00288499,0.01209049,-0.03001728,0.06972378,0.05696936,-0.05363523,0.0063313,0.06084754,-0.04750841,-0.0502704,0.06685684,-0.02226448,0.04703329,0.05123107,0.02014862,0.00885743,-0.0100954,0.11083917,-0.0744943,0.0606855,-0.02856362,0.02103583,0.01373594,0.00012562,0.00879235,-0.04234474,-0.0390032,0.06345614,-0.01740743,0.00352579,-0.0085204,0.00597579,-0.04553315,-0.03573884,-0.04993662,-0.07930484,-0.06195917,-0.01545154,-0.04488547,-0.05110197,0.05096611,-0.01674731,0.03063804,-0.0607744,-0.02362975,-0.00656414,0.0620365,-0.01365319,-0.08259063,-0.06842045,0.03716542,0.08955632,0.08564296,-0.06434993,-0.01039108,0.04624462,-0.07308346,-0.06361759,0.12950562,0.03408094,-0.08764346,-0.03065833,0.04273423,0.03946206,-0.05640496,-0.01248002,0.02908779,-0.02653254,0.01742276,0.04949442,-0.05529183,-0.04361024,0.0755975,0.020181,0.01200017,-0.0605596,-0.01758633,0.0393525,0.07530372,0.01308676,-0.05990434,-0.00711541,-0.00419378,0.02120652,-0.00839245,-0.05012489,-0.01387665,-0.02159582,-0.04410314,0.05168564,-0.02395631,0.02996538,-0.07157829,0.03516749,-0.03377192,0.06583439,0.01392479,-0.03535677,-0.03812595,-0.04168465,0.00736269,-0.05883171,0.01522534,0.04054511,-0.0142112,-0.0336621,0.06921919,0.04486669,-0.00174026,-0.06760538,-0.02792056,0.02175843,0.08853597,0.01987854,-0.01942763,0.06694996,0.01256643,-0.07074644,-0.18327869,-0.0396282,0.01100768,-0.04345768,0.01111847,-0.00483444,0.08460419,0.0384352,0.05395431,0.02598546,0.01156214,0.01220273,0.0436578,0.069631,0.00334752,0.01019608,0.03613933,-0.01667352,0.01793383,0.05698104,0.05400233,0.05815012,0.0255447,-0.08060861,0.03755366,0.07158461,0.08598644,0.04169263,0.00178553,-0.06725141,0.03021394,-0.00874813,-0.00166154,-0.11684249,0.04672334,0.01754462,0.03793124,-0.01834819,0.00524188,-0.04238459,-0.00630126,0.01323636,-0.04200919,-0.05055041,-0.05857791,-0.03968558,-0.06815901,-0.03524927,-0.08028306,0.00276371,-0.05562514,-0.03994183,-0.02381813,-0.00386109,0.02154668,-0.00621441,0.01109046,0.01331978,-0.02942322,-0.03852917,0.01867554,0.01910212,-0.017489,-0.02001841,0.05603755,-0.02023307,-0.03395923,0.03372617,0.03471881,-0.02294375,-0.03380236,0.07604855,-0.01587587,0.02551711,0.00087464,-0.03140732,0.04902108,-0.04270337,-0.02177436,-0.00321732,0.06305007,-0.04187123,0.05487281,-0.03319648,-0.01801655,0.04317242,0.05210644,0.00031125,0.06091302,-0.07707802,-0.00413484,-0.0048051,-0.04822973,-0.0369149,0.10765601,0.05107472,-0.21998422,0.05413643,-0.02167122,0.00836214,-0.03953197,-0.03027496,-0.00324627,0.02699383,0.01203184,-0.01687868,0.08668032,-0.03555176,0.04410197,-0.04754595,0.02984135,0.08996419,0.08181354,-0.01937307,0.04748804,-0.00547797,-0.01966047,-0.02420403,0.2160708,0.0348487,-0.00430513,0.00443833,0.02979618,0.00283779,0.01362534,-0.01224276,-0.00631748,0.03924912,0.05200659,-0.06110893,0.00092613,0.05444156,-0.00153753,0.00060925,0.05949697,0.01852895,-0.00455511,-0.01480696,0.0006542,-0.01326091,0.09546652,-0.02346651,-0.00639486,-0.08734003,-0.00722842,0.00145933,0.02073728,-0.0328176,0.02849415,0.06449056,0.04393592,0.03416247,-0.02678049,0.00486661,-0.02333843,-0.0096607,-0.00394365,-0.03573161,0.07064319,0.06433249,0.01481798],"last_embed":{"hash":"29cc795109f4a1136752454e2724739a6eb63bbb734421b69d52fa4e51c08a18","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"29cc795109f4a1136752454e2724739a6eb63bbb734421b69d52fa4e51c08a18","at":1743662878526},"key":"Microservices.md#6. Netflix OSS#{2}","lines":[426,426],"size":323,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#6. Netflix OSS#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05298789,0.05694821,0.01681981,-0.03695474,0.01291469,0.00433627,0.01283524,0.02863192,0.01992079,-0.02198777,0.01295638,-0.13803443,-0.03636727,0.04417155,0.07680835,-0.03158621,0.01222499,0.01848756,0.02176101,-0.00195841,0.10799731,-0.06973734,-0.01151916,0.02489019,-0.03177388,-0.04455715,-0.07357468,-0.02914788,-0.01126017,-0.16312705,0.00283538,-0.00839352,-0.04333821,0.04207921,0.05084429,-0.01476711,0.00028974,0.07389321,-0.03981574,0.0378705,0.05551707,-0.02561304,-0.02390317,-0.04384334,-0.01051109,-0.09203786,0.04650434,-0.04011995,0.00543596,0.00064858,-0.00197723,-0.00976344,0.00451427,0.02386228,-0.02280998,0.03942958,0.03845651,0.06381089,0.05526173,-0.01549593,0.06067527,0.007992,-0.24650306,0.05006242,0.00502318,0.02995005,0.0489935,0.00771125,-0.0001235,0.05562735,-0.06546869,0.06377734,-0.00375357,0.03889488,0.00049877,0.02512799,0.02986896,-0.00720644,-0.06532063,0.02637201,-0.04106435,0.05111698,-0.02201641,-0.01023034,-0.01761751,0.02334638,0.00221574,-0.04293768,0.09892693,0.01969297,-0.04359463,-0.00685848,0.03298727,-0.02693713,-0.0417515,-0.10436745,0.04732763,0.01877771,-0.08039676,0.1180283,-0.04063882,0.00147769,0.02751913,-0.05834331,0.03828606,0.01563383,0.02608704,-0.02882431,0.01701681,0.04803377,-0.02368388,0.01837249,0.0713595,-0.06820735,-0.03977392,0.02902879,-0.08707194,0.0015991,-0.04619773,0.04162627,-0.00335675,0.03468489,0.1020535,-0.06956528,0.01013391,-0.03334246,0.06173106,0.02408328,0.02808136,0.03906923,0.05106873,0.02444835,0.02272555,-0.05518236,-0.03713665,-0.03088805,0.0139463,0.00852564,-0.01257836,-0.03855527,-0.03694493,-0.03050332,0.00619383,-0.02001916,-0.02229667,-0.01173022,-0.06519293,0.05175535,-0.02580769,0.00423498,-0.01558157,-0.02228902,-0.00742504,-0.08551749,-0.07126855,0.02483223,0.05384658,0.13430506,-0.04566525,-0.01241183,0.05545112,-0.13052194,-0.04749979,0.07750708,0.02210082,-0.16199955,0.01713133,0.0319995,0.0176884,-0.07868446,0.00889386,-0.03529242,0.00348847,0.01499056,0.05392868,-0.02639347,-0.00487405,0.03519225,-0.02058554,0.04397913,-0.06132867,0.02710404,0.05040554,0.05272571,0.04131532,-0.02735503,-0.0087633,-0.02169003,0.03784966,-0.0081988,-0.07095364,-0.00090761,-0.04837323,-0.0208847,0.03847244,-0.01912209,0.01429868,-0.0497602,-0.00892456,0.01028442,0.02022086,-0.04805925,-0.04248422,-0.01388348,0.02749036,0.02534641,-0.10246302,-0.03609327,-0.00014668,0.00393887,0.01834402,0.01538361,0.06007794,0.04171324,-0.04754569,0.04338147,0.01371546,0.11384441,0.00414365,-0.00246918,0.00175885,0.03770867,-0.02535561,-0.20990828,-0.01087313,0.03923116,-0.01255694,0.0158691,-0.00808813,0.06601166,0.052573,0.04767579,0.01152238,0.04960991,0.02247005,0.01264752,0.08087515,0.00732617,0.02888226,-0.00472617,-0.00868629,0.04071366,0.01772992,0.02908535,0.00743139,-0.00927267,-0.02997813,0.01542335,0.02208044,0.13520765,-0.01900383,0.0298134,-0.01143983,0.03920563,0.05014631,-0.01161017,-0.11433699,0.06168557,0.00541466,0.0342475,-0.03105963,-0.01894058,-0.01473172,-0.0264942,0.02052835,-0.00852371,-0.10427286,-0.02156916,-0.05824907,-0.04331141,-0.01700713,-0.09885466,-0.00742313,-0.06074434,-0.03785829,0.0195719,0.04558578,0.05805043,-0.03159099,0.0407802,0.01896628,-0.02790049,-0.01646159,-0.00461935,0.01758118,-0.000688,-0.00679585,0.035455,0.03012546,0.00912193,0.05551369,-0.00657587,0.00766022,-0.03744783,0.07792445,-0.03074826,-0.02276391,0.06875208,-0.02649501,0.04701794,-0.01162465,0.00341835,-0.04656217,0.03410881,-0.02863448,0.00759495,-0.00440474,-0.03908308,0.00612353,0.0270423,0.01241829,0.05776355,-0.03624643,-0.03953293,0.01683454,-0.03728956,0.02964512,0.06225004,0.05444792,-0.30629584,0.01390686,-0.04060233,-0.02148954,-0.04236056,0.01004692,0.0014984,0.00526028,-0.06022361,0.03623408,0.04670845,0.05857776,0.0117369,-0.04897464,0.00751245,0.05886236,0.06810804,-0.0316233,0.07697006,-0.03980212,-0.01997403,0.01553408,0.22049072,0.01606425,0.02970782,0.02425881,0.01492737,-0.00345629,0.04342467,-0.01542302,-0.00863774,0.03976544,0.08364142,-0.0642018,0.06586746,0.05412983,-0.03957616,-0.05698641,0.06332061,-0.02163429,0.00070064,0.06296618,-0.069602,0.00532382,0.08006261,-0.02325903,-0.04509878,-0.08409933,0.02097667,0.02765504,-0.03230616,-0.02753906,-0.00363321,0.05943507,-0.01670566,0.02711332,-0.03575512,0.00248071,-0.02360462,-0.02525287,-0.01467083,-0.05296633,0.01875092,0.04167801,0.01708605],"last_embed":{"hash":"f7bfacb3eabd3514962393d412b685f4392bed9e1cece0eb85026d9e8217720d","tokens":175}}},"text":null,"length":0,"last_read":{"hash":"f7bfacb3eabd3514962393d412b685f4392bed9e1cece0eb85026d9e8217720d","at":1743662878541},"key":"Microservices.md#6. Netflix OSS#{3}","lines":[427,427],"size":501,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Microservices.md#6. Netflix OSS#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07788463,0.0590957,0.03766377,-0.0559047,0.00849426,-0.02898254,0.00905667,0.00534954,0.02464698,0.00104068,0.00388164,-0.13752624,-0.02982323,0.05918434,0.07522981,-0.02385456,0.03950814,-0.00144833,-0.02807165,-0.00758039,0.08534235,-0.08311901,-0.01631768,-0.0130536,-0.04032006,-0.05942724,-0.04723746,-0.05383742,-0.0012277,-0.14613697,0.00228828,-0.01196995,-0.02636197,0.0125165,0.05311138,-0.02964319,0.0215592,0.00290686,-0.00394122,0.02878002,0.08691415,0.00594561,0.0014299,-0.06725138,-0.01621616,-0.07848892,0.01069343,0.01003098,-0.00991846,0.02001974,0.00387523,-0.00326319,0.00026744,0.03512895,-0.02412859,-0.07322225,0.03708605,0.01263309,0.00833306,-0.02375957,0.09683481,0.02332525,-0.22828007,0.06136914,-0.03694732,-0.00919601,0.0387468,-0.02746339,-0.00100621,0.0171967,-0.07970309,0.04142107,0.00949151,0.07494983,-0.04367458,-0.02815254,0.02519701,0.02260358,-0.04273662,-0.01652434,-0.00793845,0.04737195,-0.00468226,-0.01426104,-0.05925414,0.00426244,0.00608466,-0.03450438,0.0759378,0.04601084,-0.01098816,-0.01495325,0.04165667,-0.01904255,-0.03763459,-0.09414467,0.01188961,0.05943004,-0.10154866,0.10430308,0.01846361,-0.0157741,0.04939833,-0.0715557,0.05351128,0.01208925,0.00929446,-0.02392564,0.02299546,0.0622601,-0.03496501,-0.03982382,0.04427789,-0.05256618,-0.01725033,0.03886562,-0.00580509,0.01231696,0.02986087,0.03097207,-0.0030546,-0.01772039,0.08122081,-0.03567673,0.04723321,-0.04569111,0.03366487,0.03119303,-0.01576087,0.04761897,0.01218164,-0.04549829,0.02037342,-0.01260593,0.01135132,-0.00822864,-0.00824306,-0.00593178,-0.05125596,-0.09881949,-0.05588366,-0.08364798,-0.00059257,-0.0513563,-0.01863615,-0.00951181,-0.02863239,0.04561411,-0.03866366,-0.00437571,0.0358634,0.00838095,0.00221941,-0.05479179,0.0042735,0.04837377,0.0796714,0.13634945,-0.02426933,-0.00596178,0.01460284,-0.08323469,-0.03971251,0.10740212,0.05218839,-0.10462244,-0.03436996,0.01467225,-0.00203047,-0.03310319,0.01741575,0.02115609,-0.01132129,-0.01315352,0.01053677,-0.02745835,0.00323897,0.01813808,-0.01956264,0.03040006,-0.12055992,0.02508575,0.01669314,0.03972495,0.02239754,-0.05024442,0.00587392,-0.03478138,0.01601538,-0.00903723,-0.08872537,0.01830611,-0.03828044,-0.05999896,0.02375797,-0.00298206,-0.00553758,-0.06371743,-0.01295475,-0.03635876,0.05000699,-0.03235654,-0.07729138,-0.03773046,-0.02728564,0.02799195,-0.10155252,0.04121258,0.08485572,0.01260165,-0.02656173,0.03146932,0.03570367,0.01118572,-0.03868805,0.01864203,0.04000462,0.06702912,0.01526445,0.00713665,0.04377561,0.0096371,-0.09067013,-0.20716262,-0.03256598,0.0109089,-0.02051077,0.0243071,0.01646778,0.03433577,0.10068426,0.07082849,0.03941603,0.05597617,-0.02452684,-0.01485202,0.08110216,0.04940519,0.04393044,0.04034539,-0.00578362,0.00416918,0.063754,0.04505787,0.03273238,-0.0033741,-0.02257552,-0.0276267,0.03833332,0.08446553,0.05431921,0.06358484,0.00606558,0.04795433,0.04666202,-0.00896206,-0.11119512,0.08002609,0.03665975,0.01094555,0.00235198,-0.01690968,-0.0627341,0.00151213,0.08156595,-0.04184048,-0.10331342,-0.02558865,-0.05070306,-0.05674578,-0.02536639,-0.1413036,-0.05594559,-0.02065564,-0.03315364,0.02458718,0.00820139,0.01983684,-0.0457536,0.02686407,-0.01084474,0.01036683,0.01357871,-0.01237764,0.03434408,0.00123244,-0.06277818,0.02010464,0.01070134,0.01099711,0.05617814,0.03272008,0.03469041,-0.06781673,0.0822625,-0.02305104,-0.01062096,0.01430797,-0.00200275,0.10287009,-0.05024292,0.02737025,-0.02236922,0.01358313,-0.06159291,0.04111416,0.01332931,0.00047469,0.04644563,0.07869513,-0.06616015,0.05699978,-0.02122756,0.02000211,-0.04031717,-0.06168943,-0.00891916,0.09296689,0.06642064,-0.24510206,0.02887583,-0.04637029,0.00638969,-0.05819798,-0.04025112,0.00969903,0.02048601,-0.07812203,0.03559814,0.10990987,0.00350352,0.04830828,-0.0189823,0.01796847,0.04432396,0.0594031,-0.01333741,0.06492964,-0.01497422,-0.01277679,-0.02432652,0.23289451,-0.00999748,0.02495102,-0.00923283,0.06389593,-0.00697131,0.00459279,-0.01512184,-0.03233231,0.00866852,0.10098264,-0.04545513,0.02039679,0.05894269,-0.03508878,-0.00263586,0.04635774,-0.01294902,-0.04727592,0.03315058,-0.02060545,0.03239756,0.07619486,-0.02027523,-0.04830409,-0.01101783,0.04503263,0.0297978,0.02077664,-0.0364203,0.00610321,0.03460824,0.0191476,0.07476398,-0.03370072,-0.01344444,-0.00252795,0.03070627,-0.01021002,-0.04383294,0.07288771,-0.00276417,0.01411496],"last_embed":{"hash":"66f4323d69f5cd6afde667623a2781a7c240abeaccd0b4259704acc43cd3cf2e","tokens":170}}},"text":null,"length":0,"last_read":{"hash":"66f4323d69f5cd6afde667623a2781a7c240abeaccd0b4259704acc43cd3cf2e","at":1743662878558},"key":"Microservices.md#6. Netflix OSS#{4}","lines":[428,429],"size":422,"outlinks":[],"class_name":"SmartBlock"},
