
"smart_sources:Operation.md": {"path":"Operation.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05143408,-0.05003597,-0.00553525,-0.03654329,-0.00362855,-0.02778434,-0.00119732,-0.00766796,0.03960181,0.04509975,0.01374367,-0.0227574,0.05848414,-0.00085769,0.00589195,-0.02335767,0.0016679,-0.02260995,-0.0770927,-0.00035749,0.03586532,0.02005154,-0.07722543,-0.07147938,0.00618205,0.03412275,0.00157185,-0.02086901,-0.05122954,-0.16706868,-0.00281948,-0.03688179,0.04383046,-0.02037625,0.07552129,-0.02553141,0.00481857,0.01441985,-0.08377745,0.00469135,0.05552413,-0.01349915,-0.0992021,-0.00010914,-0.02864219,-0.07222309,0.00297799,0.00815623,-0.00844191,-0.02701756,-0.02692657,-0.03033051,-0.04726389,-0.01513066,0.0082776,-0.01427122,0.05295106,0.05754033,-0.0044004,0.03058826,0.02078706,0.02976934,-0.2004533,0.05100855,0.034832,0.00074727,0.01584189,-0.10950775,0.0271347,0.03569914,-0.00967017,0.02830391,0.01823091,0.10897311,0.02764536,0.09304653,-0.05273475,-0.04840283,-0.00380841,-0.01816255,0.02017369,-0.06550403,0.00338519,0.07320428,-0.05714859,-0.02613081,-0.01471271,0.04225766,0.09212807,0.0618761,-0.03038802,0.03484738,0.0649545,0.02446912,-0.03012597,-0.00455016,-0.03696666,-0.03136603,-0.08393545,0.14574747,-0.02480661,0.00769133,-0.02846529,-0.02631701,0.02465204,0.00829132,0.01096142,-0.03987582,0.02535818,-0.00869192,0.00258005,-0.01535046,0.00489114,-0.04010319,0.00791097,0.02778866,-0.0029698,0.04507984,-0.05315704,-0.02018814,0.04295561,-0.02192822,0.09806298,0.00842037,0.03590117,-0.02371166,0.05906712,0.09333269,-0.04420673,0.04617088,-0.00847643,0.00020123,-0.01830045,-0.02728647,0.03421427,-0.04404601,-0.01517878,-0.01445066,-0.01782287,0.0326522,-0.03855053,-0.03006735,0.06148051,-0.06019124,0.01640117,0.13671821,-0.0155064,0.0670228,-0.05306874,-0.08862776,0.00001044,0.04323738,0.00873348,-0.0433582,-0.02624774,0.02847449,0.05445305,0.05708453,-0.048684,0.0080914,-0.04492394,-0.02364218,-0.0096661,0.1837389,-0.00505155,-0.09213648,0.02010611,0.0074431,-0.05940955,-0.00143309,-0.02519156,0.0049758,-0.04586614,-0.00247198,0.03897988,-0.05394655,-0.02802311,0.01062223,-0.06571166,0.03920687,0.02280961,-0.0433762,-0.00929075,-0.00580759,0.02814763,-0.0188973,0.01210555,-0.00758525,0.0004257,-0.03697259,-0.13879299,0.0325653,0.01734776,-0.05472056,0.01778377,-0.0678743,0.01830618,-0.02016111,0.0228824,-0.03202785,0.15164964,0.06082094,-0.00867947,-0.02389891,-0.0636054,0.0164192,-0.01897642,-0.03634737,0.02608713,0.03724679,-0.01280584,0.02938969,0.05434164,0.04524035,-0.032591,0.00462734,-0.01345817,0.04502766,-0.0041774,0.01716519,0.04204142,0.04155745,-0.06519211,-0.22210306,0.02636805,-0.02081401,-0.04562524,0.02440884,-0.011337,0.02342806,0.04138432,-0.01550846,0.03161269,0.08683259,-0.01420766,0.00942562,0.02735633,-0.00451745,-0.01693509,-0.00348197,-0.02669038,-0.0332977,0.01145561,0.04188626,0.02601455,0.05480109,-0.06797842,0.01333263,-0.01979758,0.19327307,-0.02416875,0.09592991,-0.00379419,0.04580737,0.03001698,0.02109055,-0.10930251,0.02956076,0.03471031,0.0456627,0.04789447,0.02014801,-0.03521562,-0.04718077,0.0525347,-0.01966948,-0.07012077,0.02869556,-0.04998568,-0.01613937,0.03628207,-0.04682593,-0.00628135,-0.06939106,-0.00495284,0.03528003,0.02005741,0.07272732,-0.00969143,-0.03947889,0.00428684,0.0191978,0.00004662,-0.00262367,-0.01427914,-0.0014533,-0.05840101,0.06893706,-0.08957253,-0.00092538,0.03269871,-0.01633826,-0.07166074,-0.04580331,0.06914096,-0.0495358,0.03401767,0.04889924,0.04396308,-0.00798677,-0.06798797,-0.02657899,-0.03766092,0.03697672,-0.06430189,0.02438027,-0.00239107,0.01873063,0.03686541,0.04677857,0.01836083,0.07435445,-0.07482373,-0.01528016,-0.07202955,-0.0527323,-0.01295981,0.1267295,0.00678772,-0.2339662,0.01863188,0.04531237,-0.01229323,-0.01353663,0.03057913,0.04872721,-0.0184302,-0.05722488,0.00453045,-0.03725079,0.01721237,-0.01801786,0.0163233,0.04175795,0.01655268,0.05634387,-0.01554518,0.03549014,-0.04056003,-0.00733439,0.03833454,0.19250187,-0.00314975,0.04282295,0.07692409,0.0218043,0.03161756,0.04848847,0.02820593,0.04661684,-0.00542875,0.01109398,-0.04371506,0.00258421,0.03561233,0.02899683,0.00875027,0.04640837,0.02782067,-0.03804612,-0.02320571,0.02275116,0.00935448,0.10567892,-0.04208492,-0.02337444,-0.08822319,0.02155365,0.00287119,-0.0225604,-0.06322568,0.04052499,0.02750288,0.06604326,0.0878121,-0.02831864,-0.04650344,-0.06552141,-0.00138172,0.04575078,-0.09773302,0.04271946,-0.01664266,0.00932969],"last_embed":{"hash":"6da6c2aa2161749a8d2b49dc3a02dee4afad580f26e48774e5874c1b8c534e5e","tokens":59}}},"last_read":{"hash":"6da6c2aa2161749a8d2b49dc3a02dee4afad580f26e48774e5874c1b8c534e5e","at":1743662836437},"class_name":"SmartSource","outlinks":[{"title":"DevOps","target":"DevOps","line":1},{"title":"Cloud - SaaS","target":"Cloud - SaaS","line":2},{"title":"Microservices","target":"Microservices","line":3},{"title":"Testing","target":"Testing","line":4},{"title":"Network","target":"Network","line":5},{"title":"Database","target":"Database","line":6},{"title":"Security","target":"Security","line":7},{"title":"Optimization","target":"Optimization","line":8}],"blocks":{"#":[1,8],"##{1}":[1,1],"##{2}":[2,2],"##{3}":[3,3],"##{4}":[4,4],"##{5}":[5,5],"##{6}":[6,6],"##{7}":[7,7],"##{8}":[8,8]},"last_import":{"mtime":1726812097000,"size":128,"at":1743662830171,"hash":"6da6c2aa2161749a8d2b49dc3a02dee4afad580f26e48774e5874c1b8c534e5e"}},