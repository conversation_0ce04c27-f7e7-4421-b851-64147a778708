
"smart_sources:Python.md": {"path":"Python.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08221883,-0.0128223,0.01125568,-0.00047242,0.01499861,-0.00427232,-0.02684127,0.02103645,-0.01015008,0.00370631,-0.02047223,-0.13462263,0.07623401,0.02722961,0.04227418,-0.00168068,-0.02554446,-0.0068087,-0.02832983,-0.05978402,0.06240943,0.01331324,0.02308007,-0.04383703,0.01944902,0.00348202,-0.04217752,-0.04859949,-0.02998794,-0.19039273,0.06906632,-0.0091558,-0.00621007,-0.03239778,0.05258655,0.03717114,-0.03953268,0.04042243,-0.04764402,0.01776873,0.07554583,-0.0275818,0.00747421,-0.01230695,0.01190599,-0.06532166,-0.02387528,-0.00023304,-0.02063532,0.00087527,-0.0300369,-0.03211078,-0.01247941,-0.00663801,0.03937568,0.03544406,0.04176337,0.12005648,0.0173207,0.02092473,0.0518465,-0.00791159,-0.16849016,0.12113398,0.01561147,0.01366976,-0.05160226,-0.04601805,0.0605298,0.02959813,-0.05557908,0.02049634,-0.00882541,0.09292793,-0.00733636,0.03293868,0.06204519,0.00521672,0.01015729,-0.0010908,-0.01392656,0.04705034,-0.00686381,0.01035627,-0.00173444,-0.01562719,0.01569863,-0.00400453,0.04688463,-0.02390662,-0.01229154,-0.04612413,0.01881953,0.05145806,-0.01224053,0.00740621,0.05380775,0.01135451,-0.09197245,0.12083421,-0.03842125,0.01667636,-0.0037344,0.01741774,0.07904616,0.01821267,-0.00629965,-0.02371269,-0.03306002,-0.01247042,-0.02168887,-0.01095144,-0.0043361,-0.0486735,0.02015042,0.01367351,-0.04012979,0.05182049,-0.00939708,-0.02075316,-0.02355242,0.021652,0.04957096,0.0318326,0.03045044,0.03344062,-0.02961203,0.03269546,0.01607666,0.05171721,0.02850281,0.01914073,-0.09553669,-0.0350943,0.0267806,0.01294092,0.02864397,0.01861168,0.03522833,0.05556554,-0.05121933,-0.03753118,0.00001748,0.00184424,-0.07961545,0.06741017,-0.02914708,0.00470502,-0.09283056,-0.02980769,-0.03172927,0.02638969,-0.06666897,-0.01744493,0.06064012,0.05505225,0.0678837,0.1031076,-0.03527896,0.01264909,-0.04781481,-0.03931551,-0.08968082,0.08686689,0.00484833,-0.11553966,-0.06773541,0.00683731,-0.0225125,0.00819542,0.07847256,0.00180394,-0.05248613,0.01718644,0.03560001,-0.05407628,-0.11098219,-0.00848009,0.04866817,0.01232099,-0.02281189,-0.02843831,0.0009176,-0.02037106,0.03057349,-0.03409645,-0.00645857,-0.08138689,0.04135834,-0.03289289,-0.03860105,0.04377166,-0.04693298,-0.01505146,0.00158709,-0.00506193,0.0039404,0.01389679,-0.00627853,-0.01451972,-0.01498807,0.03243897,0.01938099,-0.01581354,-0.07024094,-0.02503547,-0.05198777,-0.02984127,0.09780013,0.03127492,-0.03961892,-0.02123529,0.07837182,-0.03401972,-0.05203411,-0.04762933,0.01782528,0.06797875,-0.01410489,0.04305947,-0.06991167,-0.00851966,-0.06146287,-0.21313378,0.0482524,0.01002622,-0.01657345,0.05043438,-0.07041612,0.00579169,0.03518624,-0.00567409,0.05032125,0.08356871,0.02164126,-0.03823816,0.04702614,-0.02321212,0.0869598,0.00538246,-0.02581569,-0.0768986,0.00401276,0.000303,0.00505495,0.00897545,-0.02591252,-0.0138166,-0.00265044,0.16571011,0.02583486,0.07781734,0.05624345,0.05343478,0.04376364,-0.0434181,-0.16587524,-0.0038002,0.01487305,0.03858441,0.0498485,-0.00121781,0.00761094,0.01748297,0.03349714,0.0225008,-0.07511554,0.00486613,0.01815786,-0.00626859,-0.0558555,0.00399354,0.00257099,-0.02792723,0.02901642,0.03664023,0.05405279,-0.02595097,-0.03939955,-0.06234351,0.02553299,0.02239791,0.0316477,0.00537991,-0.04044077,0.00440342,-0.07801411,-0.04356035,-0.0452265,0.03922008,-0.05668391,0.01346841,0.00383886,-0.09636689,0.11953595,-0.00662954,0.03357066,0.02881396,0.01789257,0.00113446,0.02282346,-0.00150835,-0.00888414,0.04829708,-0.01133049,0.03239971,0.0048264,0.06493065,0.01106384,0.08587811,-0.06135626,0.13344182,0.00239928,-0.02917425,-0.03825466,-0.08681227,0.03738343,0.0551748,0.04758334,-0.26332101,0.03570568,0.01061015,0.04985363,0.02781601,-0.00503368,0.08131289,-0.06837361,-0.03049184,-0.01763334,-0.01516884,-0.01626146,0.069759,-0.01272249,0.00040115,-0.00112728,0.10837256,-0.00632594,0.02542481,-0.04899848,-0.02692265,0.04643401,0.18389854,-0.02461139,0.04351058,0.04016397,-0.06644209,-0.02119407,0.07255644,0.0123202,-0.02865681,-0.03335222,0.09455211,-0.0158632,-0.00015527,0.04791948,-0.01213054,-0.02956091,0.04362216,-0.00703201,-0.01399138,0.01531448,-0.05489035,0.00923494,0.06328505,-0.01762417,0.01383115,-0.10829227,0.01378945,0.03367759,-0.04403549,-0.0197367,-0.0234411,0.0052319,0.05994808,-0.01116888,-0.00971375,0.00460223,-0.07064226,0.00789447,-0.00755516,-0.08725484,0.09994333,-0.00542029,-0.03784117],"last_embed":{"hash":"f1b691365e4e2eb5985de2047bbeeca7dd204197af30c9bed76b33d4ba5bc97d","tokens":156}}},"last_read":{"hash":"f1b691365e4e2eb5985de2047bbeeca7dd204197af30c9bed76b33d4ba5bc97d","at":1743662848807},"class_name":"SmartSource","outlinks":[{"title":"Backend - Back-end","target":"Backend - Back-end","line":3},{"title":"Machine learning - Deep Learning - AI - ML - DL","target":"Machine learning - Deep Learning - AI - ML - DL","line":4}],"metadata":{"relates":["[[Backend - Back-end]]","[[Machine learning - Deep Learning - AI - ML - DL]]"]},"blocks":{"#---frontmatter---":[1,5],"#1. Dataset":[6,9],"#1. Dataset#{1}":[8,9],"#2. GUI":[10,13],"#2. GUI#{1}":[12,12],"#2. GUI#{2}":[13,13]},"last_import":{"mtime":1735735281734,"size":512,"at":1743662830205,"hash":"f1b691365e4e2eb5985de2047bbeeca7dd204197af30c9bed76b33d4ba5bc97d"}},"smart_blocks:Python.md#2. GUI": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0888584,-0.00173949,0.00102035,-0.0076167,-0.01545794,-0.01726787,-0.0251386,0.02476413,-0.04463832,0.02264035,-0.05070951,-0.10426109,0.04892527,0.02941374,0.03513291,0.00065047,-0.04046,-0.01301936,-0.02507307,-0.02924724,0.07747072,0.02247789,0.02541911,-0.01917906,0.00019421,0.03813246,-0.06004579,-0.05712314,-0.01271534,-0.12637062,0.03774367,-0.01545584,-0.02399208,-0.0437013,0.00802563,0.00085262,-0.05246712,0.03705978,-0.06593134,0.02870832,0.06609364,0.01937405,-0.0065848,0.00185542,-0.00096879,-0.10905124,-0.04417256,-0.00397422,0.01027903,0.00824824,-0.03702461,-0.02717951,0.01904642,-0.03536883,0.03151301,0.0370038,0.03847989,0.09959109,-0.02637321,0.02227112,0.02338635,-0.06173561,-0.14438808,0.15050209,0.03955255,0.01391409,-0.03672514,-0.04405855,0.07562214,0.02983833,-0.05315473,0.02028928,-0.02663412,0.09274779,0.03838695,-0.00493231,0.06438672,0.01249844,0.00281245,-0.01495014,-0.03371832,0.03421956,-0.00878906,0.05740604,-0.03943709,0.00876928,0.01261763,0.01082809,0.04844258,0.00574472,-0.00665549,-0.06234213,0.03139524,0.05301253,-0.01705764,-0.00093943,0.01484289,0.0301136,-0.05329793,0.15587972,-0.0282039,-0.00645825,0.01533375,0.02230906,0.08155856,0.02556525,0.02525878,-0.00449306,-0.03446031,-0.0039803,0.02233912,0.01166927,-0.02152678,-0.02677704,0.01034154,0.00274102,-0.0787645,0.05190353,-0.01195364,-0.01106784,0.00392011,0.05634497,0.03965772,0.01277345,0.04149858,0.02912415,-0.03025031,-0.00958933,-0.00127174,0.02864434,0.01772301,0.0232157,-0.07433298,-0.00496045,0.00896655,-0.00600631,0.01318909,0.02864258,0.02654801,0.0529049,-0.02176879,-0.04294462,-0.0033434,-0.03886294,-0.0560156,0.06812441,-0.02064111,0.02280273,-0.10608041,0.0072916,-0.01948616,0.02734536,-0.04904204,-0.00356112,0.02120977,0.03805453,0.06175602,0.11052262,0.01528945,0.00449727,-0.07208723,-0.00839982,-0.07434703,0.03479777,-0.01158994,-0.1189955,-0.04588019,0.02101428,-0.04070682,0.00863366,0.04931276,-0.01302444,-0.04891431,-0.00188624,0.05881292,-0.06117522,-0.1008734,-0.00253501,0.07867695,0.0342284,0.03833126,-0.03074486,-0.00752643,-0.01687279,0.0163273,-0.0465537,-0.01401614,-0.10174493,0.06225564,-0.03937818,-0.05717278,0.03012501,-0.03556716,-0.01770636,0.00359939,-0.00829945,0.01833745,-0.0193601,-0.0156782,-0.01174556,0.04921435,0.02102319,0.01371884,-0.00732294,-0.03088458,-0.02252032,-0.05785942,-0.00026973,0.10091937,0.03752489,-0.04512691,-0.00143911,0.10260233,-0.00277004,-0.06334469,-0.05131948,0.01717629,0.06995584,0.00719978,0.05908789,-0.0733599,-0.02031539,-0.03921315,-0.23873164,0.06465662,-0.01626534,-0.00281988,0.03772994,-0.04833722,-0.01692268,0.01232804,-0.03921169,0.05005721,0.12497618,0.00412969,-0.04164089,0.04755543,-0.04040537,0.06690413,-0.0246118,-0.0705067,-0.08387738,-0.02817613,0.02855358,0.01562198,0.01171273,0.00376171,-0.00390938,-0.02115444,0.15996696,0.0384685,0.09113211,0.04750816,0.06643272,0.03244392,-0.02209876,-0.14626926,-0.01029025,0.06801549,0.00015622,0.03690502,-0.00243517,0.03956484,0.02216365,0.02154833,0.06538393,-0.09727737,0.02869387,-0.00891409,-0.00074403,-0.09921106,0.01317726,-0.0291697,0.01669834,0.04006588,0.05212348,0.06844929,-0.03895027,-0.04714969,-0.05559243,0.02785145,0.01828752,-0.01518042,0.00112815,-0.01804353,0.00726232,-0.04220112,-0.02868122,-0.01739668,0.02691975,-0.07724062,0.01871341,0.00465495,-0.06355481,0.11239206,-0.03497211,0.01282841,0.00688763,0.00709711,0.0089369,0.04099905,0.00484538,-0.05232183,0.01503551,0.00316677,0.05739852,0.01767032,0.02492352,0.00632007,0.06783295,-0.08001298,0.08187963,-0.05407592,-0.0323231,-0.01021594,-0.06171746,0.0355637,0.04512092,0.04041908,-0.26000065,0.04587058,0.02153012,-0.00546034,0.02534044,-0.00524438,0.08161694,-0.06856041,-0.02547471,0.00478715,0.010795,-0.01491291,0.04869226,-0.02551733,0.03408169,0.02625651,0.11754145,-0.01162477,0.00176392,-0.05928423,-0.02003357,0.0260113,0.20412725,-0.02256791,0.0725152,0.04028429,-0.04341951,-0.00938001,0.08903003,0.04214166,0.00497729,-0.04268842,0.08671261,-0.06240737,-0.03025117,0.00991477,0.01468726,-0.01999185,0.01709776,-0.03025145,-0.05253527,0.04174777,-0.06217158,-0.01821832,0.04692478,-0.05382758,0.00738616,-0.10742642,0.02103621,0.03953295,-0.058844,-0.00275803,-0.00666405,-0.00461173,0.07551527,-0.01022689,-0.00877477,0.01539496,-0.04736607,0.0186095,-0.01669268,-0.05204152,0.0806369,0.01709297,-0.01291933],"last_embed":{"hash":"3ef1533b358b2f5db94870acc589075e26788a59bc585bce2f641ff5fb296247","tokens":76}}},"text":null,"length":0,"last_read":{"hash":"3ef1533b358b2f5db94870acc589075e26788a59bc585bce2f641ff5fb296247","at":1743662848796},"key":"Python.md#2. GUI","lines":[10,13],"size":293,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Python.md#2. GUI#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09213272,-0.01293842,-0.00055604,-0.01456926,-0.00658687,-0.01546593,-0.03640542,0.01892105,-0.03262787,0.03225396,-0.0649297,-0.09371327,0.03533753,0.03601455,0.03767375,-0.0223358,-0.03958658,-0.00898978,0.00522099,-0.02561946,0.07943802,0.03816868,0.00278114,-0.01317614,-0.00367341,0.0437929,-0.06971784,-0.0320085,-0.01739031,-0.11964238,0.05047277,0.00577226,-0.01138087,-0.05108213,0.01704043,-0.00140958,-0.03707173,0.02771585,-0.04573585,0.02130084,0.06099816,0.02641424,0.01435175,0.0206393,0.01213893,-0.13176537,-0.0491851,-0.00628036,-0.00121803,0.01034917,-0.01437057,-0.03262531,0.02711705,-0.03854498,0.01774835,0.05338866,0.06711335,0.0941315,-0.02049505,0.03547709,-0.00783648,-0.07222729,-0.14847398,0.12877981,0.0618716,0.00812096,-0.02783297,-0.03266658,0.05871393,0.02391338,-0.04553166,0.00951723,0.00073347,0.09598816,0.02729542,-0.00927184,0.05333057,0.01262947,0.01546976,-0.02006847,-0.05429402,0.03759889,0.00581665,0.05442061,-0.06033096,0.01363385,-0.00397878,0.02593638,0.05705004,-0.01546312,-0.00126884,-0.04213005,0.04284352,0.05621305,0.01100675,0.00833202,0.02363046,0.00531544,-0.02117689,0.14616795,-0.0278125,-0.02982198,0.01512249,0.01986277,0.07487073,0.03392513,0.02287552,-0.01011725,-0.03597925,0.00559681,0.02806808,0.02234697,-0.00749362,0.00174734,0.01306905,-0.02287676,-0.09380832,0.04611456,-0.01733167,0.01661602,-0.01934348,0.04641129,0.03803911,0.00343003,0.04523602,0.02017152,-0.02316854,-0.0167744,-0.02505594,0.01161815,0.03211549,0.01754084,-0.08380812,0.01461724,-0.01142148,0.00280042,-0.00821236,0.02147016,0.04150929,0.0414582,-0.03998869,-0.03319695,0.00382658,-0.04816909,-0.04718496,0.09509108,-0.01292601,0.03091938,-0.09962509,-0.02198041,-0.01312071,0.00707787,-0.04814046,-0.00405476,-0.00446541,0.01482521,0.07846392,0.09451534,0.01335534,0.01997747,-0.09495346,-0.0239081,-0.05975241,0.03351,-0.03524554,-0.11019634,-0.02701514,0.02847711,-0.04273043,-0.00249019,0.0507581,-0.01172775,-0.0683362,0.01188721,0.04058346,-0.07105478,-0.08816496,-0.02221963,0.08335557,0.05659492,0.03196187,-0.02117299,-0.01297958,-0.01040408,0.0032923,-0.03634583,-0.02371834,-0.09953599,0.07361751,-0.04583004,-0.03138702,0.05116331,-0.02220572,-0.03871053,-0.00251416,-0.02067866,0.00996683,-0.02335699,-0.01181635,0.0173443,0.06047206,0.03804051,0.02616309,0.00018617,-0.02348078,-0.04321447,-0.06227876,-0.03794462,0.09175841,0.03950816,-0.05261192,-0.01281143,0.1074162,-0.00190953,-0.08625118,-0.04753794,0.02106162,0.07598904,0.00033581,0.0453026,-0.05755217,-0.00892536,-0.05287919,-0.22164237,0.04958728,0.00718087,-0.00448444,0.00157719,-0.04602398,-0.0068173,0.01278273,-0.05738681,0.05269916,0.11756869,-0.00057871,-0.01112654,0.03097668,-0.03810311,0.06812473,-0.03228306,-0.05061635,-0.07423357,-0.03642378,0.03854262,0.03064833,-0.05354681,-0.03643746,-0.01680715,-0.00724458,0.15295798,0.019799,0.0926015,0.02600797,0.07149629,0.02973016,0.00205018,-0.15706351,-0.0265089,0.06042261,0.00137523,0.04610376,-0.01752686,0.03769797,0.00839566,0.02421769,0.06451885,-0.10941247,0.01617184,-0.02727976,0.01260218,-0.12209308,0.01637081,-0.00935965,0.02128294,0.05329763,0.06245059,0.09833396,-0.02514463,-0.03798008,-0.03472435,0.03935071,0.00792615,-0.01102969,0.00217,-0.02741393,0.00369436,-0.01497859,-0.00601636,-0.01748132,0.02216202,-0.07974032,0.01462717,0.00532767,-0.04139153,0.10109577,-0.04089597,0.00685177,0.037379,0.00643717,0.00819587,0.028628,-0.00157993,-0.0593586,0.00847032,0.0162152,0.05749442,0.01618655,0.02719259,0.01438676,0.04671938,-0.07934061,0.05452196,-0.05719187,-0.01905672,-0.00830821,-0.04455638,0.03693771,0.02380981,0.06139216,-0.25052765,0.04536851,0.00686638,-0.0185976,0.02387678,0.02193393,0.07755399,-0.05599454,-0.02201326,0.01496833,-0.00084866,-0.02732912,0.03687725,-0.05130643,0.03507454,0.02714213,0.12313583,-0.00354304,0.01103544,-0.03689577,-0.00797438,0.01323217,0.18493649,-0.04285013,0.06776901,0.04034079,-0.03386848,-0.00540344,0.11497033,0.06425893,0.00465834,-0.02798273,0.1257765,-0.06927501,-0.01290433,0.01457262,0.018914,-0.04227578,0.0261007,-0.03175433,-0.04053968,0.05046942,-0.08068003,-0.02094309,0.02957977,-0.05685228,-0.00381533,-0.09573758,0.02431691,0.02277702,-0.07502636,-0.01643731,-0.02910069,0.0238255,0.0720811,0.01475133,-0.00740801,0.02099921,-0.02629195,0.01531188,-0.00447547,-0.0590511,0.06583492,0.0374434,-0.06174685],"last_embed":{"hash":"130dda2f2a10e4aa672c096c0e862fe7d8fada8c21d51c35dbbe94ba854d9f32","tokens":52}}},"text":null,"length":0,"last_read":{"hash":"130dda2f2a10e4aa672c096c0e862fe7d8fada8c21d51c35dbbe94ba854d9f32","at":1743662848807},"key":"Python.md#2. GUI#{1}","lines":[12,12],"size":203,"outlinks":[],"class_name":"SmartBlock"},
