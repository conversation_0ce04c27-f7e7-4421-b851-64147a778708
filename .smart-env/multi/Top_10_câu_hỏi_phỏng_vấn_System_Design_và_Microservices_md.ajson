
"smart_sources:Top 10 câu hỏi phỏng vấn System Design và Microservices.md": {"path":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0526161,0.06258147,0.01814394,-0.01566897,0.00454633,-0.0418793,0.02074173,0.02343351,0.02868546,0.01766771,-0.04271508,-0.14853212,0.04391876,0.02754107,0.06194804,-0.00010721,0.01353269,-0.00192403,0.00147036,-0.05796913,0.08898368,-0.02718106,-0.02341825,-0.02786554,0.00262676,-0.04626044,-0.00791527,0.01352206,-0.01981789,-0.19402945,0.02262394,0.03937923,-0.01944309,0.01158,0.02408016,-0.00886245,0.02394784,0.00918919,-0.0008097,-0.00964189,0.04052966,0.02991272,0.00543563,-0.09514055,0.03019533,-0.02191654,-0.03504241,-0.0240276,0.00584511,-0.01830434,-0.00148197,-0.00024984,0.03227808,0.01951798,-0.02464353,0.00725217,0.04958043,0.06799669,0.00281875,-0.03955242,0.08380207,0.03502877,-0.24808016,0.06739832,0.01346113,0.0155737,0.06011514,-0.00972971,0.01053988,0.07744728,-0.03963745,0.00220605,-0.00234758,0.04433613,0.00123289,-0.02801332,0.04274713,-0.03481414,-0.05365588,0.01010687,0.01665168,0.04825626,-0.00783596,0.00490335,-0.10362066,-0.00564329,0.00538864,-0.03027329,0.06306031,-0.01712733,0.01922755,-0.00175296,0.02963487,-0.01328089,-0.0048397,-0.0288062,0.07407248,0.0254229,-0.10003096,0.11451842,-0.01076874,-0.02120725,0.05326672,-0.05327665,0.06947158,0.02004824,-0.02544706,-0.07349322,0.01209654,0.04187559,-0.05682864,-0.01022691,0.02625643,-0.05837313,-0.06500943,0.027445,-0.01638391,0.01641326,0.01521146,-0.00644397,-0.05735042,0.00339649,0.05750541,-0.06463924,0.06198706,-0.05115905,0.04365418,0.00075675,-0.01331902,0.03505822,0.02903433,-0.06055763,0.00870417,-0.03756182,-0.05224657,-0.05626034,0.00189537,-0.00656238,-0.03999069,-0.08533505,-0.04511021,-0.09145617,0.02956513,-0.07532935,-0.04333574,0.00327359,-0.03282672,0.04020404,0.01540828,-0.02305903,-0.02834434,0.00160588,-0.00846415,-0.03588742,-0.00622622,0.03383612,0.06386989,0.08511126,-0.02934197,0.03804417,0.05242881,-0.09454066,-0.08137623,0.05947122,-0.03130607,-0.08978664,-0.01552248,0.0561672,0.01707852,-0.02813469,0.04082265,0.04966876,-0.08358146,-0.0350777,0.05764927,-0.02539865,-0.01615022,-0.01629561,-0.01899068,0.00716616,-0.04228784,-0.00485578,0.03763995,0.04139623,0.03862941,-0.06351167,0.00487709,0.00341848,0.00206631,0.00915902,-0.0173399,0.04225965,-0.00884534,0.00289345,-0.03314405,-0.02879849,0.02846418,-0.05760281,0.01569365,-0.01694111,0.05547981,-0.0582989,-0.07556984,0.0017344,-0.00783346,-0.00880506,-0.04504954,0.03047636,0.08898918,0.06986671,-0.05882803,0.0325149,0.0432772,0.00223509,-0.00602247,-0.01081939,0.03976344,0.08924961,-0.03548411,0.03613424,0.00112869,-0.0020775,-0.10380549,-0.17868976,-0.01747446,0.05185469,-0.00976961,0.08166433,0.00310033,0.05530891,0.06568243,0.05505648,0.0461713,0.05052464,0.05990196,-0.05295239,0.05726331,0.03072591,0.10446107,0.04573468,0.04145616,0.02376868,-0.01163971,0.01151994,0.06795299,-0.00950406,-0.07587882,0.0063724,0.0355559,0.13306859,-0.00278794,0.01924524,0.04071746,0.03320421,0.02980397,0.00059897,-0.09840401,0.10652041,0.02606883,-0.02714024,-0.01171685,-0.04281051,-0.0336129,-0.02054964,0.06245962,-0.04628139,-0.13044114,-0.04803726,-0.04760269,-0.04773943,-0.04382507,-0.08303387,-0.03540723,-0.02080153,0.00404571,-0.0303202,0.01572626,0.01230695,-0.05087289,0.00933114,0.00841306,0.02928919,0.00429264,-0.02315769,-0.02474942,0.06160866,-0.02971053,-0.0142131,-0.05588862,0.002622,0.04224856,0.04515996,0.02387292,-0.03458235,0.09293059,0.00626559,0.02877075,0.0819625,-0.03499444,0.00405179,-0.05156624,0.01352984,0.00130125,-0.01711423,-0.04432107,0.04236683,-0.00471793,-0.00630751,0.01171406,0.04351036,-0.00248543,0.02352834,-0.04045828,0.05247744,0.02591506,-0.02930298,-0.00157561,0.045484,0.04727968,-0.26870784,0.03522081,-0.06604572,0.03957815,-0.02926379,-0.00266252,-0.03830332,-0.00599629,-0.05513153,0.03025962,0.03629372,0.03618173,0.03707545,-0.02327866,-0.01345476,-0.01679548,0.00688868,-0.03540935,0.07359806,-0.01437227,-0.01446121,0.02133831,0.21249239,0.00869961,0.08188704,0.01482738,-0.00337767,0.01144201,-0.01313358,-0.01272242,-0.02879042,0.04159967,0.12988816,-0.00892557,-0.01885703,0.07913799,-0.06619497,-0.03173065,-0.02260977,0.06576985,-0.01212866,0.07207256,-0.04768277,-0.01338375,0.11486538,-0.07924467,-0.01505845,-0.08175891,0.03664929,0.07324389,-0.0044538,-0.04105375,0.00307592,-0.00301013,0.04762159,0.04425878,-0.02782545,-0.03782206,-0.00693691,-0.04494153,0.03404141,0.03050577,0.04278426,0.05293312,0.04155054],"last_embed":{"hash":"04ed3132587395b62718ec36dedf2a5df9136ab8b39dde798fe9082ad209b276","tokens":451}}},"last_read":{"hash":"04ed3132587395b62718ec36dedf2a5df9136ab8b39dde798fe9082ad209b276","at":1743662844429},"class_name":"SmartSource","outlinks":[{"title":"a03077b24546810e9aabd32f2afe2608_MD5.webp","target":"a03077b24546810e9aabd32f2afe2608_MD5.webp","line":34}],"blocks":{"#":[1,4],"##0.1. System Design:":[5,35],"##0.1. System Design:#0.1.1. Bạn trình bày giúp mình về định lý CAP?":[7,14],"##0.1. System Design:#0.1.1. Bạn trình bày giúp mình về định lý CAP?#{1}":[9,14],"##0.1. System Design:#0.1.2. Một bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khiến cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?":[15,21],"##0.1. System Design:#0.1.2. Một bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khiến cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?#{1}":[17,21],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?":[22,35],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{1}":[24,26],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{2}":[27,27],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{3}":[28,28],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{4}":[29,29],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{5}":[30,33],"##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{6}":[34,35],"##0.2. Microservice:":[36,71],"##0.2. Microservice:#0.2.1. Bạn hãy so sánh ưu nhược điểm của monolithic và microservices?":[38,43],"##0.2. Microservice:#0.2.1. Bạn hãy so sánh ưu nhược điểm của monolithic và microservices?#{1}":[40,43],"##0.2. Microservice:#0.2.2. Tại sao bạn lại chia service như này?":[44,49],"##0.2. Microservice:#0.2.2. Tại sao bạn lại chia service như này?#{1}":[46,49],"##0.2. Microservice:#0.2.3. Order được xử lý lần lượt qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?":[50,55],"##0.2. Microservice:#0.2.3. Order được xử lý lần lượt qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?#{1}":[52,55],"##0.2. Microservice:#0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?":[56,61],"##0.2. Microservice:#0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?#{1}":[58,61],"##0.2. Microservice:#0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order được điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. Làm sao để hệ thống trả lại response cho frontend?":[62,67],"##0.2. Microservice:#0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order được điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. Làm sao để hệ thống trả lại response cho frontend?#{1}":[64,67],"##0.2. Microservice:#0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?":[68,71],"##0.2. Microservice:#0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?#{1}":[70,71],"##0.3. Khác:":[72,88],"##0.3. Khác:#0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?":[74,88],"##0.3. Khác:#0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?#{1}":[76,88],"#---frontmatter---":[81,null]},"last_import":{"mtime":1742413246085,"size":6207,"at":1743662830190,"hash":"04ed3132587395b62718ec36dedf2a5df9136ab8b39dde798fe9082ad209b276"}},"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0614049,0.08372741,0.02728435,-0.02169811,0.0138911,-0.04454142,0.02268637,0.02730959,0.019718,0.00756491,-0.04865269,-0.1356466,0.04131702,0.02833732,0.06166525,-0.00514743,0.00183833,0.01480575,0.01396544,-0.03865992,0.09233849,-0.02406207,-0.0196016,-0.03000886,-0.01504723,-0.02146908,-0.0327657,0.00412155,-0.010915,-0.1636434,-0.00105281,0.00791901,-0.00259792,0.02353146,0.0378802,-0.01832332,0.0184446,0.00013276,-0.01789988,0.00057547,0.0407645,0.00713858,0.02478128,-0.10015979,0.04092907,-0.04328029,-0.0354729,-0.02032596,-0.00023735,-0.02766142,0.02065199,-0.00433627,0.01893122,0.04103072,-0.01607377,0.00863791,0.0531881,0.06775732,-0.00653842,-0.02974656,0.09095211,0.04202518,-0.25876963,0.05979782,-0.00678815,0.00430794,0.07602488,-0.0210424,0.00821955,0.07392557,-0.03227011,-0.00456861,-0.03241068,0.06837277,0.01453368,-0.01706155,0.03928384,-0.04735171,-0.03637353,0.02672123,0.0255128,0.0533262,0.00090209,0.01688582,-0.10404754,0.00350045,0.01320661,-0.02572877,0.0804088,-0.00025026,0.03313674,-0.00194146,0.01115456,-0.00783396,-0.02370496,-0.03301449,0.07525097,0.01995841,-0.11278389,0.13148881,-0.00447398,-0.03362114,0.06145881,-0.04704856,0.06849206,0.01497834,-0.01903182,-0.06407878,0.0061605,0.03057108,-0.06910343,-0.01217835,0.03234753,-0.02446873,-0.04863056,0.02806026,-0.02002475,0.02260192,0.00655066,-0.00867173,-0.01807524,-0.01090615,0.05492472,-0.06013615,0.08089502,-0.0660573,0.04599985,0.00923841,-0.03549932,0.02514088,0.03798877,-0.06828985,-0.00837105,-0.04198742,-0.03489432,-0.05610977,-0.00363501,0.02574301,-0.05212554,-0.08457623,-0.03669032,-0.08721521,0.02705085,-0.06648901,-0.02540522,0.00445854,-0.01205835,0.05512051,-0.00090584,-0.03690472,-0.02885546,0.0007715,-0.02138617,-0.05204215,-0.00980997,0.03836796,0.05702778,0.09809498,-0.02759568,0.03066577,0.05913088,-0.09144375,-0.09324675,0.07600287,-0.03258027,-0.09983084,0.00096584,0.04889596,0.02042907,-0.00924782,0.02854602,0.04534288,-0.07560675,-0.02361115,0.07370199,-0.01928799,0.00082928,-0.01962049,-0.03206481,0.00545002,-0.04659485,0.01525113,0.04583883,0.0371294,0.05717208,-0.03902565,0.00836755,0.02388939,0.0021795,-0.00220167,-0.03072773,0.0400896,-0.01996403,-0.01210704,-0.01457253,-0.00936804,0.0247884,-0.04670325,-0.001321,-0.03184888,0.07570226,-0.04567297,-0.080755,0.00216999,-0.02728655,-0.02271689,-0.05772588,0.0219416,0.08889565,0.05157338,-0.03694568,0.01732209,0.07894365,-0.0151829,0.00588067,-0.00455918,0.0227323,0.08105884,-0.02896628,0.01530713,0.01529715,0.01501477,-0.10146963,-0.19324031,-0.02713272,0.02993754,-0.01334639,0.05967359,0.0140446,0.05309585,0.05434462,0.04734467,0.03297266,0.06122184,0.0404219,-0.05120119,0.07214794,0.02934911,0.09134106,0.03514438,0.02889886,0.00077575,-0.01583422,0.00917016,0.07707569,0.00568066,-0.0661266,0.00938737,0.04728453,0.13338958,0.00419405,0.02828959,0.02385353,0.01495377,0.0271519,0.0170231,-0.10489587,0.10146645,0.00996916,-0.01707004,0.01284783,-0.03777724,-0.03469227,-0.01458731,0.0625416,-0.03301117,-0.11182927,-0.02444144,-0.06561264,-0.05318726,-0.06452466,-0.06482874,-0.02731646,-0.0315507,0.00678851,-0.0221346,0.03383273,-0.00700631,-0.04505962,-0.00601763,0.00717534,0.03165924,-0.00383389,-0.01924547,-0.05480078,0.04312596,-0.0522667,-0.00547491,-0.06489184,-0.02669394,0.03150725,0.03727967,0.00985404,-0.02479172,0.08631632,0.00764821,0.00210623,0.05760475,-0.02446834,0.01174759,-0.05137575,0.011425,-0.00746454,-0.00872906,-0.02796756,0.01259248,0.01314392,-0.00994406,-0.00179633,0.05414651,0.00621844,0.0141171,-0.03881452,0.04659262,0.01693376,-0.05153275,0.01815123,0.04131521,0.04442382,-0.27730158,0.03124333,-0.05142193,0.03026517,-0.03217008,-0.01630067,-0.0405484,-0.00286967,-0.04789463,0.0510518,0.04158654,0.03202264,0.03474019,-0.00066157,0.00838908,-0.00382236,0.02542241,-0.04154593,0.06732439,-0.03031074,-0.01944874,0.01850344,0.20699607,0.01752649,0.09808745,0.02584976,0.00407145,0.00600713,0.01256461,-0.02952399,-0.01682351,0.01306755,0.13582614,-0.02142008,-0.00397288,0.06897136,-0.04011384,-0.03225622,-0.01748749,0.04961707,-0.01775419,0.07439958,-0.05984931,0.01011674,0.12168521,-0.059625,-0.01548651,-0.10407688,0.0531081,0.05433791,-0.01587436,-0.04450011,-0.0060898,0.00967599,0.03580543,0.04225567,-0.04539033,-0.04558292,-0.02619558,-0.05625254,0.02327998,0.02601896,0.03321815,0.06132277,0.03992938],"last_embed":{"hash":"9ea8a093069e1c846f1893588ac54c53ffbcf12a1e5ea6411de1cba29b931e13","tokens":171}}},"text":null,"length":0,"last_read":{"hash":"9ea8a093069e1c846f1893588ac54c53ffbcf12a1e5ea6411de1cba29b931e13","at":1743662844005},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md#","lines":[1,4],"size":385,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05062512,0.04789967,0.01019544,-0.00957115,-0.0173407,-0.026717,0.03250874,0.03173552,0.02636426,0.01342068,-0.0385876,-0.16863054,0.04070742,0.05609646,0.05709409,0.00654221,0.02833199,-0.00393111,0.0015594,-0.04827329,0.0693791,-0.02498969,-0.02293597,-0.03725537,0.02023685,-0.04275711,0.02917814,0.01344643,-0.03193581,-0.20586081,0.02566985,0.02906287,-0.01353541,0.01365762,0.01390883,0.00096485,0.01489477,0.01628278,0.0133026,0.00164704,0.04102333,0.04927344,0.00225216,-0.08254761,0.02035299,-0.0007749,-0.0285546,-0.00858021,0.03625462,-0.0073904,-0.01387402,-0.00328193,0.03341131,-0.00246508,-0.02582307,-0.00458281,0.03638362,0.05422096,0.03343791,-0.06342217,0.06103886,0.02978751,-0.25530255,0.07005439,0.02208872,0.01609134,0.0327707,-0.00002611,0.01565039,0.08257224,-0.0512821,-0.00305605,0.00742356,0.02722146,-0.00783216,-0.02046009,0.03968938,-0.01895054,-0.05594702,0.00193992,0.00979877,0.03944847,-0.03118807,-0.00013464,-0.08986015,-0.02057518,-0.00220359,-0.00925964,0.03294404,-0.02374833,0.01841499,-0.00370492,0.03897206,-0.002771,-0.00729898,-0.01677861,0.05981869,0.03279261,-0.06762465,0.10513592,-0.01470533,0.00022427,0.03166767,-0.06203489,0.0516856,0.02942767,-0.01991124,-0.06375247,0.02036329,0.0354298,-0.03335029,-0.0117279,0.04195575,-0.07891198,-0.06472242,0.03480721,0.00678507,0.01275444,0.01085933,-0.02247197,-0.07484921,0.00647704,0.05303993,-0.07597819,0.05643917,-0.04394875,0.04107294,-0.00521182,0.00761307,0.05822381,0.01683518,-0.05755566,0.02165257,-0.04571526,-0.08238138,-0.05209192,0.00307598,-0.021741,-0.01635367,-0.0669535,-0.04790193,-0.09196948,-0.00710762,-0.08039779,-0.04033794,0.00344153,-0.04944799,0.02011844,0.0138292,-0.01454238,-0.01728421,0.00910039,-0.00216245,-0.0260814,0.00033862,0.03482651,0.06613489,0.08402076,-0.02770265,0.04343054,0.06500286,-0.08678574,-0.06544388,0.08697705,-0.02564852,-0.08348584,-0.0346886,0.04490352,0.00980504,-0.0378403,0.04111248,0.06176595,-0.07117628,-0.02806474,0.0536985,-0.03016566,-0.02536743,-0.00087056,-0.0292226,0.01827073,-0.02728052,-0.02785867,0.01718504,0.0367823,0.02138478,-0.08996816,-0.01062852,-0.00466,0.02392262,0.02836094,-0.02341387,0.02931481,0.00750969,0.02441305,-0.04895585,-0.04453411,0.02840845,-0.0739705,0.02580988,-0.00735805,0.04337253,-0.05800038,-0.08426853,0.02073319,0.00656448,0.0096488,-0.03430853,0.03407516,0.07534052,0.06892525,-0.07930812,0.03082798,-0.00001676,0.00313532,-0.01276851,-0.03397185,0.02749272,0.08336644,-0.02960616,0.01759433,0.00976531,-0.01277373,-0.08421692,-0.18784943,0.0097532,0.04778962,-0.01909821,0.09477716,-0.00984706,0.04899736,0.07651278,0.06382596,0.06310156,0.04654288,0.06083634,-0.04993433,0.04294796,0.04294208,0.10087795,0.04298234,0.02507027,0.04355398,0.00040881,0.01575379,0.05901168,-0.02623536,-0.04183604,0.01438763,0.01469309,0.14937729,0.00255085,0.00072533,0.03334967,0.0444505,0.02072215,0.00381813,-0.08970971,0.09302683,0.01259901,-0.04363453,-0.02746107,-0.03433996,-0.02537177,-0.0231537,0.03849262,-0.05849397,-0.14329468,-0.06683175,-0.02419846,-0.05266365,-0.02679219,-0.09855789,-0.02133668,-0.00289326,0.01092971,-0.03668543,0.02761264,0.01143565,-0.05828342,0.01060175,0.00774786,0.0143654,0.02201714,-0.02003681,0.00405736,0.06941209,-0.01468797,-0.02247586,-0.02772751,0.01268911,0.02967273,0.04861213,0.03642307,-0.04867961,0.08421423,0.01173755,0.04085964,0.10206152,-0.04509286,-0.02003612,-0.04036207,0.00699523,0.01200733,0.00265123,-0.05412544,0.07910214,-0.00679711,-0.00231443,0.02057115,0.0341655,-0.01224626,0.02573954,-0.04335161,0.0506705,0.02438078,-0.03770591,-0.01189462,0.05490398,0.0348781,-0.27488932,0.04573961,-0.06886312,0.05842873,-0.01709871,0.00780796,-0.01362599,-0.04028058,-0.07071579,0.0109099,0.04968552,0.04111592,0.03551383,-0.03886898,-0.03645288,-0.02518493,-0.00841787,-0.03234484,0.06673077,0.01223997,0.00071187,0.0155295,0.19982696,0.00763234,0.06811403,0.01779553,0.00089688,0.00471377,-0.02059179,-0.00327436,-0.02427078,0.05504264,0.12175643,-0.00796434,-0.03609027,0.0939626,-0.07811976,-0.00180044,-0.01213175,0.05768151,-0.01585842,0.05366881,-0.031341,-0.02709272,0.11789829,-0.06788217,-0.00880951,-0.06178825,0.02629436,0.06432854,0.00381102,-0.04368603,0.01042087,-0.01281111,0.04867073,0.03743655,-0.02305168,-0.04225145,-0.00099742,-0.02715803,0.03907913,0.03099968,0.05373358,0.03439451,0.0403473],"last_embed":{"hash":"bb6fd2d3aaccb9f7b10b5cc74d5709812e0a7a9695643be33bb85db0635d0cdc","tokens":467}}},"text":null,"length":0,"last_read":{"hash":"bb6fd2d3aaccb9f7b10b5cc74d5709812e0a7a9695643be33bb85db0635d0cdc","at":1743662844022},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:","lines":[5,35],"size":1524,"outlinks":[{"title":"a03077b24546810e9aabd32f2afe2608_MD5.webp","target":"a03077b24546810e9aabd32f2afe2608_MD5.webp","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.1. Bạn trình bày giúp mình về định lý CAP?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04653991,0.04837921,0.00203355,-0.03284514,-0.02656817,-0.03665448,0.04018458,0.05423432,-0.00050844,0.02906511,-0.01231964,-0.15146315,0.03603976,0.0348819,0.05648804,0.00305088,0.02782617,-0.00986279,0.00806318,-0.03293606,0.06884132,-0.01667796,-0.03182844,-0.03338935,0.03629781,-0.05307695,0.03972867,0.01307463,-0.00838211,-0.2276282,0.02447704,0.03915677,0.00406432,0.01639229,-0.01626828,0.01302962,0.01959267,-0.00870738,0.01131272,-0.00396282,0.01176704,0.03726764,-0.02019404,-0.07497592,-0.00270337,-0.00489439,-0.01427015,-0.00241615,0.03610823,-0.02042254,0.00134829,-0.0094116,0.05784154,0.01177041,-0.04109967,0.01477592,0.0525435,0.04252302,0.01313004,-0.02349675,0.05837872,0.01934193,-0.24399383,0.07512511,0.01737901,0.03314051,0.0329245,-0.00691815,-0.00296571,0.07351077,-0.05176116,-0.00040028,0.00666507,-0.00176777,-0.01853018,0.00784365,0.02728957,-0.03712127,-0.04010973,0.01754098,0.01890065,0.00171461,-0.00868873,0.01434895,-0.10632747,-0.02118099,-0.01083418,0.00070611,0.04849127,-0.0261313,-0.00659999,0.00892692,0.04220353,-0.02568663,-0.00792492,-0.00864711,0.06565637,0.04433509,-0.0516175,0.12795575,-0.00479733,-0.01367577,0.02950461,-0.08431906,0.05749479,-0.00274196,-0.02468227,-0.07314768,0.02521844,0.04074886,-0.0366006,-0.01211566,0.03130881,-0.08531908,-0.06643755,0.04302841,0.01448345,0.02451321,0.01147169,-0.05178955,-0.07050929,0.0094168,0.07289715,-0.06874971,0.06765075,-0.00687375,0.0423793,0.01028826,0.02989561,0.06688556,-0.00012838,-0.05514132,0.03384971,-0.04778785,-0.07206687,-0.03890453,0.00463304,-0.02351158,-0.0087186,-0.06938006,-0.0551738,-0.05630355,-0.00590087,-0.08199471,-0.04179511,0.04425782,-0.0359973,0.01323855,0.00904734,-0.00186582,-0.00739482,0.02275423,-0.0016076,-0.01642701,-0.00162243,0.02859641,0.07107171,0.06445699,-0.02720013,0.04524226,0.04193351,-0.08521634,-0.06272603,0.09617658,-0.02189069,-0.06387132,-0.03039375,0.05012491,0.03516637,-0.02945219,0.02453453,0.02752148,-0.04495201,-0.00869218,0.06020897,-0.03839635,-0.03138888,0.00319923,-0.02802671,-0.00407551,-0.00757278,-0.00728398,0.00228037,0.03979093,0.01798148,-0.07855684,-0.01488731,-0.01674455,0.03100844,0.03268121,-0.00546231,0.02386041,0.01599318,0.04813954,-0.02528382,-0.01378231,0.02609169,-0.0723997,0.05694003,-0.01709125,0.0422562,-0.03535332,-0.0732367,0.03022378,0.01981345,0.02164594,-0.02129434,0.03687599,0.07532601,0.05362936,-0.07815901,0.02758081,-0.0136807,0.00008937,-0.02531059,-0.03134304,0.01329496,0.06168525,-0.01870833,0.0200496,0.02295143,0.00633085,-0.10541952,-0.19769046,0.02893211,0.03168197,-0.04641916,0.08712678,0.00064501,0.07227552,0.06852398,0.04082819,0.04436196,0.0570013,0.07531898,-0.04645458,0.02012461,0.03504438,0.10664626,0.03788769,0.02302627,0.03654291,-0.00472195,0.00351377,0.06702621,-0.01959816,-0.05112866,0.03486887,0.00813193,0.15218823,-0.02420169,0.01403089,0.03190341,0.02659839,0.01520523,-0.01280165,-0.11746301,0.06037221,-0.00091958,-0.03366477,-0.03898082,-0.03956481,-0.03016645,-0.03056476,0.03045599,-0.05324562,-0.14202601,-0.08309056,-0.00488506,-0.04657385,-0.06331209,-0.10449833,-0.05024062,-0.01404252,-0.00323537,-0.03417142,0.01685661,0.02409991,-0.04716059,-0.00952401,-0.00025673,0.01680759,0.05113629,-0.01940272,0.01573922,0.05789212,-0.02180271,0.00038126,-0.03327844,0.02651074,0.01297177,0.06832705,0.01807254,-0.04935422,0.09610187,0.0060095,0.03333835,0.11513177,-0.0240198,-0.01952283,-0.01985624,0.00047884,0.02106027,-0.01365001,-0.05447197,0.08799373,-0.0194504,0.01349271,0.02468073,0.00406431,-0.01185775,0.01640019,-0.06370415,0.04707028,0.04141039,-0.0565868,-0.0365542,0.07339571,0.03662215,-0.26759121,0.04841533,-0.04918324,0.06259044,-0.03977147,0.00226549,-0.01520383,-0.03912525,-0.06838086,-0.00114805,0.02647146,0.0320886,0.03043233,0.00688819,-0.03232017,-0.01751796,0.00787405,-0.04532158,0.07609476,-0.02155712,-0.00307311,0.00482002,0.21449769,0.01472328,0.0406149,0.00900455,-0.01652936,0.01392836,-0.01442138,0.01216952,-0.02945161,0.03439344,0.12170433,0.01464715,-0.05907506,0.06615081,-0.09268466,0.00026234,-0.01089335,0.07904927,-0.0065028,0.0335148,-0.01781067,-0.05035091,0.1180065,-0.05781616,-0.00740391,-0.04062052,0.01343223,0.05111815,0.00112204,-0.04561968,0.01866453,-0.01655896,0.03571583,0.03698244,-0.01709994,-0.05642675,0.00052284,-0.01430188,0.0283428,0.04911919,0.04390538,0.0329377,0.03122489],"last_embed":{"hash":"e80c0ad982aaeaff2bf8efdbf4e0a4083cba2720dd23340efd152760f59ea9a9","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"e80c0ad982aaeaff2bf8efdbf4e0a4083cba2720dd23340efd152760f59ea9a9","at":1743662844072},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.1. Bạn trình bày giúp mình về định lý CAP?","lines":[7,14],"size":253,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.1. Bạn trình bày giúp mình về định lý CAP?#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04117389,0.04789253,0.00130751,-0.030674,-0.02620981,-0.03676439,0.04103171,0.05005261,-0.00352731,0.02896641,-0.00849934,-0.1511649,0.03514846,0.0356246,0.05897861,0.00478206,0.02693499,-0.0080056,0.01143952,-0.03171329,0.06824246,-0.01397607,-0.03310931,-0.0281937,0.0355208,-0.05444213,0.03932134,0.01442236,-0.00755153,-0.22836231,0.02484929,0.03604083,0.00081767,0.01340605,-0.01809604,0.0151137,0.020767,-0.01073025,0.01365067,-0.0022502,0.01028969,0.03890335,-0.02181812,-0.07207927,-0.01080035,-0.00597113,-0.01581089,-0.00414321,0.03954696,-0.01815137,-0.00008357,-0.00954989,0.06282633,0.01156797,-0.04267184,0.01615343,0.05109893,0.04412995,0.01011028,-0.02238777,0.05870656,0.01947639,-0.24433881,0.07266607,0.015186,0.03326004,0.03077155,-0.00781377,-0.00530729,0.07956099,-0.05254077,0.00058032,0.00940159,-0.00552049,-0.01445036,0.00842847,0.02633023,-0.03470023,-0.03885909,0.01939134,0.01730074,-0.0017453,-0.01281787,0.01365899,-0.10737529,-0.02114823,-0.01057896,-0.00322221,0.0476797,-0.02789938,-0.00656607,0.00874211,0.04345788,-0.02665264,-0.0102716,-0.01078602,0.0653258,0.04841119,-0.04823084,0.12739359,-0.00518627,-0.01525703,0.02627734,-0.08275379,0.05808267,-0.00290911,-0.02434593,-0.07559056,0.0315304,0.04312468,-0.03497197,-0.01135953,0.02614378,-0.085459,-0.07045553,0.04548515,0.01229265,0.02302543,0.00991078,-0.05121562,-0.07360251,0.00777892,0.07510258,-0.06393408,0.06641896,-0.00299082,0.04198512,0.01204271,0.03310759,0.06152557,0.00082051,-0.05843862,0.0303376,-0.04873531,-0.07124261,-0.03765198,0.00692563,-0.02495767,-0.01070774,-0.06837274,-0.05620528,-0.05918143,-0.00494352,-0.08015761,-0.04204327,0.04428338,-0.03804544,0.01447159,0.00564113,-0.00805249,-0.00988928,0.02268603,-0.0002953,-0.01835082,0.00015654,0.02480829,0.06826563,0.06512351,-0.02868931,0.04673272,0.03744085,-0.08460346,-0.06010214,0.09389206,-0.01818778,-0.05737567,-0.02768114,0.05189542,0.03727243,-0.0293629,0.02477599,0.02655138,-0.04546562,-0.01204008,0.06233487,-0.03409765,-0.03730854,0.00169929,-0.02573198,-0.0006733,-0.00982897,-0.0083099,-0.00098827,0.03870639,0.02122707,-0.07731824,-0.01627786,-0.01986382,0.02688426,0.03550753,-0.00304099,0.02665943,0.01603552,0.04832692,-0.02061769,-0.01710031,0.02392221,-0.07437462,0.06104014,-0.01716853,0.04230011,-0.03328175,-0.06944314,0.03042264,0.0226007,0.02292353,-0.01719092,0.03463462,0.0706403,0.05243244,-0.07751247,0.02901775,-0.01569464,-0.00152443,-0.0259434,-0.02800223,0.01261012,0.05955295,-0.02366518,0.02328416,0.02419242,0.00440465,-0.10426786,-0.19467637,0.02788427,0.0304293,-0.04942973,0.09013861,-0.00082169,0.07674042,0.06850246,0.03779895,0.03819614,0.0581268,0.07419562,-0.04843303,0.02277101,0.03469027,0.10701208,0.03791374,0.02613293,0.0431883,-0.00706527,0.00113251,0.06816791,-0.02621369,-0.04510872,0.04030202,0.01094684,0.14902002,-0.03011234,0.01448116,0.02745109,0.02204837,0.01464704,-0.014565,-0.11628025,0.06072133,0.00147731,-0.03685576,-0.04205918,-0.03895564,-0.02626585,-0.02799554,0.02848019,-0.05866127,-0.13721184,-0.08358781,-0.00405543,-0.04581437,-0.05922755,-0.10404134,-0.05185937,-0.01320281,-0.00739194,-0.03692582,0.01391856,0.02687783,-0.04689834,-0.008494,0.00131225,0.0154291,0.04731804,-0.02062256,0.01895961,0.05875155,-0.01712503,0.00343569,-0.03425664,0.02648413,0.01101108,0.06620318,0.01768541,-0.05063454,0.09455378,0.00769569,0.03494025,0.11305796,-0.02794085,-0.01884037,-0.0196554,0.00124745,0.02608635,-0.01417864,-0.05709447,0.09055873,-0.01609565,0.00998167,0.0242641,0.00590783,-0.01523507,0.0171047,-0.06336737,0.04396447,0.04222409,-0.05267846,-0.03760192,0.07108333,0.03557863,-0.2692959,0.04580517,-0.05253338,0.06318081,-0.03823105,0.00341796,-0.01630557,-0.03943049,-0.0649445,-0.00346045,0.02733346,0.03854178,0.02804059,0.0062621,-0.03214446,-0.02148854,0.0044148,-0.04802728,0.07744785,-0.02111395,-0.00289501,0.01067906,0.21630408,0.01826955,0.0379846,0.00915912,-0.01566127,0.01627121,-0.01753848,0.01498502,-0.02586726,0.0384532,0.12457318,0.01227919,-0.06366336,0.07245407,-0.09281703,0.00100529,-0.00910741,0.08054703,-0.00274974,0.03429756,-0.01891526,-0.05142799,0.11801638,-0.05780205,-0.00362057,-0.04077555,0.01390816,0.050567,-0.00026802,-0.04255892,0.0201194,-0.01691083,0.03590984,0.03696769,-0.01795721,-0.05670759,-0.00036101,-0.01775263,0.03038791,0.05038063,0.03964011,0.02901699,0.0327709],"last_embed":{"hash":"aef2d834dec6b76660beea6a9177b7b6bdd34b444c7a544b790d29ea56daabd2","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"aef2d834dec6b76660beea6a9177b7b6bdd34b444c7a544b790d29ea56daabd2","at":1743662844092},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.1. Bạn trình bày giúp mình về định lý CAP?#{1}","lines":[9,14],"size":201,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.2. Một bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khiến cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06235897,0.06124835,0.01120435,-0.01602411,0.01697474,-0.03686878,0.03305059,0.04518409,0.06109552,0.03043122,-0.02604935,-0.14340319,0.02321615,0.0462833,0.07343599,-0.00501476,0.04211168,0.00929146,0.02745709,-0.03207669,0.07738236,-0.0302635,-0.06908648,-0.04514287,-0.01984659,-0.00766209,0.0159143,-0.02049683,-0.00950989,-0.18971886,0.02190567,0.00016271,-0.00206423,0.02366639,0.00518835,-0.00901798,0.03728564,0.05551954,-0.00922528,0.02306921,0.04387901,0.02203459,0.01589081,-0.10308519,0.01104061,-0.00521061,-0.04493545,-0.0199122,0.0042617,-0.01464182,0.00335564,0.0103599,0.00475144,0.00046466,0.00219221,-0.00793826,0.02198935,0.03363084,0.00947276,-0.05642952,0.07811842,0.01734183,-0.28260851,0.04241461,0.02264652,-0.00492736,0.03822471,0.01872977,0.04082799,0.08159787,-0.04569257,-0.00842896,0.00710237,0.07838961,-0.00470942,-0.02580436,0.05102008,-0.05092189,-0.04725875,-0.00909505,0.00666433,0.04842571,-0.02683658,-0.00638115,-0.07420336,-0.01880015,-0.00637811,-0.0244833,0.03600981,-0.04897188,-0.01213249,-0.0092848,0.00045986,0.00008269,-0.01474816,-0.01218301,0.03807409,-0.0019468,-0.07124072,0.12973365,-0.02076056,-0.00899437,0.03181032,-0.03414578,0.06898274,0.05344444,-0.00715578,-0.07431014,0.01439492,0.02133234,-0.0343988,-0.00578221,0.04534103,-0.01144957,-0.02559783,0.01994562,-0.00957937,0.02683367,0.04429437,-0.01011891,-0.0422393,-0.00926925,0.0371405,-0.09089791,0.05044857,-0.05955415,0.04701849,0.00116069,-0.02217735,0.04675701,0.02708866,-0.03943457,-0.00458512,-0.04108139,-0.06115068,-0.0464997,0.00018779,-0.00387262,-0.01830184,-0.05682326,-0.06594017,-0.06801926,-0.00009298,-0.10508987,-0.03308493,-0.03058163,-0.04310729,0.0485321,0.00243503,-0.02787519,-0.02899565,0.03759495,-0.01925904,-0.03721217,-0.03541959,0.05503541,0.0352738,0.10788281,-0.00847622,0.05511925,0.06870478,-0.0811781,-0.067068,0.11331727,-0.02426638,-0.07681007,-0.03587062,0.03539363,0.03026064,-0.02407948,0.00569577,0.07133196,-0.08989601,0.00039419,0.07178197,-0.03572543,-0.01452036,0.00390582,-0.01398533,0.02750501,-0.05758617,-0.0386053,0.04975616,0.02074355,0.04058817,-0.08376432,0.00772362,-0.01091644,0.01066513,0.0286468,-0.03667889,0.05595007,0.00682849,0.01724368,-0.0362973,-0.05977249,0.0041221,-0.06217683,-0.00918289,-0.01551696,0.05595717,-0.02978502,-0.09683377,0.00573346,-0.01297804,-0.00057721,-0.0618796,0.01721016,0.03867025,0.04192609,-0.05282743,0.00768589,0.07045493,-0.01020188,-0.01532309,-0.0474568,0.00753025,0.0772968,-0.01326222,0.00762634,0.01718439,-0.00924536,-0.10072204,-0.19342113,-0.00201311,0.0637648,0.01064162,0.08248655,-0.00500803,0.05149913,0.00713052,0.06062852,0.03834553,0.02858731,0.04485589,-0.05350477,0.0672838,0.04162451,0.08484966,0.05505776,0.00475324,0.01297039,-0.02916192,0.03490795,0.06427953,-0.02274796,-0.03658037,0.00691331,0.04184719,0.15426773,0.01173984,-0.02216558,0.05199301,0.04285756,0.01198822,-0.01623838,-0.0549756,0.10886359,0.01441614,-0.0316569,-0.01448867,-0.03412824,-0.00840903,0.00072602,0.03553295,-0.06616634,-0.11140279,-0.01363061,-0.05658063,-0.04307067,-0.01122156,-0.07255753,0.00123742,-0.0199443,0.00658646,-0.00989607,0.04281066,-0.01946139,-0.05651962,0.01101169,0.05621753,0.01251837,-0.00177408,-0.03906628,-0.02922774,0.03208847,-0.04916169,-0.03616051,-0.02275495,-0.0156237,0.04168867,0.0408163,0.04874119,-0.03330449,0.0844674,0.03321202,0.0266767,0.07361569,-0.05242916,-0.00963922,-0.06825685,0.02171193,0.01468145,-0.00672926,-0.0207473,0.04888268,-0.00378367,-0.01031725,0.03265223,0.0337019,-0.00013917,0.001787,-0.03433787,0.00936582,0.0076642,-0.04198613,0.0176861,0.05132451,0.06169157,-0.27907634,0.06360409,-0.05943886,0.03926839,-0.03437119,0.0188156,-0.02936489,-0.02436657,-0.08146276,0.02799499,0.08390816,0.02125482,0.03785185,-0.02507439,-0.0028719,-0.01429013,-0.00789497,-0.0333339,0.04686042,0.00798401,0.00722134,0.04057911,0.20069705,0.03031884,0.06905104,0.01942177,0.02297262,-0.01231655,-0.01925722,0.01392953,-0.0025689,0.0553082,0.12066846,-0.00961454,0.0109055,0.10528947,-0.05723338,-0.01963883,-0.02958513,0.04627254,-0.0415357,0.06571545,-0.01892838,0.00351475,0.11412252,-0.05637054,-0.01334325,-0.07755477,0.03380221,0.05723207,0.0022673,-0.04087785,0.00416309,-0.015068,0.02238379,0.03873248,-0.03880287,-0.02948851,0.0117943,-0.03656388,0.05004985,0.02715601,0.04224532,0.00922741,0.01984263],"last_embed":{"hash":"8d5b2688938fd88d8cdbb519621e240e04bcabe0c9ffee2055572432b2343ea6","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"8d5b2688938fd88d8cdbb519621e240e04bcabe0c9ffee2055572432b2343ea6","at":1743662844112},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.2. Một bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khiến cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?","lines":[15,21],"size":312,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07142807,0.07834149,0.06300057,-0.01982976,-0.00004363,0.02023042,0.02367656,0.03502518,0.05968197,0.00528655,-0.03419608,-0.13272254,0.03665674,0.07278418,0.03704349,-0.02619256,0.03273173,0.0204345,0.00774347,-0.02880968,0.07326864,-0.03324991,-0.02176915,-0.03998707,-0.01087428,-0.04835999,0.04140455,-0.00875412,-0.0313886,-0.17769442,0.00841155,0.01890965,-0.00816812,0.02689671,0.02102149,0.00185571,-0.00058563,0.03199335,-0.01117269,0.01500696,0.03631962,0.0163028,0.03501374,-0.05701368,0.0333529,-0.03127481,-0.05207447,-0.00929205,0.03474437,-0.01501065,0.00303794,0.01201037,0.02520514,0.01484035,-0.01850033,-0.05054255,0.03936755,0.03943834,0.03295815,-0.07335084,0.09264348,0.01868196,-0.26480317,0.07171447,0.01486363,0.00184228,0.04723125,-0.00231724,0.00864479,0.08106375,-0.03021368,0.01567833,-0.00973345,0.05462644,0.0118087,-0.05534962,0.02878915,-0.00339137,-0.03994121,-0.00227629,0.02550139,0.07607167,-0.04324655,0.00405498,-0.07959108,-0.02211508,0.01900602,-0.03725234,0.04334867,-0.0185841,0.02110112,-0.01565,0.02098292,0.03002748,-0.01749883,-0.02672305,0.01487037,0.0134276,-0.08779377,0.10698131,-0.01180174,0.02227486,0.04925946,-0.05079441,0.06182059,0.04581761,-0.0052115,-0.04818063,-0.00851186,0.0310275,-0.03545639,-0.02440027,0.01349671,-0.05086696,-0.0533027,-0.02056618,0.00004129,0.0248486,0.03335042,-0.00651433,-0.04705714,-0.01778444,0.03052611,-0.06853375,0.05718755,-0.0629406,0.05058797,-0.00139487,-0.02137341,0.05979367,0.03422286,-0.07735457,0.01125242,-0.06792866,-0.05098765,-0.05897731,0.00574699,0.01215036,-0.0347206,-0.06637671,-0.02814549,-0.0836155,0.00084093,-0.08168717,-0.04207153,0.01262414,-0.04022365,0.03770023,-0.01994631,-0.00997736,0.00683624,0.00637613,-0.02748273,-0.05405408,-0.01619768,0.02954465,0.03945179,0.09472555,-0.01932321,0.03596747,0.07726367,-0.08560581,-0.08192996,0.0812839,-0.01361974,-0.10258273,-0.02540593,0.03935965,0.0117942,-0.0290578,0.01780914,0.08741372,-0.084633,-0.03427246,0.05219568,-0.01799158,-0.01586574,-0.01732,-0.05321513,0.01603902,-0.06285691,-0.03482262,0.03967971,0.04473724,0.05489711,-0.04264665,-0.00221033,0.01589646,0.01490565,0.01737869,-0.04099871,0.04294145,-0.0078198,-0.01610951,-0.05446998,-0.01810779,0.03522678,-0.07685057,-0.01671632,-0.00043037,0.07157115,-0.07816917,-0.06816729,-0.0063412,-0.0294604,-0.0097533,-0.04850123,0.00323202,0.06209864,0.05739454,-0.0571285,0.01142932,0.03631407,-0.02947756,0.01042842,0.01161581,0.0061088,0.07058159,-0.01199282,-0.01413881,0.00918546,-0.00489952,-0.08470736,-0.18350537,0.00807557,0.02212904,-0.02289833,0.09566862,-0.00191191,0.03843695,0.05950917,0.07962398,0.03537318,0.04970866,0.02459728,-0.03831584,0.0769255,0.02080204,0.09267681,0.02027849,-0.00257092,0.014101,0.01207206,0.014542,0.07451283,-0.0179048,-0.04386273,-0.00908047,0.01487278,0.13298663,0.0087954,0.00370191,0.04328641,0.06258002,0.02639894,0.00904286,-0.07633058,0.11137374,0.01318915,-0.06033666,-0.0219641,-0.03069809,-0.01055818,-0.01364089,0.03633028,-0.04673868,-0.12745067,-0.0464888,-0.07293518,-0.0759916,-0.03473551,-0.07997374,0.01568471,0.01029005,0.02019521,-0.01670671,0.06536292,0.00392033,-0.03335446,0.00773372,0.00145246,0.02441051,-0.00189501,-0.01238866,-0.03990438,0.03517149,-0.04464676,-0.03304446,-0.04548163,-0.00785034,0.03064402,0.06337941,0.03180169,-0.07604339,0.1016464,0.02499983,0.02952949,0.07981747,-0.05131165,-0.00349329,-0.04444031,0.00928885,0.00428753,0.0117416,-0.05621229,0.04099057,0.0072293,-0.02442514,0.03848176,0.0774639,-0.01526821,0.02874703,-0.0387807,0.03088977,-0.00767479,-0.03479324,0.0397782,0.05322042,0.03523748,-0.25914532,0.06114837,-0.0368,0.04609035,-0.00071975,0.0100398,-0.0301917,-0.01987611,-0.04990322,0.01451905,0.07668482,0.02612965,0.04447462,-0.01090553,-0.00575891,0.00892212,-0.02302664,-0.02004732,0.05818435,0.02481164,-0.00133744,-0.0020055,0.20039779,-0.01877882,0.07871585,0.01181821,-0.02567868,-0.01053164,-0.01217735,-0.05264442,-0.01575571,0.06103947,0.11705527,-0.02056421,-0.01306873,0.08791783,-0.0591535,-0.0028245,0.00778587,0.05896006,-0.01699994,0.03249215,-0.04942298,0.00752481,0.10903629,-0.05894882,-0.00638176,-0.10467823,0.06127457,0.05706067,0.00707827,-0.04707679,-0.00953322,-0.00523597,0.0732047,0.05239164,-0.02325527,-0.0562353,-0.01015855,-0.04801146,0.03682856,0.04789763,0.04601318,0.03203925,0.03499494],"last_embed":{"hash":"c7161beeb0c90f2e21b22d13f548fb26ef2a2d6e170bffcac32298de1043eb9c","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"c7161beeb0c90f2e21b22d13f548fb26ef2a2d6e170bffcac32298de1043eb9c","at":1743662844133},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?","lines":[22,35],"size":933,"outlinks":[{"title":"a03077b24546810e9aabd32f2afe2608_MD5.webp","target":"a03077b24546810e9aabd32f2afe2608_MD5.webp","line":13}],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0589116,0.07240773,0.06330022,-0.03069686,0.02626365,0.02061458,0.03931638,0.06177042,0.05224548,-0.01565772,-0.03288476,-0.12330668,0.01582721,0.04060774,0.04899703,-0.02420263,0.03407944,0.01967115,-0.00119642,0.00057077,0.08758646,-0.0268176,-0.03177239,-0.04364691,-0.01846245,-0.050439,0.03963882,-0.01503986,-0.02529185,-0.17710175,-0.00342554,0.02149054,0.02810869,0.04043498,0.01675962,0.01472805,0.0104303,0.03809574,-0.05061324,0.02600965,0.02386068,0.0099533,0.03011105,-0.06330635,0.02336503,-0.04399005,-0.05143909,-0.02803531,0.02585593,-0.02986505,0.01842194,-0.01314205,0.06241581,0.01668328,-0.02317224,-0.03379172,0.04399559,0.03630587,-0.00404257,-0.04881307,0.08756042,0.03218069,-0.26344815,0.07910242,0.00994135,0.01118328,0.04549762,-0.03273066,-0.00324864,0.08007923,-0.02901531,0.01468743,-0.02978218,0.05981787,0.01029427,-0.0491802,0.01769028,-0.02141794,-0.04199756,0.00240323,0.03378682,0.085108,-0.03607254,0.02546295,-0.0658372,-0.00154696,0.02967426,-0.04192694,0.06211356,-0.03880409,0.0218173,-0.00444689,0.00646851,-0.00174074,-0.01773847,-0.03691596,0.00615721,0.02400251,-0.09347034,0.09751963,-0.0180033,0.00112416,0.05856857,-0.06603241,0.06262573,0.03523154,-0.02397648,-0.06425142,-0.03245828,0.00996088,-0.04978288,-0.01341148,0.0359915,-0.033823,-0.04801623,0.01031108,-0.00654679,0.03482223,0.03128114,0.00585206,-0.04880311,-0.02369094,0.02395074,-0.07639718,0.06918817,-0.06301048,0.01983532,0.01698383,-0.03675176,0.04386873,0.02483454,-0.06121631,0.01526044,-0.06558993,-0.0279691,-0.05937836,-0.00320191,0.03368114,-0.03333633,-0.0820671,-0.02441227,-0.06928306,0.00769797,-0.06742731,-0.05593733,0.02747692,-0.04389751,0.05662872,-0.02313818,-0.0042931,0.00550371,-0.01128475,-0.0309721,-0.05192753,-0.00701723,0.03499977,0.03709272,0.1148916,-0.02637655,0.03724163,0.07107796,-0.06960902,-0.07543663,0.0962772,-0.0079483,-0.09514676,-0.05408173,0.03308858,0.01789116,-0.00161294,0.01420709,0.06147829,-0.0970885,-0.00648906,0.05823214,-0.00314749,0.01283128,-0.0238344,-0.02142384,0.01095251,-0.05829138,-0.01016641,0.05106617,0.05514176,0.04822221,-0.02887104,-0.00947208,0.01945002,0.01582713,-0.01216715,-0.03888154,0.05875885,0.00648238,-0.01412121,-0.04067527,0.00801754,0.02024073,-0.06365363,0.01520767,-0.02347065,0.06346448,-0.07468441,-0.05830105,-0.00972422,-0.02288841,-0.00953865,-0.0541939,-0.00730307,0.07061967,0.04063316,-0.01498698,0.0113592,0.04377672,-0.03392686,-0.00773718,0.02698306,-0.00042019,0.05969433,-0.02845994,-0.0174116,0.03423025,-0.00080129,-0.10245813,-0.19053331,0.02232455,0.02777918,-0.03846647,0.08909287,-0.00733188,0.05455766,0.05742798,0.07886723,0.04156796,0.03254462,0.03082382,-0.01620387,0.07687067,0.02121867,0.09420969,0.03253532,-0.00188976,0.01467735,-0.00055459,0.0146961,0.07981268,-0.03685254,-0.04846272,-0.0018867,-0.00223036,0.12579501,0.00656369,-0.01259578,0.04297312,0.05739523,0.0507129,-0.0030699,-0.08403902,0.10473207,0.01284552,-0.05088366,-0.02163583,-0.00049542,-0.00322011,-0.02398286,0.03180846,-0.05034844,-0.1201334,-0.04876273,-0.06979632,-0.08893885,-0.05830514,-0.09660962,0.00650211,0.00143239,-0.01685903,-0.00501327,0.05629185,-0.00469574,-0.04348655,0.00064931,-0.01440923,0.01294429,0.00085313,0.00204754,-0.05136734,0.00471576,-0.04904562,-0.02782557,-0.05609099,-0.00855282,0.03237114,0.08425014,0.02028719,-0.07010605,0.08430886,0.02715267,0.01049547,0.0769522,-0.04511262,0.00450583,-0.05031359,0.01364799,0.02456442,-0.00153169,-0.04570125,0.05668782,-0.02147602,-0.00471121,0.03868696,0.04661251,-0.00055619,0.03180386,-0.05113313,0.03139385,-0.01377643,-0.04024246,0.03055213,0.05144287,0.04349981,-0.25882089,0.06060308,-0.02690451,0.05489207,-0.02110517,0.00671109,-0.03280352,-0.01516377,-0.04867536,0.0308853,0.07168365,0.01824037,0.02904349,-0.00316543,0.00601373,-0.00071573,-0.03872417,-0.02206957,0.04859643,0.00700158,0.0041277,-0.00957793,0.22485673,0.01110664,0.05103583,0.01701066,-0.0280319,0.02219695,0.00948667,-0.04673405,-0.0166168,0.04224968,0.11574712,-0.00326022,-0.02262046,0.07576514,-0.05658965,0.00624121,-0.0011538,0.07867809,0.00041295,0.03268646,-0.04232688,0.0094203,0.10539909,-0.05880959,0.00668389,-0.07352013,0.05691092,0.05069639,-0.00906063,-0.07192869,-0.00973987,0.0003455,0.06764079,0.05977001,-0.04082327,-0.05896019,-0.00851819,-0.03375766,0.03772639,0.04262479,0.03531191,0.02774107,0.02954301],"last_embed":{"hash":"c27677988776055c8b1cb1abd203a97fec71cb665850e21b38b129b8f0a25a3c","tokens":261}}},"text":null,"length":0,"last_read":{"hash":"c27677988776055c8b1cb1abd203a97fec71cb665850e21b38b129b8f0a25a3c","at":1743662844179},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.1. System Design:#0.1.3. Thiết kế hệ thống TinyURL?#{6}","lines":[34,35],"size":510,"outlinks":[{"title":"a03077b24546810e9aabd32f2afe2608_MD5.webp","target":"a03077b24546810e9aabd32f2afe2608_MD5.webp","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05336557,0.05403451,0.02989518,-0.02391126,-0.00325914,-0.02273223,0.00857097,0.02914809,0.04762071,0.02030148,-0.06078579,-0.11961948,0.05195134,0.04928107,0.06627639,-0.0071996,0.02700118,-0.00559598,0.01559893,-0.0045184,0.09751452,-0.02689195,-0.01646929,-0.02012712,-0.02192144,-0.03608116,-0.00372365,0.011854,-0.00880179,-0.18121932,0.01901669,0.01576731,-0.02672394,0.02206215,0.02756213,0.01339775,0.02703921,-0.01886568,-0.00318579,0.00411291,0.03109039,0.00918139,0.02332717,-0.07376724,0.02500358,-0.04853021,-0.01989013,-0.00625554,0.01600548,-0.04358688,0.00580693,-0.00400259,0.00882628,0.01142802,0.02441077,-0.00827605,0.0390477,0.03298838,0.04262397,-0.03743044,0.06037007,0.04932913,-0.25683299,0.05445376,-0.02696202,0.00547559,0.04568791,-0.01655226,-0.01646695,0.06884362,-0.02731626,0.00454812,-0.03160156,0.05906542,0.00678712,0.00573357,0.03310556,-0.06096392,-0.08186125,-0.00856714,0.01246907,0.05742399,-0.03150974,0.00361661,-0.09662963,0.01867809,0.0078146,-0.00487263,0.06752668,-0.00499989,-0.00111226,-0.01513464,0.03300136,-0.00423268,-0.01891259,-0.01266611,0.01371692,0.03176725,-0.08299039,0.1116967,0.00689249,-0.02629027,0.01125219,-0.08129742,0.06561491,0.0366317,0.00590961,-0.05845286,0.07742648,0.04481636,-0.03965936,-0.00110103,0.01141623,-0.06186489,-0.07091947,0.02858776,-0.02701786,0.03841903,0.0226136,-0.02487985,-0.04536362,-0.01182263,0.06064807,-0.0612468,0.02037883,-0.04190727,0.06255355,0.01719476,-0.0166773,0.05113763,0.0342205,-0.07683898,-0.00411782,-0.05971312,-0.06675141,-0.05212073,-0.01349273,-0.01575571,-0.02520169,-0.08830696,-0.03626031,-0.0896612,-0.00985084,-0.075695,-0.02235092,-0.00984621,-0.00384304,0.03762822,0.00407579,-0.00282406,-0.00700357,0.06147928,-0.00692946,-0.03263032,-0.01978527,0.03266145,0.04759368,0.11169759,-0.02568424,0.00780693,0.07753943,-0.11011124,-0.06508394,0.1175473,-0.02680008,-0.10240256,-0.0271716,0.01530102,-0.00537278,-0.03738339,0.00609743,0.05388565,-0.05867898,-0.00848603,0.06638809,-0.03035371,-0.00195636,0.01461,-0.04173582,0.02023135,-0.08457507,-0.0355,0.04890781,0.02346721,0.02714168,-0.05488324,-0.01166787,0.00540229,0.0081706,-0.0319409,-0.04599375,0.05606517,-0.0423546,-0.00826989,-0.0172264,0.00087125,-0.00063355,-0.01408451,0.02732049,-0.02758481,0.06740471,-0.01681424,-0.11002973,-0.02188322,-0.03833235,0.00506189,-0.0346906,0.05164803,0.07347736,0.06402946,-0.0554332,0.0230097,0.08016015,0.01588035,-0.00903799,-0.0020966,0.055675,0.0609428,-0.00158015,-0.00174802,0.02806206,-0.00676054,-0.10669593,-0.17761683,-0.01482379,0.02536871,-0.01105855,0.06351791,-0.02036045,0.038972,0.08417653,0.05192896,0.06554238,0.07269563,0.0233375,-0.05148422,0.07111193,0.01985352,0.06770027,0.03978514,-0.00432943,0.00567451,0.02277455,0.02688072,0.06453209,0.00129478,-0.03668634,-0.01614588,0.03696169,0.13376343,-0.02904068,0.02524662,0.03272349,0.07269076,0.01073354,0.01214763,-0.07335848,0.07878589,0.01526412,-0.02766092,-0.02037503,-0.01558661,-0.00400908,0.00782729,0.04744339,-0.01205897,-0.13469605,-0.03809064,-0.0601347,-0.05767265,-0.03274511,-0.10384513,-0.0052971,-0.02675964,-0.01976624,-0.03721751,0.06811713,0.00580476,-0.0664183,0.01064017,0.00156791,0.03172721,-0.00744545,-0.00815834,0.00333584,0.05957353,-0.0509818,-0.00136847,-0.05905046,-0.00267359,0.06151228,0.04216902,0.01416207,-0.02508233,0.09918411,0.00244721,0.03396001,0.07497779,-0.02788871,-0.03405762,-0.02266456,-0.01515018,-0.00442905,0.00279691,-0.04570451,0.04548535,0.00613769,0.00711988,0.05424159,0.0343638,-0.01134447,0.01941213,-0.06146672,0.02622063,0.01251593,-0.05961449,0.00290408,0.04959523,0.05172586,-0.26442295,0.04942606,-0.02341485,0.06517827,-0.04861801,0.01758107,-0.02891503,0.00775599,-0.08679971,0.01781851,0.05537948,0.01779984,0.0247698,-0.02265222,-0.02326534,-0.01471046,-0.00479966,-0.02007305,0.02129172,-0.00266493,-0.00082952,-0.00148712,0.20826547,0.02005327,0.07787549,-0.00965018,0.01789811,-0.00213038,-0.0507373,-0.01704869,-0.02635014,0.02114121,0.15760724,-0.02131538,-0.00174472,0.06581949,-0.05284362,0.00359428,-0.02419312,0.04910352,-0.03169492,0.04630542,-0.04018903,0.0465561,0.11560632,-0.03075183,-0.01960293,-0.07144383,0.0413732,0.0975998,0.03100236,-0.0704482,-0.00468182,-0.00373202,0.01348697,0.0561055,-0.03162765,-0.06118855,-0.00689798,-0.0291547,0.01831624,0.0176164,0.03685781,0.05362862,0.02886586],"last_embed":{"hash":"0240ac386654b59a0fc47447bc3f67e1791ada51398910777c1dc35bb1435ee5","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"0240ac386654b59a0fc47447bc3f67e1791ada51398910777c1dc35bb1435ee5","at":1743662844234},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:","lines":[36,71],"size":2120,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.2. Tại sao bạn lại chia service như này?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05263519,0.04776461,0.02866693,-0.02855927,0.00911035,-0.01241386,0.01386316,0.03549431,0.04521236,0.02682602,-0.04697549,-0.11004066,0.06238174,0.05923096,0.03670509,-0.01639256,0.02545422,0.01197558,0.02150096,0.02931838,0.11777003,-0.01566133,-0.00653393,-0.03477954,-0.04357044,-0.04507973,0.02380667,0.02109687,0.00287364,-0.19107211,0.0140625,-0.00652855,-0.014569,0.02362333,-0.00666603,0.0244758,0.01110608,-0.03444,0.02539724,0.00487893,0.02336157,0.00444994,0.03031634,-0.0732723,0.01543569,-0.04953292,-0.00895115,0.01473327,-0.00689623,-0.05865688,-0.01112427,0.00557524,0.01689335,0.01730861,0.01508515,0.00068566,0.03810529,0.01690509,0.03612603,0.00275601,0.06100668,0.05004808,-0.26644823,0.04396651,-0.01959968,0.0186724,0.03673362,-0.01970945,-0.04999094,0.0635669,-0.00396198,0.00800706,-0.03252933,0.06674585,-0.00572918,0.02875577,0.03064243,-0.04492481,-0.05860031,0.0140776,0.00851651,0.01539607,-0.04449915,0.01594828,-0.11428406,0.02659661,-0.0039808,0.00088229,0.08836529,-0.00407815,-0.03537165,-0.03411073,0.01495117,-0.02613678,-0.03966348,0.02791518,0.00935007,0.01716977,-0.05610059,0.13274951,0.0302583,-0.0272834,-0.01698148,-0.09155323,0.0696949,0.03006822,0.03054854,-0.05965108,0.08395606,0.06222764,-0.02919408,-0.02088812,-0.00791613,-0.04866996,-0.07844725,0.03882227,-0.01562039,0.05116045,-0.00058849,-0.0547353,-0.01926269,-0.02326401,0.06691423,-0.05114673,0.02244377,-0.00701008,0.05005189,0.02855321,-0.01556045,0.04676217,0.01636568,-0.06966788,-0.03292388,-0.05091102,-0.05177205,-0.06491651,-0.01377551,-0.00191744,-0.03310136,-0.06405228,-0.0568935,-0.05287779,0.00260321,-0.07119101,-0.01615601,0.00412183,0.00227301,0.02545995,0.00661054,0.01091154,0.01578847,0.07124311,-0.01402588,-0.03662772,-0.03605429,0.02688474,0.04274785,0.10630077,-0.0156476,0.01455933,0.03762982,-0.09867094,-0.04756141,0.11995993,-0.00222159,-0.08148281,-0.00859365,0.03594,0.00266428,-0.02082275,0.00082375,0.04432786,-0.05419134,0.01760794,0.06158254,-0.04196839,-0.00696768,0.01197253,-0.03465964,0.02365417,-0.06110184,-0.03576405,0.02437481,0.00738026,0.0164499,-0.04727978,-0.0062973,0.00172128,0.00002308,-0.02353193,-0.03429364,0.05173372,-0.03770504,0.04302391,-0.00587901,-0.01250667,0.01086041,-0.01747044,0.02702357,-0.04323785,0.04559755,0.01179411,-0.11936168,-0.03234461,-0.04237847,-0.0168507,-0.02552629,0.02007527,0.04353589,0.04706998,-0.06767641,0.04688426,0.04447534,-0.00230302,-0.00393173,0.00399376,0.02238793,0.05415574,0.00600481,-0.0075166,0.05682688,0.02245196,-0.08269577,-0.20566356,-0.01840841,-0.01463314,-0.06339142,0.09018847,-0.01210521,0.05091899,0.06897424,0.03910032,0.04258072,0.04989699,0.01567178,-0.06168476,0.08669417,0.00588626,0.04089898,0.04930861,-0.03222998,-0.02765593,0.03420016,0.03786457,0.04824934,-0.00294829,-0.04133632,-0.00550989,0.03064584,0.15763918,-0.03600704,0.05524923,0.02249539,0.05628058,0.01102815,0.0169444,-0.10043795,0.05868122,0.00441603,-0.02001896,-0.03642551,-0.03090834,0.01020046,0.0262574,0.03837073,-0.0042503,-0.1139275,-0.031375,-0.03420774,-0.05387118,-0.03938119,-0.0763679,-0.02308714,-0.02841182,-0.01726296,-0.02657167,0.05280862,-0.02486792,-0.06630947,-0.00850936,-0.00541358,0.03865415,0.0023042,-0.00061809,-0.01844529,0.06502093,-0.0707534,0.00100313,-0.05749609,-0.01105869,0.02937898,0.05579324,0.01883478,-0.01668002,0.09344257,0.01562189,0.04017133,0.07905734,-0.03183336,-0.02640442,-0.00783785,-0.01247117,0.02308035,-0.00652293,-0.0658474,0.05655381,0.02548006,0.00423432,0.076087,0.03314715,-0.03568521,-0.01400102,-0.07308935,0.012663,0.00493582,-0.08492451,-0.01042556,0.0552123,0.03461487,-0.26432034,0.05479283,0.00553632,0.09011335,-0.02998584,0.04321317,0.00153189,-0.00800845,-0.07237126,0.00863456,0.04868462,0.01582793,0.03051369,0.0222267,-0.01438461,0.00008858,0.0013561,-0.01285261,0.04465891,-0.01786662,-0.00946143,-0.00876219,0.22185338,-0.00899736,0.07791849,-0.00821986,0.01797652,-0.01858486,-0.0466246,0.02273119,-0.00334786,0.02643123,0.15977713,-0.02210621,-0.02484273,0.05588465,-0.07059927,0.01039798,-0.02341823,0.04361471,0.0033828,0.01287914,-0.04139687,0.04478618,0.08520572,-0.01695845,-0.02822011,-0.06625991,0.02956776,0.09177842,0.02983176,-0.07561157,0.0010015,-0.01854759,0.03937982,0.05919063,-0.00057157,-0.06303986,-0.03973518,-0.04879707,0.02008651,0.05686692,-0.00141379,0.05023813,0.03080621],"last_embed":{"hash":"6601b7860dea9392ef0eaec5d88b0c9be14c82786776d7e592cc9d5c05a454f4","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"6601b7860dea9392ef0eaec5d88b0c9be14c82786776d7e592cc9d5c05a454f4","at":1743662844269},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.2. Tại sao bạn lại chia service như này?","lines":[44,49],"size":248,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.3. Order được xử lý lần lượt qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06594587,0.04595941,0.02874147,-0.04739441,-0.00179595,-0.02779358,0.01045341,0.04780871,0.04875355,0.01446432,-0.03520393,-0.08675977,0.03492366,0.03502811,0.05663932,-0.03554996,0.06336392,-0.01585076,-0.01742375,-0.02261391,0.07582982,-0.01398707,-0.04199355,-0.01064631,-0.01055152,-0.03715283,-0.00547154,0.00352477,-0.01606948,-0.15178157,-0.0166904,-0.0065258,0.01532495,0.02019737,0.0426349,0.0243878,0.02809462,0.00540361,-0.03941126,0.03742156,0.04351683,-0.01689107,-0.00127985,-0.07474189,0.01528338,-0.06518287,-0.02061595,0.00444802,0.02233606,-0.03558587,0.02638468,0.01872732,0.00497574,0.02480934,0.02013728,0.00962053,0.03903205,0.0283984,0.04601135,0.00351812,0.0803162,0.03240619,-0.27176064,0.0780646,-0.04579829,-0.02826738,0.06121397,-0.00227543,-0.01720652,0.0886009,-0.0454786,-0.00944109,-0.01771291,0.07505245,-0.00730663,-0.02296337,0.0275957,-0.07645585,-0.09238706,0.00299433,0.00505713,0.05495942,-0.02590468,0.00486012,-0.08505019,0.00720505,-0.0023222,-0.00488843,0.06511527,-0.02135694,0.00199293,0.01859149,0.00587058,-0.00279328,-0.03490105,-0.04837774,0.01247096,-0.01099033,-0.08325418,0.11711682,-0.00781061,0.00787314,0.04503884,-0.03968591,0.03759338,0.04122418,-0.00522884,-0.06896098,0.03954193,0.03246998,-0.0802838,-0.00809346,0.06606745,-0.04876438,-0.04864861,0.03527512,-0.00774429,0.02095532,0.02084218,0.01784595,-0.02269563,-0.01566522,0.05188296,-0.07439056,0.07054056,-0.05100172,0.05742105,0.01876755,-0.02203382,0.02786359,0.01930523,-0.07280093,-0.0104198,-0.07478581,-0.07161465,-0.04217533,-0.01607135,0.03730091,-0.02331733,-0.06297293,-0.01645493,-0.09638709,-0.03082744,-0.08543623,-0.01858359,0.0273009,-0.01428353,0.04109733,-0.04082081,-0.0300809,-0.00858438,0.04407781,-0.00591534,-0.06448947,-0.03110698,0.06523805,0.02876164,0.12862653,-0.04504755,0.03788864,0.08512802,-0.08286229,-0.06425636,0.10811945,-0.04467699,-0.09146165,-0.02245374,-0.00592456,0.01245282,-0.04458303,-0.01096976,0.06574234,-0.08075622,-0.00815289,0.04580245,-0.02810074,0.04292507,0.04290339,-0.02776413,0.01528971,-0.08938144,-0.0249251,0.09047747,0.06160716,0.05535493,-0.05975443,-0.01700713,0.00460005,0.02606525,-0.02182433,-0.06398591,0.03231162,0.0351069,-0.02565304,-0.031216,-0.00166612,-0.036916,-0.02937568,0.03541259,-0.02668569,0.0892648,-0.05160112,-0.07552578,0.00226478,-0.04606207,0.00754012,-0.02476513,0.03013187,0.04589625,0.02500536,-0.03358952,-0.01446559,0.06537852,0.01007976,-0.01003975,0.00348932,0.03225729,0.06142418,-0.00507033,-0.03707191,0.0204205,0.03127479,-0.1198246,-0.17794482,-0.02298857,0.02507588,-0.01107606,0.05951994,0.03090474,0.05408045,0.07484311,0.07169924,0.06292836,0.07376692,0.02899491,-0.03602643,0.05659323,0.03639608,0.06641807,0.01903564,-0.03816488,0.01057241,-0.0049997,0.0166705,0.08137285,-0.0272779,-0.00134154,0.01991995,0.02691057,0.11731471,0.00151977,0.0030888,0.0410479,0.04618796,0.0066034,0.01731957,-0.03744441,0.07984075,-0.01421728,-0.03471424,-0.00093296,-0.00389099,0.01979351,-0.01798954,0.0354061,-0.02200782,-0.08649036,-0.04644629,-0.04512767,-0.09309876,-0.01728419,-0.09943648,-0.04097493,-0.00673194,-0.00055609,-0.03896123,0.05215238,0.04877734,-0.03140223,0.00830237,0.00918531,0.00541329,-0.0118586,-0.01073004,0.01432827,0.03195988,-0.08272118,-0.01417656,-0.05483861,-0.01355976,0.03561393,0.04117051,0.01848342,-0.03625156,0.0753641,-0.00032242,-0.00217208,0.09997779,-0.00358007,-0.02456752,-0.04645437,-0.01387184,-0.00520674,0.06545069,-0.02387629,0.06596366,0.00425085,-0.01651062,0.04197367,0.03755686,0.00759049,0.02955898,-0.0387358,0.02254448,-0.00573817,-0.04342709,0.02056678,0.07259683,0.04483084,-0.26290435,0.04588293,-0.01718004,0.04690838,-0.06235058,0.02308842,-0.05137264,0.0109253,-0.08483411,0.03861976,0.06727723,0.04086354,0.02747802,-0.02037464,-0.02707474,0.03039682,-0.00485779,-0.06149509,0.01129568,0.00246213,0.00240736,-0.03170406,0.19559896,0.03920911,0.05365173,0.02275705,-0.00691313,0.02416414,-0.05095278,-0.03939547,0.02053796,0.03261376,0.11537487,-0.03373404,0.00501898,0.01801963,-0.02491499,0.02786894,0.00683517,0.04316818,-0.04532264,0.04103891,-0.06323376,0.01631382,0.11782494,-0.02768444,-0.02872864,-0.0761029,0.04271539,0.07309773,0.0080394,-0.08239745,0.00605337,0.02879632,-0.0039039,0.07476243,-0.06767575,-0.06348921,0.02665602,-0.01913947,0.0273301,0.00744419,0.07321037,0.02124531,-0.00458725],"last_embed":{"hash":"e621cd69d410b16afdc4c608615b0f60d019810d156149f0a68f3a87efe41c97","tokens":126}}},"text":null,"length":0,"last_read":{"hash":"e621cd69d410b16afdc4c608615b0f60d019810d156149f0a68f3a87efe41c97","at":1743662844281},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.3. Order được xử lý lần lượt qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?","lines":[50,55],"size":262,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05726799,0.07584631,0.02042995,-0.03236723,0.00531874,-0.03184595,0.0291354,0.03332517,0.02330192,0.03598177,-0.02735664,-0.12437826,0.03307157,0.03490194,0.07677088,-0.00036578,0.00457622,0.01407562,0.03101656,-0.04308751,0.09402819,-0.04309674,-0.05405714,-0.01334434,-0.00872003,-0.0137343,0.00125061,0.00940148,-0.00031291,-0.19108139,0.01256075,0.01914038,-0.03838443,0.02875873,0.02461512,-0.02055587,0.03136918,0.02854954,0.01301569,-0.00184109,0.02269712,0.00782485,0.00965524,-0.07502957,0.01014617,-0.03936374,-0.02012307,0.02475556,0.0442867,-0.02117415,0.00661444,-0.02658036,0.00857218,0.02459839,0.02839184,-0.00810897,0.05272004,0.0014943,0.01879382,-0.04700523,0.06096463,0.03514466,-0.25089142,0.06981824,-0.02172898,0.02148469,0.05269383,-0.02561329,0.01776042,0.03398283,-0.04466461,-0.00688087,-0.03805244,0.03020465,0.00230408,-0.0095761,0.04007848,-0.05371716,-0.03934721,-0.00468696,-0.00210667,0.07453471,-0.00668792,0.02062333,-0.07383901,-0.00395823,-0.02235137,-0.00932572,0.03842044,-0.01094466,0.00299467,-0.00732791,0.05077497,-0.00673507,0.00502006,-0.0603679,0.0267053,0.02073068,-0.09585227,0.14136478,0.01284763,-0.03465804,0.00040537,-0.06399537,0.04415396,0.01979783,-0.00538734,-0.05032584,0.06053176,0.0321406,-0.03754845,-0.00117991,-0.00155261,-0.05073742,-0.03813226,-0.00810045,-0.01656788,0.02347437,0.05232015,-0.01476691,-0.04399412,-0.02588301,0.0504196,-0.04005706,0.05261194,-0.07301477,0.06401312,0.00831763,-0.04548648,0.05560989,0.04113162,-0.06705305,0.02122911,-0.05417279,-0.04679706,-0.0541893,0.00469787,-0.02929561,-0.02183167,-0.06022869,-0.02990957,-0.1029001,-0.00562767,-0.06864989,-0.0191109,-0.00394046,0.00007838,0.05943645,0.00656073,-0.02498274,-0.0125289,0.07535589,-0.0388024,-0.04379921,-0.01588657,0.00099288,0.03245196,0.07085422,-0.02934028,0.02311669,0.08061509,-0.08626914,-0.06893438,0.11218879,-0.04312983,-0.0814875,-0.04329059,0.00637134,0.01884612,-0.01149289,-0.00087454,0.05598856,-0.04879033,-0.01751495,0.08840412,-0.01560613,-0.0247502,0.00051385,-0.06777458,0.03007759,-0.08062317,-0.00903776,0.01849424,0.0331778,0.05851601,-0.03827622,-0.01916468,-0.00596439,0.03736274,0.0127229,-0.0200556,0.06516416,-0.05041582,-0.05051971,0.00143282,-0.03699013,-0.01338109,-0.0432271,-0.01129894,-0.01696347,0.07685851,-0.01597433,-0.09410357,0.01035915,-0.01991417,0.00029082,-0.04851123,0.0507479,0.0912764,0.04286359,-0.04187362,0.00791971,0.1161285,-0.00026797,-0.01886513,-0.00563254,0.03917448,0.08213803,0.01570401,-0.00702869,0.01953134,-0.02447006,-0.0839918,-0.18578279,0.00092984,0.04360877,0.00336185,0.03456137,-0.02008813,0.07976119,0.05925151,0.04702052,0.04895802,0.06982449,0.00014109,-0.03000602,0.0747127,0.02088151,0.09333319,0.0298478,-0.00239311,0.02967956,-0.01073202,-0.00189943,0.07140146,0.03041531,0.01303207,0.01677455,0.07234918,0.12171132,-0.03116586,-0.01557796,0.03398174,0.04462836,0.0111086,-0.02054065,-0.05861118,0.09520466,0.0066468,-0.02417211,0.00740846,-0.00683505,-0.03961271,0.013348,0.06108359,-0.03880534,-0.11747367,-0.02406948,-0.06667678,-0.03708469,0.00120876,-0.12637796,0.02131846,-0.05357868,0.00436246,-0.0222206,0.05299842,0.0164126,-0.06280058,0.02001953,0.03545204,0.02039774,0.01548719,-0.04147267,0.00644076,0.04576391,-0.05356266,-0.03505946,-0.03157243,-0.00216017,0.05547264,0.03476282,-0.01034395,-0.02277585,0.11437458,-0.0005649,0.02147741,0.08799011,-0.02330404,-0.00013435,-0.04852672,-0.02838205,-0.03009733,0.01258455,-0.04606357,0.04671571,-0.0093109,-0.02142451,0.02839192,0.02158992,0.01041651,0.02566097,-0.04009644,0.00663797,0.04119932,-0.04843088,0.0081865,0.05272103,0.02695063,-0.27541819,0.03218836,-0.0351752,0.03821605,-0.04476097,-0.00124367,-0.04765598,-0.00531204,-0.11399786,0.02073988,0.06911926,0.00658494,0.04481159,-0.02712661,0.01099848,-0.01091018,0.00722291,-0.05216119,0.04195068,-0.03112749,0.03036794,0.01856396,0.20032369,0.01236749,0.06415143,0.00475832,0.0223406,0.00188258,-0.02933417,-0.02711094,-0.05073101,0.01614105,0.13825613,-0.00131952,0.03164643,0.05670109,-0.04942251,0.00492953,0.02502003,0.05561876,-0.04279951,0.04732499,-0.04114249,0.00224596,0.1394904,-0.01197309,-0.02172162,-0.07531399,0.03687712,0.08105129,0.0217501,-0.07885154,-0.00312985,0.00182944,0.02231094,0.03690509,-0.06763579,-0.04071879,-0.01336508,-0.03175413,0.03402968,0.02149958,0.04177073,0.0238247,0.02274307],"last_embed":{"hash":"688521ff766e88bdbf15470d33326aa167f06dbd751ccbb689dec52479cc2291","tokens":209}}},"text":null,"length":0,"last_read":{"hash":"688521ff766e88bdbf15470d33326aa167f06dbd751ccbb689dec52479cc2291","at":1743662844293},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?","lines":[56,61],"size":485,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05572634,0.07818232,0.01938102,-0.03076192,0.0051034,-0.03006818,0.02988693,0.03103304,0.02315487,0.03637555,-0.02545504,-0.12260746,0.03382686,0.0362984,0.07991456,-0.00195013,0.00759137,0.01102182,0.03182989,-0.04497165,0.0951649,-0.04316901,-0.05527056,-0.01284458,-0.01075291,-0.00784164,-0.00002904,0.00597122,0.00158829,-0.18743578,0.0124182,0.01795714,-0.04170892,0.03161841,0.02269669,-0.02083234,0.03205082,0.03014361,0.01497394,-0.00097385,0.022434,0.0057885,0.01439996,-0.07789035,0.00987048,-0.04005004,-0.0235799,0.02386401,0.0464756,-0.02111769,0.00561074,-0.02962236,0.01083064,0.0237766,0.03140603,-0.01112475,0.05265694,0.00114135,0.01798273,-0.04598394,0.0616125,0.03604969,-0.25031313,0.06496572,-0.02426093,0.02080337,0.05219182,-0.02580563,0.01788413,0.03423376,-0.04293592,-0.00475863,-0.03730145,0.03033728,0.00146074,-0.01080409,0.04142218,-0.05653029,-0.03952689,-0.0052627,0.00111976,0.07462965,-0.01131022,0.02287216,-0.07542742,-0.00420412,-0.01956006,-0.01081128,0.03823156,-0.01101376,0.00352875,-0.00783669,0.0452412,-0.00961962,0.00331729,-0.05944965,0.02770546,0.02194381,-0.09616217,0.14084402,0.01326585,-0.0357408,-0.00323712,-0.06351809,0.04605352,0.02231537,-0.00796911,-0.04910475,0.061981,0.03502156,-0.04030968,0.00028162,-0.00420839,-0.04650017,-0.03872966,-0.00827108,-0.01743449,0.02686043,0.05241097,-0.01275518,-0.04218819,-0.02832638,0.05352392,-0.03994446,0.05437935,-0.07493354,0.06357138,0.00846223,-0.04515596,0.05429235,0.04267846,-0.06835451,0.02172815,-0.05486151,-0.04580519,-0.05356956,0.00600489,-0.03115136,-0.01944668,-0.06145745,-0.03080315,-0.10279201,-0.00618268,-0.06816573,-0.02329391,-0.0058422,-0.00250984,0.06000181,0.00384385,-0.02484761,-0.0149025,0.07686133,-0.03882259,-0.0471045,-0.01740612,0.00005711,0.03107944,0.07128715,-0.03128671,0.02607637,0.07964731,-0.08236136,-0.06653908,0.10952609,-0.03929378,-0.07913274,-0.04237771,0.00657262,0.01763233,-0.01230013,-0.0027,0.05306859,-0.05086378,-0.01694721,0.08791482,-0.01145416,-0.02849181,0.00230115,-0.06696807,0.02932066,-0.08153167,-0.01250375,0.01651579,0.03250278,0.06313697,-0.03831708,-0.017612,-0.00513056,0.03606358,0.01370185,-0.01973824,0.06650957,-0.05220317,-0.04970174,0.00373169,-0.03889064,-0.0111572,-0.04306848,-0.01032786,-0.01518832,0.07457098,-0.01862421,-0.086099,0.00951355,-0.02006363,-0.00223944,-0.05106875,0.0497177,0.09057723,0.04393336,-0.04039213,0.00205024,0.11684613,-0.00169977,-0.01849428,-0.00576116,0.03918297,0.07879862,0.01458097,-0.0063364,0.01921004,-0.02518346,-0.0861993,-0.18297099,-0.00040751,0.04320911,0.00237455,0.03809605,-0.02100059,0.08194336,0.06007509,0.04693898,0.0467159,0.07168648,-0.00154427,-0.02593571,0.07714388,0.02130429,0.09604923,0.03269149,-0.00322311,0.03418568,-0.01105566,0.00041084,0.07129631,0.02791129,0.01144724,0.01823192,0.0697021,0.11921325,-0.03539096,-0.01428838,0.03058178,0.044048,0.00692342,-0.02067527,-0.05541826,0.09602229,0.00730644,-0.02571097,0.00538026,-0.00772001,-0.03498998,0.01044834,0.05512489,-0.03910867,-0.11886127,-0.02315818,-0.06627818,-0.04107393,0.00223542,-0.12678804,0.01955492,-0.05222689,0.00620052,-0.02408686,0.04841631,0.01954759,-0.06272478,0.02148798,0.03365219,0.0243462,0.01289617,-0.04048681,0.00778476,0.04636978,-0.05358019,-0.032902,-0.03314238,0.00016137,0.05804662,0.03470378,-0.01203552,-0.02397556,0.11525628,0.00044448,0.02517649,0.08663157,-0.02440928,0.00010815,-0.04719773,-0.03024753,-0.02770028,0.01225782,-0.04697754,0.0491304,-0.00999436,-0.02250528,0.02980018,0.01878894,0.01140163,0.02591183,-0.03968733,0.00648803,0.04249126,-0.04679221,0.01031922,0.05152608,0.02859607,-0.2751762,0.03165448,-0.03843251,0.03391253,-0.0477951,-0.00234499,-0.04949601,-0.00145701,-0.11160813,0.01953072,0.06849781,0.00606249,0.04461697,-0.02997168,0.00835929,-0.00743024,0.00720508,-0.05500059,0.04427392,-0.02997089,0.0316603,0.01873883,0.20175244,0.01514527,0.06428608,0.00579669,0.02311881,0.00192761,-0.03372426,-0.02846763,-0.04727229,0.01639216,0.13922535,-0.00244248,0.02794074,0.06025307,-0.05065587,0.00657207,0.02457448,0.05580636,-0.04218012,0.04724107,-0.04064683,0.00452794,0.13960956,-0.00885199,-0.01881624,-0.07330945,0.04015111,0.08318903,0.02314581,-0.07954575,-0.00300879,0.00294916,0.02566881,0.03768418,-0.06837565,-0.04269598,-0.01360179,-0.03419735,0.03737737,0.02155528,0.03729687,0.02036543,0.02085855],"last_embed":{"hash":"18ab42e7eee2549b83ab1321d87b7cd2e0d01c8384dfd1e753ed4ac353e6f6f9","tokens":207}}},"text":null,"length":0,"last_read":{"hash":"18ab42e7eee2549b83ab1321d87b7cd2e0d01c8384dfd1e753ed4ac353e6f6f9","at":1743662844309},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?#{1}","lines":[58,61],"size":337,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order được điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. Làm sao để hệ thống trả lại response cho frontend?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0895637,0.06441855,0.01271814,-0.00754011,-0.01497058,-0.05017048,0.00308805,0.02832707,0.07016413,-0.003204,-0.03570471,-0.10870232,0.00981897,0.04295406,0.06434448,-0.02276566,0.04624858,-0.02618559,-0.0101038,-0.04806709,0.08067588,-0.04857003,-0.04202614,-0.00908774,-0.03541805,-0.02901422,-0.01013087,-0.00020725,-0.00697792,-0.17301266,-0.0024629,-0.02158126,-0.00184792,-0.01257866,0.0613561,0.01091733,0.03549507,-0.02191995,-0.00222801,0.04371036,0.07173054,0.02939337,0.01276519,-0.08249751,0.02399517,-0.07556632,-0.06125945,0.0048033,0.00702181,-0.02720894,0.02244471,-0.01156938,0.00084666,0.03146292,0.01541638,0.02040607,0.06611177,0.02115209,0.04210313,-0.00124528,0.08888059,0.01821634,-0.24895214,0.07102615,-0.0406718,-0.00363378,0.02998739,-0.00911998,0.01825671,0.08159006,-0.05646053,-0.00708772,-0.00192533,0.08887382,-0.00464203,-0.02445061,0.05413905,-0.03779992,-0.04012737,0.02061501,0.00385402,0.05203904,-0.01355954,0.04803126,-0.06833374,0.01559502,-0.01815038,-0.01218242,0.05697682,0.01189851,0.00920199,-0.01394961,-0.01671437,0.01254341,0.00623078,-0.05460309,0.03583298,-0.01229878,-0.07015478,0.11716384,0.00554891,-0.0012477,0.02892052,-0.04082574,0.04998675,0.01889021,-0.01897165,-0.07200413,0.02357846,0.02192506,-0.05370437,-0.01651501,0.03007692,-0.01745146,-0.05684247,0.03588767,-0.007914,0.01371236,0.01018608,-0.0329539,-0.0141362,-0.0230772,0.05338607,-0.07967566,0.01936755,-0.05813207,0.04508491,0.02882534,-0.07437289,0.04982078,0.05474982,-0.07635744,-0.02457211,-0.05487082,-0.03644346,-0.02987749,0.01462227,0.03837216,-0.04337206,-0.06605493,-0.04672775,-0.11444505,-0.04027983,-0.0830728,-0.01963048,0.06875101,-0.01759507,0.03610535,-0.01449174,-0.03971107,-0.02418934,0.02939785,-0.02287932,-0.0836661,0.0072218,0.05600751,0.04680668,0.11966223,0.0053232,0.01063625,0.06957888,-0.08753336,-0.06058297,0.11873023,-0.02520708,-0.11458653,-0.03620901,0.01370589,-0.00279738,-0.00151537,-0.01192608,0.06177303,-0.07959687,-0.00923376,0.0402951,-0.00872096,0.02951589,-0.0254041,-0.02337137,0.01736448,-0.0918824,-0.0355309,0.05510095,0.01721168,0.05749262,-0.05458188,0.0085093,0.01710542,0.01480921,-0.0769852,-0.06353287,0.03049898,0.00226083,0.00272862,-0.01196491,-0.01665044,-0.01229291,-0.0407168,0.02938377,-0.01322534,0.08391719,-0.00754716,-0.07328875,0.02299906,-0.03041914,-0.01425301,-0.02077719,0.02348706,0.063387,0.02329643,-0.03276187,0.00841833,0.07868445,0.0200104,-0.05113706,-0.0111337,0.03409712,0.0591155,-0.0011891,-0.01112122,0.03813617,0.03673619,-0.12203312,-0.19195206,-0.02683385,-0.00354129,0.00772699,0.03860506,-0.01574716,0.05690965,0.07774476,0.06464745,0.06070242,0.09749383,0.02514754,-0.00346169,0.09909118,0.00980745,0.0880574,0.02788307,-0.00206736,0.03559605,-0.03455186,0.01825882,0.0854003,-0.01175399,-0.02939256,-0.01427566,0.0479916,0.11804198,0.02144228,-0.03404597,0.00530423,0.03049725,0.01264292,0.03405501,-0.05535777,0.07900599,0.02244738,-0.01873112,0.00143672,-0.00597614,-0.02002857,-0.008531,0.06147629,-0.00766331,-0.10032496,-0.0011826,-0.0396365,-0.07300477,-0.0475991,-0.09977608,-0.01707097,-0.05039959,-0.029561,-0.02382117,0.0460381,-0.01006964,-0.05415037,0.01742679,-0.0010535,0.01754844,0.00283512,-0.03041526,0.00984934,0.03885569,-0.05459351,-0.04725667,-0.04441898,-0.02676475,0.02275373,0.04463656,0.01098367,-0.02240706,0.09536377,0.00224766,0.02966538,0.07895437,0.03606495,0.00956419,-0.03494349,0.00161105,0.01093903,0.02763959,-0.00760387,0.02956159,-0.00941974,0.02742394,0.0420903,0.03094446,0.02551398,-0.01214347,-0.00737706,0.00692213,0.02558061,-0.05058168,-0.02356757,0.0667088,0.07011918,-0.26766574,0.04405354,-0.03380595,0.05124334,-0.04835542,0.03692117,-0.02430126,-0.00333145,-0.09636777,0.05195764,0.06455422,0.05975443,0.04324503,-0.03391658,-0.03568847,0.01609516,0.0363676,-0.01813653,0.03219418,-0.01374509,0.00195263,-0.00769841,0.21437706,0.07693094,0.00768188,0.0072903,-0.00436734,0.00017226,-0.00450986,-0.06872133,-0.00115361,0.00699487,0.11272729,0.0040417,0.03270979,0.00098057,0.00515096,-0.0000352,-0.01992764,0.04420471,-0.04484018,0.04542796,-0.03836773,0.01440934,0.0964454,0.00506515,-0.04872598,-0.07057441,0.03186243,0.05833367,-0.00900575,-0.08975266,-0.02920579,-0.00197858,0.03200027,0.03946215,-0.03272446,-0.05167824,0.00284252,-0.00847305,0.01382827,-0.00505392,0.06295601,0.02149541,0.03778427],"last_embed":{"hash":"7a436a564ce92cd9dbbc1e28a654ec772b736027c67801fad82dc1c8c74a6601","tokens":170}}},"text":null,"length":0,"last_read":{"hash":"7a436a564ce92cd9dbbc1e28a654ec772b736027c67801fad82dc1c8c74a6601","at":1743662844329},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order được điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. Làm sao để hệ thống trả lại response cho frontend?","lines":[62,67],"size":414,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07620936,0.05270103,0.01533409,-0.0173235,0.02406218,-0.05864241,0.0139574,0.04575275,0.03863325,0.03476365,-0.00142002,-0.13418444,0.01303202,0.04601204,0.07986608,-0.00733468,0.0075345,-0.00116467,0.03728717,-0.02924317,0.07640628,-0.03158106,-0.05840459,-0.0152734,0.00161687,-0.03793251,-0.00761481,-0.02549838,-0.03789972,-0.19461077,-0.03275029,0.01173418,0.01760654,0.02920436,0.03338699,-0.01123397,0.03690405,0.03209968,-0.00766015,0.04287123,0.02744183,0.00278298,-0.00762496,-0.09685279,0.02686583,-0.03424214,-0.00568107,-0.02548815,0.0242334,-0.03505436,-0.00115609,0.02523987,-0.01722127,0.02741501,-0.02267477,-0.01550238,0.05202683,0.0034044,0.0187809,-0.03910312,0.10639723,0.05202508,-0.27071568,0.05898568,0.00216515,0.03350536,0.04448336,0.00368712,0.02300244,0.06833453,-0.05901742,-0.01235721,-0.03687359,0.05695989,-0.01669413,-0.03690711,0.03912818,-0.03373278,-0.0559288,-0.00787242,0.0282584,0.08740173,-0.04251311,0.02311097,-0.05644758,-0.00753577,-0.00411985,0.002548,0.06094462,-0.01595012,0.01349206,-0.00258647,0.03382379,-0.00117186,-0.02005475,-0.05335037,0.02584127,0.02420052,-0.09113204,0.12321533,-0.01772226,-0.00971268,0.02785051,-0.05173027,0.06208572,0.01881309,0.01124084,-0.06666602,0.03270612,0.04042245,-0.03641216,-0.00517938,0.0290499,-0.02850837,-0.03378281,0.05392567,-0.00659,0.00689965,0.04039463,0.01778209,-0.02858803,-0.01715735,0.03152516,-0.08330814,0.07749691,-0.06062809,0.06151109,-0.0130663,-0.02602893,0.04046723,0.03229515,-0.05646543,0.01448756,-0.03960538,-0.06912245,-0.06444006,0.00686542,0.01548426,-0.02635186,-0.08568943,-0.0441753,-0.04211883,-0.02603524,-0.06522987,-0.05290334,0.01523596,-0.01984531,0.0519525,-0.00875684,-0.03518479,0.00006062,0.06294435,-0.00264669,-0.06029307,-0.0030211,0.03100416,0.04838805,0.09738556,-0.03642539,0.0269993,0.08889167,-0.08265318,-0.07175487,0.12319674,-0.02792071,-0.08974858,0.00712086,0.02157574,0.04485708,-0.05429775,0.00113452,0.06934828,-0.06508835,-0.03201427,0.05698834,-0.03346834,-0.01097537,0.00867779,-0.02242132,0.03391488,-0.08292948,-0.01704867,0.04738707,0.03829535,0.04321082,-0.04756138,0.0087217,-0.00925463,0.01166147,0.01916086,-0.03703927,0.01898735,-0.03817203,0.01201695,-0.03557507,-0.03555818,-0.0050072,-0.04691346,-0.01305913,-0.04354494,0.06671962,-0.07258385,-0.11759422,0.03801438,-0.02885815,0.00248107,-0.04761915,0.02545235,0.04540671,0.06208017,-0.05995926,0.0004676,0.05879587,-0.03651683,-0.01707364,-0.01047622,0.01697541,0.08980237,-0.01995163,-0.01697679,0.03522456,0.02989159,-0.0603829,-0.17284144,-0.00727492,0.0332404,0.02108585,0.09714494,-0.01439662,0.0564648,0.06660377,0.03282302,0.06485081,0.06359006,0.06018639,-0.04723962,0.05455376,0.03764628,0.09345865,0.04683231,0.00829889,0.01357968,-0.03212988,0.00677281,0.07562172,-0.0257481,0.00361752,0.01953816,0.03068248,0.11813979,-0.00195419,-0.00975735,0.05479823,0.02827958,0.00358953,0.01639796,-0.08260898,0.11589424,0.01398111,-0.0360964,0.00226002,-0.01971284,-0.01940108,0.00793977,0.0402073,-0.05076108,-0.08461979,-0.03874672,-0.05234451,-0.05196619,0.0115309,-0.10262038,-0.01996598,-0.00744134,-0.03004002,-0.02937173,0.05018609,0.00909173,-0.03885844,0.02225621,-0.00330321,-0.00126259,0.01336585,-0.02186505,-0.00458706,0.03478528,-0.05245194,0.01133652,-0.02853295,-0.00094342,0.05987887,0.04864481,0.02634389,-0.01049938,0.12985587,0.01224676,-0.02929704,0.07168366,-0.01794304,-0.01253489,-0.06365982,0.00312153,-0.02686303,0.01328821,-0.03886703,0.03437256,0.00338405,-0.01397363,0.02147683,0.02566174,0.03018007,0.02071053,-0.03562946,-0.01169129,0.01167778,-0.0540264,0.01304644,0.06121841,0.05302916,-0.25485656,0.0416596,-0.00868813,0.02987129,-0.02936703,0.01936446,-0.03520118,-0.01644912,-0.09291787,0.02182227,0.10038084,0.05184594,0.0490801,-0.02184528,0.00317649,-0.0025817,-0.02107676,-0.05889044,0.05366562,0.00751789,0.01491449,-0.0073834,0.21441615,0.01025853,0.05216713,0.01064702,0.00776511,0.01037251,-0.0076801,-0.0545735,-0.02355782,0.03152002,0.12782039,-0.03638773,-0.00498666,0.0673405,-0.02238808,-0.00259204,-0.01356623,0.04178751,-0.03028867,0.03195422,-0.05155598,-0.02357467,0.09417595,-0.0095929,-0.02344804,-0.08161364,0.01211745,0.06237267,-0.00721286,-0.07106332,-0.02656853,-0.00502254,0.02036992,0.04497357,-0.05720866,-0.05278133,-0.01821197,-0.02719197,0.02687817,0.01774403,0.05260167,0.02380333,0.0155087],"last_embed":{"hash":"dbc0d81da5ea294897100d0f53bdde11edbfa1ea98bdb66a1295801b5b6c43b9","tokens":217}}},"text":null,"length":0,"last_read":{"hash":"dbc0d81da5ea294897100d0f53bdde11edbfa1ea98bdb66a1295801b5b6c43b9","at":1743662844342},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?","lines":[68,71],"size":502,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07376548,0.05806098,0.01779339,-0.01286786,0.02323114,-0.06111258,0.0185087,0.04136946,0.04302667,0.03453202,-0.00046717,-0.13165109,0.01263676,0.04969904,0.08051717,-0.01102971,0.00422105,-0.00207527,0.04006881,-0.03440715,0.07703082,-0.02796246,-0.06014663,-0.01517969,-0.00129898,-0.03924302,-0.00874909,-0.02796332,-0.03648218,-0.19771381,-0.03173694,0.00902632,0.01650773,0.02627954,0.03110525,-0.0102635,0.03664705,0.03535223,-0.00671125,0.04336376,0.03035879,0.00467327,-0.00148009,-0.09513231,0.02847227,-0.03518671,-0.00794806,-0.0253666,0.02762983,-0.03563069,0.00112135,0.02477194,-0.01585747,0.02708672,-0.02486648,-0.01556756,0.05070616,0.00180493,0.02112668,-0.04062017,0.10533955,0.05167984,-0.26942629,0.05700661,-0.0029016,0.03193868,0.04578337,0.00139782,0.02454596,0.06794339,-0.06185255,-0.0116632,-0.03812123,0.05237244,-0.01741827,-0.03591804,0.03573084,-0.03729456,-0.05579372,-0.00441678,0.03309872,0.08986297,-0.04566318,0.02259888,-0.05264418,-0.00732672,-0.00297164,-0.00125004,0.06097792,-0.01298958,0.01162559,-0.00600213,0.03161848,-0.0024052,-0.0204499,-0.05443466,0.02770355,0.0267434,-0.08819976,0.12427489,-0.02171179,-0.00788143,0.02410889,-0.0465141,0.06298995,0.02018885,0.00818681,-0.06550966,0.03226046,0.04017505,-0.03626454,-0.00501346,0.02563061,-0.03007508,-0.03120588,0.059751,-0.00815265,0.007635,0.03862085,0.01457978,-0.02933431,-0.01874236,0.03126651,-0.08049559,0.07882345,-0.05985131,0.05764852,-0.01304703,-0.03196223,0.04312246,0.02981972,-0.06008884,0.01562891,-0.03854089,-0.07080082,-0.0634098,0.01153574,0.01298691,-0.02822834,-0.08428192,-0.04256227,-0.04505633,-0.02696019,-0.06258296,-0.05459798,0.01462737,-0.02061913,0.05096679,-0.00970429,-0.04103533,-0.0008496,0.05972867,-0.00303727,-0.0620598,-0.00290844,0.03034648,0.04502649,0.09995136,-0.03535619,0.02773122,0.08395401,-0.08526632,-0.06863111,0.12296399,-0.02495658,-0.08315261,0.00431912,0.02338659,0.04892028,-0.05292141,0.00338118,0.06780624,-0.06442723,-0.03297079,0.05761037,-0.03303738,-0.01338978,0.01066023,-0.0232385,0.03345789,-0.08574615,-0.01576722,0.04622389,0.03939075,0.04437348,-0.04660685,0.00570081,-0.0086298,0.01105205,0.02201971,-0.03516101,0.02074103,-0.03849661,0.01108577,-0.03311886,-0.03711595,-0.00827535,-0.04414356,-0.01321202,-0.04212893,0.06589488,-0.07671849,-0.11816372,0.03844678,-0.02718107,-0.00147484,-0.04855927,0.02211663,0.04536388,0.06479778,-0.0610154,-0.00201615,0.05614519,-0.03846765,-0.01694413,-0.006953,0.02007407,0.08678217,-0.02340958,-0.01521896,0.03537373,0.02998218,-0.05655733,-0.1732216,-0.00672509,0.0345132,0.02416688,0.10196324,-0.01481638,0.06038483,0.06570596,0.03528376,0.05948195,0.06289061,0.06221925,-0.04989349,0.05863669,0.03969729,0.09745301,0.04835927,0.00697055,0.01325551,-0.03363821,0.00689842,0.07442782,-0.02642859,0.01058602,0.0197376,0.02813172,0.11747807,-0.00665161,-0.01047281,0.05323765,0.02527874,0.00305144,0.01413716,-0.07936248,0.1161962,0.01780767,-0.03662718,0.0037466,-0.01895913,-0.01469177,0.00753266,0.03591756,-0.05431978,-0.08290964,-0.03736691,-0.04986105,-0.05081428,0.01748305,-0.10088103,-0.01939491,-0.00596468,-0.02889059,-0.03063784,0.04539957,0.01145467,-0.03881714,0.02512545,-0.00562788,-0.00689487,0.00822028,-0.02208089,-0.00721404,0.03504942,-0.04927846,0.01523415,-0.02987697,-0.00210147,0.06011548,0.05232511,0.02941468,-0.00869241,0.12969759,0.00956265,-0.02544171,0.07351142,-0.01613982,-0.0103018,-0.06765377,0.00386703,-0.02739526,0.01431066,-0.0384549,0.03172458,0.00426098,-0.01453019,0.02106747,0.02942155,0.02672132,0.01954088,-0.03337994,-0.01164516,0.01106186,-0.05224841,0.01223186,0.05991947,0.0540828,-0.25516608,0.04139417,-0.00911371,0.02356955,-0.03078888,0.01844318,-0.03659629,-0.01219347,-0.08995887,0.02110721,0.09893918,0.05400335,0.0471731,-0.01662195,0.00083106,-0.00289808,-0.02021545,-0.06394984,0.05565181,0.00689851,0.01933109,-0.00753971,0.21045592,0.0082304,0.05422894,0.00617708,0.0099046,0.0055784,-0.00800599,-0.0574047,-0.02467459,0.03052282,0.13403679,-0.03765265,-0.00711273,0.074079,-0.02525387,-0.00007359,-0.01486778,0.04218398,-0.03056736,0.03034882,-0.04791098,-0.0255759,0.09207436,-0.00766911,-0.02455417,-0.08390731,0.01129948,0.06094411,-0.00501041,-0.07040346,-0.0238364,-0.0042877,0.02490056,0.04428639,-0.05366703,-0.04997762,-0.01883472,-0.02776944,0.03092097,0.0224405,0.0484579,0.01724358,0.01150552],"last_embed":{"hash":"adea248ce08a2e21dc81ec94708a75723b0d60b5ce422892e9b92756545a951e","tokens":215}}},"text":null,"length":0,"last_read":{"hash":"adea248ce08a2e21dc81ec94708a75723b0d60b5ce422892e9b92756545a951e","at":1743662844359},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.2. Microservice:#0.2.6. Response time p(95) của quá trình xử lý order đang ở mức cao. Theo bạn, nguyên nhân có thể là gì? và cách tiếp cận của bạn để khắc phục vấn đề này là gì?#{1}","lines":[70,71],"size":336,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.3. Khác:": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05751321,0.09919721,0.03019873,-0.03136731,0.01952747,-0.02565673,0.01733121,0.04957211,0.03286804,0.01449568,-0.02151559,-0.14786224,0.05062234,0.05021753,0.05381837,-0.01793547,0.00505636,0.05495635,-0.01080852,-0.0148979,0.05138096,-0.02338918,0.00996637,-0.00980962,0.03548273,-0.01081896,0.00890314,-0.00418947,-0.01889695,-0.18244702,0.00645328,0.01103774,0.05239131,-0.01285113,0.01720572,0.01368931,0.00211053,0.05056868,-0.01048049,0.00689709,0.01345025,0.0038372,0.0296539,-0.08623664,0.01143985,0.00617923,-0.02403202,-0.02926999,0.01885921,-0.0414299,0.00541789,-0.03499453,0.02323392,0.00911144,-0.0049996,-0.00073148,-0.00501477,0.0732532,0.03280016,-0.05853193,0.08434538,0.03342256,-0.24125564,0.07430385,0.01846522,0.0259201,0.05147685,-0.00932265,0.00888252,0.0842809,-0.06542104,0.01675929,0.00210247,0.03747505,-0.00770289,-0.0216492,0.05917211,-0.02085715,-0.04076545,-0.01071023,0.02785057,0.03829465,-0.0421466,0.00711674,-0.07155997,0.04507059,0.02142101,-0.03248443,0.00147916,-0.03600389,0.02737378,-0.05771793,0.02482474,-0.00345078,-0.03735122,-0.03584618,0.05279332,0.03452581,-0.1089733,0.12392857,-0.01069095,0.02013226,0.00418577,-0.07449904,0.06139653,0.04850797,0.01304577,-0.08891528,-0.0130256,0.02891675,-0.0497131,-0.03008821,0.01338861,-0.05767945,-0.06488325,0.0354996,0.01947338,-0.00152566,0.02783243,-0.04827759,-0.05649067,0.0219468,0.08292571,-0.05386429,0.07507805,-0.03266405,0.04056856,-0.01583294,-0.00737194,0.02541962,0.03096204,-0.04383203,-0.00885046,-0.06407337,-0.02920796,-0.08087986,-0.0142177,0.01637771,-0.01478629,-0.05340706,-0.03026389,-0.06532173,0.02916241,-0.11208926,-0.04634712,-0.02797678,-0.05622434,0.04521948,-0.03765679,-0.03085726,0.00122772,0.0145254,-0.01837007,-0.04072461,0.01456654,0.03064649,0.0377157,0.09644881,-0.01226721,0.04219627,0.06733588,-0.1170285,-0.11329778,0.06973131,0.02551563,-0.08357716,-0.02549445,0.05816634,0.01211971,-0.01051193,0.03462576,0.0509074,-0.06512126,-0.02979397,0.05586372,-0.05509877,-0.03251958,0.03130602,-0.01161811,-0.00351381,-0.08023651,-0.03019113,0.04104222,0.04156238,0.05531494,-0.05393714,-0.00321822,-0.00146687,0.00273737,0.06056647,-0.06470781,0.05567002,-0.02293429,0.01904156,-0.05211077,-0.01656219,0.00203978,-0.04697325,0.00951825,-0.03727389,0.06368949,-0.03892356,-0.0791958,0.01310223,-0.02801457,0.00746811,-0.05875576,0.03382989,0.08785297,0.0704675,-0.11167485,-0.01786755,0.05261493,-0.01011451,-0.00188086,-0.02440011,0.0309707,0.04896592,-0.02024888,0.00522125,-0.01400663,0.02243103,-0.08299542,-0.17450735,0.02798677,0.02476381,-0.01908697,0.09455701,0.00176724,0.04811195,0.04305686,0.05677009,0.02132403,0.05661348,0.04814481,-0.05857707,0.07842602,0.03374682,0.12470649,0.03633896,0.02784037,-0.01267622,0.02636158,0.0163876,0.09610583,-0.03875208,-0.03321551,0.00227549,0.01925807,0.16128275,-0.02487359,0.01455467,0.03779886,0.04569451,-0.00020768,-0.03134333,-0.05895804,0.11024591,0.00712766,-0.0317437,-0.01953716,-0.02529195,-0.01368796,0.00317427,0.04504617,-0.04072673,-0.10203595,-0.03696249,-0.05423496,-0.05645039,-0.01351506,-0.04641557,-0.02341968,0.03670869,0.00523905,-0.06858487,0.0426239,0.00148632,-0.02875686,-0.02132006,0.02100842,0.00654114,0.03535339,0.00319498,-0.01548433,0.03902691,-0.0005085,-0.03237553,0.00993186,0.01472181,0.08094678,0.04189176,0.06460557,-0.03196009,0.08905558,0.01275839,0.02463942,0.08771125,-0.02709183,-0.01116317,-0.02965687,0.02417337,0.00483218,-0.01406906,-0.08233882,0.01043683,0.02155191,-0.00720804,0.00706033,0.04187716,-0.02850408,0.03858509,-0.04357733,0.0270438,-0.00291596,-0.06566814,-0.00321745,0.05014868,0.03943687,-0.23266388,0.03949047,-0.04703952,0.03772639,0.02299616,-0.00309333,-0.03034517,-0.01958223,-0.06446425,0.0170902,0.04391601,0.02134146,-0.00609939,-0.01199181,-0.01980359,-0.01342255,-0.02384522,-0.02577974,0.05740616,0.00382007,-0.00611212,0.00901566,0.20203456,-0.04639604,0.0534036,-0.01934341,-0.02148725,0.00466151,-0.02597974,-0.03305082,-0.00562273,0.01393692,0.13395962,-0.04417596,-0.01577302,0.06377216,-0.0575742,0.0283608,-0.01128775,0.02360228,-0.02957808,0.04108695,-0.04345281,0.02365514,0.10045118,-0.04337773,-0.01485335,-0.10936098,0.06148217,0.0522199,0.00757963,-0.03281341,0.0039714,0.01206129,0.01704091,0.04825535,-0.02971976,-0.07080965,-0.01735071,-0.05516094,0.03407983,0.07806045,0.04331319,0.00628799,-0.0096246],"last_embed":{"hash":"4d20ebf827bfd8eb06c43c1ba23b9f7bc81184e8b9e083c83ede9adc6d76f45f","tokens":332}}},"text":null,"length":0,"last_read":{"hash":"4d20ebf827bfd8eb06c43c1ba23b9f7bc81184e8b9e083c83ede9adc6d76f45f","at":1743662844376},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.3. Khác:","lines":[72,88],"size":791,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.3. Khác:#0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0575494,0.10007646,0.03124722,-0.02488492,0.0218234,-0.02547224,0.01988834,0.04907741,0.03004779,0.01648636,-0.01574759,-0.14952493,0.05079276,0.04729876,0.05604301,-0.01767681,0.00171152,0.05227361,-0.01266452,-0.01492038,0.05462323,-0.02354782,0.00914493,-0.00420351,0.04068888,-0.01113042,0.01092014,-0.00474925,-0.01737409,-0.18333343,0.00743363,0.01015807,0.0517715,-0.01487682,0.01717388,0.01351815,0.00036451,0.05272207,-0.01390145,0.00613795,0.01413217,0.00453849,0.03084323,-0.08672132,0.00919511,0.00486384,-0.02211366,-0.02916036,0.02020418,-0.04250367,0.00289396,-0.03726845,0.02382294,0.00838807,-0.00629519,-0.00046816,-0.00883014,0.07156607,0.03389267,-0.05610086,0.08393802,0.03327714,-0.23956132,0.07320881,0.0174675,0.0274529,0.05111366,-0.01392886,0.00934761,0.08587825,-0.06644677,0.01803947,0.00282898,0.03734619,-0.00997644,-0.02219862,0.05719766,-0.01828505,-0.04146809,-0.01139395,0.02836201,0.03533343,-0.044451,0.00601255,-0.07062834,0.04479177,0.0192556,-0.0372758,-0.00184639,-0.03592938,0.02532744,-0.05521336,0.0274795,-0.00688412,-0.03834134,-0.03141227,0.05143307,0.03568061,-0.10775217,0.12156292,-0.01107142,0.02060466,-0.00024573,-0.07272752,0.05961076,0.04908542,0.01409071,-0.08854117,-0.01264721,0.02883327,-0.05213762,-0.02820416,0.01042601,-0.05983146,-0.06753258,0.03992063,0.02006014,-0.00409451,0.02551289,-0.04796267,-0.05470985,0.01937256,0.08612306,-0.05187927,0.07675014,-0.03128617,0.03761473,-0.01440592,-0.00579505,0.0235483,0.03285127,-0.04262143,-0.01227662,-0.06373654,-0.03032397,-0.08416515,-0.01364801,0.01654427,-0.01954015,-0.05378593,-0.02826447,-0.06632525,0.02754328,-0.11033335,-0.04719307,-0.02910223,-0.05966644,0.03889824,-0.03907609,-0.03409555,0.00182902,0.01345658,-0.01727105,-0.04198992,0.01580421,0.02542592,0.03824489,0.09559821,-0.01121225,0.04353283,0.06578451,-0.11686196,-0.11285065,0.06835743,0.03085072,-0.08143309,-0.02213556,0.05879347,0.01078317,-0.00925218,0.03922908,0.04961528,-0.0658861,-0.03259221,0.05456869,-0.05095768,-0.03244001,0.03288337,-0.00897584,-0.00547762,-0.07966106,-0.03304518,0.03850475,0.04302005,0.05396346,-0.05591642,-0.00539012,-0.00220635,0.00353815,0.06178775,-0.06606781,0.05671633,-0.02388271,0.01847959,-0.05036178,-0.01722178,0.0009336,-0.04557596,0.01008056,-0.03853756,0.06487703,-0.03693318,-0.07572779,0.01462054,-0.02757435,0.00375107,-0.05733631,0.03269698,0.08836351,0.06884611,-0.11314455,-0.01977845,0.05321287,-0.0114721,0.00117573,-0.02372614,0.033901,0.04547806,-0.02522331,0.00769986,-0.01358487,0.0208075,-0.07966304,-0.17364623,0.0312711,0.02712568,-0.01928552,0.10045331,0.00028626,0.05084167,0.04208086,0.05637887,0.01754389,0.05704596,0.04808693,-0.05775911,0.07844915,0.03514736,0.12890898,0.03579998,0.0284196,-0.01110601,0.0285223,0.01483986,0.0939585,-0.04311882,-0.02977365,0.00346687,0.01728375,0.16168779,-0.02814317,0.01863164,0.03246909,0.04275309,-0.00357551,-0.03574058,-0.05688006,0.11134979,0.0102537,-0.03182895,-0.01922515,-0.02570415,-0.01296385,0.00366152,0.0417268,-0.04225562,-0.10408957,-0.03455478,-0.05310889,-0.05700511,-0.01130532,-0.044775,-0.02354567,0.03974889,0.007651,-0.06945317,0.04174098,0.00404166,-0.02504812,-0.02056254,0.0203035,0.00437728,0.033971,0.00133487,-0.01405517,0.03823322,0.00371925,-0.02987937,0.01039907,0.01740158,0.08075219,0.04079828,0.06987117,-0.02952801,0.08724179,0.01295928,0.0262671,0.08996528,-0.02480529,-0.00862262,-0.02991012,0.02362687,0.00408257,-0.01617474,-0.08295178,0.00920525,0.02252514,-0.00974528,0.00531606,0.04084138,-0.03065722,0.03810575,-0.04178031,0.02464206,-0.00149868,-0.06034815,-0.0075685,0.05144906,0.03957782,-0.23230281,0.0356485,-0.05014143,0.03869209,0.02684204,-0.00287117,-0.0318843,-0.01945791,-0.06566481,0.01632781,0.04305409,0.02224705,-0.00803673,-0.01145221,-0.01781894,-0.01324713,-0.02459889,-0.02644174,0.05825366,0.00182048,-0.00539701,0.01324159,0.20234041,-0.04508857,0.0480627,-0.02330318,-0.01947354,0.0047697,-0.02539087,-0.03161018,-0.00312143,0.01384729,0.13834092,-0.04698606,-0.01449198,0.06256168,-0.05799908,0.02419392,-0.01215074,0.02653698,-0.02915176,0.04255176,-0.04099364,0.02326414,0.09763143,-0.03949418,-0.01499295,-0.10985874,0.06237292,0.05160336,0.00975992,-0.02967988,0.00457631,0.01097198,0.01818177,0.04921219,-0.02925897,-0.07008214,-0.01845174,-0.05472022,0.03428001,0.07882819,0.03972919,0.00403672,-0.00999782],"last_embed":{"hash":"3fba53a2001e328bac6fef6c38da793ebd8eabb8b75725abea977ade96d00a92","tokens":331}}},"text":null,"length":0,"last_read":{"hash":"3fba53a2001e328bac6fef6c38da793ebd8eabb8b75725abea977ade96d00a92","at":1743662844403},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.3. Khác:#0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?","lines":[74,88],"size":776,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.3. Khác:#0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05719169,0.10344095,0.02882748,-0.02402121,0.02260938,-0.01948406,0.02093473,0.04635844,0.03366332,0.01415746,-0.02141654,-0.15040722,0.05580977,0.05204614,0.05268766,-0.01903019,0.00676411,0.0536226,-0.00930096,-0.01366764,0.05430199,-0.02422864,0.00771383,-0.00715393,0.03723976,-0.00956345,0.00575615,-0.00316506,-0.01185212,-0.18270847,0.00817009,0.01141236,0.04459933,-0.01856853,0.01569373,0.01825139,-0.0009295,0.04900005,-0.00908729,0.00554902,0.01659957,0.00443858,0.03248913,-0.08694649,0.0091722,0.00519975,-0.02429025,-0.02919307,0.02264553,-0.04192505,0.00527595,-0.03614411,0.02677886,0.00391838,-0.00230017,-0.0005505,-0.01016501,0.07451528,0.03044603,-0.0560278,0.08518491,0.02981361,-0.23859864,0.07199226,0.0188012,0.0266941,0.04981573,-0.01192743,0.01013982,0.08822554,-0.06452119,0.02008835,0.00366767,0.03441473,-0.00775193,-0.02117805,0.0586988,-0.01722084,-0.04208751,-0.01312542,0.02888365,0.03221541,-0.04542283,0.00779293,-0.07033991,0.04590075,0.02606034,-0.04086701,0.00276531,-0.03412328,0.02043725,-0.05600576,0.02363044,-0.0061534,-0.03667551,-0.0295552,0.05337654,0.03592076,-0.10584915,0.12229087,-0.01252654,0.01423222,-0.00085599,-0.06927663,0.06196734,0.05254412,0.01144867,-0.08991126,-0.01055144,0.0319887,-0.04788779,-0.02215317,0.00894852,-0.05550543,-0.06645265,0.03638856,0.01543897,-0.00510163,0.02392477,-0.04263651,-0.054614,0.02020833,0.08320186,-0.05137605,0.06926018,-0.02983095,0.03733553,-0.01449259,-0.00556291,0.02228887,0.03190592,-0.04505901,-0.01134107,-0.06360626,-0.02789477,-0.08296674,-0.0132453,0.01603872,-0.01677958,-0.05300373,-0.03113457,-0.06625083,0.02699099,-0.10955931,-0.04802372,-0.03135488,-0.06225096,0.04664617,-0.03924007,-0.03550611,-0.00204396,0.00929728,-0.01721205,-0.04324454,0.01345413,0.0252803,0.04078146,0.09633599,-0.01241367,0.04679345,0.06469975,-0.11701804,-0.11599626,0.0661583,0.02975594,-0.08091731,-0.0240954,0.05954352,0.01115569,-0.0109014,0.03998096,0.05032949,-0.06455533,-0.03636923,0.05431954,-0.05132202,-0.03508659,0.0316141,-0.01273686,-0.00350953,-0.08364215,-0.03476754,0.04101521,0.04286369,0.05668512,-0.0530923,-0.00687821,-0.00299885,0.00211028,0.06011254,-0.06931102,0.06227665,-0.0218453,0.01761093,-0.050287,-0.02005931,0.0004461,-0.04696277,0.00865489,-0.03620591,0.06196401,-0.03850272,-0.07236078,0.01555774,-0.02514391,0.00380919,-0.05960929,0.02827589,0.08550323,0.0717742,-0.11155611,-0.02363577,0.0518709,-0.01086612,0.00355513,-0.01922214,0.03597093,0.04469634,-0.02213387,0.01067895,-0.01573832,0.01940509,-0.08474728,-0.17456253,0.02684616,0.02726152,-0.01530661,0.09985741,0.00133972,0.04758747,0.03787711,0.05720741,0.01434227,0.05573769,0.04983451,-0.05869209,0.07887118,0.03904038,0.12705396,0.03943548,0.03178894,-0.01154777,0.02852457,0.01109628,0.09783942,-0.04556392,-0.02909266,0.00399709,0.02003573,0.15837696,-0.03191261,0.01767487,0.03379595,0.04303446,0.00063843,-0.03712034,-0.0539902,0.10877254,0.01048874,-0.03506277,-0.02247188,-0.02473478,-0.01346569,0.00379443,0.04225176,-0.04074008,-0.10618731,-0.03380661,-0.05459259,-0.05412109,-0.01355921,-0.04690939,-0.0237824,0.04220441,0.00733819,-0.07095368,0.03731309,0.00253018,-0.02610865,-0.01970175,0.02963116,0.00595277,0.03325366,0.00099516,-0.01220003,0.0384447,0.0041632,-0.0300122,0.01286714,0.01794516,0.07995,0.04100767,0.07233586,-0.02973202,0.08838286,0.0165109,0.02803827,0.08711374,-0.02465324,-0.00957688,-0.03009086,0.02522378,0.00708571,-0.01846382,-0.08430184,0.01195829,0.02535656,-0.00784281,0.00356783,0.04055035,-0.03527813,0.04115699,-0.04029723,0.02770359,-0.00385923,-0.06319207,-0.00357525,0.04725931,0.0400059,-0.23391506,0.03669023,-0.05137317,0.03591071,0.02260182,-0.00582002,-0.02950499,-0.01708767,-0.06668267,0.02013969,0.04171997,0.02362995,-0.00756813,-0.01604307,-0.01794766,-0.01101014,-0.02221928,-0.02709838,0.05275993,-0.00014548,-0.00733939,0.01554874,0.20345545,-0.0434036,0.05057446,-0.02318534,-0.02292464,0.00541912,-0.02750838,-0.03120905,-0.00588658,0.01569236,0.1371727,-0.04419394,-0.0146742,0.06874985,-0.0573989,0.0233083,-0.0137179,0.02323787,-0.03173713,0.03758965,-0.04104628,0.02495253,0.09919106,-0.03819543,-0.01276179,-0.10736836,0.05997709,0.05157977,0.00949659,-0.02671717,0.00710353,0.01515656,0.01970727,0.04342352,-0.03309207,-0.07105587,-0.01391417,-0.05476076,0.03351189,0.07673591,0.04009123,0.00395577,-0.01266665],"last_embed":{"hash":"2524eded22c615957c8e2a008ebe158082efa5ab224c7a5d0177b726f24e0fd4","tokens":329}}},"text":null,"length":0,"last_read":{"hash":"2524eded22c615957c8e2a008ebe158082efa5ab224c7a5d0177b726f24e0fd4","at":1743662844429},"key":"Top 10 câu hỏi phỏng vấn System Design và Microservices.md##0.3. Khác:#0.3.1. Trong quá trình làm việc, bạn gặp phải những vấn đề kỹ thuật nào khó?#{1}","lines":[76,88],"size":694,"outlinks":[],"class_name":"SmartBlock"},
