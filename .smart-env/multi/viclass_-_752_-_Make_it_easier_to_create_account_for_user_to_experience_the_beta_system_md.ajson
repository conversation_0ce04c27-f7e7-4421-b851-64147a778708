
"smart_sources:viclass - 752 - Make it easier to create account for user to experience the beta system.md": {"path":"viclass - 752 - Make it easier to create account for user to experience the beta system.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"9a10ee3446e2fc392fc417578f56698063b674d66fcfead4d03eb6a3935687e1","at":*************},"class_name":"SmartSource","outlinks":[{"title":"Viclass","target":"Viclass","line":3}],"metadata":{"relates":["[[Viclass]]"]},"blocks":{"#---frontmatter---":[1,4],"#":[5,22],"##{1}":[7,7],"##{2}":[8,8],"##{3}":[9,9],"##{4}":[10,13],"##{5}":[14,14],"##{6}":[15,15],"##{7}":[16,16],"##{8}":[17,17],"##{9}":[18,21],"##{10}":[22,22]},"last_import":{"mtime":*************,"size":730,"at":*************,"hash":"9a10ee3446e2fc392fc417578f56698063b674d66fcfead4d03eb6a3935687e1"}},
"smart_sources:viclass - 752 - Make it easier to create account for user to experience the beta system.md": {"path":"viclass - 752 - Make it easier to create account for user to experience the beta system.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.0085192,-0.********,0.********,0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,-0.********,0.********,0.********,0.********,-0.********,-0.********,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.0382789,-0.********,0.********,-0.********,-0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.0424684,0.********,-0.********,-0.********,0.********,-0.0725914,-0.0043263,-0.********,0.********,-0.********,0.********,-0.********,0.********,-0.********,-0.********,0.********,0.0041142,0.********,-0.********,-0.********,0.********,0.********,-0.********,0.********,-0.********,0.********,-0.********,-0.********,0.********,0.0118416,-0.********,-0.********,0.********,0.********,-0.********,0.********,0.********,-0.********,0.04587167,0.06126371,-0.08080308,0.05639295,0.01037747,0.02611698,-0.01491542,-0.0007329,0.06474768,-0.03404929,0.02611123,-0.01111952,-0.03954549,-0.04040876,0.05834105,0.0225479,-0.02459609,-0.04532554,0.05264462,0.0072836,-0.11071503,0.12387131,-0.04085366,0.02797846,0.03985429,-0.04102067,0.04832571,0.02341273,-0.01476396,-0.02441009,0.03936905,-0.01368794,-0.01211637,-0.01872471,-0.02320783,-0.04906119,-0.01200476,0.07175004,0.00427242,-0.00268875,-0.05323984,-0.04689837,0.01294254,-0.01837312,0.02892804,-0.02749911,0.05553558,-0.00709786,0.03029652,0.06253106,-0.00121962,0.06948552,0.05775165,0.01116367,-0.09632802,-0.03325402,0.02014724,0.01538826,-0.01164734,0.00051402,0.01233816,0.04981663,0.00664455,-0.01323813,-0.01692075,-0.03705565,-0.04145177,0.06107422,0.01937503,-0.05084067,-0.029607,-0.01835561,0.02840783,-0.02841198,-0.06085188,-0.0874282,0.00580584,0.02888021,0.05992027,0.06346861,-0.07180703,0.00299838,-0.06923865,-0.0748362,-0.0013132,0.0356749,0.03673436,-0.12867565,0.01260403,0.02811606,0.05140183,-0.04333086,0.03332836,0.01543648,0.00234762,0.00684591,0.10970446,-0.0022314,-0.0162651,0.01684642,0.03808163,0.06943711,-0.00003473,-0.03719108,-0.0050358,-0.00098299,0.03635847,-0.01790448,0.01418759,-0.02095314,-0.03158943,-0.03378439,-0.0570956,0.0285434,-0.08125927,-0.03141841,-0.02391668,-0.01240181,-0.01953862,-0.06070436,-0.00342009,-0.01855434,0.01026121,-0.0056684,0.02282428,0.01093544,-0.00660181,0.07402249,0.00890427,-0.05883935,0.09182243,0.03114314,-0.046639,-0.01661453,0.0322617,-0.02004386,-0.03153246,0.01619243,-0.04738794,0.03713847,0.07498744,0.0005706,-0.01604623,0.05280701,-0.02118262,-0.18895116,-0.01770652,0.03307129,-0.05407129,-0.06006056,-0.05133227,0.03725201,-0.00249863,0.03297646,0.00137445,0.16655299,0.01957899,0.00842485,-0.0119874,0.06062706,0.04962138,0.01057152,0.03358369,0.04087249,-0.00856374,-0.07987985,0.00559691,0.07708399,-0.04151771,0.00003922,0.03750212,0.14645363,0.00527118,-0.0022086,0.01809353,0.06128265,0.04484162,-0.00589001,-0.15427573,0.03131939,-0.02006981,-0.00595818,0.01547389,0.02522887,-0.02009249,-0.01799287,0.01979227,-0.00910202,-0.06278927,-0.01685678,-0.09822334,-0.02562127,-0.06747871,-0.08044723,0.03904133,0.06107949,0.01587405,0.08169094,0.06051948,0.00213008,-0.03434135,-0.04342601,0.01570135,0.0162087,0.09899204,-0.02214448,-0.02533723,-0.06195079,-0.02380696,-0.01616316,-0.00487404,-0.02809968,-0.03141086,0.04014377,-0.06465373,-0.04847094,0.07156008,-0.02345283,0.07532328,0.0740208,-0.03248323,0.0136934,-0.00526828,-0.02569829,-0.0317192,-0.0546862,-0.06225312,0.01376905,0.02345084,0.02706023,0.05247542,0.06862571,0.00605608,0.03056421,-0.02690799,-0.14261156,-0.04483447,-0.02137197,0.01150965,0.01846169,-0.01144245,-0.2334397,-0.03854984,0.0878204,0.10436385,0.05405594,0.01683074,0.1436262,-0.03271235,-0.0553695,0.01659659,-0.00568063,-0.00867828,0.08934543,-0.00259404,0.02794388,0.04604882,0.01531098,-0.02954791,0.04623282,-0.08717699,-0.00723072,-0.01061772,0.15775868,0.02738979,-0.00672304,0.05446068,0.0184526,0.03567864,0.06145244,0.08611081,0.02282664,-0.00402832,0.15246476,-0.05588053,0.03259053,-0.01054872,-0.0479922,-0.02036162,0.04646767,-0.05740247,-0.04410263,-0.02925051,-0.08741222,0.06217891,0.03016739,-0.01133029,0.04800629,-0.02921366,0.00814966,0.01986979,0.01310807,-0.031339,0.00222863,0.10373756,0.06835525,0.02654293,-0.00886914,-0.00350863,-0.00287036,-0.02259455,0.05592608,0.00951718,0.04742276,0.00691136,0.02564418],"last_embed":{"hash":"9a10ee3446e2fc392fc417578f56698063b674d66fcfead4d03eb6a3935687e1","tokens":195}}},"last_read":{"hash":"9a10ee3446e2fc392fc417578f56698063b674d66fcfead4d03eb6a3935687e1","at":1743662883072},"class_name":"SmartSource","outlinks":[{"title":"Viclass","target":"Viclass","line":3}],"metadata":{"relates":["[[Viclass]]"]},"blocks":{"#---frontmatter---":[1,4],"#":[5,22],"##{1}":[7,7],"##{2}":[8,8],"##{3}":[9,9],"##{4}":[10,13],"##{5}":[14,14],"##{6}":[15,15],"##{7}":[16,16],"##{8}":[17,17],"##{9}":[18,21],"##{10}":[22,22]},"last_import":{"mtime":*************,"size":730,"at":*************,"hash":"9a10ee3446e2fc392fc417578f56698063b674d66fcfead4d03eb6a3935687e1"}},"smart_blocks:viclass - 752 - Make it easier to create account for user to experience the beta system.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.042806,-0.********,-0.********,-0.0547301,0.********,0.********,-0.********,0.********,0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,-0.********,0.********,0.********,0.********,-0.********,-0.********,-0.********,0.********,0.********,-0.0276396,-0.********,-0.********,-0.********,0.********,-0.0547461,0.********,-0.********,-0.********,-0.********,-0.********,-0.0095833,-0.0502696,0.********,0.********,0.********,-0.********,-0.********,0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,0.********,0.0042247,0.********,-0.********,-0.********,0.********,0.********,-0.********,0.0580702,-0.********,0.********,-0.********,-0.********,0.********,0.********,-0.********,-0.********,0.********,0.********,-0.********,0.********,0.********,-0.02630729,0.0434747,0.05839159,-0.08035062,0.04944623,0.0077894,0.03027079,-0.01748318,-0.00138801,0.06571756,-0.04099814,0.02408241,-0.01700559,-0.03374377,-0.04035095,0.05644104,0.02495382,-0.02301848,-0.04700394,0.04928287,0.00877286,-0.11185155,0.12939759,-0.04229786,0.02288186,0.03504068,-0.03872906,0.04627541,0.02676277,-0.01605163,-0.02520575,0.03926331,-0.01099259,-0.00995228,-0.01133432,-0.02283188,-0.05098045,-0.01384316,0.07752801,0.012049,-0.00515539,-0.05664356,-0.04965861,0.00502775,-0.02168033,0.02455676,-0.02977355,0.05657814,-0.00385877,0.03552146,0.06338036,-0.00164629,0.07314257,0.05350259,0.00441527,-0.09469379,-0.0373527,0.02142951,0.01750472,-0.01257082,0.00159188,0.00416998,0.04566299,0.00248987,-0.01044204,-0.01403728,-0.04283856,-0.03860689,0.0610989,0.02118428,-0.05430649,-0.0285616,-0.01967526,0.02606418,-0.02134531,-0.0632555,-0.08534817,-0.00062851,0.03116979,0.0518729,0.06308819,-0.07459463,0.00609023,-0.0710775,-0.07773566,-0.00612299,0.03733377,0.03447831,-0.12887549,0.01619958,0.03800365,0.05132763,-0.03845029,0.03415027,0.0191191,0.00404136,0.00296082,0.11045025,-0.00594444,-0.0191043,0.01905119,0.03224833,0.07251094,-0.0041619,-0.03486554,0.00020465,-0.00290381,0.04605677,-0.014128,0.01700812,-0.02356935,-0.02867461,-0.02351108,-0.05498191,0.03207066,-0.07594489,-0.03425704,-0.02218012,-0.01091258,-0.021364,-0.05950371,0.00346481,-0.01658931,0.01482979,-0.00807798,0.02746182,0.00722333,-0.00400233,0.07227945,0.0064468,-0.05474463,0.08897873,0.03481039,-0.04354653,-0.01577388,0.02956861,-0.02358865,-0.03367881,0.01332507,-0.04854005,0.03541036,0.07531065,-0.00335403,-0.00825574,0.05799573,-0.02581518,-0.18968235,-0.02001614,0.03423756,-0.05634776,-0.05104296,-0.04914007,0.03746285,-0.00615297,0.03224003,0.00177297,0.16982083,0.02497257,0.00471934,-0.01213328,0.06401106,0.04814187,0.01373355,0.03399612,0.03516815,-0.01320452,-0.08668628,0.0048772,0.07889434,-0.04294427,0.01050945,0.03745497,0.14636296,0.00495493,0.00538415,0.01659867,0.05863704,0.04708631,-0.01470858,-0.15357617,0.03087826,-0.02370377,-0.00411972,0.01065222,0.01847612,-0.02223781,-0.01409431,0.02029822,-0.00735898,-0.06625556,-0.01015818,-0.09808091,-0.0204155,-0.06555904,-0.07841424,0.04066566,0.06252396,0.01458168,0.08323391,0.06146223,-0.0016862,-0.03603202,-0.04275047,0.01402099,0.0175633,0.09394854,-0.01970047,-0.02499839,-0.06566451,-0.0217017,-0.01937398,-0.00233895,-0.03141239,-0.03265075,0.04551288,-0.06727705,-0.04721979,0.06776293,-0.01765345,0.07589608,0.08372914,-0.03547412,0.02035104,-0.00146477,-0.03207009,-0.02755729,-0.06028636,-0.05922544,0.01147841,0.02307774,0.03291742,0.05240325,0.07538615,0.00866767,0.02485922,-0.02589939,-0.14390494,-0.04121551,-0.02487478,0.01330643,0.01532477,-0.00721063,-0.22473276,-0.03397667,0.08450275,0.10651698,0.05075634,0.01495499,0.14307772,-0.02924292,-0.05128934,0.01495853,-0.00769125,-0.00990479,0.08612612,-0.00569587,0.02656257,0.03746162,0.01501854,-0.03001754,0.04394741,-0.09403784,-0.00718481,-0.00998466,0.15821551,0.02300625,-0.00880467,0.06076593,0.01818345,0.03666975,0.05812909,0.08587017,0.02023511,-0.00066042,0.15281312,-0.05479758,0.03709716,-0.00615983,-0.04605507,-0.01743657,0.0353675,-0.05658268,-0.04184193,-0.03691783,-0.08812775,0.06059789,0.02552044,-0.********,0.********,-0.********,0.********,0.********,0.********,-0.********,0.********,0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.0149734,0.********,0.********,0.********],"last_embed":{"hash":"9279112fdef9b9873e3bc2ddf8492e8a0057ce88ec029564652a74a6ab4c5815","tokens":177}}},"text":null,"length":0,"last_read":{"hash":"9279112fdef9b9873e3bc2ddf8492e8a0057ce88ec029564652a74a6ab4c5815","at":*************},"key":"viclass - 752 - Make it easier to create account for user to experience the beta system.md#","lines":[5,22],"size":695,"outlinks":[],"class_name":"SmartBlock"},
