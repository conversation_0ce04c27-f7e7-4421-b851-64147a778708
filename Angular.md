---
tags:
  - angular
  - components
  - concise
  - design
  - english
  - frontend
  - frontend-development
  - libraries
  - management
  - quick-reference
  - resources
  - state
  - tools
  - web-development
---

# 1. Resources

- Tìm hiểu về Change detection trong Angular: https://viblo.asia/p/tim-hieu-ve-change-detection-trong-angular-djeZ18EjKWz
- Understand Angular detection: https://danielwiehl.github.io/edu-angular-change-detection
- Dynamic Module: https://viblo.asia/p/dynamic-module-3-khai-niem-can-nho-trong-nestjs-register-forroot-forfeature-PAoJen21L1j

# 2. Libraries

- rx-angular - Reactive Extensions for Angular: https://github.com/rx-angular/rx-angular
- Web APIs for Angular: https://taiga-family.github.io/ng-web-apis/home
- i18n: https://github.com/Romanchuk/angular-i18next

# 3. State management

- Signals
- RxJS
- ngrx - Redux style: https://github.com/ngrx/platform
- rx-angular: https://github.com/rx-angular/rx-angular

# 4. Tools

- Hot Module Replacement (HMR): `npm install @angularclass/hmr - save-dev`

# 5. Design components

- https://github.com/SAP/fundamental-ngx