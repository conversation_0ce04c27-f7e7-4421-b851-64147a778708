# 📁 Category Index - Chỉ mục Danh mục

Đây là chỉ mục tất cả các danh mục trong workspace.

## 📊 Thống kê danh mục

| Danh mục | Số file | Tỷ lệ |
|----------|---------|-------|
| General | 127 | 58.0% |
| English Learning | 22 | 10.0% |
| AI & Machine Learning | 16 | 7.3% |
| Programming Languages | 15 | 6.8% |
| Frontend Development | 8 | 3.7% |
| Business & Management | 7 | 3.2% |
| Cloud & DevOps | 6 | 2.7% |
| Backend Development | 5 | 2.3% |
| Interview Preparation | 4 | 1.8% |
| Algorithms & Data Structures | 3 | 1.4% |
| Travel | 2 | 0.9% |
| Lifestyle & Fashion | 2 | 0.9% |
| Personal Development | 2 | 0.9% |

## 📂 Chi tiết từng danh mục

### General

**Số lượng**: 127 files

**Danh sách files**:
- [[25 phương pháp giúp bạn ngừng overthinking]]
- [[ARBO]]
- [[Bài 2 - Prompt Engineering, RAG và Finetuning]]
- [[Bài toán liệt kê]]
- [[Bảng màu gradient đẹp]]
- [[Các bước xây dựng ứng dụng]]
- [[Các câu hỏi phỏng vấn Laravel]]
- [[Các loại áo nên có trong tủ đồ]]
- [[Các loại giày nên có]]
- [[Các loại quần nên có trong tủ đồ]]
- [[Các loại trang phục cho tủ đồ]]
- [[Các loại vải phù hợp cho mùa hè]]
- [[Cách giải các bài thuật toán]]
- [[Cách làm sạch và bảo quản boots]]
- [[Câu hỏi phỏng vấn]]
- [[Cấu trúc dữ liệu & giải thuật]]
- [[Chiến lược backup dữ liệu 3-2-1]]
- [[Chỗ mua đồ]]
- [[Clean Code notes]]
- [[Compiler]]
- [[Computer Science - Khoa học máy tính]]
- [[Concurrency - Parallel - Asynchronus - Multi-threading]]
- [[Công cụ học tiếng Anh]]
- [[Cross-platform]]
- [[Cân Bằng Giữa P&L và Chất Lượng Dự Án]]
- [[Dagger & Koin]]
- [[Danh sách kiến thức chuẩn bị để phỏng vấn]]
- [[Data Analyze]]
- [[Design]]
- [[Development documentations]]
- [[Diagrams - Vẽ sơ đồ]]
- [[Done in Troodonlabs]]
- [[Drawing 2025-07-29 11.46.50.excalidraw]]
- [[Du lịch Huế]]
- [[Du lịch]]
- [[ElasticSearch]]
- [[Error handler]]
- [[Fastpass]]
- [[Flutter]]
- [[Fullstack - Full-stack]]
- [[Game]]
- [[Git - Github]]
- [[GraphQL]]
- [[HTTP - HTTPS - TLS - SSL]]
- [[IaaS]]
- [[Inceptionlabs - Viclass]]
- [[Inceptionlabs]]
- [[Kafka]]
- [[Kho chung IT]]
- [[Kinh nghiệm deal lương]]
- [[Kudofoto]]
- [[LM Studio]]
- [[Laravel - Eloquent]]
- [[Laravel]]
- [[Layered Design in Go - iRi]]
- [[Linter]]
- [[Linux]]
- [[Logging]]
- [[Lời khuyên]]
- [[Luận bàn về async]]
- [[MCP - Model Context Protocol]]
- [[Microservices]]
- [[Monitor server]]
- [[Mục lục thuật toán]]
- [[NAT port]]
- [[Nest]]
- [[Network]]
- [[Nén file]]
- [[Nginx]]
- [[Những thứ đã học ở Grab tech talk]]
- [[No-code - nocode - low-code - lowcode]]
- [[Node.js]]
- [[Note câu hỏi phỏng vấn Laravel]]
- [[Note ebook Thuật toán của thầy Lê Minh Hoàng]]
- [[Note lại từ Huyền Chip]]
- [[Note mẹ]]
- [[Nướng thịt ở nhà bác Cửu]]
- [[OS Scheduler]]
- [[Ollama]]
- [[Operation]]
- [[Optimization]]
- [[Phỏng vấn JV-IT]]
- [[Phượt]]
- [[Promt]]
- [[Quy tắc chọn thắt lưng]]
- [[Quy trình làm việc]]
- [[Râm Generation]]
- [[Research websites]]
- [[SAP - Systems, Applications, and Products]]
- [[SEO Content]]
- [[SaaS]]
- [[Security]]
- [[Severless]]
- [[Software Engineer Roadmap 2025 - The Complete Guide]]
- [[SolidJS]]
- [[Solution sao lưu lịch sử chỉnh sửa]]
- [[Solutions & System Designs & Design Patterns]]
- [[Some shit I need]]
- [[Sống Platform]]
- [[Target of users in a workspace]]
- [[Tập thể dục]]
- [[Terminal UI - TUI]]
- [[Testing]]
- [[Thanh toán chuyển khoản ngân hàng]]
- [[Thống kê tủ đồ hiện tại]]
- [[Tools]]
- [[Top 10 câu hỏi phỏng vấn System Design và Microservices]]
- [[Trải nghiệm]]
- [[Troodonlabs]]
- [[Tự vệ]]
- [[Ứng tuyển]]
- [[VPN - Proxy - Firewall]]
- [[VPS - Hosting]]
- [[Viclass - 514 - Synchronize mouse position & mouse shape of the presenter]]
- [[Viclass - Load classroom coordinator states]]
- [[Viclass - editor.geo]]
- [[Vietop]]
- [[Windows Tips]]
- [[Wordpress]]
- [[Work]]
- [[Working day of users in a workspace]]
- [[Workspace]]
- [[ngosangns - home]]
- [[viclass - 752 - Make it easier to create account for user to experience the beta system]]
- [[viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow]]
- [[vocab]]
- [[Đi du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu]]

### English Learning

**Số lượng**: 22 files

**Danh sách files**:
- [[English with LLM - Câu bị động]]
- [[English with LLM - Câu gián tiếp]]
- [[English with LLM - Câu nhấn mạnh]]
- [[English with LLM - Câu điều kiện hỗn hợp]]
- [[English with LLM - Cấu trúc câu phức tạp]]
- [[English with LLM - Cấu trúc câu]]
- [[English with LLM - Danh sách 200 từ vựng cần thiết trong TOEIC]]
- [[English with LLM - Irregular verbs]]
- [[English with LLM - Loại câu]]
- [[English with LLM - Mạo từ]]
- [[English with LLM - Mệnh đề quan hệ]]
- [[English with LLM - Mệnh đề trạng ngữ]]
- [[English with LLM - Số ít số nhiều]]
- [[English with LLM - Tense]]
- [[English with LLM - Thành ngữ]]
- [[English with LLM - Từ nối]]
- [[English with LLM - Đảo ngữ]]
- [[English with LLM - Động từ nguyên mẫu và danh động từ]]
- [[English with LLM]]
- [[English]]
- [[Kho tài liệu sau 3 năm học IELTS của t - phần 1]]
- [[Note TOEIC Mỗi Ngày]]

### AI & Machine Learning

**Số lượng**: 16 files

**Danh sách files**:
- [[6 Chiến lược Prompt Hiệu quả của OpenAI]]
- [[AI - Large Language Models (LLM)]]
- [[AI support for coding]]
- [[Airblade - AB - Air blade]]
- [[Airblade]]
- [[Blockchain]]
- [[Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai)]]
- [[Cách đặt câu hỏi cho ChatGPT]]
- [[Domain knowledge]]
- [[LLM promt engineering]]
- [[Machine learning - Deep Learning - AI - ML - DL]]
- [[Reflow, Repaint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals 2]]
- [[Reflow, Repaint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals]]
- [[Roadmap học tiếng Anh từ ChatGPT4]]
- [[Tạo video bằng các tool AI]]
- [[VPC - Virtual Private Cloud - AZ - Availability Zone]]

### Programming Languages

**Số lượng**: 15 files

**Danh sách files**:
- [[C - C++]]
- [[Defer - Async - Inline, cách browser thực thi JavaScript]]
- [[Fresher Java Interview]]
- [[Golang Scheduler]]
- [[Golang]]
- [[Interview - Senior Software Engineer (PHP, Javascript)]]
- [[Java Microservices]]
- [[Java Spring]]
- [[Java]]
- [[Javascript - Typescript]]
- [[Khóa học Golang scalable của Việt Trần]]
- [[Kotlin]]
- [[PHP]]
- [[Python]]
- [[Rust]]

### Frontend Development

**Số lượng**: 8 files

**Danh sách files**:
- [[37 Tips from a Senior Frontend Developer]]
- [[Angular]]
- [[CSS]]
- [[Các câu hỏi phỏng vấn VueJS]]
- [[Frontend - Front-end]]
- [[React - Next]]
- [[Top 50 React interview quetions]]
- [[Vue - Nuxt]]

### Business & Management

**Số lượng**: 7 files

**Danh sách files**:
- [[Business]]
- [[Freelance 1 - Dropshipping]]
- [[Freelance 2 - Justbijay]]
- [[Freelance 3 - Spa]]
- [[Freelance]]
- [[Management]]
- [[Product management]]

### Cloud & DevOps

**Số lượng**: 6 files

**Danh sách files**:
- [[AWS]]
- [[Azure]]
- [[DevOps]]
- [[Google Cloud Platform - GCP]]
- [[Kubernetes - K8S]]
- [[Lộ trình thi cert AWS Solutions Architect Assoc]]

### Backend Development

**Số lượng**: 5 files

**Danh sách files**:
- [[Backend - Back-end]]
- [[Database]]
- [[MongoDB]]
- [[MySQL]]
- [[Postgresql]]

### Interview Preparation

**Số lượng**: 4 files

**Danh sách files**:
- [[Fresher Back-end Interview]]
- [[Interview - Phỏng vấn]]
- [[Interview Senior Engineer]]
- [[Tổng hợp các nguồn ôn luyện thuật toán & Coding interview]]

### Algorithms & Data Structures

**Số lượng**: 3 files

**Danh sách files**:
- [[Algorithms & Data Structures CheatSheet]]
- [[Data structures & Algorithms]]
- [[Post score algorithm - Trending algorithm]]

### Travel

**Số lượng**: 2 files

**Danh sách files**:
- [[Chuyến Du Lịch Công Ty Inception Labs 2025]]
- [[Du lịch Trung Quốc]]

### Lifestyle & Fashion

**Số lượng**: 2 files

**Danh sách files**:
- [[Fashion - Tủ đồ - Quần áo]]
- [[Skincare]]

### Personal Development

**Số lượng**: 2 files

**Danh sách files**:
- [[Life]]
- [[Softskill - Kỹ năng mềm]]

