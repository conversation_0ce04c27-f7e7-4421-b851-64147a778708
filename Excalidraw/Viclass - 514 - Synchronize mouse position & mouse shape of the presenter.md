---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data
## Text Elements
ClassroomCoordinator ^tMxB8JuD

HandlerAttachmentManager ^UsGmIKtO

CursorManager ^ReABHyFG

cursorManager ^zbPwNdhJ

private hams: Map<string, HandlerAttachmentManager> ^Y4Ij2jPR

Mouse awareness: ClassroomCoordinator
 ^6SnadPMB

Classroom viewport pan: ClassroomPresenterTool
 ^DOo2jAZs

Classroom boundary change: 
- Sender: GeometryEditor, crd.tool.ts
- Receiver: DefaultBoundedGraphicLayerCtrl ^6pnEweq4

Classroom doc state change: UpdatePropTool ^ms7f926C

Buffer - Interpolation ^xHign8e1

DefaultViewportEventManager ^EKoWuYq0

NativeMouseListener ^VFhbbbRY

LayerEventListener ^r6clg9Tp

Layer phát ra mouse change event ^0ELEdmZv

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQA2bQB2GjoghH0EDihmbgBtcDBQMBKIEm4IAEYkgGF4gGkADjYAcXUAVRaEAA0ARwBJAHUANQB9DiNUkshYRArA7CiOZWCp

0sxuZwBmAFYABn5SmG5dveSdpMbKnh3DyAoSdW4eeIPCyEkEQmVpbgAWPjvCDWFbiVBvaYQZhQUhsADWCBqbHwbFIFQAxJUEFisWtIJpcNg4cpYUIOMQkSi0RIYdZmHBcIFsniIAAzQj4fAAZVgqwkgg8LOhsIRg0ekmedyhMPhCB5MD56AF5SlpJ+HHCuTQlSlbAZ2DUx21ewhpRJwjg/WIWtQeQAulLWeRMlbuBwhJypYRyVgKrg9izSeSNcwb

cVIbMwVt3gBfKVhBDEbjVXYAyrxLamyCMFjsLhoLY8JJSnOsTgAOU4YmTAE4a5U/lskvEW17mAARdJQRPcVkEMJSzTCckAUWCmWyNoK0yK71K5RpAFlMAAhRoAKSE7bxpUjFW7mCgEDncbn4chC/QhAAilBlAB5Gu4ACalUIi6SxAAVpVNEkdpIAAqhA7jM8BgsCpCwlQJ7vA6QJCHAxC4N2SbakkhYmvshaVI0jRSkQHBwm6Hr4ARbBEj2aB9vg

YSFKeJTnmUaHoFAy5rpu25SnuNJYEeUobGg2w3NojQ7HWWyVDWfw7I0Jr4UCRqoLsNbaDclzXLcQIPMQTxoDwlSVMkUmNBmjT1q8lQ7PEUqfN8vz6VpkIgoqWbSiKiLIqiGI4tiSCDoSxJBhSXnUqx5AcPSjJZPxQLspy8qKlCyIqkCwqymKukSvp8YygiiUQcqSaqsI6qasmur6oayYmlK5qIVaU7wZCTq4C6LHup6QLesQvoSLglSBsOxAhjan

VkWlCBUag6bxEkfx7PEAIlkwZb5jNSQ6kCpZ5pWHDVmgMmNn8VlbApF4dl2000QOQJDmSxBjhkMVNVKiHIahyYYTwWF7DheEEd6xFoON5GUSxN0INxfEVDU+ChMwsIGEiqKOBwKHeaqlCATDEhwwjSP6CjpBoxjaKOpwUBcoQRhgj9FPZAAYm1HJKU5u58QAgkQyjrRAYjZEwLI5lA5gENz3x8/oJDEKsUp6NkuDekwroSNUdRNK0HRdH0QxjBML

Kot83oEDjh6w/DoaE8TpNQJjQK4EIdsAErhDTYIwkIUNAoRCAABJfD8R7ampOz0YcTGXhA14EM+9b1IQnZ7FyMBfs4pg7H7mjFUCPHoAsSygiygm8FKSl/I0fznBpNxSjpenKVJtmBw5qB/Ek2h1l33c91KLlgm56UIpS3kSJifm4gFRL1eSI9hdAEVRUysUtRy3K8oVKU55CQ8IJlDeAjveVyhvFRFUNfiSKNFVAnqhLVcabn1Za1r5M1pSte1J

FdRePol8CPAL7BnKmgc8YE5gFljPGKaLEaxbAzHAvYskVq5k4NwWS51Sg7QrFWOmfw5pJB+tJNsnZgifWov2b2kJ7qjnHC9fIZ45yQCju0ZgLR9D9HqFAe8oFoDgX3DDE8dxZwzmYSxCAAAZP2jNMDtAAAo1GGIuLkzAazEBrK+QgAAtbojNcjCPARBRk0FjwzgYtMMBYiKg3jvI+F8b4Pzfl/P+ICIEDF8IgegYxbAYJmLgm9JCKFpoph+kgv6B

kAY+yBt/CakIUTg17JQ8OhRI7iNYewzh3CWR52gDDASJwzh7FCTWWSNwpI7EkofI4/wayJHUlcWu2lxTPHiHEEpNY5KNC2H8WpexiFAjskHZ47NID924IPY+c8fIT38ndQKM8QpUn3IvBky8WTxXXgqTegpcoeX3tlUuaVj4FTPlvC+ZVQw30hHfA0sAapP1JC/V6cVnQIFVqgUG3U/5+h4MMIBI0QEfNItA4JPAeA1kWvEQyblsHrX2FU7Mq1dq

4O4EkPY6Y9iNAwpg5hl0yHXUoYOYaT0Jw5DfgEj6wTvq/X+jiiAhFgZAp/qUeJCIIaEtzrjdAftrCyyYJzGQhJJCksXNYaIQssYUDNsHblvLgikAFVEbAwqYqivRsoCVcVKbU1ps8NyrJKbM2lvgNm0NDwS15hUAW3ZybbSYKLdwFqpYyzlkCBWURlakHedHWO8dE4IGTqndO9BM7Z0NqQY26N8DSoqDy8k8rFVCpFWKjVtrnJOzYK7Vguq0Ceyo

Sy5WAd7IyqMjcZJjFuriMaJgegzs4BbFIDsTmfxnD0HaPQaE/RGasnLCubJ/CJAFzGfkw6xZFLcHiDsIy/4a4jIgPXA5Uk3KDNbvgvuyxXK7NlFMsevlJ5zOnsFHd4U6SrJiusteJz+RnK3aKZpOUjkeSvUqG9QI1RX0BVta5VU7mPzqo8xq5KXltTeR1YFXzer/1wFsf519QFzg8VGKBk1gn1h4PgvpLwUFrVRa2O1qCOB7QOjNa4pl0ViT+CQq

67LaL5vxMSuhk4GEzkscxCortOYrj9jARmLReE5IPEeIRjDRFsYkFImR8jFHKNUeozROi9H8YHV4qCPjTHTBjP4hCgTyEbUwmE2lgMiIxLBmyxJtHy0iPnOIjjXGeN8e4sp3J5sR2oDRckE0SCkgdK2FsDpfxKPjrQC2OI9TNJ13vTNAE2hxIdMrn9TClTm7FuGeuouaAJkeWPRAcevkWQEkPcNbLtJIpnuZI6S9p9r07MfRlSLCL3KymfclGrkJ

31wZmpVe+v6Zq1SBM/QDaB7SOlee8z5v9IN+j+LBwF43SgJhYj9XY1RZKLWw3mCd811s4P2mCCFOweBNisoFi6pCEC6chkSh6JL6FDffpAd6QSWIhJpREulDKTM+womZihtGzUyogDUEQghSBqvFWm0o5ApVcsB8D1EYPU3rO1e7PVDMoBGtZug/7TqrUxU1ZCEWYt8A44kNLYgfKWTuqVhqL1Vaa11obU2ltbaO1QC7T2vtuoI3+FNjDoHLB4cp

vx6UR2Ls3Y5tQHmoz/sW4ltDpZ1JFQJOyIUUolRaiNFvgU/o3OTnvFUFc1ZNp8QuklPEl08FZcJ37Bi0WALhCsWvHRRFrK4zRJFMxRRk6hYaybWS0M7U6Y1JnQhQ2bz3Sik2Qdhuget7PJLN3TM/L8yj2hWWae6K5W4qVa2ac1r83j77MlLV/KVWX358gO1z9XXblKUqH1yEA3X53ZGyBsb4GJt9S8TWGblz4MzkQ88ZDO8YHoN2HhRo4KYVIrQc

aL9WCZ+EZRdqcSFdKi7AuFR/FNHbrUIY89JjLftOUue99LFYKDK4Xe9EkGHeWXfYJX9oEcA2DejJUNuc04ZxZhKHsOc92wAv9phnAzg5Ifo8IdgAt19wU/cmFnBDI6kQ90V244EFoXh/8tM4lQgoAkR9BpYZBEw5FX9mRb9mUBAohSAoAVwepvRlBuAwEMBGM2c6da161G1m1W121O1u1e0dw2QKIhAbQzhDI0UDIixJ0Sl5p8EcVIBlBcA4Bkxg

8/pJ0xC/M6wylhE2RCBMBCDiCZVxph9SgshiBqDyRaD6CEN0hSVvUY58A45KgE4k4U404M4s5t4WoBChDtAkESkoVSMDsWwZIkgx0B85CFCQ4Kk9gMJfNMUbg6w9gAtND2QdDiAiC38YlDDyDGQoBOZVMKBPhcAwMyCMByRcjoICjxF9cWQgghwKBH86IShzFFcJAbEHwnxNFHEfw/wAJgJ+1PFIITFDdFpRJDsoVsU5pCxmwrdDorgEhalukMFu

kwCXcG5cJVJJ94EUxTJvopiBlZcao4gdhZIWxxJLgzJGw0tN0S949R50Bcs/Jk9CsHpisVlM8V4P4c8kpz448i8H0j4n0y8WtUo2tSoP0+9Otb4f068G8zQANm9bR7s2RRsijYl5xvl+pOZe8wwEM84eBMioRR80ADsTIFpultt1oxJySiM6Yp0/MloLjuo8Vzt6i6MIAaFHomDnlIRHtdMQlz8wVDJIk4kb8mVUTIBWUWSpQX838pxP8mEf8wA/

8ZwACgCSh4DzIRjNjxidio9RF4CikYtjiJCzizpGwMDphETLYcCDB8DUI0iSDRTcpsjTC0Y6D+9IQrCYobDfUHD/VA0XCQ03C+CDVsBBDNhCkwVsJMw4F68pIXgMJNCwiThvCoV8EWx0wp0Ok+kkjtDdD0jSD8B8TjCXTzD3SjCmDvV1YGhmg2hJBOgegBgRhxhJgkjPDwzkhMw/Mjjji/MukySEMky0AQjV4Uj7T9DSJ8ToRsiyifEKjPsPTSi8

i5z+o8jqj8BaiWSFdK0KgjBNA5EKByxiBJB1w+iIJBNi4vothpjUALgO4Z0Gk50F1kwFpvC6w8JSTEtrh/dW5GlnIY9xk49st7j91qEU8is08aRXi1kKsEpATvjrjfjDl/ims4LX0QTL4Ot59IAbkH5esHkLRBsETW8v4CyvR0SvFOc31hoOs5sBBCTlIixgj4g6wTsF8CMvoqTl9UAzdfd4Eikt9mSd9WT2SbtD8iLj8ntLzQlsI3sjNGVaL6UH

8hL/srU4dQchcIdK9sYYdQyBd1L1VhdIADVsgdU6Z9VDUWYTUsdOVzUeY+ZghWR3jEVKCicSdWI9RKdKZqcVYUSudI1edzYJBdKQcEdDLgQM0s0Udc1SAvZpci0A8Zp5dGiI5tyJBnw/h+gvweAvw5FnZTyBEXMgQS4zpry1Dq4HyViDkDs7ypJXgAsawIl/xvyZVfyRd/yMtAKIK7i91ZlQKnjZ4uqF4M9oLs9YLc9qtgSC89l6s49mt4L0KLkb

QsKIAcKet698KGp4ThtgMSLHSIMu9gQagsT5z5t6L0MFopI4E6VYVnhrrF9qSTgyNfcIUBKLsOU99rtOSgNuSdMqV9Mr9giDs5KTrxSlLzNd8OZAr0A4AI16AglUBJA2p5BUBRU4AAAeYUWg6gVAONPlBVQVZVZNAy0gAAPkDG0qhogBhsIDhu7ARqRrQFRoxphCxpxrlX5QJpVWyFCtJqRxMqit4HMqZkstNRspyLsoqAcqcoYHtVcolppA8vli

8s9Xb2KKNh52jRh2ptpoQHpv0GRqZsxuWGxtxoTU5qJvBzJr7givFw9hitZN9nitblLTDmSpSVSvQHiC5HRlSMXEoojCc3PNc2cCuESGYqQSOm2J4EpKC1QC6SrkuGCLCT+nTFMkqpOAhQ7N9ysguDGKbj2JSyJLcjGQ6uuKAp6seKCnAoTxPVKzeIvTGq+LQqmrq1dz+JbtL3GvL0msr1BMwpr1wvWv/QIq2sRM/lAxBrKHIuBBHGOtIpQxYjTK

uGkN1LYpw30ibE4t22TDBVMlGMn1eslLun31JS5NKB5L+vEPminTEmWo+3nriTBt+whpmBh0XGEDCFQEoEz01DQHxitjYGRkAZJhNjtlIAAB0uBJUY0JB37BDdbv6mRf7UB/7EZAGiZgHbZURIG+aqYBb6YtVhbjVRaIwuZ5b0ApbhZZbHVyHoBFa3VlaadVaxSVrucTZNbKa4HP7EGshkHUHrZMHQHsGoGHYbbs07bYqokNQna5cy03aK0LxxF2

x7w2BsrOYtEdcA7+ig6irNgrhVJrh19mwTJQ7ryIVEgelCFoU+l/wGx07R0q44i0UZJzJK4mw50V0ZV9hLjY8y7BrgLerSgCsq7njBqSsl5z0YLNkm6K9Gs7026kKO6T4u6gT3De6MLq9ITutoSNqnlvqP5kTJ6eoDrcBGY569qR9ppfcp0LhzdyTngwUt7iMp9CFx9V7cUzs3qn8PraED939xKfqT8pLmxr7cIp1gaH778Eln7WTzy8ZLY0GDBU

AzAEAKAX9KDUAGQOA/6FnCY5FAgwhBZSBAJAH8AcHoG+ddn0HlnCBVn1moBNnrAdmCZ0H9nwg8djnTnznCG8GJcCGPCiHMciTsdaHKGUEHVxZaG7YFClbFYVbfLb42Go0YH0B+HrmVm1nUQHmtnnmAGDA3nDmbUTnkRvn00xdxHuApcpGZdC7Eq5GwAmiPaIB4g4AOARw6jehptHNtG8ldGhJ9HtBDGmwoU4t0wzH/xkh/w8IMwEj0M8NIQnzDpo

7vC6SHcanWkPH9ihy50S7wROqa6csK6p4QmBqDXwmytpaNk5rm7yDpqEmGtd5rXYmq9wTlrVqcnh7Nqz6jLCnJnmFp7cAHMqKHoaK786LpofoDtukzoSr8N17eBxm43kVt7tQsV9gJ8KlD7lLj7Pq+nvWIAL7T9Cx+TL8hSC1jM/XFLpnUBLsxaLYXmln2TGQYBUBlUQQEA0BIHnBUAuRjCmA0AugDBztSAYARxHAwHsbsASZtA7ZkQZ3mAu3UBX

YxAab+3UBOw+wPQqDhpEwWhyA4BJBzAJFcAYAmAagYR8BybodKa0XG3hpm3W3EblgO3UBF3e2fRSAB2EAh2YRR3x3URJ3p3Z38B53F3l2vgcw0AN3HZ8Bt2Hpd393D3sBj3T3SBz3SBL20dTLUcfmMcrLgWxa3L6UEBHKqGXKaHJZ9x6HIQqd4XJ71b2GUXAcrm72HoH223n3O2XAe2+3P3UBB3Mhf2x21AAPW2gPTnQPuPwPV2+PoOt2Vwd3iA9

35CkOUOz2L2WRRdM1bbKX7a4qtW6XXaGWUrFGKh9akhWQGr4gjruWzzeXIQS4Q6fpBX4sszbG+lWLIBoS4FtA6SZJrgIUziGtFW24ZJzheKp0oi0waxmrUto90s9W/GDWAnK6FkXjhrInRrontke64m94ZrrinXcuXWlqB61qYTZC4T83x7mGyLJt+o/ZymFKFtUVEFvNZX6ntQTommwQ0VXHkCs3wbhKT7bsBnz7fqi2iwKMYDCwJmKmpmfsa33

rIaAdb39BUBiAKJUApy6aOONU0B2hfr9m9RiXMO30KbVuWP1vNvsBtuohdun39vUBDvKVju4BTvcHsP9Ihb0cRbrLSHbLKOJAwX8MIXicoXqPShaOmGEXrkkWArLuG3rutudvda9uX2Xugk3uPvrbyWBaqXhTpGDOXatzTOJBrwYB1x6hVGag/Y4RsB2gawKJKgJFmAvx6hSBnx8rB0EBFhh0+XUAQ68JtAFosIGw4FJJDJrzmwdgYs+kilfdMUJ

j2n51Is5JVJsIAQfp0xzGYuC6Er9hZf4Fz9TiAQOkGtdXMtt1/GjWD0TXFlbihq66RrV5G6cu0m8vEKHXjlULnW+7Mnv1sn7lPW8mj8WpfX5v/X6uvF+hymGC85owNMQVnt25RDzeVebr9IY6Cd7quKxISkgjMxBuZmrtenT78mHsJvhmKNIUMw5uFKJShLSfrMKhMA/ZJZGhsRufWJ7P1hNgJi1JNoKlukMJrJJJpfnOKlTIoUKlDfPPVf7Wg9Y

sxJltrJWqPgDP1/gR2rEvkLh4bek9jW0uwmoLMvXfsu89cvd4vfZrffiv/fXWyuPX+squK+kS29Ye0To/gQTySoQ3Zsw2BJKlNNwOwH0k2s+XgHrxz4EYHqRJVbAdhYrF8lu3TIJiNzErbVBmkldCJMTmj4IMMw5cUiKQb5P1kBL9ZzADhXBCBWQrIJgIL1QD9AjmL+eGKLDQQXNKalA6gbQO7YMCbUTAlCBtiw74MfueHEhityI4g9oBYPIjtC0

8pwsYe9HeHhwwoFUCaBpAOgTwKYB8CWBIjMltpwpbRVJGhPGlglRJ7yMrMViCQCOGp6DAhAz4XoAGFs4FVpaJcOMqJACxHEzi4kaOlAOqRoA5IZwLXuZGCIh4IUV5JpPa2ki+dzcRSOVn0ncaxdHIPjACkl0d4pcj+qeM1qfyzzn8iuHva/gVz37JMYm9/DJo/yya15g+L/EetVwj4KVimUGeoE10AEtdDo6KJOpXAz6L5uADVHrgcQBClIGshAJ

kl0zIEiUvqH+FjEwjEzoAVwEiQCO0EqBch4ggwP4DAEIBbB+gmgD9OWC/AcBAE7iHJFUWEyTDRMUcHgO0GIDtAdgLQSKPrTEA1BNAgwRcNeBrBzCHBUww4auVggWkKUWAvTFNyi4JEgaUjeSoAMb5DcVKEgOTrB2GC3NMWlBEcIwG5oaUr2THaEVAFhF3MsWiI1VCiMEF/NhBf3AjgD3FpA8KGJHaWoTgo6WoFaMLBhnIJ8oKD/KSgioOiMxHwio

AOI5EcTU05iN8eenaljI0UL0tGWZPdAMMEZiSBNA0o52Fz0cG8RCqDndBHNE7ha9YiJ0DoT4K87dCzoMWCFJihNDgprI+dBVpFkuoi9vMYkU3G4yar68fyOrHflb337JdbefVe3ul2d5n8PibvS/nkMLwFCkmuQ85GCVK7lDB6FXCAE3hqEf8imAbCRE0OKItDUAIrWaAZGnzsVDoX5cAUvhTYzRwUApOaGtkZKdMj6PTDknm2YwWIphUcWYfMMW

HLDVh6wzYdsN2H7CPheuL4X4h+ESVeSOxGXmLwIH0oiBYIkgbWxJEVByw/AxgFwwQASIhh3YGnKiJhyTjRY04j+rOPnG8NNK/BfmgSLRwiD/uYg0FhSLI5SCIedImjow0ZGVsGOyLZcVOIQAzi5x0ILcbyLx4S4Ce5bYwc7SSrGd3aYoiALWIWFLCVhawjYVsI1A7C9h3fAYmplcwXAq4EeQ0d5mkjtwwhkIJSHAg7jiR5Iho3wiaNKAhd68HcbO

nvUbAJZrgg4zxsmEMiCs5ouEMFPtk2iTokhpdQoeXUP529j+mQjLtkO9EX8Jqfou1gfFv4pN5qkOB/qGMD4VC/0VQr1m/xq6f8o+JTRcHHxxLKY8SSfBejVEuAdJDI8/TPjeUSLZjYB3FGVmUjAGnZqMEInNmX1G4YDxuQzbAQCPmhAj0J5bUEcUXBEl9n8ehWUjOFVKKkDESpbsQFLgLETO4m0MiZ2UqQEC1ShkIyOmCimMSvMGZc0iUEtLYFcC

tpPMg6Wa4UEqCNBZYBYQHyelsg3qCUVKJlFyiEMIZMMkJDOAz9mw6GJsD0nbgnRPOsheQlchHK5TxynIIsuSBLLFSyykAMqcwQqBWC2ANguwe8IHx1SbQIBYyFEQxTMVrgYKbrgOW6nagcyo5PQhkS0lHxpyS5EIEpJKLEAZy+RU6X6FXJSgaiPiTcmYOaL5x4g2AfAMoBrCAQLxu4QOr30gCOdVIkhBIh0jqrWRzIy1TCe3G8KGix+ZvE0FqIX6

rEsUMWS0T0hanpgFoKvaiYkPi5XF2JB/PLOkOrqO9zW9dKJkGJ+IBjbWKFMSTawgAlcep2FKEpUMbyv8w+BTGMZW3qF+hywCYlhkmObBFJ4EGozrjNDTomSuKDYSdPgnrzIJix1knyWWNEr9NVSrGM4RcKuE3DmAdwxEI8OeGvDAIc0gfJ8JMTfCMpvw3sZMX7FIJqg9fEcdWzHErclcJ7JgFyKgDPiFxYVKHExzU6kA3ZHs18fiLMr7iiRN5EFm

SOI6kdwWctCOTINhYep5BN4xQT7Jdl+ykR7szcYuNx56D+Rhgr8UKJDgiiTOLfCQOcMuHXDbhzAe4XrJeFvCYJVRVzLMXjIthE65jcLLHTgSqRvMp0JsNK3rzBczRPSQVsDIiSkk4yWM4nvXhiy+Zr6FuIIgkVYm78kmHEwmVxIyEkyshlrT4u7yFD+j7Wok4oR7wZk7Swx5XXJoRQck+tOZkfKet/1wA8I/+wCcEvHw0mTl6KuEEZmgUgKizJIB

k3PrmOYolJ0wNwefkMJLHZslZ4wsbpXycn/DLggImSO5MIEVsb53k0gayWlLoC5SoiBUiFPNnYKZw4vYeWjNwhjyrOBieAlPOH6zzCw88v4OlLACZToQ2UtQHaX2mVspylBYaW6VQAMFxp3qUgK9PemfTvpH8Nsg1LUiLRqg0kC4GJCLDAy4pkY7aW5kkX8kjicM5ibtL6kHSzBY0oaUVJ4V8KKy4iKaTNPsHBlxF4IaeSdAC714sU8CX3DISUXh

FlIokJYhCgMjMTo6LE2qbmVSLsLRSk5AqZdOXI3zjCIS66SuUGJAh7pdRJvk9KZZ7ARwEiMdvoC0T0AYJOjJUQWEOIAgFo8WeaPn3n7QlNoyQEpIdm8XSRAu9jZSJJBRm+5CEVSxqpq1pZb9Le+rVIa6KCZgVQmPEz0XxKMrbzfRu84SQcm94AlaZfvUoVJNKDusWZsJaoQpNqGADuZ/UORHzOT6tdx8B2NdNmNupIKZaMArihcCkieDQFww0sag

Nzbl92ZMCv4SEg0jgyL8dsryaOOW6v1KavszZpIAACHDzcgKgH0DrjH27bVAAgHTlLjPlqc75X8tQAAqgV8DEFc+zBUQqg5OHAFr92IaHiPlpImkeSKjmg8Y5eKuhqIv5hXjacTIjWinNQ4wr/luAQFcCvR4oqvRoyPkR+IFFGCC5hneiOAGajAg4AcAHkEEhKkzBPgmQSWilkOAMA4RK4Hpaa1SHUDFVrINYPzBEDLx+g3YfQDyCywEyHiUq3Su

qs1Wyr+qDveeKTJd6QADVXpTVYzCGWCSVVVq8qZqu1Wt0RJhQVVVBGtUZAXVndQ+Q6rVVer9AzsSST1I9WGqMg94ZmbJKh4BqnVGQRmBZSxXEjLVsatnDauRx7j3VjqtNRkGlTiCTx+q1NRqu9XBKTphRE6mGsDUjhFy5RSJSpmiUxrPVca/QJdMAhOZgoKqqubCE5DdBuhk6FzmMy7Izp2YUIKdsiDsL99ukMWUhd7ixSEIK4UqowOgxFXZgCAX

sRQu3EOyT5LMla5tcGv/7gl6Zw0FVSSBIBfdBaUq09cQB5AIAXFI6q9e/V6jVrcAmgYIBAsq4kBj0TEBTvgHESkBlABIAABSeLsaIG3gLbKsU7AAAlCyFdjKAPQjIeYABtwDAai+vANDYdgOCQaYNO67NT6oQCRrtBY0O/O/0yCuwP2pZXhTEvRivrpon4y1UQBcX0aIA6McVQYNZLCAoAvsXTl7B3V2AvwvPHIN7XkKPqEAz62je+uBCLBV2H3M

soPgkBPsPeoZaEAYDbWeJiBDs95fSmwKcxpNjAWTYEsaLgAGIWhYIGGE0wxggAA=
```
%%