---
tags:
  - general
  - luc
  - muc
  - quick-reference
  - thuat
  - toan
  - vietnamese
---

# Thuật toán sắp xếp

- Sắp xếp nổi bọt (Bubble sort)
- Sắp xế<PERSON> chèn (Insertion sort)
- Sắp xếp l<PERSON><PERSON> chọ<PERSON> (Selection sort)
- Sắp xế<PERSON> (Quick sort)
- Sắp xếp trộn (Merge sort)

# Thuật toán tìm kiếm

- Tìm kiếm tuần tự (Sequential search)
- Tìm kiếm nhị phân (Binary search)

# Học máy

- C<PERSON><PERSON> quyết định (Decision tree)
- <PERSON><PERSON><PERSON> vector hỗ trợ (Support vector machine-SVM)
- Mạng nơ-ron nhân tạo (Artificial neural network)
- R<PERSON>ng ngẫu nhiên (Random forest)

# Thị giác máy tính

- <PERSON><PERSON><PERSON> hiện cạnh (Edge detection)
- Nhận dạng khuôn mặt (Face recognition)
- <PERSON><PERSON> tì<PERSON> đố<PERSON> tượ<PERSON> (Object detection)

# <PERSON><PERSON> lý ngôn ngữ tự nhiên

- TF-IDF
- Word2Vec
- LSTM

# Graph

1. Thu<PERSON><PERSON> to<PERSON>jkstra: Tì<PERSON> đường đi ngắn nhất từ một đỉnh đến tất cả các đỉnh khác trong đồ thị có hướng.
2. Thuật toán Bellman-Ford: Tìm đường đi ngắn nhất từ một đỉnh đến tất cả các đỉnh khác trong đồ thị có hướng, bao gồm cả đồ thị có trọng số âm.
3. Thuật toán Floyd-Warshall: Tìm đường đi ngắn nhất giữa tất cả các cặp điểm trong đồ thị có trọng số dương hoặc âm.
4. Thuật toán Kruskal: Tìm cây khung nhỏ nhất của một đồ thị vô hướng.
5. Thuật toán Prim: Tìm cây khung nhỏ nhất của một đồ thị vô hướng.
6. Thuật toán Topological Sort: Sắp xếp các đỉnh của đồ thị có hướng sao cho mọi cạnh đi từ đỉnh có chỉ số thấp hơn đỉnh có chỉ số cao hơn.
7. Thuật toán Kosaraju: Tìm các thành phần liên thông mạnh của đồ thị có hướng.
8. Thuật toán Tarjan: Tìm các thành phần liên thông mạnh của đồ thị có hướng.
9. Thuật toán A*: Tìm đường đi ngắn nhất từ một đỉnh đến một đỉnh khác trong đồ thị có trọng số không âm.
10. Thuật toán DFS: Duyệt đồ thị theo chiều sâu để tìm kiếm các thành phần liên thông và các chu trình trong đồ thị.
11. Thuật toán BFS: Duyệt đồ thị theo chiều rộng để tìm kiếm các thành phần liên thông và tìm đường đi ngắn nhất giữa hai đỉnh trong đồ thị vô hướng hoặc có trọng số không âm.
12. Thuật toán Maximum Flow: Tìm lưu lượng lớn nhất có thể chảy từ một đỉnh đến một đỉnh khác trong đồ thị có hướng và có trọng số.
13. Thuật toán Minimum Cut: Tìm cách cắt bỏ các cạnh trong đồ thị sao cho số lượng cạnh bị cắt là nhỏ nhất, giữa hai đỉnh đã cho.
14. Thuật toán Ford-Fulkerson: Tìm lưu lượng lớn nhất có thể chảy từ một đỉnh đến một đỉnh khác trong đồ thị có hướng và có trọng số.
15. Thuật toán Edmonds-Karp: Tìm lưu lượng lớn nhất có thể chảy từ một đỉnh đến một đỉnh khác trong đồ thị có hướng và có trọng số không âm, sử dụng kỹ thuật BFS.
16. Thuật toán Hopcroft-Karp: Tìm cặp ghép lớn nhất trong đồ thị hai phía.
17. Thuật toán Dinitz: Tìm cặp ghép lớn nhất trong đồ thị hai phía.
18. Thuật toán Johnson: Tìm các đường đi ngắn nhất trong đồ thị có trọng số âm.
19. Thuật toán Bron-Kerbosch: Tìm tất cả các tập con độc lập của đỉnh trong đồ thị vô hướng.
20. Thuật toán Chromatic Number: Tìm số lượng màu cần thiết để tô màu các đỉnh của đồ thị sao cho hai đỉnh kề nhau có màu khác nhau.
21. Thuật toán Union-Find Algorithm/Disjoint Set Union (DSU): Là một thuật toán để quản lý các tập hợp phân biệt. Nó thường được sử dụng để giải quyết các vấn đề liên quan đến việc phân nhóm hoặc phân loại các đối tượng