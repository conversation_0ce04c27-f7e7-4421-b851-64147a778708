---
relates:
  - "[[Microservices]]"
  - "[[Backend - Back-end]]"
---
# 1. Resources

- Programming language for cloud: https://github.com/winglang/wing

![[Fzo-pUoaEAApb8M.jpg]]


- Firebase:
	- FireBase Cloud Messaging - Bắn message bằng Firebase: Hướng dẫn bắn notification bằng FCM (FireBase Cloud Messaging) API (viblo.asia) - https://viblo.asia/p/huong-dan-ban-notification-bang-fcm-firebase-cloud-messaging-api-63vKj6yAK2R
- Automation tasks:
	- Lark Anycross
	- Zapier
- Search:
	- https://www.algolia.com
- Database:
	- Bytebase - Migration / Schema manager: https://www.bytebase.com
- Payment:
	- Braintree
- Storage:
	- Synology Object Storage: https://c2.synology.com/en-us/object-storage/overview
- Media optimization and transformations:
	- https://imagekit.io
	- https://cloudinary.com
- Website builder:
	- Full-website builder with page builder, cms and more: https://framer.com
- Email:
	- https://resend.com
- Secure network:
	- https://tailscale.com
		- https://github.com/juanfont/headscale: Triển khai máy chủ điều phối (control server) cho mạng riêng ảo (VPN) sử dụng Tailscale. Tailscale là một giải pháp VPN hiện đại dựa trên WireGuard, giúp kết nối các thiết bị một cách an toàn và dễ dàng thông qua NAT traversal. Tuy nhiên, máy chủ điều phối của Tailscale là phần mềm độc quyền. Headscale được phát triển để cung cấp một giải pháp thay thế mã nguồn mở và tự lưu trữ cho máy chủ điều phối này.
- Map:
	- https://goong.io
- AMP (Giám sát hiệu suất ứng dụng):
	- newrelic: https://newrelic.com
- Others:
	- Event ticket: https://lu.ma