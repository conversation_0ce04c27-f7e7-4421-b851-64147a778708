# 📋 Tag Index - Chỉ mục Tags

Đây là chỉ mục tất cả các tags được sử dụng trong workspace.

## 📊 Thống kê tổng quan

- **Tổng số file**: 219
- **Tổng số tags**: 702
- **Tổng số danh mục**: 13

## 🏷️ Top 30 Tags phổ biến

| Rank | Tag | Số file | Tỷ lệ |
|------|-----|---------|-------|
|  1 | `vietnamese` | 167 | 76.3% |
|  2 | `quick-reference` | 156 | 71.2% |
|  3 | `concise` | 137 | 62.6% |
|  4 | `general` | 127 | 58.0% |
|  5 | `english` | 74 | 33.8% |
|  6 | `resources` | 52 | 23.7% |
|  7 | `ai-ml` | 32 | 14.6% |
|  8 | `tutorial` | 24 | 11.0% |
|  9 | `notes` | 23 | 10.5% |
| 10 | `english-learning` | 22 | 10.0% |
| 11 | `language-learning` | 22 | 10.0% |
| 12 | `detailed` | 21 | 9.6% |
| 13 | `llm` | 21 | 9.6% |
| 14 | `golang` | 19 | 8.7% |
| 15 | `coding` | 18 | 8.2% |
| 16 | `artificial-intelligence` | 16 | 7.3% |
| 17 | `ai--machine-learning` | 15 | 6.8% |
| 18 | `programming` | 15 | 6.8% |
| 19 | `programming-languages` | 15 | 6.8% |
| 20 | `cau` | 14 | 6.4% |
| 21 | `tips` | 12 | 5.5% |
| 22 | `libraries` | 11 | 5.0% |
| 23 | `tools` | 11 | 5.0% |
| 24 | `cac` | 11 | 5.0% |
| 25 | `quan` | 10 | 4.6% |
| 26 | `van` | 9 | 4.1% |
| 27 | `java` | 9 | 4.1% |
| 28 | `frontend` | 8 | 3.7% |
| 29 | `frontend-development` | 8 | 3.7% |
| 30 | `web-development` | 8 | 3.7% |

## 📂 Tags theo danh mục

### General (127 files)

**Tags phổ biến**: `general` (127), `quick-reference` (104), `concise` (93), `vietnamese` (89), `english` (38), `resources` (27), `notes` (11), `ai-ml` (10), `tutorial` (8), `cac` (8)

**Files tiêu biểu**:
- [[25 phương pháp giúp bạn ngừng overthinking]]
- [[ARBO]]
- [[Bài 2 - Prompt Engineering, RAG và Finetuning]]
- [[Bài toán liệt kê]]
- [[Bảng màu gradient đẹp]]
- ... và 122 file khác

### English Learning (22 files)

**Tags phổ biến**: `english` (22), `english-learning` (22), `language-learning` (22), `vietnamese` (22), `llm` (19), `quick-reference` (15), `concise` (14), `cau` (7), `detailed` (4), `tu` (4)

**Files tiêu biểu**:
- [[English with LLM - Câu bị động]]
- [[English with LLM - Câu gián tiếp]]
- [[English with LLM - Câu nhấn mạnh]]
- [[English with LLM - Câu điều kiện hỗn hợp]]
- [[English with LLM - Cấu trúc câu phức tạp]]
- ... và 17 file khác

### AI & Machine Learning (16 files)

**Tags phổ biến**: `ai-ml` (16), `artificial-intelligence` (16), `ai--machine-learning` (15), `vietnamese` (15), `quick-reference` (7), `tutorial` (6), `concise` (5), `prompt` (2), `prompt-engineering` (2), `quan` (2)

**Files tiêu biểu**:
- [[6 Chiến lược Prompt Hiệu quả của OpenAI]]
- [[AI - Large Language Models (LLM)]]
- [[AI support for coding]]
- [[Airblade - AB - Air blade]]
- [[Airblade]]
- ... và 11 file khác

### Programming Languages (15 files)

**Tags phổ biến**: `coding` (15), `programming` (15), `programming-languages` (15), `vietnamese` (12), `quick-reference` (10), `java` (7), `concise` (6), `resources` (6), `libraries` (5), `golang` (5)

**Files tiêu biểu**:
- [[C - C++]]
- [[Defer - Async - Inline, cách browser thực thi JavaScript]]
- [[Fresher Java Interview]]
- [[Golang Scheduler]]
- [[Golang]]
- ... và 10 file khác

### Frontend Development (8 files)

**Tags phổ biến**: `frontend` (8), `frontend-development` (8), `web-development` (8), `vietnamese` (5), `detailed` (3), `english` (3), `quick-reference` (3), `react` (3), `notes` (2), `tips` (2)

**Files tiêu biểu**:
- [[37 Tips from a Senior Frontend Developer]]
- [[Angular]]
- [[CSS]]
- [[Các câu hỏi phỏng vấn VueJS]]
- [[Frontend - Front-end]]
- ... và 3 file khác

### Business & Management (7 files)

**Tags phổ biến**: `business` (7), `business--management` (7), `management` (7), `concise` (6), `quick-reference` (6), `vietnamese` (5), `freelance` (4), `english` (2), `server` (2), `design` (1)

**Files tiêu biểu**:
- [[Business]]
- [[Freelance 1 - Dropshipping]]
- [[Freelance 2 - Justbijay]]
- [[Freelance 3 - Spa]]
- [[Freelance]]
- ... và 2 file khác

### Cloud & DevOps (6 files)

**Tags phổ biến**: `cloud` (6), `cloud--devops` (6), `devops` (6), `infrastructure` (6), `resources` (4), `vietnamese` (4), `concise` (4), `quick-reference` (4), `aws` (2), `golang` (2)

**Files tiêu biểu**:
- [[AWS]]
- [[Azure]]
- [[DevOps]]
- [[Google Cloud Platform - GCP]]
- [[Kubernetes - K8S]]
- ... và 1 file khác

### Backend Development (5 files)

**Tags phổ biến**: `backend` (5), `backend-development` (5), `server-side` (5), `vietnamese` (5), `database` (4), `resources` (3), `tutorial` (3), `tools` (2), `back` (1), `cache` (1)

**Files tiêu biểu**:
- [[Backend - Back-end]]
- [[Database]]
- [[MongoDB]]
- [[MySQL]]
- [[Postgresql]]

### Interview Preparation (4 files)

**Tags phổ biến**: `career` (4), `interview` (4), `interview-preparation` (4), `vietnamese` (4), `concise` (2), `quick-reference` (2), `back` (1), `fresher` (1), `java` (1), `golang` (1)

**Files tiêu biểu**:
- [[Fresher Back-end Interview]]
- [[Interview - Phỏng vấn]]
- [[Interview Senior Engineer]]
- [[Tổng hợp các nguồn ôn luyện thuật toán & Coding interview]]

### Algorithms & Data Structures (3 files)

**Tags phổ biến**: `algorithms` (3), `algorithms--data-structures` (3), `computer-science` (3), `data-structures` (3), `golang` (3), `cheatsheet` (2), `data` (2), `english` (2), `structures` (2), `resources` (2)

**Files tiêu biểu**:
- [[Algorithms & Data Structures CheatSheet]]
- [[Data structures & Algorithms]]
- [[Post score algorithm - Trending algorithm]]

### Travel (2 files)

**Tags phổ biến**: `lch` (2), `personal` (2), `travel` (2), `vietnamese` (2), `2025` (1), `chuyn` (1), `cng` (1), `detailed` (1), `golang` (1), `inception` (1)

**Files tiêu biểu**:
- [[Chuyến Du Lịch Công Ty Inception Labs 2025]]
- [[Du lịch Trung Quốc]]

### Lifestyle & Fashion (2 files)

**Tags phổ biến**: `lifestyle` (2), `lifestyle--fashion` (2), `personal` (2), `vietnamese` (2), `concise` (1), `fashion` (1), `o` (1), `quan` (1), `quick-reference` (1), `ai-ml` (1)

**Files tiêu biểu**:
- [[Fashion - Tủ đồ - Quần áo]]
- [[Skincare]]

### Personal Development (2 files)

**Tags phổ biến**: `concise` (2), `personal-development` (2), `quick-reference` (2), `resources` (2), `self-improvement` (2), `english` (1), `life` (1), `mem` (1), `nang` (1), `softskill` (1)

**Files tiêu biểu**:
- [[Life]]
- [[Softskill - Kỹ năng mềm]]

## 📖 Hướng dẫn sử dụng Tags

### Tìm kiếm theo tag
- Trong Obsidian: Sử dụng `tag:#tên-tag` trong search
- Hoặc click vào tag trong file để xem tất cả file có tag đó

### Các loại tag chính
- **Technology tags**: `python`, `javascript`, `react`, `aws`, etc.
- **Content type tags**: `tutorial`, `notes`, `cheatsheet`, `interview`
- **Language tags**: `vietnamese`, `english`
- **Complexity tags**: `beginner`, `intermediate`, `advanced`
- **Size tags**: `quick-reference`, `comprehensive`, `detailed`

---
*Được tạo tự động bởi hệ thống phân tích Markdown*
