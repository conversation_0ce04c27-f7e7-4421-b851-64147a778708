{"summary": {"total_files": 219, "total_categories": 13, "total_tags": 778, "total_size": 1244462, "total_lines": 19811}, "categories": {"General": {"count": 127, "files": ["25 phương pháp giúp bạn ngừng overthinking.md", "ARBO.md", "Bài 2 - Prompt Engineering, RAG và Finetuning.md", "Bài toán liệt kê.md", "Bảng màu gradient đẹp.md", "Các bước xây dựng ứng dụng.md", "Các câu hỏi phỏng vấn Laravel.md", "Các loại áo nên có trong tủ đồ.md", "Các loại giày nên có.md", "Các loại quần nên có trong tủ đồ.md", "Các loại trang phục cho tủ đồ.md", "Các loại vải phù hợp cho mùa hè.md", "Cách giải các bài thuật toán.md", "Cách làm sạch và bảo quản boots.md", "Câu hỏi phỏng vấn.md", "Cấu trúc dữ liệu & giải thuật.md", "Chiến lược backup dữ liệu 3-2-1.md", "Chỗ mua đồ.md", "Clean Code notes.md", "Compiler.md", "Computer Science - Khoa học máy tính.md", "Concurrency - Parallel - Asynchronus - Multi-threading.md", "Công cụ học tiếng Anh.md", "Cross-platform.md", "Cân Bằng Giữa P&L và Chất Lượng Dự Án.md", "Dagger & Koin.md", "<PERSON>h sách kiến thức chuẩn bị để phỏng vấn.md", "Data Analyze.md", "Design.md", "Development documentations.md", "Diagrams - Vẽ sơ đồ.md", "Done in Troodonlabs.md", "Du lịch Huế.md", "Du lịch.md", "ElasticSearch.md", "Error handler.md", "Drawing 2025-07-29 11.46.50.excalidraw.md", "Viclass - 514 - Synchronize mouse position & mouse shape of the presenter.md", "<PERSON><PERSON> - Load classroom coordinator states.md", "<PERSON><PERSON> - editor.geo.md", "vocab.md", "Fastpass.md", "Flutter.md", "Fullstack - Full-stack.md", "Game.md", "Git - Github.md", "GraphQL.md", "HTTP - HTTPS - TLS - SSL.md", "IaaS.md", "Inceptionlabs - Viclass.md", "Inceptionlabs.md", "Kafka.md", "<PERSON><PERSON> chung IT.md", "Kinh nghiệm deal lương.md", "Kudofoto.md", "LM Studio.md", "Laravel - Eloquent.md", "Laravel.md", "Layered Design in Go - iRi.md", "Linter.md", "Linux.md", "Logging.md", "Lời khuyên.md", "Luận bàn về async.md", "MCP - Model Context Protocol.md", "Microservices.md", "Monitor server.md", "Mục lục thuật toán.md", "NAT port.md", "Nest.md", "Network.md", "Nén file.md", "Nginx.md", "Những thứ đã học ở Grab tech talk.md", "No-code - nocode - low-code - lowcode.md", "Node.js.md", "Note câu hỏi phỏng vấn Laravel.md", "Note ebook Thuật toán của thầy Lê Minh Hoàng.md", "Note lại từ Huyền Chip.md", "Note mẹ.md", "Nướng thịt ở nhà bác Cửu.md", "OS Scheduler.md", "Ollama.md", "Operation.md", "Optimization.md", "Phỏng vấn JV-IT.md", "Phượt.md", "Promt.md", "Quy tắc chọn thắt lưng.md", "Quy trình làm việc.md", "Râm Generation.md", "Research websites.md", "SAP - Systems, Applications, and Products.md", "SEO Content.md", "SaaS.md", "Security.md", "Severless.md", "Software Engineer Roadmap 2025 - The Complete Guide.md", "SolidJS.md", "Solution sao lưu lịch sử chỉnh sửa.md", "Solutions & System Designs & Design Patterns.md", "Some shit I need.md", "Sống Platform.md", "Target of users in a workspace.md", "Tập thể dục.md", "Terminal UI - TUI.md", "Testing.md", "Thanh toán chuyển khoản ngân hàng.md", "Thống kê tủ đồ hiện tại.md", "Tools.md", "Top 10 câu hỏi phỏng vấn System Design và Microservices.md", "Trải nghiệm.md", "Troodonlabs.md", "Tự vệ.md", "Ứng tuyển.md", "VPN - Proxy - Firewall.md", "VPS - Hosting.md", "Vietop.md", "Windows Tips.md", "Wordpress.md", "Work.md", "Working day of users in a workspace.md", "Workspace.md", "ngosangns - home.md", "viclass - 752 - Make it easier to create account for user to experience the beta system.md", "viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md", "<PERSON>i du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu.md"]}, "Frontend Development": {"count": 8, "files": ["37 Tips from a Senior Frontend Developer.md", "Angular.md", "CSS.md", "Các câu hỏi phỏng vấn VueJS.md", "Frontend - Front-end.md", "React - Next.md", "Top 50 React interview quetions.md", "Vue - Nuxt.md"]}, "AI & Machine Learning": {"count": 16, "files": ["6 <PERSON><PERSON><PERSON> Prompt <PERSON><PERSON><PERSON> quả của OpenAI.md", "AI - Large Language Models (LLM).md", "AI support for coding.md", "Airblade - AB - Air blade.md", "Airblade.md", "Blockchain.md", "Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai).md", "Cách đặt câu hỏi cho ChatGPT.md", "Domain knowledge.md", "LLM promt engineering.md", "Machine learning - Deep Learning - AI - ML - DL.md", "Reflow, <PERSON>aint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals 2.md", "Reflow, <PERSON><PERSON><PERSON>, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals.md", "Roadmap học tiếng Anh từ ChatGPT4.md", "Tạo video bằng các tool AI.md", "VPC - Virtual Private Cloud - AZ - Availability Zone.md"]}, "Cloud & DevOps": {"count": 6, "files": ["AWS.md", "Azure.md", "DevOps.md", "Google Cloud Platform - GCP.md", "Kubernetes - K8S.md", "Lộ trình thi cert AWS Solutions Architect Assoc.md"]}, "Algorithms & Data Structures": {"count": 3, "files": ["Algorithms & Data Structures CheatSheet.md", "Data structures & Algorithms.md", "Post score algorithm - Trending algorithm.md"]}, "Backend Development": {"count": 5, "files": ["Backend - Back-end.md", "Database.md", "MongoDB.md", "MySQL.md", "Postgresql.md"]}, "Business & Management": {"count": 7, "files": ["Business.md", "Freelance 1 - Dropshipping.md", "Freelance 2 - Justbijay.md", "Freelance 3 - Spa.md", "Freelance.md", "Management.md", "Product management.md"]}, "Programming Languages": {"count": 15, "files": ["C - C++.md", "Defer - Async - Inline, cách browser thực thi JavaScript.md", "Fresher Java Interview.md", "Golang Scheduler.md", "Golang.md", "Interview - Senior Software Engineer (PHP, Javascript).md", "Java Microservices.md", "Java Spring.md", "Java.md", "Javascript - Typescript.md", "Khóa học Golang scalable của Việt Trần.md", "Kotlin.md", "PHP.md", "Python.md", "Rust.md"]}, "Travel": {"count": 2, "files": ["Chuyến Du Lịch Công Ty Inception Labs 2025.md", "<PERSON> Quốc.md"]}, "English Learning": {"count": 22, "files": ["English with LLM - Câu bị động.md", "English with LLM - Câu gián tiếp.md", "English with LLM - Câu nhấn mạnh.md", "English with LLM - Câu điều kiện hỗn hợp.md", "English with LLM - Cấu trúc câu phức tạp.md", "English with LLM - Cấu trúc câu.md", "English with LL<PERSON> - <PERSON><PERSON> sa<PERSON>ch 200 từ vựng cần thiết trong TOEIC.md", "English with LLM - Irregular verbs.md", "English with LLM - Loại câu.md", "English with LLM - Mạo từ.md", "English with LLM - Mệnh đề quan hệ.md", "English with LLM - Mệnh đề trạng ngữ.md", "English with LLM - Số ít số nhiều.md", "English with LLM - Tense.md", "English with LLM - Thành ngữ.md", "English with LLM - Từ nối.md", "English with LLM - Đảo ngữ.md", "English with LLM - Động từ nguyên mẫu và danh động từ.md", "English with LLM.md", "English.md", "Kho tài liệu sau 3 năm học IELTS của t - phần 1.md", "Note TOEIC Mỗi Ngày.md"]}, "Lifestyle & Fashion": {"count": 2, "files": ["Fashion - Tủ đồ - Quần áo.md", "Skincare.md"]}, "Interview Preparation": {"count": 4, "files": ["Fresher Back-end Interview.md", "Interview - Phỏng vấn.md", "Interview Senior Engineer.md", "Tổng hợp các nguồn ôn luyện thuật toán & Coding interview.md"]}, "Personal Development": {"count": 2, "files": ["Life.md", "Softskill - Kỹ năng mềm.md"]}}, "top_tags": {"general": 126, "resources": 52, "tutorial": 24, "english-learning": 22, "with": 21, "english": 20, "llm": 20, "notes": 19, "ai-&-machine-learning": 15, "programming-languages": 15, "câu": 13, "trong": 13, "tips": 12, "libraries": 11, "tools": 11, "các": 11, "frontend-development": 8, "data": 8, "design": 8, "phỏng": 8, "vấn": 8, "interview": 8, "quan": 7, "business-&-management": 7, "đồ": 7, "frameworks": 6, "cloud-&-devops": 6, "cheatsheet": 6, "toán": 6, "hỏi": 6}, "files": [{"file_path": "25 phương pháp giúp bạn ngừng overthinking.md", "file_name": "25 phương pháp giúp bạn ngừng overthinking.md", "file_size": 4820, "content_length": 3679, "line_count": 38, "headers": [], "keywords": ["t<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "ngu", "c<PERSON><PERSON>", "ti<PERSON>u", "người", "t<PERSON>ch", "hoạt", "nhữ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "thích", "<PERSON><PERSON><PERSON>", "trung", "giúp", "ngh<PERSON>", "v<PERSON><PERSON><PERSON>", "ng<PERSON>y", "pha", "đ<PERSON><PERSON>"], "category": "General", "tags": ["general", "<PERSON><PERSON><PERSON><PERSON>", "overthinking", "ngừng", "bạn", "pha<PERSON>p", "phương"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "37 Tips from a Senior Frontend Developer.md", "file_name": "37 Tips from a Senior Frontend Developer.md", "file_size": 13425, "content_length": 13306, "line_count": 487, "headers": ["1. Master the fundamentals", "2. Understand how the web works", "3. Get familiar with Data Structures & Algorithms", "4. Learn by doing rather than reading/watching", "5. Ask for help when stuck", "6. Ask for help the proper way", "7. Don't copy/paste code you don't understand", "8. Don't blindly apply every piece of advice found online", "9. Assume good intent: people want you to succeed ❤️", "10. Done is better than perfect"], "keywords": ["tough", "wants", "online", "development", "learn", "about", "them", "into", "than", "always", "their", "that", "this", "documentation", "mistakes", "value", "tips", "issues", "done", "just"], "category": "Frontend Development", "tags": ["fundamentals", "watching", "reading", "frontend", "notes", "tutorial", "learn", "than", "frontend-development", "works", "from", "stuck", "with", "tips", "master"], "language": "English", "content_type": "Tutorial"}, {"file_path": "6 <PERSON><PERSON><PERSON> Prompt <PERSON><PERSON><PERSON> quả của OpenAI.md", "file_name": "6 <PERSON><PERSON><PERSON> Prompt <PERSON><PERSON><PERSON> quả của OpenAI.md", "file_size": 20588, "content_length": 15952, "line_count": 157, "headers": ["1. <PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn một cách rõ ràng", "2. <PERSON><PERSON> cấp v<PERSON>n bản liên quan", "3. <PERSON><PERSON> task p<PERSON><PERSON><PERSON> tạ<PERSON> thành các task con đơ<PERSON> g<PERSON><PERSON><PERSON> h<PERSON>n", "4. <PERSON><PERSON> để \"<PERSON><PERSON><PERSON>\"", "5. <PERSON><PERSON> \"<PERSON><PERSON><PERSON> T<PERSON>ại Nội Tâm\" hoặc Chuỗi T<PERSON>y Vấn để Ẩn Quá Trình <PERSON>", "6. Hỏi <PERSON>ô Hình xem nó có Bỏ lỡ Gì không", "7. <PERSON><PERSON>ls (<PERSON>ông c<PERSON><PERSON><PERSON>)", "8. <PERSON><PERSON><PERSON> (Sử Dụng Evals)"], "keywords": ["<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "thích", "to<PERSON>", "<PERSON><PERSON><PERSON>", "thời", "tổng", "cu<PERSON>i", "thêm", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "l<PERSON><PERSON>"], "category": "AI & Machine Learning", "tags": ["prompt", "ai-&-machine-learning", "task", "truy", "<PERSON><PERSON><PERSON>", "quan", "tutorial", "gian", "openai", "<PERSON><PERSON><PERSON><PERSON>", "c<PERSON>a", "<PERSON><PERSON><PERSON><PERSON>", "quả", "chia", "cung"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "AI - Large Language Models (LLM).md", "file_name": "AI - Large Language Models (LLM).md", "file_size": 27568, "content_length": 23418, "line_count": 292, "headers": ["1. <PERSON><PERSON><PERSON> ng<PERSON> h<PERSON> tập", "2. Libraries / Frameworks", "2.1. Make It Heavy", "2.1.1. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> bật", "2.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ch<PERSON>h", "2.1.3. <PERSON><PERSON> t<PERSON><PERSON> h<PERSON> sẵn", "2.1.4. <PERSON><PERSON><PERSON><PERSON>", "2.1.5. <PERSON><PERSON><PERSON> đặt & <PERSON><PERSON> d<PERSON><PERSON> c<PERSON> bản", "2.1.6. Ưu và nh<PERSON><PERSON><PERSON> điểm", "2.1.6.1. Ưu điểm"], "keywords": ["multi", "triton", "kh<PERSON>ng", "phap", "<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "lora", "b<PERSON><PERSON><PERSON>", "fine", "ollama", "<PERSON><PERSON><PERSON>", "mistral", "python", "<PERSON><PERSON><PERSON><PERSON>", "trong", "khai", "to<PERSON>", "<PERSON><PERSON><PERSON>", "thích", "thời"], "category": "AI & Machine Learning", "tags": ["ai-&-machine-learning", "heavy", "large", "libraries", "tutorial", "frameworks", "language", "(llm)", "make", "models"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "AI support for coding.md", "file_name": "AI support for coding.md", "file_size": 6029, "content_length": 4937, "line_count": 87, "headers": ["1. Resources", "2. <PERSON><PERSON>", "2.1. Agent File", "2.1.1. <PERSON><PERSON><PERSON> <PERSON><PERSON> của Agent File (.af)", "2.1.2. Ưu điểm", "2.1.3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "2.1.4. Usecases (Trư<PERSON>ng hợp sử dụng)", "3. CLI", "4. VS Code Extensions", "5. <PERSON><PERSON><PERSON> công cụ kh<PERSON>c"], "keywords": ["th<PERSON>nh", "file", "công", "kh<PERSON>ng", "agent", "chỉnh", "c<PERSON><PERSON>", "dàng", "<PERSON><PERSON><PERSON>", "https", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "vi<PERSON>n", "support", "giúp", "h<PERSON><PERSON>", "dụng", "<PERSON><PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>"], "category": "AI & Machine Learning", "tags": ["ai-&-machine-learning", "support", "resources", "file", "agent", "tools", "tutorial", "coding", "for"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "ARBO.md", "file_name": "ARBO.md", "file_size": 657, "content_length": 657, "line_count": 28, "headers": ["Recently Access feature"], "keywords": ["admin", "untitled", "arbo", "arbocollab", "https", "tssrm"], "category": "General", "tags": ["general", "recently", "feature", "access", "arbo"], "language": "English", "content_type": "General Documentation"}, {"file_path": "AWS.md", "file_name": "AWS.md", "file_size": 10207, "content_length": 9074, "line_count": 150, "headers": ["1. Resources", "2. <PERSON><PERSON>", "3. DynamoDB", "4. <PERSON><PERSON><PERSON>", "5. <PERSON><PERSON>", "6. <PERSON><PERSON>", "7. <PERSON>", "7.1. Services", "8. Practices"], "keywords": ["whitepapers", "appsync", "golang", "items", "kh<PERSON>ng", "c<PERSON><PERSON>", "table", "eventbridge", "lens", "d<PERSON>ng", "người", "lambda", "zendesk", "t<PERSON>ch", "hoạt", "https", "from", "<PERSON><PERSON><PERSON><PERSON>", "trong", "amazon"], "category": "Cloud & DevOps", "tags": ["dynamodb", "resources", "cloud-&-devops", "eventbridge", "lambda", "aws"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Airblade - AB - Air blade.md", "file_name": "Airblade - AB - Air blade.md", "file_size": 489, "content_length": 476, "line_count": 30, "headers": ["1. <PERSON><PERSON> dễ tắt máy khi giảm ga", "2. <PERSON><PERSON><PERSON><PERSON>"], "keywords": ["image", "pasted", "air", "airblade", "blade"], "category": "AI & Machine Learning", "tags": ["airblade", "ai-&-machine-learning", "blade", "air"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Airblade.md", "file_name": "Airblade.md", "file_size": 248, "content_length": 222, "line_count": 8, "headers": ["1. <PERSON><PERSON><PERSON><PERSON>", "2. <PERSON><PERSON><PERSON><PERSON> sửa xe"], "keywords": ["airblade"], "category": "AI & Machine Learning", "tags": ["airblade", "ai-&-machine-learning"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Algorithms & Data Structures CheatSheet.md", "file_name": "Algorithms & Data Structures CheatSheet.md", "file_size": 36397, "content_length": 36292, "line_count": 1575, "headers": ["1. <PERSON><PERSON><PERSON>", "1.1. <PERSON><PERSON><PERSON> vs linked lists", "1.2. <PERSON><PERSON><PERSON> lists", "1.3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON>er", "2. Searching", "2.1. Linear Search", "2.2. Binary Search", "2.3. Crystal Balls Exercise", "3. Sorting", "3.1. Bubble sort"], "keywords": ["start", "position", "logn", "length", "lookup", "hasleft", "arraybuffer", "breaks", "into", "always", "root", "that", "this", "because", "continue", "between", "index", "seen", "console", "detach"], "category": "Algorithms & Data Structures", "tags": ["searching", "cheatsheet", "linked", "data", "arrays", "array", "algorithms-&-data-structures", "algorithms", "structures", "arraybuffer", "ringbuffer", "lists"], "language": "English", "content_type": "Cheatsheet"}, {"file_path": "Angular.md", "file_name": "Angular.md", "file_size": 896, "content_length": 891, "line_count": 26, "headers": ["1. Resources", "2. Libraries", "3. State management", "4. <PERSON><PERSON>", "5. Design components"], "keywords": ["angular", "trong", "change", "detection", "module", "github", "https"], "category": "Frontend Development", "tags": ["angular", "resources", "libraries", "tools", "management", "frontend-development", "state", "design", "components"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Azure.md", "file_name": "Azure.md", "file_size": 218, "content_length": 217, "line_count": 10, "headers": ["Resources"], "keywords": ["hans", "azure", "thorsten"], "category": "Cloud & DevOps", "tags": ["cloud-&-devops", "azure", "resources"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Backend - Back-end.md", "file_name": "Backend - Back-end.md", "file_size": 11812, "content_length": 10057, "line_count": 151, "headers": ["1. Resources", "1.1. <PERSON><PERSON><PERSON><PERSON>", "1.2. <PERSON><PERSON><PERSON>", "2. Libraries / Frameworks", "2.1. <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "2.1.1. <PERSON><PERSON><PERSON>", "2.1.2. <PERSON><PERSON> khác biệt giữ cache aside và read through", "2.1.3. <PERSON><PERSON><PERSON> dẹp dữ liệu thừa", "2.1.4. <PERSON><PERSON>", "2.1.4.1. Redis Sentinel"], "keywords": ["redis", "<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "g<PERSON><PERSON><PERSON>", "khóa", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "thời", "ti<PERSON>n", "thêm", "<PERSON><PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "phan", "cache", "thông", "dong", "request"], "category": "Backend Development", "tags": ["back", "resources", "backend", "notes", "nginx", "libraries", "tutorial", "end", "frameworks", "caching", "backend-development", "cache"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Bài 2 - Prompt Engineering, RAG và Finetuning.md", "file_name": "Bài 2 - Prompt Engineering, RAG và Finetuning.md", "file_size": 5110, "content_length": 3996, "line_count": 47, "headers": [], "keywords": ["cu<PERSON>n", "v<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "chỉnh", "c<PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "d<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "c<PERSON>ng", "nhữ<PERSON>", "trong", "thêm", "quan", "v<PERSON><PERSON><PERSON>", "dụng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agents"], "category": "General", "tags": ["general", "prompt", "rag", "engineering,", "tutorial", "và", "bài", "finetuning"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Bài toán liệt kê.md", "file_name": "Bài toán liệt kê.md", "file_size": 4752, "content_length": 3847, "line_count": 101, "headers": ["**Chỉnh hợp, tổ hợp**", "Phương ph<PERSON><PERSON> sinh", "<PERSON><PERSON><PERSON><PERSON> toán quay lui"], "keywords": ["kh<PERSON>ng", "chỉnh", "c<PERSON><PERSON>", "ebook", "<PERSON><PERSON><PERSON><PERSON>", "quay", "<PERSON><PERSON><PERSON><PERSON>", "trong", "to<PERSON>", "ho<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "h<PERSON><PERSON>", "d<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "begin", "<PERSON><PERSON><PERSON><PERSON>", "hoặc", "ph<PERSON>n", "<PERSON><PERSON>ận"], "category": "General", "tags": ["general", "liệt", "sinh", "kê", "bài", "<PERSON><PERSON><PERSON><PERSON>", "quay"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Bảng màu gradient đẹp.md", "file_name": "Bảng màu gradient đẹp.md", "file_size": 338, "content_length": 338, "line_count": 20, "headers": [], "keywords": ["image", "pasted", "gradient"], "category": "General", "tags": ["general", "gradient", "màu", "đẹp", "bảng"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Blockchain.md", "file_name": "Blockchain.md", "file_size": 423, "content_length": 389, "line_count": 3, "headers": [], "keywords": ["asia", "viblo", "transaction", "blockchain"], "category": "AI & Machine Learning", "tags": ["ai-&-machine-learning", "blockchain"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Business.md", "file_name": "Business.md", "file_size": 51, "content_length": 47, "line_count": 2, "headers": [], "keywords": ["business"], "category": "Business & Management", "tags": ["business-&-management", "business"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "C - C++.md", "file_name": "C - C++.md", "file_size": 201, "content_length": 201, "line_count": 11, "headers": ["1. Resources", "2. Libraries"], "keywords": [], "category": "Programming Languages", "tags": ["c++", "programming-languages", "resources", "libraries"], "language": "English", "content_type": "General Documentation"}, {"file_path": "CSS.md", "file_name": "CSS.md", "file_size": 3550, "content_length": 3129, "line_count": 107, "headers": ["1. CSS", "1.1. <PERSON><PERSON><PERSON>", "1.2. <PERSON><PERSON><PERSON> v<PERSON>", "1.3. <PERSON><PERSON>", "1.4. <PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> <PERSON>", "1.5. CSS Builders", "1.6. <PERSON><PERSON> <PERSON>", "1.7. <PERSON><PERSON><PERSON><PERSON>", "1.8. <PERSON><PERSON><PERSON> n<PERSON>ng CSS nâng cao", "1.8.1. <PERSON><PERSON><PERSON> vị neo CSS (CSS Anchor Positioning)"], "keywords": ["<PERSON><PERSON><PERSON>n", "position", "animation", "hoạt", "https", "hover", "animate", "anchor", "k<PERSON><PERSON><PERSON>", "scroll", "viện", "cu<PERSON>n", "giao", "phép", "stylelint", "github", "postcss", "ph<PERSON>n", "<PERSON><PERSON><PERSON>", "css"], "category": "Frontend Development", "tags": ["css", "linter", "frontend-development"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Các bước xây dựng ứng dụng.md", "file_name": "Các bước xây dựng ứng dụng.md", "file_size": 2877, "content_length": 2266, "line_count": 37, "headers": ["1. Resources", "1.1. Non-Functional Requirements (NFR)", "1.2. Capability", "2. <PERSON><PERSON><PERSON><PERSON> vụ", "3. <PERSON><PERSON><PERSON> y<PERSON>u c<PERSON><PERSON> c<PERSON><PERSON> thi<PERSON>"], "keywords": ["công", "hoạt", "trong", "thời", "dụng", "requirements", "đ<PERSON><PERSON>", "ch<PERSON><PERSON>", "thông", "gian", "<PERSON><PERSON><PERSON><PERSON>", "hoặc", "q<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "thực", "functional", "thống", "<PERSON><PERSON><PERSON>", "năng", "capability"], "category": "General", "tags": ["general", "functional", "bước", "resources", "xây", "các", "dựng", "ứng", "capability", "dụng", "requirements"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Các câu hỏi phỏng vấn Laravel.md", "file_name": "Các câu hỏi phỏng vấn Laravel.md", "file_size": 8679, "content_length": 7231, "line_count": 127, "headers": [], "keywords": ["<PERSON><PERSON><PERSON>", "website", "file", "route", "untitled", "<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "builder", "c<PERSON><PERSON>", "session", "illuminate", "model", "guarded", "d<PERSON>ng", "người", "delete", "fillable", "https", "injection", "trong"], "category": "General", "tags": ["general", "phỏng", "vấn", "laravel", "câu", "các", "hỏi"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Các câu hỏi phỏng vấn VueJS.md", "file_name": "Các câu hỏi phỏng vấn VueJS.md", "file_size": 800, "content_length": 681, "line_count": 7, "headers": [], "keywords": ["back", "render", "component", "pho", "dụng", "keepalive", "v<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON>"], "category": "Frontend Development", "tags": ["phỏng", "frontend-development", "vấn", "câu", "các", "hỏi", "v<PERSON><PERSON><PERSON>"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Các loại áo nên có trong tủ đồ.md", "file_name": "Các loại áo nên có trong tủ đồ.md", "file_size": 9155, "content_length": 7169, "line_count": 129, "headers": ["1. <PERSON><PERSON> thun trơn cổ tròn", "2. <PERSON><PERSON> (Oxford Button Down Cloth)", "3. <PERSON><PERSON>", "4. <PERSON><PERSON> shirt", "5. <PERSON><PERSON> hen<PERSON>", "6. <PERSON><PERSON>", "7. <PERSON><PERSON> blazer", "8. Bộ suit màu xanh navy", "9. <PERSON>o denim jacket"], "keywords": ["tham", "<PERSON><PERSON><PERSON><PERSON>", "ch<PERSON>t", "untitled", "kh<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "q<PERSON><PERSON><PERSON>", "nhân", "<PERSON><PERSON><PERSON>", "short", "ph<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "người", "trắng", "denim", "hoạt", "henley", "nhữ<PERSON>", "trong"], "category": "General", "tags": ["đồ", "ocbd", "nên", "cloth", "loại", "button", "có", "henley", "trong", "oxford", "down", "a<PERSON>o", "polo", "dress", "các"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Các loại giày nên có.md", "file_name": "Các loại giày nên có.md", "file_size": 933, "content_length": 739, "line_count": 19, "headers": ["<PERSON><PERSON><PERSON><PERSON> đen", "Giày Derby nâu", "Chelsea boots", "Sneakers thi<PERSON><PERSON> kế tối gi<PERSON>n"], "keywords": ["trong", "g<PERSON><PERSON><PERSON>", "untitled", "chelsea", "loa", "boots", "gia"], "category": "General", "tags": ["general", "oxford", "sneakers", "chelsea", "các", "giày", "nên", "boots", "loại", "derby", "có"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Các loại quần nên có trong tủ đồ.md", "file_name": "Các loại quần nên có trong tủ đồ.md", "file_size": 1187, "content_length": 939, "line_count": 19, "headers": ["Quần jeans dáng slim-fit", "Quần chinos (kaki)", "<PERSON><PERSON><PERSON><PERSON> tây"], "keywords": ["trong", "untitled", "qua", "kh<PERSON>ng", "jeans", "chinos", "q<PERSON><PERSON><PERSON>", "slim", "c<PERSON>ng", "loa"], "category": "General", "tags": ["general", "đồ", "trong", "quần", "jeans", "các", "tủ", "chinos", "nên", "slim", "loại", "có", "kaki"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Các loại trang phục cho tủ đồ.md", "file_name": "Các loại trang phục cho tủ đồ.md", "file_size": 99, "content_length": 67, "line_count": 4, "headers": [], "keywords": ["cho", "loa", "trang", "phu"], "category": "General", "tags": ["general", "đồ", "các", "tủ", "cho", "loại", "phục", "trang"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Các loại vải phù hợp cho mùa hè.md", "file_name": "Các loại vải phù hợp cho mùa hè.md", "file_size": 1054, "content_length": 821, "line_count": 6, "headers": [], "keywords": ["lo<PERSON><PERSON>", "phu", "cho", "loa", "cotton"], "category": "General", "tags": ["general", "vải", "mùa", "các", "phù", "hè", "cho", "loại", "hợp"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Cách giải các bài thuật toán.md", "file_name": "Cách giải các bài thuật toán.md", "file_size": 218, "content_length": 189, "line_count": 5, "headers": [], "keywords": ["gia", "thua", "toa"], "category": "General", "tags": ["general", "giải", "thuật", "các", "bài", "<PERSON><PERSON><PERSON><PERSON>", "ca<PERSON>ch"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Cách làm sạch và bảo quản boots.md", "file_name": "Cách làm sạch và bảo quản boots.md", "file_size": 2547, "content_length": 1961, "line_count": 34, "headers": ["<PERSON><PERSON><PERSON> vệ sinh và làm sạch đôi boots của bạn", "<PERSON><PERSON><PERSON> vệ sinh boots da lộn", "<PERSON><PERSON><PERSON> b<PERSON><PERSON> quản boots nam", "<PERSON><PERSON>ch sử dụng và phối đồ với boots nam"], "keywords": ["g<PERSON><PERSON><PERSON>", "untitled", "b<PERSON><PERSON><PERSON>", "sạch", "<PERSON><PERSON><PERSON><PERSON>", "c<PERSON><PERSON>", "q<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong", "<PERSON><PERSON><PERSON>", "dụng", "ch<PERSON>g", "ngoài", "qua", "boots", "hoặc", "q<PERSON><PERSON><PERSON>", "bằng"], "category": "General", "tags": ["general", "sinh", "và", "làm", "boots", "sạch", "bảo", "quản", "ca<PERSON>ch"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai).md", "file_name": "Cách tôi xây kênh TikTok nhanh gấp 10 lần bằng AI (Đơn giản nhưng 90% mọi người vẫn sai).md", "file_size": 4671, "content_length": 3748, "line_count": 93, "headers": [], "keywords": ["công", "kh<PERSON>ng", "ngu", "c<PERSON><PERSON>", "d<PERSON>ng", "người", "t<PERSON>ch", "quay", "trong", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "giúp", "<PERSON><PERSON><PERSON>", "retention", "k<PERSON><PERSON>", "thêm", "viral", "dung", "d<PERSON>ng", "content"], "category": "AI & Machine Learning", "tags": ["xây", "bằng", "kênh", "vẫn", "tutorial", "90%", "sai)", "nhưng", "người", "tôi", "lần", "(đơn", "nhanh", "gấp", "ai-&-machine-learning"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Cách đặt câu hỏi cho ChatGPT.md", "file_name": "Cách đặt câu hỏi cho ChatGPT.md", "file_size": 13181, "content_length": 10781, "line_count": 77, "headers": ["**1. CONTEXT IS KING**", "**2. ORGANIZE WITH VARIABLE**", "**3. GRAMMAR POLICE**", "**4. DIVIDE AND CONQUER**", "**5. EVIDENCE**", "**6. FLOW IS BETTER THAN PROMPT**", "**7. PRACTICE MAKES PERFECT**"], "keywords": ["build", "kh<PERSON>ng", "<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "development", "their", "that", "this", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "nhữ<PERSON>", "<PERSON><PERSON><PERSON>", "ch<PERSON><PERSON>", "thêm", "outsourcing", "variable", "h<PERSON><PERSON>", "role", "digitalization"], "category": "AI & Machine Learning", "tags": ["grammar", "divide", "ai-&-machine-learning", "conquer", "organize", "chatgpt", "câu", "evidence", "cho", "with", "đặt", "variable", "king", "hỏi", "police"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Câu hỏi phỏng vấn.md", "file_name": "Câu hỏi phỏng vấn.md", "file_size": 1652, "content_length": 1299, "line_count": 22, "headers": [], "keywords": ["nhữ<PERSON>", "công", "thấy", "kh<PERSON>ng", "nghệ", "pho", "dụng", "<PERSON><PERSON><PERSON><PERSON>", "ph<PERSON>n", "thực"], "category": "General", "tags": ["general", "phỏng", "câu", "hỏi", "vấn"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Cấu trúc dữ liệu & giải thuật.md", "file_name": "Cấu trúc dữ liệu & giải thuật.md", "file_size": 6756, "content_length": 5085, "line_count": 47, "headers": ["1. <PERSON><PERSON><PERSON> cấu trúc dữ liệu để biểu diễn bài toán", "2. <PERSON><PERSON><PERSON> to<PERSON>", "2.1. <PERSON><PERSON><PERSON> đặc trưng của thuật toán", "2.2. <PERSON><PERSON><PERSON> lỗi chương trình chứa thuật toán", "2.3. <PERSON><PERSON><PERSON> ch<PERSON> trình"], "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "kh<PERSON>ng", "công", "g<PERSON><PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "ti<PERSON>u", "ph<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "nhữ<PERSON>", "to<PERSON>", "<PERSON><PERSON><PERSON>", "thời", "v<PERSON><PERSON><PERSON>", "dụng", "<PERSON><PERSON><PERSON><PERSON>", "tr<PERSON><PERSON>", "gia"], "category": "General", "tags": ["general", "dữ", "giải", "thuật", "trúc", "liệu", "cấu"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Chiến lược backup dữ liệu 3-2-1.md", "file_name": "Chiến lược backup dữ liệu 3-2-1.md", "file_size": 7184, "content_length": 5406, "line_count": 45, "headers": ["1. <PERSON><PERSON><PERSON><PERSON><PERSON>n", "2. <PERSON><PERSON><PERSON> số câu hỏi", "3. <PERSON><PERSON><PERSON> back-up"], "keywords": ["chie", "khỏi", "công", "kh<PERSON>ng", "c<PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "saas", "nhân", "<PERSON><PERSON><PERSON>", "chẳng", "ph<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "hỏng", "<PERSON><PERSON><PERSON><PERSON>", "trong", "<PERSON><PERSON><PERSON>", "giúp", "th<PERSON><PERSON><PERSON>", "quan", "dụng"], "category": "General", "tags": ["general", "dữ", "back", "chiến", "lược", "liệu", "backup"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Chỗ mua đồ.md", "file_name": "Chỗ mua đồ.md", "file_size": 183, "content_length": 154, "line_count": 16, "headers": ["<PERSON><PERSON> si", "<PERSON><PERSON> hi<PERSON>", "<PERSON><PERSON><PERSON><PERSON> s<PERSON>p", "<PERSON><PERSON><PERSON><PERSON>"], "keywords": ["cho", "mua"], "category": "General", "tags": ["general", "mua", "đồ", "chỗ"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Chuyến Du Lịch Công Ty Inception Labs 2025.md", "file_name": "Chuyến Du Lịch Công Ty Inception Labs 2025.md", "file_size": 12282, "content_length": 10521, "line_count": 208, "headers": ["1. 📌 <PERSON><PERSON><PERSON>i", "1.1. 📅 <PERSON> 9/7", "1.2. 📅 <PERSON> 10/7", "1.3. 📅 <PERSON> 11/7", "1.4. 📅 <PERSON> 12/7", "2. 📆 <PERSON><PERSON><PERSON>", "2.1. <PERSON><PERSON><PERSON> 1 – 9/7 (<PERSON><PERSON> → <PERSON><PERSON>)", "2.2. <PERSON><PERSON><PERSON> 2 – 10/7 (<PERSON><PERSON> → <PERSON><PERSON>)", "2.3. <PERSON><PERSON><PERSON> 3 – 11/7 (<PERSON><PERSON> → <PERSON><PERSON>)", "2.4. <PERSON><PERSON><PERSON> 4 – 12/7 (<PERSON><PERSON> → <PERSON><PERSON><PERSON>)"], "keywords": ["tham", "ch<PERSON>y", "th<PERSON>nh", "g<PERSON><PERSON><PERSON>", "static", "<PERSON><PERSON><PERSON><PERSON>", "công", "kh<PERSON>ng", "ngon", "labs", "b<PERSON>h", "party", "<PERSON><PERSON><PERSON><PERSON>", "ti<PERSON><PERSON>", "ch<PERSON><PERSON>", "hoạt", "coffee", "https", "thời", "nghỉ"], "category": "Travel", "tags": ["travel", "công", "notes", "labs", "tutorial", "<PERSON><PERSON><PERSON><PERSON>", "2025", "inception", "l<PERSON><PERSON>"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Clean Code notes.md", "file_name": "Clean Code notes.md", "file_size": 3523, "content_length": 3521, "line_count": 102, "headers": ["General rules", "Design rules", "Understandability tips", "Names rules", "Functions rules", "Comments rules", "Source code structure", "Objects and data structures", "Tests", "Code smells"], "keywords": ["rules", "notes", "names", "functions", "than", "always", "code", "clean", "change", "prefer", "variables", "data", "should", "structures", "small", "avoid", "close", "class", "keep", "methods"], "category": "General", "tags": ["general", "code", "rules", "clean", "notes", "names", "functions", "understandability", "tips", "design"], "language": "English", "content_type": "Notes"}, {"file_path": "Compiler.md", "file_name": "Compiler.md", "file_size": 123, "content_length": 123, "line_count": 7, "headers": [], "keywords": ["compiler"], "category": "General", "tags": ["general", "compiler"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Computer Science - Khoa học máy tính.md", "file_name": "Computer Science - Khoa học máy tính.md", "file_size": 1367, "content_length": 1087, "line_count": 9, "headers": ["OS và các cơ chế time sclicing và scheduling"], "keywords": ["computer", "science", "lu<PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "trong", "to<PERSON>", "thời", "<PERSON><PERSON><PERSON><PERSON>", "l<PERSON><PERSON>", "time", "t<PERSON><PERSON><PERSON>", "tr<PERSON><PERSON>", "nhất", "gian", "hoặc", "scheduling", "khoa", "<PERSON><PERSON><PERSON><PERSON>", "thực", "slicing"], "category": "General", "tags": ["general", "computer", "science", "ti<PERSON>nh", "ma<PERSON>y", "scheduling", "khoa", "time", "học", "sclicing"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Concurrency - Parallel - Asynchronus - Multi-threading.md", "file_name": "Concurrency - Parallel - Asynchronus - Multi-threading.md", "file_size": 4032, "content_length": 3363, "line_count": 43, "headers": ["1. Resources", "2. <PERSON><PERSON> switch-context manually", "3. <PERSON><PERSON> <PERSON><PERSON><PERSON> nên dùng"], "keywords": ["multi", "ch<PERSON>t", "asynchronus", "v<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "parallel", "d<PERSON>ng", "ph<PERSON><PERSON>", "process", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "synchronize", "<PERSON><PERSON><PERSON>", "thread", "t<PERSON><PERSON><PERSON>", "threading", "cache", "thanh", "<PERSON><PERSON><PERSON><PERSON>", "trên"], "category": "General", "tags": ["general", "multi", "handle", "resources", "manually", "asynchronus", "switch", "parallel", "threading", "context", "concurrency"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Công cụ học tiếng Anh.md", "file_name": "Công cụ học tiếng Anh.md", "file_size": 1625, "content_length": 1398, "line_count": 25, "headers": [], "keywords": ["tie", "<PERSON><PERSON><PERSON><PERSON>", "cu<PERSON>n", "writing", "nghe", "ielts", "ph<PERSON>n", "https", "anh"], "category": "General", "tags": ["general", "tiếng", "công", "cụ", "học", "anh"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Cross-platform.md", "file_name": "Cross-platform.md", "file_size": 2225, "content_length": 2225, "line_count": 43, "headers": ["1. Frameworks", "1.1. Lynx", "1.1.1. Core Philosophy and Features", "1.1.2. Key Repositories", "1.1.3. Development and Community"], "keywords": ["expo", "source", "lynx", "open", "core", "performance", "cross", "engine", "development", "framework", "platform", "native"], "category": "General", "tags": ["general", "philosophy", "community", "lynx", "core", "repositories", "cross", "tutorial", "development", "features", "frameworks", "platform"], "language": "English", "content_type": "Tutorial"}, {"file_path": "Cân Bằng Giữa P&L và Chất Lượng Dự Án.md", "file_name": "Cân Bằng Giữa P&L và Chất Lượng Dự Án.md", "file_size": 5850, "content_length": 4991, "line_count": 164, "headers": ["1. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>", "1.1. <PERSON> lo<PERSON>i dự án phổ biến", "1.2. <PERSON><PERSON><PERSON> t<PERSON> bộ", "1.3. <PERSON> <PERSON>", "1.4. **Case 1 – Project-Based, GM 45%**", "1.5. **Case 2 – <PERSON><PERSON>, burn<PERSON> <PERSON>n nh<PERSON>ng chất lư<PERSON> gi<PERSON>**", "2. <PERSON><PERSON><PERSON><PERSON> Bằng P&L & Quality", "2.1. <PERSON><PERSON><PERSON><PERSON> chất l<PERSON> & chi phí thành thứ có thể **đo lườ<PERSON>**", "2.2. <PERSON><PERSON> <PERSON> theo **vòng đời dự án**, không chỉ sprint", "2.3. <PERSON><PERSON><PERSON>h b<PERSON>ch – **ch<PERSON>a kh<PERSON>a gi<PERSON> khách hàng & team**"], "keywords": ["effort", "ch<PERSON>t", "leakage", "kh<PERSON>ng", "velocity", "coverage", "rate", "team", "lead", "<PERSON><PERSON><PERSON>", "trong", "junior", "defect", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "k<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "h<PERSON><PERSON>", "time", "<PERSON><PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "bằng", "gi<PERSON>a", "case", "p&l", "ch<PERSON>t", "based", "notes", "cân", "study", "<PERSON><PERSON><PERSON><PERSON>", "project"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Dagger & Koin.md", "file_name": "Dagger & Koin.md", "file_size": 764, "content_length": 674, "line_count": 12, "headers": [], "keywords": ["dagger", "dependencies", "component", "koin", "module"], "category": "General", "tags": ["general", "dagger", "koin"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "<PERSON>h sách kiến thức chuẩn bị để phỏng vấn.md", "file_name": "<PERSON>h sách kiến thức chuẩn bị để phỏng vấn.md", "file_size": 163, "content_length": 163, "line_count": 12, "headers": [], "keywords": ["danh", "pho", "kie", "chua", "thu"], "category": "General", "tags": ["general", "sa<PERSON>ch", "phỏng", "kiến", "để", "danh", "bị", "thức", "vấn", "chuẩn"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Data Analyze.md", "file_name": "Data Analyze.md", "file_size": 900, "content_length": 714, "line_count": 3, "headers": [], "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "analyze", "data", "apache", "b<PERSON>ng"], "category": "General", "tags": ["general", "analyze", "data"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Data structures & Algorithms.md", "file_name": "Data structures & Algorithms.md", "file_size": 11309, "content_length": 9359, "line_count": 134, "headers": ["1. Resources", "1.1. <PERSON><PERSON>", "2. Courses", "3. Challenges", "4. <PERSON><PERSON> ph<PERSON><PERSON> tạp thu<PERSON>t to<PERSON> - Big O notation", "5. <PERSON><PERSON><PERSON><PERSON> thu<PERSON> đệ quy", "6. <PERSON><PERSON>", "7. <PERSON><PERSON>", "8. <PERSON> filter"], "keywords": ["th<PERSON>nh", "t<PERSON><PERSON><PERSON>", "ch<PERSON>g", "k<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "bloom", "c<PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "filter", "khóa", "d<PERSON>ng", "courses", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "https", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong"], "category": "Algorithms & Data Structures", "tags": ["sort", "challenges", "cheatsheet", "resources", "data", "notes", "algorithms-&-data-structures", "algorithms", "structures", "notation", "courses"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Database.md", "file_name": "Database.md", "file_size": 73029, "content_length": 59747, "line_count": 692, "headers": ["1. <PERSON><PERSON><PERSON> quan về Cơ sở dữ liệu", "1.1. <PERSON><PERSON> lo<PERSON> sở dữ liệu", "1.2. <PERSON><PERSON><PERSON> mô hình lưu trữ NoSQL", "1.3. NewSQL", "1.4. Vector Database", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> niệm cốt lõi trong Cơ sở dữ liệu", "2.1. ACID", "2.2. Index", "2.2.1. Clustered Index và Non-Clustered Index", "2.2.2. <PERSON><PERSON><PERSON> cấu trúc dữ liệu Index trong MySQL"], "keywords": ["join", "tham", "redis", "version", "g<PERSON><PERSON><PERSON>", "real", "trong", "availability", "endpoint", "<PERSON><PERSON><PERSON><PERSON>", "snapshot", "optimistic", "value", "<PERSON><PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "repeatable", "acid", "cache", "query", "giới"], "category": "Backend Development", "tags": ["vector", "cheatsheet", "quan", "tutorial", "database", "nosql", "newsql", "backend-development"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Defer - Async - Inline, cách browser thực thi JavaScript.md", "file_name": "Defer - Async - Inline, cách browser thực thi JavaScript.md", "file_size": 4015, "content_length": 3364, "line_count": 137, "headers": ["1. Cách browser thực thi JavaScript: Hi<PERSON>u rõ `defer`, `async`, và `inline`", "2. Inline script là gì?", "2.1. Đặc điểm:", "2.2. <PERSON><PERSON> nào nên dùng?", "3. <PERSON><PERSON><PERSON> c<PERSON> thu<PERSON><PERSON> t<PERSON> `defer`", "3.1. Đặc điểm:", "4. <PERSON><PERSON><PERSON> c<PERSON> thu<PERSON><PERSON> t<PERSON> `async`", "4.1. Đặc điểm:", "5. So sánh `inline`, `defer`, `async`", "6. <PERSON><PERSON> nào dùng cái nào?"], "keywords": ["<PERSON><PERSON><PERSON>", "inline", "<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "tr<PERSON><PERSON><PERSON>", "d<PERSON>ng", "browser", "t<PERSON>ch", "trong", "<PERSON><PERSON><PERSON><PERSON>", "parser", "t<PERSON><PERSON><PERSON>", "module", "script", "<PERSON><PERSON><PERSON><PERSON>", "thi", "gi<PERSON>a", "tr<PERSON><PERSON>", "phân", "html"], "category": "Programming Languages", "tags": ["thi", "inline", "inline,", "c<PERSON><PERSON>", "async", "programming-languages", "defer", "script", "javascript", "browser", "thực"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Design.md", "file_name": "Design.md", "file_size": 55, "content_length": 49, "line_count": 4, "headers": [], "keywords": ["design"], "category": "General", "tags": ["general", "design"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "DevOps.md", "file_name": "DevOps.md", "file_size": 7094, "content_length": 6185, "line_count": 100, "headers": ["1. Github Actions", "2. <PERSON><PERSON>", "3. Reverse proxy", "4. Usecases", "4.1. 🔥 <PERSON><PERSON><PERSON> đốt tiền infra – <PERSON>ập 2", "4.1.1. <PERSON><PERSON><PERSON> “bốc h<PERSON>” chi ph<PERSON>", "4.1.2. <PERSON><PERSON> tích chi ph<PERSON>", "4.1.3. <PERSON><PERSON><PERSON> ra", "5. <PERSON><PERSON><PERSON>", "5.1. Beta9"], "keywords": ["kh<PERSON>ng", "c<PERSON><PERSON>", "actions", "d<PERSON>ng", "graalvm", "người", "lambda", "trăm", "python", "beam", "https", "<PERSON><PERSON><PERSON><PERSON>", "code", "<PERSON><PERSON><PERSON><PERSON>", "trong", "khai", "memory", "thời", "giúp", "serverless"], "category": "Cloud & DevOps", "tags": ["proxy", "usecases", "devops", "reverse", "infra", "cloud-&-devops", "tools", "actions", "github"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Development documentations.md", "file_name": "Development documentations.md", "file_size": 214, "content_length": 214, "line_count": 11, "headers": ["1. <PERSON><PERSON><PERSON><PERSON>"], "keywords": ["documentations", "https", "development"], "category": "General", "tags": ["general", "resouces", "documentations", "development"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Diagrams - Vẽ sơ đồ.md", "file_name": "Diagrams - Vẽ sơ đồ.md", "file_size": 209, "content_length": 209, "line_count": 13, "headers": ["1. <PERSON>'s foot notation", "2. Diagram tools", "2.1. Database"], "keywords": ["diagrams", "github", "https"], "category": "General", "tags": ["general", "đồ", "sơ", "diagram", "diagrams", "notation", "foot", "tools", "database", "ve<PERSON>", "crow"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Domain knowledge.md", "file_name": "Domain knowledge.md", "file_size": 90, "content_length": 90, "line_count": 4, "headers": ["1. ARBO"], "keywords": ["knowledge", "domain"], "category": "AI & Machine Learning", "tags": ["arbo", "knowledge", "ai-&-machine-learning", "domain"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Done in Troodonlabs.md", "file_name": "Done in Troodonlabs.md", "file_size": 225, "content_length": 225, "line_count": 7, "headers": [], "keywords": ["done", "troodonlabs"], "category": "General", "tags": ["general", "done", "troodonlabs"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Du lịch Huế.md", "file_name": "Du lịch Huế.md", "file_size": 4372, "content_length": 3462, "line_count": 89, "headers": ["1. <PERSON><PERSON><PERSON>", "2. <PERSON><PERSON>"], "keywords": ["xu<PERSON>", "công", "<PERSON><PERSON><PERSON><PERSON>", "b<PERSON>h", "l<PERSON>ng", "nhân", "ti<PERSON>n", "k<PERSON><PERSON><PERSON>", "thịt", "hue", "h<PERSON><PERSON>", "thi<PERSON>n", "sông", "ch<PERSON>a", "<PERSON><PERSON><PERSON><PERSON>", "cafe", "quán", "b<PERSON>ch", "c<PERSON><PERSON>", "tàng"], "category": "General", "tags": ["general", "huế", "lịch"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Du lịch.md", "file_name": "Du lịch.md", "file_size": 559, "content_length": 465, "line_count": 24, "headers": ["1. <PERSON><PERSON> ho<PERSON> du lịch <PERSON>", "2. <PERSON><PERSON> ho<PERSON><PERSON>"], "keywords": ["quốc"], "category": "General", "tags": ["general", "lịch"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "<PERSON> Quốc.md", "file_name": "<PERSON> Quốc.md", "file_size": 15843, "content_length": 12502, "line_count": 135, "headers": ["1. Resources"], "keywords": ["giang", "kh<PERSON>ng", "ch<PERSON><PERSON>", "check", "nhữ<PERSON>", "trong", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON>", "cu<PERSON>i", "thời", "thêm", "l<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "h<PERSON><PERSON>", "alipay", "thông", "minh", "nepal", "<PERSON><PERSON>ận", "nước"], "category": "Travel", "tags": ["resources", "trung", "travel", "quốc", "l<PERSON><PERSON>"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "ElasticSearch.md", "file_name": "ElasticSearch.md", "file_size": 280, "content_length": 265, "line_count": 2, "headers": [], "keywords": ["elasticsearch"], "category": "General", "tags": ["general", "elasticsearch"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Câu bị động.md", "file_name": "English with LLM - Câu bị động.md", "file_size": 3114, "content_length": 2485, "line_count": 49, "headers": [], "keywords": ["th<PERSON>nh", "t<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "mary", "người", "trong", "<PERSON><PERSON><PERSON>", "with", "t<PERSON><PERSON><PERSON>", "been", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "english", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "llm", "hoặc", "sang", "thực"], "category": "English Learning", "tags": ["english", "llm", "câu", "english-learning", "bị", "with", "động"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Câu gián tiếp.md", "file_name": "English with LLM - Câu gián tiếp.md", "file_size": 2968, "content_length": 2555, "line_count": 49, "headers": [], "keywords": ["th<PERSON>nh", "kh<PERSON>ng", "that", "<PERSON><PERSON><PERSON>", "with", "t<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "tr<PERSON><PERSON>", "đ<PERSON><PERSON>", "gia", "tie", "english", "<PERSON><PERSON><PERSON>", "told", "llm", "<PERSON><PERSON><PERSON><PERSON>", "hoặc", "they", "asked", "trực"], "category": "English Learning", "tags": ["english", "gia<PERSON>n", "llm", "câu", "tiếp", "english-learning", "with"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Câu nhấn mạnh.md", "file_name": "English with LLM - Câu nhấn mạnh.md", "file_size": 2352, "content_length": 1904, "line_count": 31, "headers": [], "keywords": ["mạnh", "english", "trong", "trạng", "ch<PERSON><PERSON>", "m<PERSON><PERSON>", "llm", "nhấn", "nha", "cleft", "with", "<PERSON><PERSON><PERSON><PERSON>", "that", "tr<PERSON><PERSON>", "ph<PERSON>n"], "category": "English Learning", "tags": ["english", "llm", "câu", "mạnh", "english-learning", "with", "nhấn"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Câu điều kiện hỗn hợp.md", "file_name": "English with LLM - Câu điều kiện hỗn hợp.md", "file_size": 2408, "content_length": 1924, "line_count": 36, "headers": [], "keywords": ["taken", "công", "kh<PERSON>ng", "m<PERSON><PERSON>", "<PERSON>u<PERSON><PERSON>", "trong", "<PERSON><PERSON><PERSON>", "kie", "with", "t<PERSON>nh", "english", "gi<PERSON>a", "llm", "would", "better", "lo<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "kiện", "wouldn", "have"], "category": "English Learning", "tags": ["english", "kiện", "llm", "câu", "hỗn", "english-learning", "with", "hợp", "điều"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Cấu trúc câu phức tạp.md", "file_name": "English with LLM - Cấu trúc câu phức tạp.md", "file_size": 2462, "content_length": 1979, "line_count": 33, "headers": [], "keywords": ["english", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "phu", "llm", "quan", "mình", "with", "<PERSON><PERSON><PERSON><PERSON>", "hoặc", "li<PERSON>n", "m<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ghép", "tru", "<PERSON><PERSON><PERSON><PERSON>"], "category": "English Learning", "tags": ["english", "llm", "câu", "trúc", "english-learning", "phức", "with", "cấu", "tạp"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Cấu trúc câu.md", "file_name": "English with LLM - Cấu trúc câu.md", "file_size": 6655, "content_length": 5606, "line_count": 159, "headers": ["1. <PERSON><PERSON><PERSON>", "1.1. <PERSON><PERSON><PERSON> thứ<PERSON> tổng quát:", "1.2. <PERSON><PERSON><PERSON> dạng cấu trúc câu đơn phổ biến", "2. <PERSON><PERSON><PERSON> quan hệ (Relative Clause)", "2.1. <PERSON><PERSON><PERSON><PERSON><PERSON> chung:", "2.2. <PERSON><PERSON>:", "3. <PERSON><PERSON><PERSON> đề trạng ngữ (Adverbial Clause)", "3.1. **<PERSON><PERSON><PERSON> thức chung:**", "3.2. **Ví dụ:**", "4. <PERSON><PERSON><PERSON> đi<PERSON> ki<PERSON> (Conditional Sentence)"], "keywords": ["subject", "sung", "công", "kh<PERSON>ng", "object", "clause", "relative", "d<PERSON>ng", "simple", "người", "verb", "m<PERSON><PERSON>", "will", "trong", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "thức", "thời", "quan", "with"], "category": "English Learning", "tags": ["english", "llm", "câu", "chung", "trúc", "quan", "english-learning", "clause", "with", "relative", "cấu"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LL<PERSON> - <PERSON><PERSON> sa<PERSON>ch 200 từ vựng cần thiết trong TOEIC.md", "file_name": "English with LL<PERSON> - <PERSON><PERSON> sa<PERSON>ch 200 từ vựng cần thiết trong TOEIC.md", "file_size": 8518, "content_length": 6985, "line_count": 223, "headers": [], "keywords": ["th<PERSON>nh", "công", "nhân", "<PERSON><PERSON><PERSON><PERSON>", "vựng", "cuộ<PERSON>", "do<PERSON>h", "trong", "th<PERSON><PERSON><PERSON>", "with", "giám", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "h<PERSON><PERSON>", "nhập", "l<PERSON><PERSON>", "trang", "ch<PERSON><PERSON>", "english"], "category": "English Learning", "tags": ["sa<PERSON>ch", "english", "cần", "trong", "llm", "thiết", "danh", "english-learning", "with", "200", "từ", "vựng", "<PERSON>ic"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Irregular verbs.md", "file_name": "English with LLM - Irregular verbs.md", "file_size": 33732, "content_length": 30780, "line_count": 904, "headers": [], "keywords": ["misheard", "misread", "kh<PERSON>ng", "split", "overbuilt", "undersold", "about", "party", "spelled", "spent", "into", "built", "always", "their", "burst", "g<PERSON><PERSON><PERSON>", "this", "blessed", "causing", "because"], "category": "English Learning", "tags": ["english", "verbs", "llm", "english-learning", "irregular", "with"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Loại câu.md", "file_name": "English with LLM - Loại câu.md", "file_size": 6428, "content_length": 5482, "line_count": 175, "headers": ["1. <PERSON><PERSON><PERSON> (Inversion Sentence)", "1.1. <PERSON><PERSON><PERSON><PERSON> ngữ với trạng từ phủ định", "1.2. <PERSON><PERSON><PERSON><PERSON> ngữ với ONLY", "1.3. <PERSON><PERSON><PERSON> đ<PERSON>o ngữ với NO SOONER và HARDLY", "1.4. <PERSON><PERSON><PERSON> ngữ với SO, SUCH", "2. <PERSON><PERSON><PERSON> (Subjunctive Sentence)", "2.1. <PERSON><PERSON><PERSON> gi<PERSON> định với động từ đề nghị, y<PERSON><PERSON> c<PERSON>u", "2.2. <PERSON><PERSON><PERSON> g<PERSON><PERSON> định với \"It is important/necessary/essential that\"", "2.3. <PERSON><PERSON><PERSON> g<PERSON><PERSON> định với If / Wish / As if", "2.3.1. <PERSON><PERSON><PERSON> điều kiện loại 2 và 3 (<PERSON><PERSON><PERSON><PERSON> c<PERSON> thật ở hiện tại hoặc quá khứ)"], "keywords": ["hardly", "such", "công", "kh<PERSON>ng", "d<PERSON>ng", "that", "verb", "being", "mạnh", "trong", "wish", "<PERSON><PERSON><PERSON>", "thức", "nhấn", "main", "with", "t<PERSON><PERSON><PERSON>", "been", "<PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>"], "category": "English Learning", "tags": ["hardly", "english", "sooner", "such", "llm", "câu", "english-learning", "only", "with", "loại", "sentence", "inversion"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Mạo từ.md", "file_name": "English with LLM - Mạo từ.md", "file_size": 2625, "content_length": 2155, "line_count": 42, "headers": [], "keywords": ["english", "<PERSON><PERSON><PERSON><PERSON>", "llm", "kh<PERSON>ng", "danh", "tr<PERSON><PERSON><PERSON>", "with", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "d<PERSON>ng", "hoặc", "người"], "category": "English Learning", "tags": ["english", "mạo", "llm", "english-learning", "with", "từ"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Mệnh đề quan hệ.md", "file_name": "English with LLM - Mệnh đề quan hệ.md", "file_size": 3352, "content_length": 2671, "line_count": 36, "headers": [], "keywords": ["kh<PERSON>ng", "c<PERSON><PERSON>", "clause", "mary", "relative", "d<PERSON>ng", "người", "that", "m<PERSON><PERSON>", "trong", "thêm", "quan", "with", "đ<PERSON><PERSON>", "which", "english", "talking", "thông", "llm", "book"], "category": "English Learning", "tags": ["english", "mệnh", "llm", "đề", "quan", "english-learning", "with", "hệ"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Mệnh đề trạng ngữ.md", "file_name": "English with LLM - Mệnh đề trạng ngữ.md", "file_size": 3949, "content_length": 3197, "line_count": 48, "headers": [], "keywords": ["ngu", "c<PERSON><PERSON>", "clause", "d<PERSON>ng", "that", "m<PERSON><PERSON>", "will", "trong", "thời", "tra", "though", "th<PERSON><PERSON><PERSON>", "tr<PERSON>i", "with", "<PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "adverbial", "ch<PERSON><PERSON>", "english", "llm"], "category": "English Learning", "tags": ["english", "ngữ", "mệnh", "llm", "đề", "english-learning", "with", "trạng"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Số ít số nhiều.md", "file_name": "English with LLM - Số ít số nhiều.md", "file_size": 2572, "content_length": 2098, "line_count": 41, "headers": [], "keywords": ["english", "<PERSON><PERSON><PERSON><PERSON>", "th<PERSON>nh", "bằng", "đ<PERSON><PERSON>", "llm", "danh", "thêm", "dạng", "h<PERSON><PERSON>", "nhie", "c<PERSON>ng", "with", "hoặc", "<PERSON><PERSON><PERSON><PERSON>"], "category": "English Learning", "tags": ["english", "llm", "số", "english-learning", "with", "i<PERSON>t", "nhiều"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Tense.md", "file_name": "English with LLM - Tense.md", "file_size": 33722, "content_length": 27889, "line_count": 520, "headers": ["1. Present Simple (<PERSON><PERSON><PERSON> tại đơn)", "1.1. <PERSON><PERSON><PERSON>", "1.2. <PERSON><PERSON> <PERSON>o dùng", "1.3. <PERSON><PERSON> <PERSON> nâng cao (6 câu)", "2. Present Continuous (<PERSON><PERSON><PERSON> tạ<PERSON> ti<PERSON><PERSON>)", "2.1. <PERSON><PERSON><PERSON>", "2.2. <PERSON><PERSON> <PERSON>o dùng", "2.3. <PERSON><PERSON> nân<PERSON>o (6 câu)", "3. Present Perfect (<PERSON><PERSON><PERSON> tại hoàn thành)", "3.1. <PERSON><PERSON><PERSON>"], "keywords": ["tham", "kh<PERSON>ng", "that", "<PERSON><PERSON><PERSON>", "this", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "conference", "<PERSON><PERSON><PERSON>", "th<PERSON>i", "thời", "nhấn", "cu<PERSON>i", "ch<PERSON><PERSON>", "thêm", "<PERSON><PERSON><PERSON><PERSON>", "thỏa", "đ<PERSON><PERSON>", "l<PERSON><PERSON>"], "category": "English Learning", "tags": ["english", "llm", "notes", "english-learning", "tutorial", "continuous", "with", "simple", "tense", "present"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "English with LLM - Thành ngữ.md", "file_name": "English with LLM - Thành ngữ.md", "file_size": 9379, "content_length": 8065, "line_count": 204, "headers": [], "keywords": ["th<PERSON>nh", "weather", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "kh<PERSON>ng", "ngu", "c<PERSON><PERSON>", "b<PERSON>h", "than", "người", "đ<PERSON>ng", "dogs", "trong", "thời", "under", "thấy", "cake", "quan", "blue", "<PERSON><PERSON><PERSON><PERSON>"], "category": "English Learning", "tags": ["english", "ngữ", "llm", "english-learning", "thành", "with"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Từ nối.md", "file_name": "English with LLM - Từ nối.md", "file_size": 3219, "content_length": 2699, "line_count": 37, "headers": [], "keywords": ["english", "thích", "llm", "order", "v<PERSON><PERSON><PERSON>", "dụng", "with", "hoặc", "also", "contrast", "addition"], "category": "English Learning", "tags": ["english", "llm", "english-learning", "with", "từ", "nối"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Đảo ngữ.md", "file_name": "English with LLM - Đảo ngữ.md", "file_size": 3527, "content_length": 2897, "line_count": 47, "headers": [], "keywords": ["hardly", "such", "kh<PERSON>ng", "ngu", "that", "trong", "th<PERSON><PERSON><PERSON>", "dụng", "with", "neither", "đ<PERSON><PERSON>", "english", "llm", "would", "<PERSON><PERSON><PERSON>", "only", "trạng", "lo<PERSON><PERSON>", "have", "<PERSON><PERSON><PERSON>"], "category": "English Learning", "tags": ["english", "ngữ", "đảo", "llm", "english-learning", "with"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM - Động từ nguyên mẫu và danh động từ.md", "file_name": "English with LLM - Động từ nguyên mẫu và danh động từ.md", "file_size": 3094, "content_length": 2521, "line_count": 37, "headers": [], "keywords": ["english", "trong", "llm", "nguye", "danh", "thêm", "dạng", "<PERSON><PERSON><PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "with", "<PERSON><PERSON><PERSON>", "good", "đ<PERSON><PERSON>"], "category": "English Learning", "tags": ["english", "nguyên", "mẫu", "llm", "danh", "english-learning", "và", "with", "từ", "động"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English with LLM.md", "file_name": "English with LLM.md", "file_size": 3500, "content_length": 2797, "line_count": 52, "headers": ["1. <PERSON><PERSON> p<PERSON>", "2. <PERSON><PERSON> vựng"], "keywords": ["th<PERSON>nh", "<PERSON><PERSON><PERSON>", "c<PERSON><PERSON>", "ti<PERSON><PERSON>", "m<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "thức", "giáo", "dụng", "with", "<PERSON><PERSON><PERSON>", "t<PERSON><PERSON><PERSON>", "tr<PERSON><PERSON>", "đ<PERSON><PERSON>", "english", "pháp", "llm", "danh"], "category": "English Learning", "tags": ["llm", "english", "with", "english-learning"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "English.md", "file_name": "English.md", "file_size": 616, "content_length": 560, "line_count": 20, "headers": ["1. Resources", "2. <PERSON><PERSON> tr<PERSON><PERSON> h<PERSON>"], "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "english", "https", "ielts"], "category": "English Learning", "tags": ["english", "notes", "resources", "english-learning"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Error handler.md", "file_name": "Error handler.md", "file_size": 66, "content_length": 66, "line_count": 5, "headers": [], "keywords": ["error", "handler"], "category": "General", "tags": ["general", "error", "handler"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Excalidraw/Drawing 2025-07-29 11.46.50.excalidraw.md", "file_name": "Drawing 2025-07-29 11.46.50.excalidraw.md", "file_size": 574, "content_length": 570, "line_count": 14, "headers": ["Drawing"], "keywords": ["excalidraw", "drawing"], "category": "General", "tags": ["general", "2025", "11.46.50.excalidraw", "drawing"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Excalidraw/Viclass - 514 - Synchronize mouse position & mouse shape of the presenter.md", "file_name": "Viclass - 514 - Synchronize mouse position & mouse shape of the presenter.md", "file_size": 12165, "content_length": 12160, "line_count": 135, "headers": ["Excalidraw Data", "Text Elements", "Drawing"], "keywords": ["the", "position", "synchronize", "excalidraw", "presenter", "change", "shape", "classroom", "mouse", "viclass"], "category": "General", "tags": ["general", "the", "position", "synchronize", "excalidraw", "data", "elements", "presenter", "shape", "drawing", "text", "mouse", "viclass", "514"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Excalidraw/<PERSON> - Load classroom coordinator states.md", "file_name": "<PERSON><PERSON> - Load classroom coordinator states.md", "file_size": 11337, "content_length": 11333, "line_count": 121, "headers": ["Excalidraw Data", "Text Elements", "Drawing"], "keywords": ["excalidraw", "load", "coordinator", "classroom", "viclass", "states"], "category": "General", "tags": ["general", "excalidraw", "data", "load", "elements", "states", "drawing", "classroom", "text", "viclass", "coordinator"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Excalidraw/<PERSON> - editor.geo.md", "file_name": "<PERSON><PERSON> - editor.geo.md", "file_size": 5848, "content_length": 5826, "line_count": 70, "headers": ["Excalidraw Data", "Text Elements", "Drawing"], "keywords": ["excalidraw", "element", "editor", "geo", "viclass"], "category": "General", "tags": ["general", "excalidraw", "data", "elements", "editor.geo", "drawing", "text", "viclass"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Excalidraw/vocab.md", "file_name": "vocab.md", "file_size": 45313, "content_length": 45123, "line_count": 438, "headers": ["Excalidraw Data", "Text Elements", "Drawing"], "keywords": ["excalidraw", "data", "pháp", "<PERSON><PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON>", "thanh", "nghe", "vocab", "<PERSON><PERSON><PERSON><PERSON>", "vựng"], "category": "General", "tags": ["general", "excalidraw", "data", "elements", "notes", "drawing", "vocab", "text"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Fashion - Tủ đồ - Quần áo.md", "file_name": "Fashion - Tủ đồ - Quần áo.md", "file_size": 393, "content_length": 308, "line_count": 10, "headers": [], "keywords": ["qua", "lo<PERSON><PERSON>", "fashion"], "category": "Lifestyle & Fashion", "tags": ["đồ", "fashion", "quần", "tủ", "a<PERSON>o", "lifestyle-&-fashion"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Fastpass.md", "file_name": "Fastpass.md", "file_size": 393, "content_length": 393, "line_count": 18, "headers": ["Fastpass"], "keywords": ["fastpass"], "category": "General", "tags": ["general", "fastpass"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Flutter.md", "file_name": "Flutter.md", "file_size": 211, "content_length": 192, "line_count": 3, "headers": ["Resources"], "keywords": ["flutter"], "category": "General", "tags": ["general", "flutter", "resources"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Freelance 1 - Dropshipping.md", "file_name": "Freelance 1 - Dropshipping.md", "file_size": 585, "content_length": 583, "line_count": 25, "headers": ["Server staging", "Server kh<PERSON><PERSON> h<PERSON>ng", "Design"], "keywords": ["user", "password", "dropshipping", "freelance", "https"], "category": "Business & Management", "tags": ["server", "business-&-management", "dropshipping", "freelance", "design", "staging"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Freelance 2 - Justbijay.md", "file_name": "Freelance 2 - Justbijay.md", "file_size": 53, "content_length": 53, "line_count": 6, "headers": [], "keywords": ["<PERSON><PERSON><PERSON>", "freelance"], "category": "Business & Management", "tags": ["<PERSON><PERSON><PERSON>", "business-&-management", "freelance"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Freelance 3 - Spa.md", "file_name": "Freelance 3 - Spa.md", "file_size": 5485, "content_length": 5447, "line_count": 105, "headers": ["New knowledge", "Client server", "<PERSON><PERSON><PERSON> l<PERSON>nh chạy thủ công", "Hosting SSH", "Deploy keys"], "keywords": ["spa", "template", "private", "freelance", "artisan"], "category": "Business & Management", "tags": ["spa", "client", "hosting", "server", "business-&-management", "keys", "knowledge", "freelance", "deploy"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Freelance.md", "file_name": "Freelance.md", "file_size": 617, "content_length": 519, "line_count": 20, "headers": ["Upwork", "Personal Freelance Jobs"], "keywords": ["kh<PERSON>ng", "ti<PERSON>u", "freelance"], "category": "Business & Management", "tags": ["business-&-management", "jobs", "freelance", "upwork", "personal"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Fresher Back-end Interview.md", "file_name": "Fresher Back-end Interview.md", "file_size": 2772, "content_length": 2333, "line_count": 30, "headers": [], "keywords": ["back", "tham", "trong", "bằng", "firebase", "s<PERSON>h", "end", "java", "interview", "<PERSON><PERSON><PERSON>", "fresher"], "category": "Interview Preparation", "tags": ["back", "interview-preparation", "end", "interview", "fresher"], "language": "Vietnamese", "content_type": "Interview Guide"}, {"file_path": "Fresher Java Interview.md", "file_name": "Fresher Java Interview.md", "file_size": 6115, "content_length": 5208, "line_count": 88, "headers": ["1. <PERSON><PERSON> Interview"], "keywords": ["join", "<PERSON><PERSON><PERSON>", "static", "công", "kh<PERSON>ng", "c<PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "phỏng", "ti<PERSON><PERSON>", "d<PERSON>ng", "abstract", "fresher", "<PERSON><PERSON><PERSON>", "c<PERSON>ng", "trong", "khai", "login", "ch<PERSON><PERSON>", "arraylist", "usecase"], "category": "Programming Languages", "tags": ["programming-languages", "fresher", "java", "interview"], "language": "Vietnamese", "content_type": "Interview Guide"}, {"file_path": "Frontend - Front-end.md", "file_name": "Frontend - Front-end.md", "file_size": 26877, "content_length": 24573, "line_count": 586, "headers": ["1. <PERSON><PERSON><PERSON> tảng và <PERSON><PERSON>i niệm <PERSON> lõi", "1.1. Web APIs", "1.1.1. WebGPU", "*******. <PERSON><PERSON><PERSON> n<PERSON>ng và <PERSON>u điểm ch<PERSON>h", "*******. Hỗ trợ trong Chrome", "*******. <PERSON><PERSON><PERSON> n<PERSON>ng <PERSON>ng dụng", "1.2. <PERSON><PERSON><PERSON> thành phần tiềm năng", "1.3. <PERSON><PERSON><PERSON> hình lưu trữ có thể hỗ trợ", "1.4. <PERSON><PERSON> dụ cấu trúc lớ<PERSON> (TypeScript)", "1.5. Web Components"], "keywords": ["tham", "build", "length", "kh<PERSON>ng", "<PERSON><PERSON><PERSON>", "editor", "g<PERSON><PERSON><PERSON>", "drag", "storage", "this", "shadcn", "<PERSON><PERSON><PERSON><PERSON>", "trong", "react", "<PERSON><PERSON><PERSON>", "thời", "tổng", "console", "<PERSON><PERSON><PERSON><PERSON>", "viện"], "category": "Frontend Development", "tags": ["chrome", "trong", "webgpu", "apis", "notes", "frontend", "end", "frontend-development", "front"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Fullstack - Full-stack.md", "file_name": "Fullstack - Full-stack.md", "file_size": 384, "content_length": 377, "line_count": 14, "headers": ["1. Resources", "2. Framework"], "keywords": ["fullstack", "schema", "stack", "full"], "category": "General", "tags": ["general", "stack", "resources", "fullstack", "framework", "full"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Game.md", "file_name": "Game.md", "file_size": 160, "content_length": 151, "line_count": 6, "headers": [], "keywords": ["game"], "category": "General", "tags": ["general", "game"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Git - Github.md", "file_name": "Git - Github.md", "file_size": 5671, "content_length": 4872, "line_count": 154, "headers": ["1. Workflows", "1.1. Trunk Flow (Trunk-based Development)", "1.1.1. <PERSON><PERSON> trì<PERSON>", "1.1.2. Ưu điểm", "1.1.3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "1.2. Forking Flow (GitHub Flow / Forking Workflow)", "1.2.1. <PERSON><PERSON> trì<PERSON>:", "1.2.2. Ưu điểm:", "1.2.3. <PERSON><PERSON><PERSON><PERSON><PERSON> đi<PERSON>:", "1.3. So s<PERSON>h Trunk Flow vs Forking Flow"], "keywords": ["th<PERSON>nh", "push", "công", "kh<PERSON>ng", "filter", "mailmap", "team", "trunk", "d<PERSON>ng", "người", "<PERSON><PERSON><PERSON>", "commit", "flow", "https", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ri<PERSON>ng", "trong", "repo", "source"], "category": "General", "tags": ["general", "based", "development", "workflows", "git", "trunk", "github", "flow"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Golang Scheduler.md", "file_name": "Golang Scheduler.md", "file_size": 486, "content_length": 447, "line_count": 19, "headers": ["Resources"], "keywords": ["queue", "golang", "scheduler"], "category": "Programming Languages", "tags": ["programming-languages", "golang", "resources", "scheduler"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Golang.md", "file_name": "Golang.md", "file_size": 19797, "content_length": 18092, "line_count": 302, "headers": ["1. <PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "1.1. <PERSON><PERSON><PERSON> <PERSON><PERSON> tậ<PERSON> và Roadmap", "1.2. <PERSON><PERSON><PERSON>", "1.3. Mẹo và <PERSON>", "2. <PERSON><PERSON><PERSON> Ứng <PERSON>", "2.1. Web Frameworks và Routing", "2.2. <PERSON><PERSON> sở dữ liệu và ORM", "2.3. Microservices", "2.4. GraphQL", "2.5. <PERSON> Patterns (Mẫu thiết kế)"], "keywords": ["router", "build", "redis", "<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "g<PERSON><PERSON><PERSON>", "medium", "phỏng", "guide", "<PERSON><PERSON><PERSON><PERSON>", "trong", "khai", "thích", "<PERSON><PERSON><PERSON>", "tổng", "thời", "clean", "event", "zalopay", "message"], "category": "Programming Languages", "tags": ["golang", "quan", "tutorial", "roadmap", "programming-languages", "tips"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Google Cloud Platform - GCP.md", "file_name": "Google Cloud Platform - GCP.md", "file_size": 114, "content_length": 114, "line_count": 3, "headers": ["Resources"], "keywords": ["file", "google", "gcp", "platform", "cloud"], "category": "Cloud & DevOps", "tags": ["resources", "google", "cloud-&-devops", "gcp", "platform", "cloud"], "language": "English", "content_type": "General Documentation"}, {"file_path": "GraphQL.md", "file_name": "GraphQL.md", "file_size": 214, "content_length": 214, "line_count": 15, "headers": ["1. Resources", "2. <PERSON><PERSON>", "3. Frameworks"], "keywords": ["graphql"], "category": "General", "tags": ["general", "graphql", "resources", "tools", "frameworks"], "language": "English", "content_type": "General Documentation"}, {"file_path": "HTTP - HTTPS - TLS - SSL.md", "file_name": "HTTP - HTTPS - TLS - SSL.md", "file_size": 4744, "content_length": 3782, "line_count": 68, "headers": ["Resources", "**CÁC METHOD HTTP PHỔ BIẾN**"], "keywords": ["kh<PERSON>ng", "c<PERSON><PERSON>", "http", "ti<PERSON>u", "g<PERSON><PERSON><PERSON>", "delete", "https", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "<PERSON><PERSON><PERSON>", "giúp", "v<PERSON><PERSON><PERSON>", "post", "dụng", "dung", "ch<PERSON><PERSON>", "h<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "thông"], "category": "General", "tags": ["general", "resources", "ssl", "http", "tls", "method", "https"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "IaaS.md", "file_name": "IaaS.md", "file_size": 89, "content_length": 89, "line_count": 2, "headers": ["Resources"], "keywords": ["ubicloud", "iaas"], "category": "General", "tags": ["general", "iaas", "resources"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Inceptionlabs - Viclass.md", "file_name": "Inceptionlabs - Viclass.md", "file_size": 1026, "content_length": 987, "line_count": 31, "headers": ["1. Resources", "2. Editor", "3. <PERSON><PERSON><PERSON>", "3.1. CCS", "3.2. <PERSON><PERSON> <PERSON> <PERSON><PERSON>", "3.3. <PERSON><PERSON>"], "keywords": ["start", "editor", "inceptionlabs", "syncer", "viclass"], "category": "General", "tags": ["general", "resources", "bson", "editor", "inceptionlabs", "viclass", "pojo"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Inceptionlabs.md", "file_name": "Inceptionlabs.md", "file_size": 2188, "content_length": 2163, "line_count": 45, "headers": ["1. <PERSON> Stack Developer", "1.1. <PERSON><PERSON>ả công vi<PERSON><PERSON>", "1.2. <PERSON><PERSON><PERSON> c<PERSON><PERSON> công vi<PERSON>c", "2. <PERSON><PERSON><PERSON><PERSON> lợ<PERSON> đư<PERSON> hưởng"], "keywords": ["code", "experience", "backend", "your", "inceptionlabs", "work", "team", "with", "help", "working"], "category": "General", "tags": ["general", "stack", "inceptionlabs", "developer", "full"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Interview - Phỏng vấn.md", "file_name": "Interview - Phỏng vấn.md", "file_size": 3040, "content_length": 2732, "line_count": 40, "headers": ["Resources", "Sharing", "<PERSON><PERSON><PERSON><PERSON>", "Test"], "keywords": ["nhữ<PERSON>", "phong", "five", "công", "google", "viblo", "pho", "medium", "canva", "dụng", "phỏng", "interview", "levels", "companies", "engineer", "asia", "fresher", "https"], "category": "Interview Preparation", "tags": ["phỏng", "resources", "interview-preparation", "sharing", "interview", "test", "vấn"], "language": "Vietnamese", "content_type": "Interview Guide"}, {"file_path": "Interview - Senior Software Engineer (PHP, Javascript).md", "file_name": "Interview - Senior Software Engineer (PHP, Javascript).md", "file_size": 3746, "content_length": 3135, "line_count": 36, "headers": ["1. <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>"], "keywords": ["kh<PERSON>ng", "c<PERSON><PERSON>", "php", "software", "người", "engineer", "code", "nhữ<PERSON>", "<PERSON><PERSON><PERSON>", "thấy", "restful", "<PERSON><PERSON><PERSON>", "dụng", "singleton", "mình", "framework", "interview", "test", "kinh", "design"], "category": "Programming Languages", "tags": ["(php,", "laravel", "senior", "javascript)", "software", "programming-languages", "interview", "engineer"], "language": "Vietnamese", "content_type": "Interview Guide"}, {"file_path": "Interview Senior Engineer.md", "file_name": "Interview Senior Engineer.md", "file_size": 6609, "content_length": 5348, "line_count": 98, "headers": [], "keywords": ["<PERSON><PERSON><PERSON>", "version", "công", "kh<PERSON>ng", "c<PERSON><PERSON>", "nhân", "phỏng", "ph<PERSON><PERSON>", "blah", "người", "live", "engineer", "đ<PERSON>ng", "c<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "code", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "thời", "thức"], "category": "Interview Preparation", "tags": ["engineer", "interview-preparation", "interview", "senior"], "language": "Vietnamese", "content_type": "Interview Guide"}, {"file_path": "Java Microservices.md", "file_name": "Java Microservices.md", "file_size": 133, "content_length": 123, "line_count": 7, "headers": ["1. Resources"], "keywords": ["java", "microservices"], "category": "Programming Languages", "tags": ["programming-languages", "microservices", "java", "resources"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Java Spring.md", "file_name": "Java Spring.md", "file_size": 437, "content_length": 437, "line_count": 21, "headers": ["1. Resources", "2. <PERSON><PERSON>", "2.1. Authorize", "3. Libraries"], "keywords": ["java", "mapstruct", "spring", "https"], "category": "Programming Languages", "tags": ["resources", "authorize", "spring", "libraries", "programming-languages", "java", "auth"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Java.md", "file_name": "Java.md", "file_size": 4165, "content_length": 4044, "line_count": 125, "headers": ["1. Resources", "1.1. Java deployment", "1.2. Concurrency and Multithreading in Java", "2. Framework", "3. JD<PERSON><PERSON>", "4. Libraries", "4.1. <PERSON><PERSON><PERSON>", "4.2. Validation", "4.3. Connection pool", "5. Notes"], "keywords": ["dagger", "baeldung", "akka", "util", "d<PERSON>ng", "concurrent", "https", "reactive", "trong", "reactor", "boot", "spring", "deploy", "dependency", "collections", "google", "viblo", "framework", "hoặc", "github"], "category": "Programming Languages", "tags": ["multithreading", "resources", "jdks", "notes", "deployment", "tutorial", "programming-languages", "java", "framework", "concurrency"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Javascript - Typescript.md", "file_name": "Javascript - Typescript.md", "file_size": 7529, "content_length": 6889, "line_count": 174, "headers": ["1. JavaScript", "1.1. <PERSON><PERSON><PERSON><PERSON> c<PERSON> & <PERSON><PERSON><PERSON> cao", "1.1.1. <PERSON><PERSON> chế bất đồng bộ", "1.1.2. <PERSON><PERSON><PERSON> năng mới ECMAScript (ES)", "1.1.3. <PERSON><PERSON> thu<PERSON> tối <PERSON>u hóa", "1.1.4. <PERSON><PERSON><PERSON>", "1.1.5. <PERSON><PERSON> <PERSON> nâng cao (<PERSON><PERSON><PERSON> hợp)", "1.1.6. <PERSON><PERSON><PERSON> hỏi phỏng vấn", "2. TypeScript", "2.1. <PERSON><PERSON><PERSON> & <PERSON><PERSON> th<PERSON>"], "keywords": ["build", "promise", "công", "microtask", "g<PERSON><PERSON><PERSON>", "medium", "nâng", "<PERSON><PERSON><PERSON>", "lerna", "t<PERSON>ch", "rspack", "prototype", "https", "code", "thích", "trong", "to<PERSON>", "loop", "quan", "event"], "category": "Programming Languages", "tags": ["ecmascript", "typescript", "tutorial", "programming-languages", "tips", "javascript"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Kafka.md", "file_name": "Kafka.md", "file_size": 303, "content_length": 303, "line_count": 10, "headers": ["1. Resources"], "keywords": ["patterns", "design", "kafka"], "category": "General", "tags": ["general", "resources", "kafka"], "language": "English", "content_type": "General Documentation"}, {"file_path": "<PERSON><PERSON> chung IT.md", "file_name": "<PERSON><PERSON> chung IT.md", "file_size": 1826, "content_length": 1793, "line_count": 49, "headers": ["1. Resources", "2. <PERSON><PERSON>", "3. Research resources", "4. <PERSON><PERSON><PERSON>"], "keywords": ["computer", "science", "chung", "kho", "github", "https"], "category": "General", "tags": ["general", "resources", "chung", "tools", "kho", "orthers", "tips", "research"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Kho tài liệu sau 3 năm học IELTS của t - phần 1.md", "file_name": "Kho tài liệu sau 3 năm học IELTS của t - phần 1.md", "file_size": 15626, "content_length": 15500, "line_count": 73, "headers": [], "keywords": ["unlimited", "file", "kho", "sharing", "sau", "https", "facebook", "listening", "band", "pha", "<PERSON><PERSON><PERSON><PERSON>", "tactics", "english", "google", "vocabulary", "ielts", "drive", "intermediate", "lie", "fbclid"], "category": "English Learning", "tags": ["tài", "liệu", "english-learning", "năm", "kho", "phần", "ielts", "tips", "của", "sau", "học"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Khóa học Golang scalable của Việt Trần.md", "file_name": "Khóa học Golang scalable của Việt Trần.md", "file_size": 3727, "content_length": 3168, "line_count": 92, "headers": ["<PERSON><PERSON><PERSON><PERSON> scalable của Việt Trần", "NGÔN NGỮ GOLANG - KEY FEATURE", "**PHÂN TÍCH DỰ ÁN**", "**THIẾT LẬP DATABASE**", "**VIẾT API (CƠ BẢN) TRONG GOLANG**", "**VIẾT API (MỞ RỘNG) TRONG GOLANG**", "**ASYNC HANDLERS, XỬ LÝ SIDE EFFECT TRONG GOLANG**", "**TRIỂN KHAI (DEPLOY) & MONITORING**", "**SỬ DỤNG GRPC ĐỂ TĂNG TẢI SERVICE**", "**MICROSERVICE CƠ BẢN (KHOÁ LIVESTREAM MỚI)**"], "keywords": ["golang", "c<PERSON><PERSON>", "kho", "t<PERSON>ch", "scalable", "trong", "tra", "monitoring", "grpc", "dụng", "database", "d<PERSON>ng", "module", "b<PERSON>ng", "deploy", "tr<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "t<PERSON>ng", "gi<PERSON>a", "giao"], "category": "Programming Languages", "tags": ["trong", "golang", "việt", "trần", "database", "feature", "programming-languages", "của", "kho<PERSON>a", "scalable", "học"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Kinh nghiệm deal lương.md", "file_name": "Kinh nghiệm deal lương.md", "file_size": 11042, "content_length": 8383, "line_count": 97, "headers": ["**Deal lương - ai trong đời mà không trải qua 1 lần**"], "keywords": ["deal", "tham", "<PERSON><PERSON><PERSON>", "công", "kh<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "ch<PERSON><PERSON>", "phỏng", "ph<PERSON><PERSON>", "người", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cuộ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "trong", "nhữ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ch<PERSON><PERSON>"], "category": "General", "tags": ["general", "deal", "trong", "nghiệm", "lương", "kinh"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Kotlin.md", "file_name": "Kotlin.md", "file_size": 4318, "content_length": 3683, "line_count": 53, "headers": ["1. Resources", "1.1. <PERSON><PERSON>in Coroutines cheat sheet nâng cao dành cho Android Engineer", "2. Libraries / Frameworks", "3. Trick lỏ", "3.1. <PERSON><PERSON><PERSON><PERSON> lý lỗi hiệu quả với `runCatching`"], "keywords": ["ngoại", "th<PERSON>nh", "t<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "koin", "c<PERSON><PERSON>", "cheat", "runcatching", "d<PERSON>ng", "catch", "flow", "hoạt", "https", "block", "trong", "code", "dụng", "value", "sheet", "result"], "category": "Programming Languages", "tags": ["resources", "coroutines", "libraries", "cheat", "programming-languages", "runcatching", "sheet", "frameworks", "kotlin", "engineer", "trick", "android"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Kubernetes - K8S.md", "file_name": "Kubernetes - K8S.md", "file_size": 986, "content_length": 866, "line_count": 16, "headers": ["1. Resources", "2. <PERSON><PERSON>"], "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "triển", "kubernetes", "microservices", "tools", "https"], "category": "Cloud & DevOps", "tags": ["resources", "kubernetes", "k8s", "cloud-&-devops", "tools"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Kudofoto.md", "file_name": "Kudofoto.md", "file_size": 139, "content_length": 139, "line_count": 9, "headers": [], "keywords": ["kudofoto"], "category": "General", "tags": ["general", "kudofoto"], "language": "English", "content_type": "General Documentation"}, {"file_path": "LLM promt engineering.md", "file_name": "LLM promt engineering.md", "file_size": 5176, "content_length": 4240, "line_count": 180, "headers": ["1. <PERSON><PERSON> Prompt <PERSON><PERSON> (Text-Based Prompting)", "1.1.1. Zero-Shot Prompting", "1.1.2. Few-Shot Prompting", "1.1.3. Exemplar Selection", "1.1.4. Instruction Selection", "1.1.5. Emotion Prompting", "1.1.6. Role Prompting", "1.1.7. Style Prompting", "2. <PERSON><PERSON> (Thought Generation)", "2.1.1. Chain-of-Thought (CoT) Prompting"], "keywords": ["generation", "kh<PERSON>ng", "từng", "g<PERSON><PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "d<PERSON>ng", "thought", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON>", "trung", "promt", "quan", "h<PERSON><PERSON>", "v<PERSON><PERSON><PERSON>", "dụng", "<PERSON><PERSON><PERSON><PERSON>", "ch<PERSON><PERSON>", "prompt"], "category": "AI & Machine Learning", "tags": ["prompt", "zero", "ai-&-machine-learning", "based", "exemplar", "llm", "instruction", "promt", "prompting", "engineering", "shot", "tutorial", "text", "selection"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "LM Studio.md", "file_name": "LM Studio.md", "file_size": 310, "content_length": 310, "line_count": 13, "headers": ["1. Resources", "2. SD<PERSON>"], "keywords": ["python", "studio", "lmstudio", "https"], "category": "General", "tags": ["general", "studio", "resources"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Laravel - Eloquent.md", "file_name": "Laravel - Eloquent.md", "file_size": 2088, "content_length": 1648, "line_count": 21, "headers": ["1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "2. <PERSON><PERSON>", "3. <PERSON><PERSON><PERSON><PERSON>"], "keywords": ["mu<PERSON>n", "eager", "kh<PERSON>ng", "model", "relation", "trong", "ch<PERSON><PERSON>", "thức", "load", "dụng", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loadmissing", "<PERSON><PERSON><PERSON><PERSON>", "hoặc", "eloquent", "truy", "with<PERSON><PERSON><PERSON>", "laravel", "<PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "with<PERSON><PERSON><PERSON>", "push", "laravel", "loadmissing", "eloquent"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Laravel.md", "file_name": "Laravel.md", "file_size": 21308, "content_length": 20712, "line_count": 413, "headers": ["1 Resources", "2 Monitors / Debuggers", "3 Front-end", "3.1 Blade & Livewire", "3.2 Inertia", "4 <PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> triển khai dự á<PERSON>", "5 Features", "5.1 Chatting", "5.2 File managers", "6 Frameworks"], "keywords": ["logged", "diffforhumans", "admin", "kh<PERSON>ng", "migrations", "short", "comment", "trong", "khai", "facade", "post", "backup", "cache", "query", "case", "image", "webhook", "request", "performance", "frankenphp"], "category": "General", "tags": ["general", "cheatsheet", "livewire", "resources", "monitors", "inertia", "laravel", "notes", "debuggers", "tips", "front", "blade"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Layered Design in Go - iRi.md", "file_name": "Layered Design in Go - iRi.md", "file_size": 20917, "content_length": 16445, "line_count": 118, "headers": [], "keywords": ["tham", "kh<PERSON>ng", "<PERSON><PERSON><PERSON>", "sạch", "g<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong", "thích", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "thời", "cu<PERSON>i", "<PERSON><PERSON><PERSON><PERSON>", "ti<PERSON>n", "thêm", "chu<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>"], "category": "General", "tags": ["general", "layered", "iri", "design"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Life.md", "file_name": "Life.md", "file_size": 172, "content_length": 168, "line_count": 8, "headers": ["1. Resources"], "keywords": ["life"], "category": "Personal Development", "tags": ["life", "resources", "personal-development"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Linter.md", "file_name": "Linter.md", "file_size": 4178, "content_length": 4178, "line_count": 99, "headers": ["1. ESLint", "2. OXC", "2.1. Key Features and Components", "2.1.1. Pa<PERSON>r and AST", "2.1.2. <PERSON><PERSON> (ox<PERSON>)", "2.1.3. <PERSON><PERSON><PERSON> (oxc_resolver)", "2.1.4. Transformer (oxc-transform)", "2.1.5. Minifier", "2.1.6. <PERSON><PERSON><PERSON>", "2.1.7. Isolated Declarations"], "keywords": ["than", "projects", "parser", "faster", "compiler", "under", "fast", "resolution", "with", "node", "biome", "performance", "without", "rust", "github", "used", "testing", "available", "eslint", "typescript"], "category": "General", "tags": ["general", "parser", "eslint", "linter", "tutorial", "features", "ox<PERSON>", "components"], "language": "English", "content_type": "Tutorial"}, {"file_path": "Linux.md", "file_name": "Linux.md", "file_size": 155, "content_length": 147, "line_count": 5, "headers": ["1. Resources"], "keywords": ["linux"], "category": "General", "tags": ["general", "linux", "resources"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Logging.md", "file_name": "Logging.md", "file_size": 1285, "content_length": 1105, "line_count": 17, "headers": ["1. Resources", "2. Các logging collection service"], "keywords": ["plugin", "<PERSON><PERSON><PERSON><PERSON>", "logstash", "fluent", "thống", "năng", "fluentd", "logging", "th<PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "resources", "service", "logging", "collection"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Lời khuyên.md", "file_name": "Lời khuyên.md", "file_size": 387, "content_length": 307, "line_count": 6, "headers": [], "keywords": ["khuye"], "category": "General", "tags": ["general", "lời", "khuyên"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Lộ trình thi cert AWS Solutions Architect Assoc.md", "file_name": "Lộ trình thi cert AWS Solutions Architect Assoc.md", "file_size": 2138, "content_length": 1694, "line_count": 23, "headers": [], "keywords": ["người", "cert", "assoc", "c<PERSON>ng", "trong", "<PERSON><PERSON><PERSON><PERSON>", "thức", "giúp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "tri", "thi", "tr<PERSON><PERSON>", "journey", "architect", "ki<PERSON>n", "associate", "aws", "solutions", "first"], "category": "Cloud & DevOps", "tags": ["trình", "thi", "cloud-&-devops", "aws", "architect", "lộ", "cert", "assoc", "solutions"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Luận bàn về async.md", "file_name": "Luận bàn về async.md", "file_size": 1874, "content_length": 1581, "line_count": 19, "headers": ["Async trong Javascript", "Async trong PHP"], "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "trong", "stack", "<PERSON><PERSON><PERSON>", "lua", "q<PERSON><PERSON><PERSON>", "call", "async", "thread", "theo", "fiber", "trao", "javascript", "process"], "category": "General", "tags": ["general", "trong", "bàn", "luận", "async", "javascript", "về"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "MCP - Model Context Protocol.md", "file_name": "MCP - Model Context Protocol.md", "file_size": 1304, "content_length": 1243, "line_count": 48, "headers": ["1. Resources", "2. MCP registry", "3. Coding CLI", "3.1. <PERSON><PERSON>s", "4. Automate MCP", "5. Context storage", "6. Cursor IDE", "6.1. Resources", "7. <PERSON><PERSON><PERSON>"], "keywords": ["model", "cursor", "browsermcp", "protocol", "registry", "github", "context", "mcp", "https"], "category": "General", "tags": ["general", "resources", "openhands", "model", "protocol", "registry", "coding", "automate", "context", "mcp"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Machine learning - Deep Learning - AI - ML - DL.md", "file_name": "Machine learning - Deep Learning - AI - ML - DL.md", "file_size": 28517, "content_length": 26569, "line_count": 260, "headers": ["1. Machine Learning, Deep Learning & AI", "1.1. <PERSON><PERSON><PERSON> ng<PERSON> h<PERSON> tập", "1.1.1. <PERSON><PERSON><PERSON><PERSON>", "1.1.2. <PERSON><PERSON><PERSON>", "1.2. Ứng dụng AI SaaS", "1.2.1. <PERSON><PERSON><PERSON> h<PERSON><PERSON>", "1.2.2. Document", "1.3. Tools / Libraries / Frameworks", "1.3.1. Slide", "1.3.2. Media"], "keywords": ["build", "supermemoryai", "kernels", "<PERSON><PERSON><PERSON>", "ph<PERSON>m", "reading", "editor", "kh<PERSON>ng", "g<PERSON><PERSON><PERSON>", "khóa", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong", "khai", "directory", "<PERSON><PERSON><PERSON>", "tổng", "thời"], "category": "AI & Machine Learning", "tags": ["ai-&-machine-learning", "deep", "notes", "saas", "tutorial", "machine", "learning"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Management.md", "file_name": "Management.md", "file_size": 1197, "content_length": 897, "line_count": 10, "headers": ["Resource"], "keywords": ["mạnh", "rằng", "nhất", "kh<PERSON>ng", "c<PERSON><PERSON>", "mình", "management", "người", "<PERSON><PERSON>ận", "l<PERSON>nh"], "category": "Business & Management", "tags": ["resource", "management", "business-&-management"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Microservices.md", "file_name": "Microservices.md", "file_size": 39700, "content_length": 34916, "line_count": 533, "headers": ["1. KHÁI NIỆM & KIẾN TRÚC CỐT LÕI", "1.1. <PERSON><PERSON>", "1.2. <PERSON><PERSON>", "2. CÁC THÀNH PHẦN & CÔNG CỤ TRONG HỆ THỐNG PHÂN TÁN", "2.1. API Gateway & Reverse Proxy", "2.2. <PERSON><PERSON><PERSON>a <PERSON>c <PERSON> (Service Communication)", "2.3. Service Discovery & Registry", "2.4. <PERSON> Mesh", "2.5. Container & Orchestration", "2.6. CI/CD & Feature Flags"], "keywords": ["tham", "redis", "wizard", "g<PERSON><PERSON><PERSON>", "keycloak", "trong", "khai", "formbricks", "giám", "<PERSON><PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "kubernetes", "case", "microservice", "broker", "<PERSON><PERSON><PERSON><PERSON>", "stream", "t<PERSON>h", "ng<PERSON><PERSON><PERSON>", "logstash"], "category": "General", "tags": ["general", "proxy", "trong", "reverse", "notes", "microservices", "gateway"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "MongoDB.md", "file_name": "MongoDB.md", "file_size": 210, "content_length": 202, "line_count": 7, "headers": ["1. Resources"], "keywords": ["mongodb"], "category": "Backend Development", "tags": ["mongodb", "backend-development", "resources"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Monitor server.md", "file_name": "Monitor server.md", "file_size": 190, "content_length": 187, "line_count": 5, "headers": [], "keywords": ["monitor", "server", "cachet"], "category": "General", "tags": ["general", "monitor", "server"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Mục lục thuật toán.md", "file_name": "Mục lục thuật toán.md", "file_size": 4005, "content_length": 3090, "line_count": 55, "headers": ["<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> sắp xếp", "<PERSON><PERSON><PERSON><PERSON> to<PERSON> tìm kiếm", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> m<PERSON>h", "<PERSON><PERSON> lý ngôn ngữ tự nhiên", "Graph"], "keywords": ["th<PERSON>nh", "k<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "trong", "to<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "thua", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "gi<PERSON>a", "thông", "nhất", "tr<PERSON>ng", "phân", "hoặc", "đỉnh", "li<PERSON>n", "ph<PERSON>n", "<PERSON><PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "mục", "lục", "thuật", "<PERSON><PERSON><PERSON><PERSON>"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "MySQL.md", "file_name": "MySQL.md", "file_size": 6551, "content_length": 5228, "line_count": 105, "headers": ["1. Resources", "2. <PERSON><PERSON>", "3. Storage engine"], "keywords": ["th<PERSON>nh", "<PERSON><PERSON><PERSON><PERSON>", "kh<PERSON>ng", "locking", "khóa", "storage", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "https", "<PERSON><PERSON><PERSON><PERSON>", "trong", "thích", "<PERSON><PERSON><PERSON><PERSON>", "thời", "engine", "dụng", "dung", "backup", "b<PERSON>ng", "level"], "category": "Backend Development", "tags": ["resources", "tools", "engine", "storage", "backend-development", "mysql"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "NAT port.md", "file_name": "NAT port.md", "file_size": 435, "content_length": 367, "line_count": 8, "headers": [], "keywords": ["port", "mạng", "nat"], "category": "General", "tags": ["general", "port", "nat"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Nest.md", "file_name": "Nest.md", "file_size": 167, "content_length": 164, "line_count": 7, "headers": ["1. Resources"], "keywords": ["nest"], "category": "General", "tags": ["general", "nest", "resources"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Network.md", "file_name": "Network.md", "file_size": 3911, "content_length": 3167, "line_count": 49, "headers": ["1. Load balancer / Reverse proxy", "1.1. HAProxy", "1.2. <PERSON>voy", "2. <PERSON><PERSON><PERSON>", "2.1. <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON> cụ bắt giữ lưu lượ<PERSON> HTTP(S))"], "keywords": ["q<PERSON><PERSON>", "công", "g<PERSON><PERSON><PERSON>", "http", "d<PERSON>ng", "người", "t<PERSON>ch", "https", "<PERSON><PERSON><PERSON><PERSON>", "load", "dụng", "dung", "<PERSON><PERSON><PERSON><PERSON>", "haproxy", "zstd", "k<PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "balancing", "proxy", "ch<PERSON><PERSON>"], "category": "General", "tags": ["general", "proxy", "envoy", "debugger", "reverse", "load", "tutorial", "http", "haproxy", "network", "proxypin", "balancer"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Nén file.md", "file_name": "Nén file.md", "file_size": 3925, "content_length": 3160, "line_count": 28, "headers": [], "keywords": ["file", "mu<PERSON>n", "size", "kh<PERSON>ng", "từng", "g<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ph<PERSON><PERSON>", "d<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "c<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thời", "k<PERSON><PERSON><PERSON>", "zstd", "nhưng", "nhất", "winrar", "solid", "gian"], "category": "General", "tags": ["general", "ne<PERSON>n", "file"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Nginx.md", "file_name": "Nginx.md", "file_size": 302, "content_length": 299, "line_count": 10, "headers": ["1. Resources"], "keywords": ["https", "nginx"], "category": "General", "tags": ["general", "resources", "nginx"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Những thứ đã học ở Grab tech talk.md", "file_name": "Những thứ đã học ở Grab tech talk.md", "file_size": 2487, "content_length": 2008, "line_count": 29, "headers": ["<PERSON><PERSON><PERSON> ch<PERSON>n công nghệ", "<PERSON><PERSON><PERSON><PERSON> kế", "Performance", "Maintain", "Upgrade"], "keywords": ["<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "tech", "metric", "grab", "d<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "thêm", "t<PERSON><PERSON><PERSON>", "nhu", "gi<PERSON>a", "tr<PERSON><PERSON><PERSON>", "thay", "service", "<PERSON><PERSON><PERSON><PERSON>", "gateway", "truy", "talk"], "category": "General", "tags": ["general", "thứ", "maintain", "upgrade", "tech", "ở", "performance", "đa<PERSON>", "talk", "grab", "những", "học"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "No-code - nocode - low-code - lowcode.md", "file_name": "No-code - nocode - low-code - lowcode.md", "file_size": 138, "content_length": 126, "line_count": 9, "headers": ["1. Resources"], "keywords": ["low", "code", "nocode", "lowcode"], "category": "General", "tags": ["general", "code", "nocode", "resources", "low", "lowcode"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Node.js.md", "file_name": "Node.js.md", "file_size": 3924, "content_length": 3924, "line_count": 74, "headers": ["1. Node.js Overview", "1.1. Frameworks", "1.2. ORMs (OBJECT RELATIONAL MAPPER)", "1.2.1. Additional Frameworks", "1.3. Libraries", "1.3.1. Validators", "1.3.2. Database and ORM", "1.4. Runtime", "1.5. <PERSON><PERSON>"], "keywords": ["redis", "telegraf", "that", "https", "supports", "powerful", "client", "databases", "knex", "database", "with", "time", "rich", "node", "flexible", "framework", "frameworks", "github", "designed", "building"], "category": "General", "tags": ["general", "orms", "overview", "object", "libraries", "node.js", "frameworks", "relational", "mapper", "additional", "node"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Note TOEIC Mỗi Ngày.md", "file_name": "Note TOEIC Mỗi Ngày.md", "file_size": 1360, "content_length": 1136, "line_count": 30, "headers": [], "keywords": ["trạng", "lo<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "s<PERSON>h", "danh", "note", "t<PERSON>h", "<PERSON><PERSON><PERSON>", "nga", "<PERSON>ic"], "category": "English Learning", "tags": ["ngày", "mỗi", "note", "english-learning", "<PERSON>ic"], "language": "Vietnamese", "content_type": "Notes"}, {"file_path": "Note câu hỏi phỏng vấn Laravel.md", "file_name": "Note câu hỏi phỏng vấn Laravel.md", "file_size": 557, "content_length": 468, "line_count": 7, "headers": [], "keywords": ["laravel", "pho", "trang", "note"], "category": "General", "tags": ["general", "phỏng", "vấn", "laravel", "câu", "note", "hỏi"], "language": "Vietnamese", "content_type": "Notes"}, {"file_path": "Note ebook Thuật toán của thầy Lê Minh Hoàng.md", "file_name": "Note ebook Thuật toán của thầy Lê Minh Hoàng.md", "file_size": 151, "content_length": 117, "line_count": 6, "headers": [], "keywords": ["hoa", "minh", "toa", "note", "tha", "thua", "ebook"], "category": "General", "tags": ["general", "minh", "thuật", "hoàng", "notes", "note", "lê", "của", "<PERSON><PERSON><PERSON><PERSON>", "ebook", "thầy"], "language": "Vietnamese", "content_type": "Notes"}, {"file_path": "Note lại từ Huyền Chip.md", "file_name": "Note lại từ Huyền Chip.md", "file_size": 7617, "content_length": 5747, "line_count": 57, "headers": ["Underpromise, overdeliver", "Proactive thinking", "Tại sao phụ nữ yêu đàn ông không tử tế", "<PERSON><PERSON> năng quản lý tài ch<PERSON>h", "<PERSON><PERSON><PERSON><PERSON> nên \"<PERSON><PERSON><PERSON> điều nhịn, ch<PERSON> điều lành\"", "10 điều mình ước mình biết thời đi học"], "keywords": ["th<PERSON>nh", "mu<PERSON>n", "công", "kh<PERSON>ng", "c<PERSON><PERSON>", "q<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "phỏng", "ph<PERSON><PERSON>", "người", "<PERSON><PERSON><PERSON><PERSON>", "xung", "đ<PERSON>ng", "cuộ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong", "c<PERSON>ng", "thời", "giúp"], "category": "General", "tags": ["general", "lại", "overdeliver", "proactive", "note", "huyền", "thinking", "từ", "chip", "underpromise"], "language": "Vietnamese", "content_type": "Notes"}, {"file_path": "Note mẹ.md", "file_name": "Note mẹ.md", "file_size": 24, "content_length": 20, "line_count": 3, "headers": [], "keywords": ["note"], "category": "General", "tags": ["general", "note"], "language": "Vietnamese", "content_type": "Notes"}, {"file_path": "Nướng thịt ở nhà bác Cửu.md", "file_name": "Nướng thịt ở nhà bác Cửu.md", "file_size": 391, "content_length": 293, "line_count": 5, "headers": [], "keywords": ["thi", "nha", "than"], "category": "General", "tags": ["general", "bác", "ở", "cửu", "nướng", "nhà", "thịt"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "OS Scheduler.md", "file_name": "OS Scheduler.md", "file_size": 81, "content_length": 81, "line_count": 3, "headers": ["Resources"], "keywords": ["scheduler"], "category": "General", "tags": ["general", "resources", "scheduler"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Ollama.md", "file_name": "Ollama.md", "file_size": 95, "content_length": 95, "line_count": 7, "headers": ["1. Resources"], "keywords": ["ollama"], "category": "General", "tags": ["general", "ollama", "resources"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Operation.md", "file_name": "Operation.md", "file_size": 120, "content_length": 120, "line_count": 8, "headers": [], "keywords": ["operation"], "category": "General", "tags": ["general", "operation"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Optimization.md", "file_name": "Optimization.md", "file_size": 2220, "content_length": 1685, "line_count": 24, "headers": ["<PERSON><PERSON><PERSON> lý CAP", "<PERSON><PERSON><PERSON> tra hiệu su<PERSON>"], "keywords": ["d<PERSON>ng", "người", "<PERSON><PERSON><PERSON>", "optimization", "trong", "thời", "dụng", "k<PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "nhất", "phân", "<PERSON><PERSON><PERSON><PERSON>", "testing", "quán", "thống", "<PERSON><PERSON><PERSON>", "năng", "<PERSON><PERSON><PERSON>", "t<PERSON>h"], "category": "General", "tags": ["general", "optimization"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "PHP.md", "file_name": "PHP.md", "file_size": 21820, "content_length": 19490, "line_count": 361, "headers": ["1. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> thi", "1.1. SAPI (Server API)", "1.2. Web Servers phổ biến", "1.3. Thread Safety (TS) vs Non-Thread Safe (NTS)", "1.4. Standard PHP Library (SPL)", "2. <PERSON><PERSON><PERSON>", "2.1. <PERSON><PERSON><PERSON>", "2.2. Just-In-Time (JIT) Compilation", "3. <PERSON><PERSON>h thái - Frameworks & Thư viện", "3.1. Frameworks"], "keywords": ["hack", "pest", "trace", "admin", "<PERSON><PERSON><PERSON>", "kh<PERSON>ng", "g<PERSON><PERSON><PERSON>", "php", "<PERSON><PERSON><PERSON>", "guide", "revoltphp", "<PERSON><PERSON><PERSON><PERSON>", "trong", "injection", "<PERSON><PERSON><PERSON>", "thời", "jwts", "ti<PERSON>n", "event", "laracasts"], "category": "Programming Languages", "tags": ["safety", "server", "standard", "library", "notes", "php", "safe", "programming-languages", "sapi", "thread", "tips", "servers"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Phỏng vấn JV-IT.md", "file_name": "Phỏng vấn JV-IT.md", "file_size": 2905, "content_length": 2308, "line_count": 55, "headers": ["<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u bản thân", "C<PERSON>u hỏi nên hỏi công ty", "Hỏi trước HR", "Hỏi HR", "Hỏi leader", "<PERSON><PERSON><PERSON>"], "keywords": ["công", "kh<PERSON>ng", "nhân", "phỏng", "ph<PERSON><PERSON>", "người", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "vi<PERSON>n", "thời", "th<PERSON><PERSON><PERSON>", "phòng", "thằng", "kinh", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pho", "<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "phỏng", "notes", "leader", "vấn"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Phượt.md", "file_name": "Phượt.md", "file_size": 124, "content_length": 106, "line_count": 6, "headers": [], "keywords": ["phu"], "category": "General", "tags": ["general", "phượt"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Post score algorithm - Trending algorithm.md", "file_name": "Post score algorithm - Trending algorithm.md", "file_size": 221, "content_length": 221, "line_count": 12, "headers": ["Resources"], "keywords": ["score", "trending", "algorithm", "post"], "category": "Algorithms & Data Structures", "tags": ["resources", "trending", "algorithms-&-data-structures", "post", "algorithm", "score"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Postgresql.md", "file_name": "Postgresql.md", "file_size": 7635, "content_length": 6601, "line_count": 97, "headers": ["1. Đặc điểm nổi bật", "2. <PERSON><PERSON><PERSON> ng<PERSON> h<PERSON> tập", "3. <PERSON><PERSON><PERSON> (Performance Tuning)", "4. <PERSON><PERSON><PERSON> (Tools)", "5. Mở rộng và Dịch vụ (Extensions & Services)", "5.1. <PERSON>s ph<PERSON> biến", "5.2. <PERSON><PERSON><PERSON> <PERSON><PERSON> (Managed Services)", "5.3. <PERSON><PERSON><PERSON> c<PERSON> liên quan", "6. <PERSON><PERSON> <PERSON> <PERSON><PERSON>", "6.1.1. 🔍 <PERSON><PERSON><PERSON><PERSON> nổi bật"], "keywords": ["postgres", "công", "<PERSON><PERSON><PERSON>", "storage", "<PERSON><PERSON><PERSON>", "hoạt", "t<PERSON>ch", "https", "neondatabase", "mạnh", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "thời", "tuning", "serverless", "v<PERSON><PERSON><PERSON>", "settings", "database", "dụng", "<PERSON><PERSON><PERSON>"], "category": "Backend Development", "tags": ["extensions", "tuning", "postgresql", "performance", "tools", "tutorial", "services", "backend-development"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Product management.md", "file_name": "Product management.md", "file_size": 290, "content_length": 279, "line_count": 9, "headers": ["Resources"], "keywords": ["product", "<PERSON><PERSON><PERSON>", "https", "management"], "category": "Business & Management", "tags": ["product", "resources", "management", "business-&-management"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Promt.md", "file_name": "Promt.md", "file_size": 2641, "content_length": 2636, "line_count": 43, "headers": ["Promt", "Interview", "Stable Diffusion", "Control the camera", "Thần chú tà ác", "Outside", "Negative for best hand"], "keywords": ["eyes", "body", "legs", "fingers", "hands", "smile", "nipples", "pink", "face", "promt", "arms", "extremely", "quality", "worst", "interview", "pussy", "hand", "small", "your", "best"], "category": "General", "tags": ["general", "stable", "promt", "diffusion", "interview", "camera", "control"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Python.md", "file_name": "Python.md", "file_size": 651, "content_length": 639, "line_count": 17, "headers": ["1. Dataset", "2. GUI", "3. Libraries"], "keywords": ["reflex", "python", "github", "https"], "category": "Programming Languages", "tags": ["programming-languages", "python", "dataset", "libraries"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Quy tắc chọn thắt lưng.md", "file_name": "Quy tắc chọn thắt lưng.md", "file_size": 170, "content_length": 137, "line_count": 2, "headers": [], "keywords": ["tha", "cho", "quy"], "category": "General", "tags": ["general", "tắc", "lưng", "quy", "chọn", "thắt"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Quy trình làm việc.md", "file_name": "Quy trình làm việc.md", "file_size": 581, "content_length": 534, "line_count": 9, "headers": ["Agile/Scrum", "OKR"], "keywords": ["trong", "agile", "quy", "vie", "https", "tri"], "category": "General", "tags": ["general", "trình", "việc", "agile", "làm", "quy", "scrum"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Râm Generation.md", "file_name": "Râm Generation.md", "file_size": 1402, "content_length": 1307, "line_count": 45, "headers": ["Râm Generation"], "keywords": ["generation", "civitai", "fbclid", "models", "https"], "category": "General", "tags": ["general", "generation", "râm"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "React - Next.md", "file_name": "React - Next.md", "file_size": 8827, "content_length": 7839, "line_count": 282, "headers": ["1. Resources", "2. <PERSON><PERSON>", "3. Optimization", "4. <PERSON><PERSON>", "5. Design Systems", "6. SSR - Server-side Rendering", "7. Visual builder", "8. Framework", "9. Libraries", "10. <PERSON><PERSON><PERSON><PERSON>"], "keywords": ["router", "build", "công", "<PERSON><PERSON><PERSON>", "chỉnh", "tremor", "learn", "state", "t<PERSON>ch", "hoạt", "https", "vite", "next", "trong", "react", "<PERSON><PERSON><PERSON><PERSON>", "giúp", "component", "refine", "dụng"], "category": "Frontend Development", "tags": ["hooks", "next", "optimization", "react", "resources", "systems", "tutorial", "frontend-development", "kinh", "design"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Reflow, <PERSON>aint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals 2.md", "file_name": "Reflow, <PERSON>aint, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals 2.md", "file_size": 4321, "content_length": 3501, "line_count": 59, "headers": ["1. <PERSON><PERSON><PERSON> quan v<PERSON> Reflow, Repaint và Layout Shift", "2. <PERSON><PERSON><PERSON><PERSON> gâ<PERSON> ra <PERSON>, Repaint và Layout Shift", "3. <PERSON><PERSON><PERSON>u để tránh CLS cao", "3.1. Đặt kích thước rõ ràng cho hình ảnh và video", "3.2. <PERSON><PERSON> trữ không gian cho quảng cáo", "3.3. <PERSON><PERSON><PERSON><PERSON> chèn nội dung động vào giữa nội dung hiện có", "3.4. Sử dụng font chữ hệ thống hoặc tối ưu tải font chữ", "3.5. <PERSON><PERSON><PERSON><PERSON> thay đổi kích th<PERSON><PERSON><PERSON> các phần tử hiện có", "3.6. <PERSON><PERSON> d<PERSON>ng `transform` thay vì `top`, `left`", "3.7. <PERSON><PERSON> dụng `content-visibility: auto`"], "keywords": ["cao", "kh<PERSON>ng", "<PERSON><PERSON><PERSON>", "repaint", "layout", "trong", "<PERSON><PERSON><PERSON><PERSON>", "shift", "<PERSON><PERSON><PERSON>", "giúp", "tra", "reflow", "core", "h<PERSON><PERSON>", "dụng", "dung", "đ<PERSON><PERSON>", "trang", "ch<PERSON>g", "tránh"], "category": "AI & Machine Learning", "tags": ["cao", "repaint", "reflow,", "layout", "trong", "shift", "để", "core", "repaint,", "reflow", "quan", "tra<PERSON><PERSON>", "cls", "ưu", "video"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Reflow, <PERSON><PERSON><PERSON>, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals.md", "file_name": "Reflow, <PERSON><PERSON><PERSON>, Layout Shift là gì? Tối ưu để tránh CLS cao trong Core Web Vitals.md", "file_size": 5740, "content_length": 4787, "line_count": 168, "headers": ["1. <PERSON><PERSON> sao bạn cần hi<PERSON>u <PERSON>, <PERSON><PERSON>t và Layout Shift?", "2. Reflow là gì?", "3. <PERSON><PERSON><PERSON> là gì?", "4. Layout Shift là gì?", "5. <PERSON><PERSON><PERSON> (Cumulative Layout Shift)", "6. <PERSON><PERSON><PERSON> g<PERSON><PERSON> và Repaint không c<PERSON><PERSON> thiết", "7. <PERSON><PERSON><PERSON> tối ưu để tr<PERSON>h CLS cao", "7.1. <PERSON><PERSON><PERSON> đặt chiều cao cố định cho ảnh", "7.2. Tránh font nhảy – preload font sớm", "7.3. Đặt chỗ sẵn cho component động (banner, quảng cáo...)"], "keywords": ["cao", "kh<PERSON>ng", "repaint", "c<PERSON><PERSON>", "tr<PERSON><PERSON>", "d<PERSON>ng", "cumulative", "người", "height", "layout", "<PERSON><PERSON><PERSON><PERSON>", "trong", "banner", "shift", "<PERSON><PERSON><PERSON>", "to<PERSON>", "reflow", "core", "quan", "h<PERSON><PERSON>"], "category": "AI & Machine Learning", "tags": ["cao", "trong", "ai-&-machine-learning", "shift", "vitals", "cls", "t<PERSON>i", "core", "repaint,", "reflow", "repaint", "web", "gì?", "reflow,", "cumulative"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Research websites.md", "file_name": "Research websites.md", "file_size": 1513, "content_length": 1208, "line_count": 18, "headers": [], "keywords": ["k<PERSON><PERSON><PERSON>", "tri<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "trên", "khoa", "websites", "research"], "category": "General", "tags": ["general", "websites", "research"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Roadmap học tiếng Anh từ ChatGPT4.md", "file_name": "Roadmap học tiếng Anh từ ChatGPT4.md", "file_size": 5063, "content_length": 3930, "line_count": 50, "headers": ["Promt", "<PERSON><PERSON><PERSON>"], "keywords": ["tham", "<PERSON><PERSON><PERSON>", "website", "t<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "công", "khóa", "nâng", "ti<PERSON><PERSON>", "người", "<PERSON><PERSON><PERSON><PERSON>", "vựng", "cuộ<PERSON>", "<PERSON><PERSON><PERSON>", "trong", "<PERSON><PERSON><PERSON><PERSON>", "giúp", "thi<PERSON><PERSON>", "v<PERSON><PERSON><PERSON>", "quan"], "category": "AI & Machine Learning", "tags": ["ai-&-machine-learning", "tiếng", "promt", "roadmap", "anwser", "chatgpt4", "từ", "học", "anh"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Rust.md", "file_name": "Rust.md", "file_size": 6086, "content_length": 5091, "line_count": 62, "headers": ["1. <PERSON><PERSON> ch<PERSON> về Rust", "1.1. <PERSON><PERSON><PERSON> (Libraries)", "1.1.1. Mạng và Web", "1.1.2. <PERSON><PERSON> liệu và Serialization", "1.1.3. AI v<PERSON> G<PERSON><PERSON>", "1.1.4. <PERSON><PERSON> lý lỗi", "1.2. <PERSON><PERSON><PERSON> (Tools)", "1.2.1. <PERSON><PERSON><PERSON><PERSON> lý dự án và Build", "1.2.2. <PERSON><PERSON><PERSON> lư<PERSON> mã và Môi trường phát triển", "2. Framework"], "keywords": ["công", "c<PERSON><PERSON>", "nghệ", "d<PERSON>ng", "dàng", "người", "https", "mạnh", "<PERSON><PERSON><PERSON><PERSON>", "trong", "<PERSON><PERSON><PERSON><PERSON>", "thời", "giúp", "k<PERSON><PERSON><PERSON>", "cross", "dụng", "t<PERSON><PERSON><PERSON>", "d<PERSON>ng", "viện", "<PERSON><PERSON><PERSON>"], "category": "Programming Languages", "tags": ["giao", "notes", "serialization", "libraries", "rust", "programming-languages"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "SAP - Systems, Applications, and Products.md", "file_name": "SAP - Systems, Applications, and Products.md", "file_size": 1824, "content_length": 1496, "line_count": 33, "headers": ["1. **🔍 SAP là gì?**", "2. **⚙️ <PERSON><PERSON><PERSON> dòng sản phẩm ch<PERSON>h của SAP:**", "3. **🎯 SAP g<PERSON><PERSON><PERSON> do<PERSON>h nghiệp thế nào?**"], "keywords": ["and", "ch<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "sap", "công", "thống", "systems", "products", "applications", "<PERSON><PERSON><PERSON><PERSON>", "q<PERSON><PERSON><PERSON>", "do<PERSON>h"], "category": "General", "tags": ["general", "and", "sap", "applications,", "products", "systems,", "do<PERSON>h"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "SEO Content.md", "file_name": "SEO Content.md", "file_size": 401, "content_length": 371, "line_count": 8, "headers": ["Resources"], "keywords": ["gtvseo", "seo", "https", "content"], "category": "General", "tags": ["general", "seo", "resources", "content"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "SaaS.md", "file_name": "SaaS.md", "file_size": 1695, "content_length": 1559, "line_count": 41, "headers": ["1. Resources"], "keywords": ["firebase", "<PERSON><PERSON><PERSON><PERSON>", "messaging", "saas", "tailscale", "storage", "ph<PERSON>i", "builder", "https", "cloud"], "category": "General", "tags": ["general", "tutorial", "resources", "saas"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Security.md", "file_name": "Security.md", "file_size": 1365, "content_length": 1282, "line_count": 22, "headers": ["1. Resources", "2. OSWAP", "3. Secret manager"], "keywords": ["owasp", "part", "secret", "viblo", "snort", "manager", "dụng", "idor", "dung", "theo", "asia", "https", "security"], "category": "General", "tags": ["general", "oswap", "resources", "secret", "manager", "security"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Severless.md", "file_name": "Severless.md", "file_size": 371, "content_length": 363, "line_count": 9, "headers": [], "keywords": ["serverless", "https", "severless"], "category": "General", "tags": ["general", "severless"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Skincare.md", "file_name": "Skincare.md", "file_size": 38606, "content_length": 31979, "line_count": 601, "headers": ["1. <PERSON><PERSON><PERSON><PERSON>' routine (b<PERSON><PERSON> v<PERSON><PERSON><PERSON> da li<PERSON>)", "2. Resources", "3. <PERSON><PERSON><PERSON>n phẩm đang consider (đợi khám da liễu và tư vấn)", "4. <PERSON><PERSON><PERSON> trang", "5. <PERSON><PERSON><PERSON><PERSON> mặt", "6. <PERSON><PERSON> (dùng để cân bằng pH)", "7. <PERSON><PERSON>", "7.1. <PERSON><PERSON><PERSON> (này là vitamin A)", "7.2. <PERSON><PERSON>", "7.3. <PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> (thường đ<PERSON><PERSON><PERSON> biết đến là <PERSON>min B3)"], "keywords": ["vichy", "tham", "roche", "witch", "lutein", "ch<PERSON>", "cerave", "kh<PERSON>ng", "ph<PERSON>m", "sạch", "<PERSON><PERSON><PERSON>", "khóa", "b<PERSON><PERSON><PERSON>", "nhờn", "klairs", "vitamin", "<PERSON><PERSON><PERSON>", "neutrogena", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "category": "Lifestyle & Fashion", "tags": ["consider", "resources", "routine", "ngosang<PERSON>", "trang", "skincare", "lifestyle-&-fashion"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Softskill - Kỹ năng mềm.md", "file_name": "Softskill - Kỹ năng mềm.md", "file_size": 348, "content_length": 313, "line_count": 7, "headers": ["1. Resources"], "keywords": ["softskill"], "category": "Personal Development", "tags": ["softskill", "resources", "kỹ", "mềm", "năng", "personal-development"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Software Engineer Roadmap 2025 - The Complete Guide.md", "file_name": "Software Engineer Roadmap 2025 - The Complete Guide.md", "file_size": 9285, "content_length": 9228, "line_count": 127, "headers": ["1. <PERSON>", "2. Data Structures & Algorithms", "3. Testing", "4. <PERSON> Patterns", "5. Cloud Services", "6. CI/CD", "7. System Design", "8. Performance Optimization", "8.1. Frontend Optimization", "8.2. Backend Optimization"], "keywords": ["complete", "development", "learn", "about", "efficient", "storage", "that", "this", "pipelines", "guide", "the", "documentation", "examples", "main", "devmastery", "performance", "structures", "secure", "help", "there"], "category": "General", "tags": ["general", "the", "data", "testing", "structures", "software", "roadmap", "complete", "tools", "algorithms", "patterns", "2025", "engineer", "guide", "services"], "language": "English", "content_type": "General Documentation"}, {"file_path": "SolidJS.md", "file_name": "SolidJS.md", "file_size": 152, "content_length": 148, "line_count": 1, "headers": [], "keywords": ["solidjs"], "category": "General", "tags": ["general", "solidjs"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Solution sao lưu lịch sử chỉnh sửa.md", "file_name": "Solution sao lưu lịch sử chỉnh sửa.md", "file_size": 2955, "content_length": 2407, "line_count": 45, "headers": [], "keywords": ["version", "document", "tr<PERSON><PERSON><PERSON>", "solution", "sao", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "rule", "change", "k<PERSON><PERSON><PERSON>", "dung", "chi", "b<PERSON>ng", "node", "thằng", "thông", "nhưng", "nhất", "thay", "<PERSON><PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "lưu", "chỉnh", "solution", "sửa", "sao", "sử", "lịch"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Solutions & System Designs & Design Patterns.md", "file_name": "Solutions & System Designs & Design Patterns.md", "file_size": 60365, "content_length": 51529, "line_count": 711, "headers": ["1. <PERSON><PERSON><PERSON> quan về <PERSON>ết kế Hệ thống", "1.1. <PERSON><PERSON><PERSON> bản", "1.1.1. <PERSON><PERSON><PERSON> (Trade-offs)", "*******. <PERSON><PERSON><PERSON> đổi giữa tính nhất quán và tính sẵn sàng", "1.2. <PERSON><PERSON><PERSON>n đề Thường Gặp và G<PERSON>ải Pháp trong Thiết kế Hệ thống", "1.3. <PERSON><PERSON><PERSON> l<PERSON> và <PERSON>ng cụ <PERSON> kh<PERSON>o", "2. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> mề<PERSON>", "2.1. <PERSON><PERSON><PERSON> Mẫu <PERSON>ến trúc <PERSON> biến", "2.1.1. Clean Architecture", "2.1.2. Hexagonal Architecture (Ports and Adapters Architecture)"], "keywords": ["tham", "router", "redis", "version", "principles", "g<PERSON><PERSON><PERSON>", "real", "aggregator", "reddit", "trong", "khai", "availability", "value", "giám", "<PERSON><PERSON><PERSON><PERSON>", "designs", "đ<PERSON><PERSON>", "cache", "query", "kubernetes"], "category": "General", "tags": ["general", "offs", "trong", "system", "cheatsheet", "notes", "patterns", "quan", "trade", "tutorial", "designs", "tips", "design", "solutions"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Some shit I need.md", "file_name": "Some shit I need.md", "file_size": 12172, "content_length": 9168, "line_count": 68, "headers": ["40 THÓI QUEN TỐT CHO NGƯỜI MUỐN SỐNG LÂU VÀ KHOẺ MẠNH", "20 NGHỊCH LÝ Ở ĐỜI TÔI ĐÃ TỪNG GẶP VÀ LUÔN ĐÚNG"], "keywords": ["th<PERSON>nh", "h<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "khỏi", "công", "kh<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "c<PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "t<PERSON><PERSON><PERSON>", "ph<PERSON><PERSON>", "nhân", "<PERSON><PERSON><PERSON>", "ti<PERSON><PERSON>", "ph<PERSON><PERSON>", "người", "đ<PERSON>ng", "lòng", "cuộ<PERSON>"], "category": "General", "tags": ["general", "shit", "some", "need", "quen"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Sống Platform.md", "file_name": "Sống Platform.md", "file_size": 554, "content_length": 552, "line_count": 33, "headers": ["FTP", "Server kh<PERSON><PERSON> h<PERSON>ng", "SSH", "Website B2C", "Website B2B"], "keywords": ["user", "admin", "songplatform", "password", "platform"], "category": "General", "tags": ["general", "website", "server", "platform", "sống"], "language": "English", "content_type": "Technical Documentation"}, {"file_path": "Target of users in a workspace.md", "file_name": "Target of users in a workspace.md", "file_size": 3238, "content_length": 2877, "line_count": 52, "headers": ["<PERSON>ột số kh<PERSON>i ni<PERSON>", "**Orther**", "Target", "Scene", "**Target & Scene**", "**Target & Scene period (month)**", "**Target period (month)**"], "keywords": ["th<PERSON>nh", "k<PERSON><PERSON><PERSON>", "từng", "total", "target", "inclusive", "trong", "<PERSON><PERSON><PERSON>", "scene", "workspace", "expected", "levels", "ng<PERSON>y", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "thực", "days", "users", "user", "points"], "category": "General", "tags": ["general", "users", "scene", "workspace", "target", "orther"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Tạo video bằng các tool AI.md", "file_name": "Tạo video bằng các tool AI.md", "file_size": 1069, "content_length": 881, "line_count": 25, "headers": ["1. T<PERSON>o video bằng các tool AI", "1.1. **TẠO VIDEO SOCIAL KHÔNG CẦN CAMERA HAY THU GIỌNG CHUYÊN NGHIỆP, CHỈ CẦN CÓ AI**", "1.2. **A. TRANG BỊ:**", "1.3. **B. LÀM THEO CÁC BƯỚC SAU**"], "keywords": ["bằng", "clone", "video", "b<PERSON><PERSON><PERSON>", "theo", "ti<PERSON><PERSON>", "tool", "g<PERSON><PERSON><PERSON>"], "category": "AI & Machine Learning", "tags": ["social", "ai-&-machine-learning", "bằng", "các", "video", "tạo", "theo", "camera", "tool", "trang"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Tập thể dục.md", "file_name": "Tập thể dục.md", "file_size": 410, "content_length": 410, "line_count": 13, "headers": [], "keywords": ["the"], "category": "General", "tags": ["general", "tập", "thể", "dục"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Terminal UI - TUI.md", "file_name": "Terminal UI - TUI.md", "file_size": 521, "content_length": 521, "line_count": 22, "headers": ["1. Resources"], "keywords": ["github", "tui", "cointop", "terminal", "https"], "category": "General", "tags": ["general", "tui", "terminal", "resources"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Testing.md", "file_name": "Testing.md", "file_size": 32702, "content_length": 26356, "line_count": 429, "headers": ["1. Resources", "2. <PERSON><PERSON><PERSON>", "3. <PERSON><PERSON><PERSON> \"uống lộn thuốc\" ở các hệ thống mình gặp", "4. <PERSON><PERSON><PERSON> số lo<PERSON>i test", "4.1. Functional Test (<PERSON><PERSON><PERSON> thử chức năng)", "4.2. Non-Functional Test (<PERSON><PERSON><PERSON> thử phi chức năng)", "4.3. <PERSON><PERSON><PERSON> so s<PERSON>h nhanh Functional vs Non-Functional Test", "4.4. Architecture Testing", "4.4.1. <PERSON><PERSON><PERSON><PERSON> và <PERSON>ch", "4.4.2. <PERSON><PERSON> trình Architecture Testing"], "keywords": ["build", "kh<PERSON>ng", "<PERSON><PERSON><PERSON>", "analysis", "<PERSON><PERSON><PERSON>", "score", "<PERSON><PERSON><PERSON><PERSON>", "perf", "<PERSON><PERSON><PERSON><PERSON>", "trong", "nhữ<PERSON>", "to<PERSON>", "<PERSON><PERSON><PERSON>", "cu<PERSON>i", "thời", "tổng", "thêm", "<PERSON><PERSON><PERSON><PERSON>", "đ<PERSON><PERSON>", "h<PERSON><PERSON>"], "category": "General", "tags": ["general", "functional", "resources", "tutorial", "sharing", "test", "testing"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "Thanh toán chuyển khoản ngân hàng.md", "file_name": "Thanh toán chuyển khoản ngân hàng.md", "file_size": 4306, "content_length": 3423, "line_count": 54, "headers": ["<PERSON><PERSON> to<PERSON> chuyển khoản ngân hàng", "**VỀ CƠ CHẾ HOẠT ĐỘNG**", "**VỀ CÁI VỤ BANK**"], "keywords": ["c<PERSON><PERSON>", "description", "d<PERSON>ng", "vọng", "<PERSON><PERSON><PERSON><PERSON>", "trong", "c<PERSON>ng", "to<PERSON>", "code", "ri<PERSON>ng", "k<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON>", "dụng", "<PERSON><PERSON><PERSON><PERSON>", "h<PERSON><PERSON>", "pháp", "giao", "nhưng", "nhất", "chuye"], "category": "General", "tags": ["general", "chuyển", "ngân", "thanh", "bank", "<PERSON><PERSON><PERSON><PERSON>", "khoản", "hàng"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Thống kê tủ đồ hiện tại.md", "file_name": "Thống kê tủ đồ hiện tại.md", "file_size": 186, "content_length": 163, "line_count": 10, "headers": ["<PERSON><PERSON><PERSON> mua thêm"], "keywords": ["hie", "tho"], "category": "General", "tags": ["general", "đồ", "tại", "tủ", "kê", "thống", "hiện"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Tools.md", "file_name": "Tools.md", "file_size": 233, "content_length": 223, "line_count": 7, "headers": [], "keywords": ["tools", "maybe"], "category": "General", "tags": ["general", "tools"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Top 10 câu hỏi phỏng vấn System Design và Microservices.md", "file_name": "Top 10 câu hỏi phỏng vấn System Design và Microservices.md", "file_size": 6207, "content_length": 4817, "line_count": 87, "headers": ["0.1. System Design:", "0.1.1. <PERSON><PERSON><PERSON> trình bày gi<PERSON>p mình về định lý CAP?", "0.1.2. <PERSON><PERSON><PERSON> bảng có lượng dữ liệu lớn và tăng dần theo thời gian, khi<PERSON><PERSON> cho các query tới bảng cũng chậm dần. Bạn sẽ xử lý như nào?", "0.1.3. <PERSON><PERSON><PERSON><PERSON> <PERSON>ế hệ thống TinyURL?", "0.2. Microservice:", "0.2.1. <PERSON><PERSON><PERSON> so s<PERSON>h ưu nh<PERSON><PERSON><PERSON> điểm của monolithic và microservices?", "0.2.2. <PERSON><PERSON>i sao bạn lại chia service như này?", "0.2.3. Order được xử lý lần lư<PERSON><PERSON> qua nhiều service. Do một sự cố nào đó, 1 service X trong đó bị down. Bạn xử lý để đảm bảo order đó được thực thi tiếp ngay khi service X sống lại?", "0.2.4. Service A cần dữ liệu user của service B nhưng service B có user schema khác so với của service A. Bạn sẽ xử lý tình huống này như nào?", "0.2.5. Frontend gửi request order tới API Gateway thông qua REST API, order đư<PERSON>c điều hướng tới service A. Service A xử lý xong gửi order sang service B thông qua message queue. Service B xử lý xong là hết thúc quá trình xử lý order. <PERSON>àm sao để hệ thống trả lại response cho frontend?"], "keywords": ["tham", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "order", "g<PERSON><PERSON><PERSON>", "c<PERSON><PERSON>", "ti<PERSON><PERSON>", "người", "engineer", "top", "https", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "trong", "system", "<PERSON><PERSON><PERSON><PERSON>", "thêm", "dụng", "t<PERSON><PERSON><PERSON>", "message"], "category": "General", "tags": ["general", "phỏng", "vấn", "system", "query", "<PERSON><PERSON><PERSON>", "câu", "microservice", "microservices", "và", "gian", "theo", "hỏi", "top", "design"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Top 50 React interview quetions.md", "file_name": "Top 50 React interview quetions.md", "file_size": 9215, "content_length": 9197, "line_count": 147, "headers": [], "keywords": ["directly", "development", "into", "that", "state", "concept", "react", "between", "term", "known", "event", "allows", "action", "information", "framework", "interview", "kind", "read", "redux", "create"], "category": "Frontend Development", "tags": ["react", "quetions", "interview", "frontend-development", "top"], "language": "English", "content_type": "Interview Guide"}, {"file_path": "Tổng hợp các nguồn ôn luyện thuật toán & Coding interview.md", "file_name": "Tổng hợp các nguồn ôn luyện thuật toán & Coding interview.md", "file_size": 7173, "content_length": 6367, "line_count": 99, "headers": [], "keywords": ["tham", "nguo", "cu<PERSON>n", "website", "<PERSON><PERSON><PERSON><PERSON>", "mu<PERSON>n", "công", "future", "g<PERSON><PERSON><PERSON>", "software", "tech", "khóa", "nghệ", "phỏng", "người", "posts", "https", "<PERSON><PERSON><PERSON><PERSON>", "nhữ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "category": "Interview Preparation", "tags": ["nguồn", "ôn", "interview-preparation", "các", "thuật", "tổng", "tips", "interview", "luyện", "<PERSON><PERSON><PERSON><PERSON>", "hợp", "coding"], "language": "Vietnamese", "content_type": "Interview Guide"}, {"file_path": "Trải nghiệm.md", "file_name": "Trải nghiệm.md", "file_size": 101, "content_length": 90, "line_count": 6, "headers": [], "keywords": ["tra", "nghie"], "category": "General", "tags": ["general", "trải", "nghiệm"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Troodonlabs.md", "file_name": "Troodonlabs.md", "file_size": 158, "content_length": 156, "line_count": 13, "headers": [], "keywords": ["troodonlabs"], "category": "General", "tags": ["general", "troodonlabs"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Tự vệ.md", "file_name": "Tự vệ.md", "file_size": 59, "content_length": 59, "line_count": 3, "headers": ["Resource"], "keywords": [], "category": "General", "tags": ["general", "tự", "resource", "vệ"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Ứng tuyển.md", "file_name": "Ứng tuyển.md", "file_size": 3047, "content_length": 2896, "line_count": 64, "headers": ["Resources", "Đã phỏng vấn", "Failed", "Đã từ chối offer", "<PERSON><PERSON><PERSON> tuyển dụng", "<PERSON><PERSON><PERSON> công ty outsource", "Các công ty product", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> nên apply", "<PERSON><PERSON><PERSON> apply"], "keywords": ["công", "technology", "momo", "phỏng", "vnpay", "https", "viec", "tuyen", "<PERSON><PERSON><PERSON><PERSON>", "jobs", "search", "dụng", "dung", "location", "minh", "academy", "apply", "page", "vnpt", "tuye"], "category": "General", "tags": ["general", "resources", "failed", "ứng", "offer", "tuyển"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "VPC - Virtual Private Cloud - AZ - Availability Zone.md", "file_name": "VPC - Virtual Private Cloud - AZ - Availability Zone.md", "file_size": 7015, "content_length": 5918, "line_count": 134, "headers": ["1. VPC trong thiết kế AWS network là gì?", "1.1. <PERSON> tiết về Vùng VPC trong thiết kế mạng AWS", "1.2. <PERSON><PERSON>ụ minh h<PERSON>a", "1.3. <PERSON><PERSON><PERSON> <PERSON> ng<PERSON> gọn", "2. VPC - AZ - subnet", "2.1. **VPC (Virtual Private Cloud)**", "2.1.1. <PERSON><PERSON><PERSON><PERSON>:", "2.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ch<PERSON>h của VPC:", "2.1.3. <PERSON><PERSON> dụ trực quan:", "2.2. **Availability Zone (AZ)**"], "keywords": ["th<PERSON>nh", "vpc", "vùng", "route", "sàng", "kh<PERSON>ng", "internet", "tr<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "singapore", "ri<PERSON>ng", "<PERSON><PERSON><PERSON><PERSON>", "trong", "khai", "cidr", "block", "availability", "southeast"], "category": "AI & Machine Learning", "tags": ["trong", "private", "ai-&-machine-learning", "vpc", "availability", "zone", "minh", "subnet", "network", "virtual", "cloud"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "VPN - Proxy - Firewall.md", "file_name": "VPN - Proxy - Firewall.md", "file_size": 566, "content_length": 554, "line_count": 15, "headers": ["1. Services", "2. SaaS"], "keywords": ["proxy", "vpn", "firewall", "github", "pfsense", "https"], "category": "General", "tags": ["general", "proxy", "vpn", "saas", "firewall", "services"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "VPS - Hosting.md", "file_name": "VPS - Hosting.md", "file_size": 7629, "content_length": 6333, "line_count": 135, "headers": ["1. <PERSON><PERSON><PERSON><PERSON> lý Firewall với UFW và Docker", "1.1. UFW (Uncomplicated Firewall)", "1.1.1. <PERSON><PERSON><PERSON><PERSON>i<PERSON>", "1.1.2. <PERSON><PERSON><PERSON> <PERSON>", "1.2. <PERSON><PERSON><PERSON> đ<PERSON> tương thích giữa UFW và Docker", "1.2.1. <PERSON><PERSON><PERSON> qu<PERSON>n lý mạng", "1.2.2. <PERSON><PERSON> đ<PERSON><PERSON> với UFW", "1.3. <PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON>: ufw-docker", "1.3.1. <PERSON><PERSON><PERSON><PERSON>i<PERSON>", "1.3.2. <PERSON><PERSON><PERSON> độ<PERSON> (kh<PERSON><PERSON> quát)"], "keywords": ["allow", "công", "chỉnh", "deny", "c<PERSON><PERSON>", "tr<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "docker", "return", "d<PERSON>ng", "t<PERSON><PERSON><PERSON>", "người", "sudo", "hoạt", "t<PERSON>ch", "systemctl", "trong", "khai", "cu<PERSON>i", "giúp"], "category": "General", "tags": ["general", "hosting", "firewall", "docker", "uncomplicated", "vps"], "language": "Vietnamese", "content_type": "Technical Documentation"}, {"file_path": "Vietop.md", "file_name": "Vietop.md", "file_size": 354, "content_length": 354, "line_count": 17, "headers": ["Staging"], "keywords": ["database", "viet<PERSON>", "staging"], "category": "General", "tags": ["general", "viet<PERSON>", "staging"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Vue - Nuxt.md", "file_name": "Vue - Nuxt.md", "file_size": 11334, "content_length": 10756, "line_count": 189, "headers": ["1. Mẹo <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "1.1. Validating", "1.2. Reactive", "2. <PERSON><PERSON><PERSON><PERSON>", "3. Libraries và Công cụ", "3.1. Form", "3.1.1. Form Validation", "3.1.2. Form Generator / Builder", "3.2. State Management", "3.3. <PERSON>outing"], "keywords": ["router", "admin", "primevue", "<PERSON><PERSON><PERSON>", "form", "netlify", "<PERSON><PERSON><PERSON><PERSON>", "trong", "thích", "pinia", "<PERSON><PERSON><PERSON>", "tổng", "fluent", "fullpage", "vorms", "viện", "cypress", "histoire", "query", "request"], "category": "Frontend Development", "tags": ["reactive", "cheatsheet", "libraries", "vue", "validating", "frontend-development", "nuxt", "tips"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Windows Tips.md", "file_name": "Windows Tips.md", "file_size": 4927, "content_length": 3921, "line_count": 57, "headers": ["LOGIN NHANH HƠN VỚI TUỲ CHỌN SIGN-IN OPTIONS", "TẮT STICKY KEYS", "DARK MODE", "LỊCH SỬ CLIPBOARD", "TẮT ĐỀ XUẤT TRONG MENU START", "BỘ LỌC ÁNH SÁNG XANH WINDOWS", "BONUS: MỘT SỐ TÍNH NĂNG ẨN CỦA WINDOWS"], "keywords": ["start", "ch<PERSON>g", "kh<PERSON>ng", "keys", "<PERSON><PERSON><PERSON>", "ph<PERSON><PERSON>", "người", "account", "<PERSON><PERSON><PERSON><PERSON>", "sticky", "nhữ<PERSON>", "trong", "<PERSON><PERSON><PERSON><PERSON>", "login", "<PERSON><PERSON><PERSON>", "mode", "giúp", "nhấn", "h<PERSON><PERSON>", "dụng"], "category": "General", "tags": ["general", "dark", "mode", "sticky", "login", "trong", "start", "clipboard", "keys", "menu", "options", "tips", "sign", "windows", "nhanh"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Wordpress.md", "file_name": "Wordpress.md", "file_size": 527, "content_length": 523, "line_count": 14, "headers": ["1. Element builder", "2. Libraries/Packages"], "keywords": ["theme", "wordpress", "https"], "category": "General", "tags": ["general", "wordpress", "element", "packages", "libraries", "builder"], "language": "English", "content_type": "General Documentation"}, {"file_path": "Work.md", "file_name": "Work.md", "file_size": 387, "content_length": 368, "line_count": 15, "headers": ["Resources", "Thông tin khác"], "keywords": ["work"], "category": "General", "tags": ["general", "work", "resources"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Working day of users in a workspace.md", "file_name": "Working day of users in a workspace.md", "file_size": 941, "content_length": 811, "line_count": 11, "headers": ["<PERSON>ột số kh<PERSON>i ni<PERSON>"], "keywords": ["users", "trong", "user", "<PERSON><PERSON><PERSON>", "extra", "workspace", "month", "holiday", "th<PERSON>g", "ng<PERSON>y", "day", "working"], "category": "General", "tags": ["general", "users", "workspace", "day", "working"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "Workspace.md", "file_name": "Workspace.md", "file_size": 169, "content_length": 152, "line_count": 3, "headers": ["Resource"], "keywords": ["workspace"], "category": "General", "tags": ["general", "workspace", "resource"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "ngosangns - home.md", "file_name": "ngosangns - home.md", "file_size": 48536, "content_length": 39247, "line_count": 702, "headers": ["1. Inceptionlabs", "2. Personal", "2.1. <PERSON><PERSON><PERSON>", "2.2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "2.3. NS Money", "2.4. <PERSON> đa n<PERSON>ng", "2.5. Camera", "2.6. <PERSON><PERSON><PERSON>", "2.7. <PERSON><PERSON><PERSON>", "2.8. <PERSON><PERSON>"], "keywords": ["tham", "build", "kh<PERSON>ng", "ph<PERSON>m", "<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "cohesion", "analysis", "phỏng", "<PERSON><PERSON><PERSON>", "score", "<PERSON><PERSON><PERSON><PERSON>", "python", "<PERSON><PERSON><PERSON><PERSON>", "trong", "nhữ<PERSON>", "to<PERSON>", "<PERSON><PERSON><PERSON>", "thời", "tổng"], "category": "General", "tags": ["general", "home", "ngosang<PERSON>", "inceptionlabs", "nhamayquangphu", "tutorial", "money", "vocab", "personal"], "language": "Vietnamese", "content_type": "Tutorial"}, {"file_path": "viclass - 752 - Make it easier to create account for user to experience the beta system.md", "file_name": "viclass - 752 - Make it easier to create account for user to experience the beta system.md", "file_size": 1422, "content_length": 1422, "line_count": 41, "headers": [], "keywords": ["redis", "experience", "account", "the", "system", "login", "email", "beta", "time", "send", "automatically", "link", "easier", "currently", "user", "create", "record", "viclass", "make", "for"], "category": "General", "tags": ["general", "the", "user", "system", "experience", "create", "752", "beta", "account", "easier", "viclass", "make", "for"], "language": "English", "content_type": "General Documentation"}, {"file_path": "viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md", "file_name": "viclass - 757 - Geo editor - Enhancing User Experience in Geo Editor Settings Flow.md", "file_size": 4325, "content_length": 4014, "line_count": 110, "headers": ["1. Definitions & Improved Flow", "1.1. Definitions", "1.1.1. Configuration Sc<PERSON><PERSON>:", "1.1.2. <PERSON><PERSON>"], "keywords": ["line", "label", "experience", "fields", "editor", "multigroup", "shape", "color", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "groups", "geo", "nhãn", "true", "flow", "field", "settings", "h<PERSON><PERSON>", "multipleitem", "v<PERSON><PERSON><PERSON>"], "category": "General", "tags": ["general", "improved", "user", "experience", "configuration", "scenario", "fields", "757", "editor", "definitions", "settings", "enhancing", "setting", "geo", "viclass"], "language": "Vietnamese", "content_type": "General Documentation"}, {"file_path": "<PERSON>i du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu.md", "file_name": "<PERSON>i du lịch Hồ Chí Minh - Cần Giờ - Vũng Tàu.md", "file_size": 3457, "content_length": 2748, "line_count": 54, "headers": [], "keywords": ["mu<PERSON>n", "tràm", "ch<PERSON><PERSON>", "q<PERSON><PERSON><PERSON>", "b<PERSON>h", "biển", "<PERSON><PERSON><PERSON><PERSON>", "thím", "thịt", "chi", "vũng", "phan", "tr<PERSON><PERSON><PERSON>", "minh", "hoặc", "quán", "gio", "lagi", "tr<PERSON><PERSON>", "nướng"], "category": "General", "tags": ["general", "cần", "giờ", "hồ", "minh", "chí", "tàu", "vũng", "lịch"], "language": "Vietnamese", "content_type": "General Documentation"}]}